using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Threading;
using System.Threading.Tasks;
using System.Xml.Serialization;
using McLaser.Device;
using GxIAPINET;
using HalconDotNet;
using System.Windows.Controls;
using McLaser.Modules.Vision;

namespace McLaser.Devices.Camera
{
    [DeviceItem("工业相机", "大恒相机", "兼容网口/USB3系列", "📷")]
    public class CameraDaheng : CameraBase
    {
        #region 私有字段

        /// <summary>
        /// 大恒相机设备对象
        /// </summary>
        private IGXDevice m_objIGXDevice = null;

        /// <summary>
        /// 大恒相机流对象
        /// </summary>
        private IGXStream m_objIGXStream = null;

        /// <summary>
        /// 大恒相机工厂对象
        /// </summary>
        private IGXFactory m_objIGXFactory = null;

        /// <summary>
        /// 大恒相机特征控制对象
        /// </summary>
        private IGXFeatureControl m_objIGXFeatureControl = null;

        /// <summary>
        /// 大恒相机流层属性控制器对象
        /// </summary>
        private IGXFeatureControl m_objIGXStreamFeatureControl = null;

        /// <summary>
        /// 图像更新事件
        /// </summary>
        private readonly ManualResetEvent updateImageEvent = new ManualResetEvent(false);

        /// <summary>
        /// 请求更新标志
        /// </summary>
        private long isRequestUpdate = 0;

        /// <summary>
        /// 当前帧数据
        /// </summary>
        private IFrameData frameData = null;

        /// <summary>
        /// 掉线回调句柄
        /// </summary>
        private GX_DEVICE_OFFLINE_CALLBACK_HANDLE m_hCB = null; 

        private bool OfflineFlag = false;

        #endregion

        #region 属性

        /// <summary>
        /// 设备状态
        /// </summary>
        [XmlIgnore, Browsable(false)]
        public override StatusDevice Status { get; set; } = new StatusDaheng();

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public CameraDaheng()
        {
            Name = "大恒图像相机";
            DeviceType = DevicesType.Camera;
        }

        #endregion

        #region 设备基本操作

        /// <summary>
        /// 搜索相机设备
        /// </summary>
        /// <returns>相机信息列表</returns>
        public override List<CameraInfo> SearchCameras()
        {
            List<CameraInfo> cameraInfos = new List<CameraInfo>();
            try
            {
                // 初始化库
                IGXFactory.GetInstance().Init();

                // 枚举设备
                List<IGXDeviceInfo> listGXDeviceInfo = new List<IGXDeviceInfo>();
                IGXFactory.GetInstance().UpdateDeviceList(200, listGXDeviceInfo);

                // 添加设备信息到列表
                for (int i = 0; i < listGXDeviceInfo.Count; i++)
                {
                    IGXDeviceInfo deviceInfo = listGXDeviceInfo[i];
                    string displayName = !string.IsNullOrEmpty(deviceInfo.GetDisplayName())
                        ? deviceInfo.GetDisplayName()
                        : deviceInfo.GetSN();

                    cameraInfos.Add(new CameraInfo()
                    {
                        CamName = displayName,
                        SerialNO = deviceInfo.GetSN(),
                        ExtInfo = deviceInfo
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"搜索大恒相机异常：{ex.Message}");
            }
            return cameraInfos;
        }

        /// <summary>
        /// 打开相机设备
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool Open()
        {
            try
            {
                if (!IsEnabled) return false;
                if (Status.IsConnected) Close();

                var deviceInfo = (IGXDeviceInfo)CameraInfo.ExtInfo;

                // 打开设备
                m_objIGXDevice = IGXFactory.GetInstance().OpenDeviceBySN(deviceInfo.GetSN(), GX_ACCESS_MODE.GX_ACCESS_EXCLUSIVE);
                if (m_objIGXDevice == null)
                {
                    return false;
                }

                // 获取特征控制对象
                m_objIGXFeatureControl = m_objIGXDevice.GetRemoteFeatureControl();

                //设置采集模式连续采集
                m_objIGXFeatureControl.GetEnumFeature("AcquisitionMode").SetValue("Continuous");
                //设置触发模式为开
                m_objIGXFeatureControl.GetEnumFeature("TriggerMode").SetValue("On");
                //选择触发源为软触发
                m_objIGXFeatureControl.GetEnumFeature("TriggerSource").SetValue("Software");

                //打开流
                m_objIGXStream = m_objIGXDevice.OpenStream(0);
                m_objIGXStreamFeatureControl = m_objIGXStream.GetFeatureControl();
                GX_DEVICE_CLASS_LIST gX = m_objIGXDevice.GetDeviceInfo().GetDeviceClass();
                if (gX == GX_DEVICE_CLASS_LIST.GX_DEVICE_CLASS_GEV)//千兆网设备（GigE Vision）
                {
                    //建议用户在打开网络相机之后，根据当前网络环境设置相机的流通道包长值，
                    //以提高网络相机的采集性能,设置方法参考以下代码。
                    GX_DEVICE_CLASS_LIST objDeviceClass = m_objIGXDevice.GetDeviceInfo().GetDeviceClass();
                    if (GX_DEVICE_CLASS_LIST.GX_DEVICE_CLASS_GEV == objDeviceClass)
                    {
                        //判断设备是否支持流通道数据包功能
                        if (true == m_objIGXFeatureControl.IsImplemented("GevSCPSPacketSize"))
                        {
                            //获取当前网络环境的最优包长值
                            uint nPacketSize = m_objIGXStream.GetOptimalPacketSize();
                            //将最优包长值设置为当前设备的流通道包长值
                            m_objIGXFeatureControl.GetIntFeature("GevSCPSPacketSize").SetValue(nPacketSize);
                        }
                    }
                }
                else if (gX == GX_DEVICE_CLASS_LIST.GX_DEVICE_CLASS_U3V || gX == GX_DEVICE_CLASS_LIST.GX_DEVICE_CLASS_USB2)//USB3.0 设备（USB3 Vision）/ USB2.0 设备
                {

                }

                //开启采集流通道
                if (null != m_objIGXStream)
                {
                    m_objIGXStream.RegisterCaptureCallback(null, OnFrameCallbackFun);
                    m_hCB = m_objIGXDevice.RegisterDeviceOfflineCallback(null, OnDeviceOfflineCallbackFun);
                    m_objIGXStream.StartGrab();
                }


                Status.IsConnected = true;

                // 启动采集
                if (!Start()) return false;

                // 获取图像尺寸
                int width = 0, height = 0;
                if (!GetWidth(ref width)) return false;
                if (!GetHeight(ref height)) return false;

                return IsOpen();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"打开大恒相机异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查设备是否已打开
        /// </summary>
        /// <returns>是否已打开</returns>
        public override bool IsOpen()
        {
            return Status.IsConnected && m_objIGXDevice != null;
        }

        /// <summary>
        /// 关闭相机设备
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool Close()
        {
            try
            {
                Stop();
                //关闭设备
                if (null != m_objIGXDevice)
                {
                    m_objIGXDevice.Close();
                    m_objIGXDevice = null;
                }

                //停止流通道和关闭流
                if (null != m_objIGXStream)
                {
                    //Stream.StopGrab();
                    //Stream.Close();
                    m_objIGXStream = null;
                    m_objIGXStreamFeatureControl = null;
                }
                Status.IsOpen = false;
                return true;
            }
            catch (Exception ex)
            {
                //LogHelper.Log.Error(ex.Message, ex);
                return false;
            }
        }

 
      
        #endregion

        #region 采集控制

        /// <summary>
        /// 开始采集
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool Start()
        {
            try
            {
                if (!IsOpen()) return false;
                if (null == m_objIGXStreamFeatureControl) return false;
                try
                {
                    //设置流层Buffer处理模式为OldestFirst
                    m_objIGXStreamFeatureControl.GetEnumFeature("StreamBufferHandlingMode").SetValue("OldestFirst");
                }
                catch (Exception)
                {
                }

                //开启采集流通道
                if (null == m_objIGXStream) return false;
                m_objIGXStream.StartGrab();

                //发送开采命令
                if (null == m_objIGXFeatureControl) return false;
                m_objIGXFeatureControl.GetCommandFeature("AcquisitionStart").Execute();

                (Status as StatusCamera).IsStart = true;
                (Status as StatusCamera).IsGrapOnce = false;
                (Status as StatusCamera).IsGrapContinue = false;
                return true;
            }
            catch (Exception ex)
            {
                //LogHelper.Log.Error(ex.Message, ex);
                return false;
            }
        }

        /// <summary>
        /// 检查是否正在采集
        /// </summary>
        /// <returns>是否正在采集</returns>
        public override bool IsStart()
        {
            return (Status as StatusCamera).IsStart;
        }

        /// <summary>
        /// 停止采集
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool Stop()
        {
            try
            {
                if (!IsOpen()) return false;
                //if (!(Status as StatusCamera).IsGrapContinue) return false;
                if (null != m_objIGXFeatureControl)
                {
                    m_objIGXFeatureControl.GetCommandFeature("AcquisitionStop").Execute();
                    m_objIGXFeatureControl = null;
                }
                return true;
            }
            catch (Exception ex)
            {
                //LogHelper.Log.Error(ex.Message, ex);
                return false;
            }
        }

        #endregion

        #region 图像采集

        /// <summary>
        /// 采集单帧图像
        /// </summary>
        /// <param name="source">输出图像数据</param>
        /// <returns>是否成功</returns>
        public override bool GrabImageData(out ImageData source)
        {
            source = new ImageData();
            try
            {
                if (!IsOpen()) return false;
                if (!IsStart()) return false;
                if (m_objIGXDevice == null) return false;
                StatusDaheng status = Status as StatusDaheng;

                // 请求修改FrameOut
                Interlocked.Exchange(ref isRequestUpdate, 1);

                if (TrigMode == TrigMode.软触发)
                {
                    m_objIGXFeatureControl.GetCommandFeature("TriggerSoftware").Execute();
                }

                // 等待帧获取完成
                if (updateImageEvent.WaitOne(1000))
                {
                    // 拷贝帧数据
                    CopyFrameData(ref source);
                    return true;
                }
                else
                {
                    return false;
                }


            }
            catch (Exception ex)
            {
                //Logger.AddLog($"{Name} 采集图像异常，{ex.Message}", MsgType.Error);
                return false;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 图像回调函数
        /// </summary>
        /// <param name="objUserParam">用户参数</param>
        /// <param name="objIFrameData">帧数据</param>
        private void OnFrameCallbackFun(object objUserParam, IFrameData objIFrameData)
        {
            try
            {
                // 获取帧
                if (Interlocked.Read(ref isRequestUpdate) == 1)
                {
                    frameData = objIFrameData;
                    updateImageEvent.Set();
                    Interlocked.Exchange(ref isRequestUpdate, 0);
                }
            }
            catch (Exception ex)
            {
                //Logger.AddLog($"{Name} 获取帧异常，{ex.Message}", MsgType.Error);
                updateImageEvent.Set();
            }
        }

        /// <summary>
        /// 拷贝帧数据
        /// </summary>
        /// <param name="source">目标图像数据</param>
        private void CopyFrameData(ref ImageData imageData)
        {
            if (frameData?.GetBuffer() != null)
            {
                imageData.PixelDataPtr = frameData.GetBuffer();
                imageData.Width = (uint)frameData.GetWidth();
                imageData.Height = (uint)frameData.GetHeight();
                Height = (int)imageData.Height;
                Width = (int)imageData.Width;
                imageData.ImageSize = frameData.GetPayloadSize();
            }
        }

        /// <summary>
        /// 掉线回调函数
        /// </summary>
        /// <param name="pUserParam">用户私有参数</param>
        private void OnDeviceOfflineCallbackFun(object pUserParam)
        {
            OfflineFlag = true;
            if (!IsConnected) return;
            try
            {
                Close();
            }
            catch (Exception)
            {

            }
        }

        #endregion
    }
}
