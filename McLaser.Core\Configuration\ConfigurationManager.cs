using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;
using Newtonsoft.Json;

namespace McLaser.Core.Configuration
{
    /// <summary>
    /// 配置管理器实现
    /// 提供统一的配置管理功能，支持多提供者、缓存、监控等特性
    /// </summary>
    public class ConfigurationManager : IConfigurationManager, IDisposable
    {
        #region 私有字段

        private readonly ConcurrentDictionary<string, object> _cache;
        private readonly List<IConfigurationProvider> _providers;
        private readonly List<IConfigurationValidationRule> _validationRules;
        private readonly ReaderWriterLockSlim _lock;
        private readonly Timer _watchTimer;
        private readonly ConfigurationStatistics _statistics;
        private readonly object _syncRoot = new object();

        private bool _isWatching;
        private bool _disposed;
        private DateTime _lastLoadTime;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public ConfigurationManager()
        {
            _cache = new ConcurrentDictionary<string, object>();
            _providers = new List<IConfigurationProvider>();
            _validationRules = new List<IConfigurationValidationRule>();
            _lock = new ReaderWriterLockSlim();
            _statistics = new ConfigurationStatistics();
            
            // 创建监控定时器（每5秒检查一次）
            _watchTimer = new Timer(OnWatchTimerElapsed, null, Timeout.Infinite, Timeout.Infinite);
        }

        #endregion

        #region 事件

        /// <summary>
        /// 配置变更事件
        /// </summary>
        public event EventHandler<ConfigurationChangedEventArgs> ConfigurationChanged;

        #endregion

        #region 基本配置操作

        /// <summary>
        /// 获取配置值
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        public T GetValue<T>(string key, T defaultValue = default(T))
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("配置键不能为空", nameof(key));

            var stopwatch = Stopwatch.StartNew();
            try
            {
                _lock.EnterReadLock();

                // 首先从缓存中查找
                if (_cache.TryGetValue(key, out var cachedValue))
                {
                    _statistics.CacheHitCount++;
                    return ConvertValue<T>(cachedValue, defaultValue);
                }

                _statistics.CacheMissCount++;

                // 从提供者中查找（按优先级排序）
                var providers = _providers.OrderByDescending(p => p.Priority).ToList();
                foreach (var provider in providers)
                {
                    try
                    {
                        var value = provider.GetValueAsync(key).Result;
                        if (value != null)
                        {
                            var convertedValue = ConvertValue<T>(value, defaultValue);
                            
                            // 缓存结果
                            _cache.TryAdd(key, convertedValue);
                            
                            _statistics.ReadCount++;
                            return convertedValue;
                        }
                    }
                    catch (Exception ex)
                    {
                        _statistics.ErrorCount++;
                        // 记录错误但继续尝试其他提供者
                        System.Diagnostics.Debug.WriteLine($"从提供者 {provider.Name} 读取配置 {key} 失败: {ex.Message}");
                    }
                }

                return defaultValue;
            }
            finally
            {
                _lock.ExitReadLock();
                stopwatch.Stop();
                UpdateAverageReadTime(stopwatch.ElapsedMilliseconds);
            }
        }

        /// <summary>
        /// 异步获取配置值
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        public async Task<T> GetValueAsync<T>(string key, T defaultValue = default(T))
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("配置键不能为空", nameof(key));

            var stopwatch = Stopwatch.StartNew();
            try
            {
                _lock.EnterReadLock();

                // 首先从缓存中查找
                if (_cache.TryGetValue(key, out var cachedValue))
                {
                    _statistics.CacheHitCount++;
                    return ConvertValue<T>(cachedValue, defaultValue);
                }

                _statistics.CacheMissCount++;
            }
            finally
            {
                _lock.ExitReadLock();
            }

            // 从提供者中查找（按优先级排序）
            var providers = _providers.OrderByDescending(p => p.Priority).ToList();
            foreach (var provider in providers)
            {
                try
                {
                    var value = await provider.GetValueAsync(key);
                    if (value != null)
                    {
                        var convertedValue = ConvertValue<T>(value, defaultValue);
                        
                        // 缓存结果
                        _cache.TryAdd(key, convertedValue);
                        
                        _statistics.ReadCount++;
                        return convertedValue;
                    }
                }
                catch (Exception ex)
                {
                    _statistics.ErrorCount++;
                    // 记录错误但继续尝试其他提供者
                    System.Diagnostics.Debug.WriteLine($"从提供者 {provider.Name} 读取配置 {key} 失败: {ex.Message}");
                }
            }

            stopwatch.Stop();
            UpdateAverageReadTime(stopwatch.ElapsedMilliseconds);
            return defaultValue;
        }

        /// <summary>
        /// 设置配置值
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        public void SetValue<T>(string key, T value)
        {
            SetValueAsync(key, value).Wait();
        }

        /// <summary>
        /// 异步设置配置值
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        /// <returns>异步任务</returns>
        public async Task SetValueAsync<T>(string key, T value)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("配置键不能为空", nameof(key));

            var stopwatch = Stopwatch.StartNew();
            try
            {
                _lock.EnterWriteLock();

                var oldValue = _cache.TryGetValue(key, out var cached) ? cached : null;

                // 更新缓存
                _cache.AddOrUpdate(key, value, (k, v) => value);

                // 写入到支持写入的提供者
                var writableProviders = _providers.Where(p => p.SupportsWrite).OrderByDescending(p => p.Priority);
                foreach (var provider in writableProviders)
                {
                    try
                    {
                        await provider.SetValueAsync(key, value);
                        _statistics.WriteCount++;
                        break; // 只写入到第一个成功的提供者
                    }
                    catch (Exception ex)
                    {
                        _statistics.ErrorCount++;
                        System.Diagnostics.Debug.WriteLine($"向提供者 {provider.Name} 写入配置 {key} 失败: {ex.Message}");
                    }
                }

                // 触发配置变更事件
                var changeType = oldValue == null ? ConfigurationChangeType.Added : ConfigurationChangeType.Modified;
                OnConfigurationChanged(new ConfigurationChangedEventArgs(key, oldValue, value, changeType));
            }
            finally
            {
                _lock.ExitWriteLock();
                stopwatch.Stop();
                UpdateAverageWriteTime(stopwatch.ElapsedMilliseconds);
            }
        }

        /// <summary>
        /// 检查配置键是否存在
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>是否存在</returns>
        public bool ContainsKey(string key)
        {
            if (string.IsNullOrEmpty(key))
                return false;

            _lock.EnterReadLock();
            try
            {
                // 首先检查缓存
                if (_cache.ContainsKey(key))
                    return true;

                // 检查提供者
                var providers = _providers.OrderByDescending(p => p.Priority).ToList();
                foreach (var provider in providers)
                {
                    try
                    {
                        if (provider.ContainsKeyAsync(key).Result)
                            return true;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"检查提供者 {provider.Name} 中的配置键 {key} 失败: {ex.Message}");
                    }
                }

                return false;
            }
            finally
            {
                _lock.ExitReadLock();
            }
        }

        /// <summary>
        /// 删除配置项
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>是否删除成功</returns>
        public bool RemoveKey(string key)
        {
            if (string.IsNullOrEmpty(key))
                return false;

            _lock.EnterWriteLock();
            try
            {
                var oldValue = _cache.TryGetValue(key, out var cached) ? cached : null;
                var removed = false;

                // 从缓存中删除
                _cache.TryRemove(key, out _);

                // 从支持写入的提供者中删除
                var writableProviders = _providers.Where(p => p.SupportsWrite).OrderByDescending(p => p.Priority);
                foreach (var provider in writableProviders)
                {
                    try
                    {
                        if (provider.RemoveValueAsync(key).Result)
                        {
                            removed = true;
                            break;
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"从提供者 {provider.Name} 删除配置 {key} 失败: {ex.Message}");
                    }
                }

                if (removed && oldValue != null)
                {
                    // 触发配置变更事件
                    OnConfigurationChanged(new ConfigurationChangedEventArgs(key, oldValue, null, ConfigurationChangeType.Removed));
                }

                return removed;
            }
            finally
            {
                _lock.ExitWriteLock();
            }
        }

        /// <summary>
        /// 获取所有配置键
        /// </summary>
        /// <returns>配置键集合</returns>
        public IEnumerable<string> GetAllKeys()
        {
            _lock.EnterReadLock();
            try
            {
                var keys = new HashSet<string>(_cache.Keys);

                // 从所有提供者获取键
                foreach (var provider in _providers)
                {
                    try
                    {
                        var providerKeys = provider.GetKeysAsync().Result;
                        foreach (var key in providerKeys)
                        {
                            keys.Add(key);
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"从提供者 {provider.Name} 获取配置键失败: {ex.Message}");
                    }
                }

                return keys.ToList();
            }
            finally
            {
                _lock.ExitReadLock();
            }
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 转换配置值类型
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="value">原始值</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>转换后的值</returns>
        private T ConvertValue<T>(object value, T defaultValue)
        {
            if (value == null)
                return defaultValue;

            try
            {
                if (value is T directValue)
                    return directValue;

                if (typeof(T) == typeof(string))
                    return (T)(object)value.ToString();

                if (typeof(T).IsEnum)
                    return (T)Enum.Parse(typeof(T), value.ToString(), true);

                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch
            {
                return defaultValue;
            }
        }

        /// <summary>
        /// 更新平均读取时间
        /// </summary>
        /// <param name="elapsedMilliseconds">耗时毫秒数</param>
        private void UpdateAverageReadTime(long elapsedMilliseconds)
        {
            lock (_syncRoot)
            {
                if (_statistics.ReadCount == 0)
                {
                    _statistics.AverageReadTime = elapsedMilliseconds;
                }
                else
                {
                    _statistics.AverageReadTime = (_statistics.AverageReadTime * (_statistics.ReadCount - 1) + elapsedMilliseconds) / _statistics.ReadCount;
                }
            }
        }

        /// <summary>
        /// 更新平均写入时间
        /// </summary>
        /// <param name="elapsedMilliseconds">耗时毫秒数</param>
        private void UpdateAverageWriteTime(long elapsedMilliseconds)
        {
            lock (_syncRoot)
            {
                if (_statistics.WriteCount == 0)
                {
                    _statistics.AverageWriteTime = elapsedMilliseconds;
                }
                else
                {
                    _statistics.AverageWriteTime = (_statistics.AverageWriteTime * (_statistics.WriteCount - 1) + elapsedMilliseconds) / _statistics.WriteCount;
                }
            }
        }

        /// <summary>
        /// 触发配置变更事件
        /// </summary>
        /// <param name="args">事件参数</param>
        private void OnConfigurationChanged(ConfigurationChangedEventArgs args)
        {
            try
            {
                ConfigurationChanged?.Invoke(this, args);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"触发配置变更事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 监控定时器回调
        /// </summary>
        /// <param name="state">状态对象</param>
        private void OnWatchTimerElapsed(object state)
        {
            if (_disposed || !_isWatching)
                return;

            try
            {
                // 检查提供者是否有变更
                foreach (var provider in _providers.Where(p => p.SupportsWatch))
                {
                    // 提供者自己会触发ConfigurationChanged事件
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"监控配置变更失败: {ex.Message}");
            }
        }

        #endregion

        #region 配置节操作

        /// <summary>
        /// 获取配置节
        /// </summary>
        /// <typeparam name="T">配置节类型</typeparam>
        /// <param name="sectionName">配置节名称</param>
        /// <returns>配置节对象</returns>
        public T GetSection<T>(string sectionName) where T : class, new()
        {
            if (string.IsNullOrEmpty(sectionName))
                throw new ArgumentException("配置节名称不能为空", nameof(sectionName));

            var sectionData = GetSectionData(sectionName);
            if (sectionData == null || sectionData.Count == 0)
                return new T();

            try
            {
                var json = JsonConvert.SerializeObject(sectionData);
                return JsonConvert.DeserializeObject<T>(json) ?? new T();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"反序列化配置节 {sectionName} 失败: {ex.Message}");
                return new T();
            }
        }

        /// <summary>
        /// 设置配置节
        /// </summary>
        /// <typeparam name="T">配置节类型</typeparam>
        /// <param name="sectionName">配置节名称</param>
        /// <param name="section">配置节对象</param>
        public void SetSection<T>(string sectionName, T section) where T : class
        {
            if (string.IsNullOrEmpty(sectionName))
                throw new ArgumentException("配置节名称不能为空", nameof(sectionName));

            if (section == null)
                throw new ArgumentNullException(nameof(section));

            try
            {
                var json = JsonConvert.SerializeObject(section);
                var sectionData = JsonConvert.DeserializeObject<Dictionary<string, object>>(json);

                foreach (var kvp in sectionData)
                {
                    var key = $"{sectionName}:{kvp.Key}";
                    SetValue(key, kvp.Value);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"序列化配置节 {sectionName} 失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取配置节的原始数据
        /// </summary>
        /// <param name="sectionName">配置节名称</param>
        /// <returns>配置数据字典</returns>
        public IDictionary<string, object> GetSectionData(string sectionName)
        {
            if (string.IsNullOrEmpty(sectionName))
                return new Dictionary<string, object>();

            var sectionPrefix = $"{sectionName}:";
            var sectionData = new Dictionary<string, object>();

            var allKeys = GetAllKeys();
            foreach (var key in allKeys.Where(k => k.StartsWith(sectionPrefix)))
            {
                var sectionKey = key.Substring(sectionPrefix.Length);
                var value = GetValue<object>(key);
                sectionData[sectionKey] = value;
            }

            return sectionData;
        }

        #endregion

        #region 配置提供者管理

        /// <summary>
        /// 添加配置提供者
        /// </summary>
        /// <param name="provider">配置提供者</param>
        /// <param name="priority">优先级（数值越大优先级越高）</param>
        public void AddProvider(IConfigurationProvider provider, int priority = 0)
        {
            if (provider == null)
                throw new ArgumentNullException(nameof(provider));

            _lock.EnterWriteLock();
            try
            {
                provider.Priority = priority;
                _providers.Add(provider);

                // 订阅提供者的配置变更事件
                provider.ConfigurationChanged += OnProviderConfigurationChanged;

                _statistics.ProviderCount = _providers.Count;
            }
            finally
            {
                _lock.ExitWriteLock();
            }
        }

        /// <summary>
        /// 移除配置提供者
        /// </summary>
        /// <param name="provider">配置提供者</param>
        public void RemoveProvider(IConfigurationProvider provider)
        {
            if (provider == null)
                return;

            _lock.EnterWriteLock();
            try
            {
                if (_providers.Remove(provider))
                {
                    // 取消订阅提供者的配置变更事件
                    provider.ConfigurationChanged -= OnProviderConfigurationChanged;
                    _statistics.ProviderCount = _providers.Count;
                }
            }
            finally
            {
                _lock.ExitWriteLock();
            }
        }

        /// <summary>
        /// 获取所有配置提供者
        /// </summary>
        /// <returns>配置提供者集合</returns>
        public IEnumerable<IConfigurationProvider> GetProviders()
        {
            _lock.EnterReadLock();
            try
            {
                return _providers.ToList();
            }
            finally
            {
                _lock.ExitReadLock();
            }
        }

        /// <summary>
        /// 提供者配置变更事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void OnProviderConfigurationChanged(object sender, ConfigurationChangedEventArgs e)
        {
            // 清除缓存中的相关项
            _cache.TryRemove(e.Key, out _);

            // 转发事件
            OnConfigurationChanged(e);
        }

        #endregion

        #region 配置监控和通知

        /// <summary>
        /// 开始监控配置变更
        /// </summary>
        public void StartWatching()
        {
            if (_isWatching)
                return;

            _isWatching = true;

            // 启动定时器
            _watchTimer.Change(TimeSpan.FromSeconds(5), TimeSpan.FromSeconds(5));

            // 启动所有支持监控的提供者
            foreach (var provider in _providers.Where(p => p.SupportsWatch))
            {
                try
                {
                    provider.StartWatching();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"启动提供者 {provider.Name} 监控失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 停止监控配置变更
        /// </summary>
        public void StopWatching()
        {
            if (!_isWatching)
                return;

            _isWatching = false;

            // 停止定时器
            _watchTimer.Change(Timeout.Infinite, Timeout.Infinite);

            // 停止所有支持监控的提供者
            foreach (var provider in _providers.Where(p => p.SupportsWatch))
            {
                try
                {
                    provider.StopWatching();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"停止提供者 {provider.Name} 监控失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 是否正在监控配置变更
        /// </summary>
        public bool IsWatching => _isWatching;

        #endregion

        #region 配置持久化

        /// <summary>
        /// 保存配置到持久化存储
        /// </summary>
        /// <returns>异步任务</returns>
        public async Task SaveAsync()
        {
            var writableProviders = _providers.Where(p => p.SupportsWrite).ToList();

            foreach (var provider in writableProviders)
            {
                try
                {
                    await provider.SaveAsync();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"保存提供者 {provider.Name} 配置失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 从持久化存储重新加载配置
        /// </summary>
        /// <returns>异步任务</returns>
        public async Task ReloadAsync()
        {
            _lock.EnterWriteLock();
            try
            {
                // 清空缓存
                _cache.Clear();

                // 重新加载所有提供者
                foreach (var provider in _providers)
                {
                    try
                    {
                        await provider.ReloadAsync();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"重新加载提供者 {provider.Name} 配置失败: {ex.Message}");
                    }
                }

                _lastLoadTime = DateTime.UtcNow;
                _statistics.LastUpdateTime = _lastLoadTime;

                // 触发重新加载事件
                OnConfigurationChanged(new ConfigurationChangedEventArgs(string.Empty, null, null, ConfigurationChangeType.Reloaded));
            }
            finally
            {
                _lock.ExitWriteLock();
            }
        }

        /// <summary>
        /// 重置配置为默认值
        /// </summary>
        public void Reset()
        {
            _lock.EnterWriteLock();
            try
            {
                _cache.Clear();
                _statistics.ReadCount = 0;
                _statistics.WriteCount = 0;
                _statistics.CacheHitCount = 0;
                _statistics.CacheMissCount = 0;
                _statistics.ErrorCount = 0;
                _statistics.LastUpdateTime = DateTime.UtcNow;
            }
            finally
            {
                _lock.ExitWriteLock();
            }
        }

        #endregion

        #region 配置验证

        /// <summary>
        /// 验证配置
        /// </summary>
        /// <returns>验证结果</returns>
        public ConfigurationValidationResult Validate()
        {
            var result = new ConfigurationValidationResult();
            var allConfigurations = new Dictionary<string, object>();

            // 收集所有配置
            var allKeys = GetAllKeys();
            foreach (var key in allKeys)
            {
                allConfigurations[key] = GetValue<object>(key);
            }

            var context = new ConfigurationValidationContext(allConfigurations);

            // 执行所有验证规则
            foreach (var rule in _validationRules)
            {
                try
                {
                    foreach (var kvp in allConfigurations)
                    {
                        var ruleResult = rule.Validate(kvp.Key, kvp.Value, context);
                        if (ruleResult != null)
                        {
                            result.Errors.AddRange(ruleResult.Errors);
                            result.Warnings.AddRange(ruleResult.Warnings);
                        }
                    }
                }
                catch (Exception ex)
                {
                    result.AddError("ValidationRule", $"验证规则 {rule.Name} 执行失败: {ex.Message}");
                }
            }

            return result;
        }

        /// <summary>
        /// 添加配置验证规则
        /// </summary>
        /// <param name="rule">验证规则</param>
        public void AddValidationRule(IConfigurationValidationRule rule)
        {
            if (rule == null)
                throw new ArgumentNullException(nameof(rule));

            _validationRules.Add(rule);
        }

        /// <summary>
        /// 移除配置验证规则
        /// </summary>
        /// <param name="rule">验证规则</param>
        public void RemoveValidationRule(IConfigurationValidationRule rule)
        {
            if (rule != null)
            {
                _validationRules.Remove(rule);
            }
        }

        #endregion

        #region 配置加密

        /// <summary>
        /// 设置加密的配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        public void SetEncryptedValue(string key, string value)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("配置键不能为空", nameof(key));

            if (value == null)
                throw new ArgumentNullException(nameof(value));

            try
            {
                var encryptedValue = EncryptValue(value);
                SetValue($"encrypted:{key}", encryptedValue);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"加密配置值失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取加密的配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>解密后的配置值</returns>
        public string GetEncryptedValue(string key, string defaultValue = null)
        {
            if (string.IsNullOrEmpty(key))
                return defaultValue;

            try
            {
                var encryptedValue = GetValue<string>($"encrypted:{key}");
                if (string.IsNullOrEmpty(encryptedValue))
                    return defaultValue;

                return DecryptValue(encryptedValue);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解密配置值失败: {ex.Message}");
                return defaultValue;
            }
        }

        /// <summary>
        /// 加密值
        /// </summary>
        /// <param name="value">原始值</param>
        /// <returns>加密后的值</returns>
        private string EncryptValue(string value)
        {
            // 简单的Base64编码（实际应用中应使用更强的加密算法）
            var bytes = System.Text.Encoding.UTF8.GetBytes(value);
            return Convert.ToBase64String(bytes);
        }

        /// <summary>
        /// 解密值
        /// </summary>
        /// <param name="encryptedValue">加密的值</param>
        /// <returns>解密后的值</returns>
        private string DecryptValue(string encryptedValue)
        {
            // 简单的Base64解码（实际应用中应使用更强的解密算法）
            var bytes = Convert.FromBase64String(encryptedValue);
            return System.Text.Encoding.UTF8.GetString(bytes);
        }

        #endregion

        #region 配置导入导出

        /// <summary>
        /// 导出配置到文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="format">导出格式</param>
        /// <returns>异步任务</returns>
        public async Task ExportAsync(string filePath, ConfigurationFormat format = ConfigurationFormat.Json)
        {
            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));

            var allConfigurations = new Dictionary<string, object>();
            var allKeys = GetAllKeys();

            foreach (var key in allKeys)
            {
                allConfigurations[key] = GetValue<object>(key);
            }

            string content;
            switch (format)
            {
                case ConfigurationFormat.Json:
                    content = JsonConvert.SerializeObject(allConfigurations, Formatting.Indented);
                    break;
                case ConfigurationFormat.Xml:
                    content = ConvertToXml(allConfigurations);
                    break;
                default:
                    throw new NotSupportedException($"不支持的导出格式: {format}");
            }

            System.IO.File.WriteAllText(filePath, content);
        }

        /// <summary>
        /// 从文件导入配置
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="format">导入格式</param>
        /// <param name="merge">是否合并现有配置</param>
        /// <returns>异步任务</returns>
        public async Task ImportAsync(string filePath, ConfigurationFormat format = ConfigurationFormat.Json, bool merge = true)
        {
            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));

            if (!System.IO.File.Exists(filePath))
                throw new System.IO.FileNotFoundException($"配置文件不存在: {filePath}");

            var content = System.IO.File.ReadAllText(filePath);
            Dictionary<string, object> importedConfigurations;

            switch (format)
            {
                case ConfigurationFormat.Json:
                    importedConfigurations = JsonConvert.DeserializeObject<Dictionary<string, object>>(content);
                    break;
                case ConfigurationFormat.Xml:
                    importedConfigurations = ConvertFromXml(content);
                    break;
                default:
                    throw new NotSupportedException($"不支持的导入格式: {format}");
            }

            if (!merge)
            {
                Reset();
            }

            foreach (var kvp in importedConfigurations)
            {
                await SetValueAsync(kvp.Key, kvp.Value);
            }
        }

        /// <summary>
        /// 转换为XML格式
        /// </summary>
        /// <param name="configurations">配置字典</param>
        /// <returns>XML字符串</returns>
        private string ConvertToXml(Dictionary<string, object> configurations)
        {
            // 简单的XML转换实现
            var xml = new System.Text.StringBuilder();
            xml.AppendLine("<?xml version=\"1.0\" encoding=\"utf-8\"?>");
            xml.AppendLine("<configuration>");

            foreach (var kvp in configurations)
            {
                xml.AppendLine($"  <add key=\"{kvp.Key}\" value=\"{kvp.Value}\" />");
            }

            xml.AppendLine("</configuration>");
            return xml.ToString();
        }

        /// <summary>
        /// 从XML格式转换
        /// </summary>
        /// <param name="xml">XML字符串</param>
        /// <returns>配置字典</returns>
        private Dictionary<string, object> ConvertFromXml(string xml)
        {
            // 简单的XML解析实现
            var configurations = new Dictionary<string, object>();

            try
            {
                var doc = new System.Xml.XmlDocument();
                doc.LoadXml(xml);

                var nodes = doc.SelectNodes("//add");
                foreach (System.Xml.XmlNode node in nodes)
                {
                    var key = node.Attributes["key"]?.Value;
                    var value = node.Attributes["value"]?.Value;

                    if (!string.IsNullOrEmpty(key))
                    {
                        configurations[key] = value;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"解析XML配置失败: {ex.Message}", ex);
            }

            return configurations;
        }

        #endregion

        #region 配置统计和诊断

        /// <summary>
        /// 获取配置统计信息
        /// </summary>
        /// <returns>配置统计信息</returns>
        public ConfigurationStatistics GetStatistics()
        {
            _lock.EnterReadLock();
            try
            {
                _statistics.TotalItems = _cache.Count;
                _statistics.ProviderCount = _providers.Count;
                return _statistics;
            }
            finally
            {
                _lock.ExitReadLock();
            }
        }

        /// <summary>
        /// 获取配置诊断信息
        /// </summary>
        /// <returns>配置诊断信息</returns>
        public ConfigurationDiagnostics GetDiagnostics()
        {
            var diagnostics = new ConfigurationDiagnostics();

            // 检查管理器状态
            if (_disposed)
            {
                diagnostics.Status = ConfigurationManagerStatus.Shutdown;
            }
            else if (_statistics.ErrorCount > 0)
            {
                diagnostics.Status = ConfigurationManagerStatus.Error;
            }
            else if (_providers.Count == 0)
            {
                diagnostics.Status = ConfigurationManagerStatus.Warning;
                diagnostics.Issues.Add(new ConfigurationIssue
                {
                    Type = ConfigurationIssueType.MissingRequired,
                    Description = "没有配置提供者",
                    Recommendation = "至少添加一个配置提供者"
                });
            }
            else
            {
                diagnostics.Status = ConfigurationManagerStatus.Healthy;
            }

            // 检查提供者状态
            foreach (var provider in _providers)
            {
                var providerDiagnostic = new ConfigurationProviderDiagnostic
                {
                    ProviderName = provider.Name,
                    Status = provider.IsInitialized ? ConfigurationProviderStatus.Healthy : ConfigurationProviderStatus.NotInitialized
                };

                var stats = provider.GetStatistics();
                providerDiagnostic.ResponseTime = stats.AverageResponseTime;
                providerDiagnostic.ItemCount = stats.ItemCount;

                if (stats.ErrorCount > 0)
                {
                    providerDiagnostic.Status = ConfigurationProviderStatus.Error;
                    providerDiagnostic.LastError = "存在错误";
                    providerDiagnostic.LastErrorTime = stats.LastAccessTime;
                }

                diagnostics.ProviderDiagnostics.Add(providerDiagnostic);
            }

            // 计算内存使用量
            diagnostics.MemoryUsage = GC.GetTotalMemory(false);

            return diagnostics;
        }

        #endregion

        #region IDisposable实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否正在释放</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                StopWatching();
                
                _watchTimer?.Dispose();
                _lock?.Dispose();

                // 释放所有提供者
                foreach (var provider in _providers)
                {
                    try
                    {
                        provider.Dispose();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"释放配置提供者 {provider.Name} 失败: {ex.Message}");
                    }
                }

                _providers.Clear();
                _cache.Clear();
                _validationRules.Clear();

                _disposed = true;
            }
        }

        #endregion
    }
}
