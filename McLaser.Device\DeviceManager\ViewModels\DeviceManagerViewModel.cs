using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using McLaser.Core.Common;

namespace McLaser.Device.ViewModels
{
    /// <summary>
    /// 设备管理器视图模型
    /// 提供完整的设备管理功能，包括设备搜索、连接、监控等
    /// </summary>
    public partial class DeviceManagerViewModel : ObservableObject
    {
        #region 私有字段

        private readonly DeviceManager _deviceManager;

        #endregion

        #region 属性

        /// <summary>
        /// 设备管理器实例
        /// </summary>
        public DeviceManager DeviceManager => _deviceManager;

        /// <summary>
        /// 设备分类组集合
        /// </summary>
        public ObservableCollection<DeviceCategoryGroup> DeviceGroups => _deviceManager.DeviceGroups;

        /// <summary>
        /// 所有设备列表
        /// </summary>
        public ObservableCollection<DeviceBase> Devices => _deviceManager.Devices;

        /// <summary>
        /// 相机设备列表
        /// </summary>
        public ObservableCollection<DeviceBase> Cameras => _deviceManager.Cameras;

        /// <summary>
        /// 运动控制卡列表
        /// </summary>
        public ObservableCollection<DeviceBase> MotionCards => _deviceManager.MotionCards;

        /// <summary>
        /// 激光器设备列表
        /// </summary>
        public ObservableCollection<DeviceBase> Lasers => _deviceManager.Lasers;

        /// <summary>
        /// 传感器设备列表
        /// </summary>
        public ObservableCollection<DeviceBase> Sensors => _deviceManager.Sensors;

        /// <summary>
        /// 当前选中的设备
        /// </summary>
        public IDevice SelectedDevice
        {
            get => _deviceManager.SelectedDevice;
            set => _deviceManager.SelectedDevice = value;
        }

        /// <summary>
        /// 当前配置界面
        /// </summary>
        public FrameworkElement CurrentConfigurationUI => _deviceManager.CurrentConfigurationUI;

        /// <summary>
        /// 状态消息
        /// </summary>
        public string StatusMessage => _deviceManager.StatusMessage;

        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized => _deviceManager.IsInitialized;

        /// <summary>
        /// 是否正在监控
        /// </summary>
        public bool IsMonitoring => _deviceManager.IsMonitoring;

        /// <summary>
        /// 已连接设备数量
        /// </summary>
        public int ConnectedDeviceCount => _deviceManager.ConnectedDeviceCount;

        /// <summary>
        /// 设备总数
        /// </summary>
        public int TotalDeviceCount => _deviceManager.TotalDeviceCount;

        /// <summary>
        /// 支持的设备类型列表
        /// </summary>
        public ObservableCollection<string> SupportedDeviceTypes { get; } = new ObservableCollection<string>();

        /// <summary>
        /// 选中的设备类型
        /// </summary>
        private string _selectedDeviceType;
        public string SelectedDeviceType
        {
            get { return _selectedDeviceType; }
            set { Set(ref _selectedDeviceType, value); }
        }

        /// <summary>
        /// 新设备名称
        /// </summary>
        private string _newDeviceName = "新设备";
        public string NewDeviceName
        {
            get { return _newDeviceName; }
            set { Set(ref _newDeviceName, value); }
        }

        /// <summary>
        /// 操作日志
        /// </summary>
        private string _operationLog = "设备管理器操作日志：\n";
        public string OperationLog
        {
            get { return _operationLog; }
            set { Set(ref _operationLog, value); }
        }

        /// <summary>
        /// 设备类型分类集合
        /// </summary>
        public ObservableCollection<DeviceCategoryGroup> DeviceTypeCategories { get; } = new ObservableCollection<DeviceCategoryGroup>();

        #endregion

        #region 命令属性

        /// <summary>
        /// 初始化设备管理器命令
        /// </summary>
        public ICommand InitializeDeviceManagerCommand { get; private set; }

        /// <summary>
        /// 添加测试设备命令
        /// </summary>
        public ICommand AddTestDeviceCommand { get; private set; }

        /// <summary>
        /// 移除选中设备命令
        /// </summary>
        public ICommand RemoveSelectedDeviceCommand { get; private set; }

        /// <summary>
        /// 连接所有设备命令
        /// </summary>
        public ICommand ConnectAllDevicesCommand { get; private set; }

        /// <summary>
        /// 断开所有设备命令
        /// </summary>
        public ICommand DisconnectAllDevicesCommand { get; private set; }

        /// <summary>
        /// 启动监控命令
        /// </summary>
        public ICommand StartMonitoringCommand { get; private set; }

        /// <summary>
        /// 停止监控命令
        /// </summary>
        public ICommand StopMonitoringCommand { get; private set; }

        /// <summary>
        /// 保存配置命令
        /// </summary>
        public ICommand SaveConfigurationCommand { get; private set; }

        /// <summary>
        /// 加载配置命令
        /// </summary>
        public ICommand LoadConfigurationCommand { get; private set; }

        /// <summary>
        /// 清空日志命令
        /// </summary>
        public ICommand ClearLogCommand { get; private set; }

        /// <summary>
        /// 搜索设备命令
        /// </summary>
        public ICommand SearchDevicesCommand { get; private set; }

        /// <summary>
        /// 从设备类型添加设备命令
        /// </summary>
        public ICommand AddDeviceFromTypeCommand { get; private set; }

        /// <summary>
        /// 添加设备到分组命令
        /// </summary>
        public ICommand AddDeviceToGroupCommand { get; private set; }

        /// <summary>
        /// 从分组移除设备命令
        /// </summary>
        public ICommand RemoveDeviceFromGroupCommand { get; private set; }

        /// <summary>
        /// 刷新设备分组命令
        /// </summary>
        public ICommand RefreshDeviceGroupsCommand { get; private set; }

        /// <summary>
        /// 导出日志命令
        /// </summary>
        public ICommand ExportLogCommand { get; private set; }

        /// <summary>
        /// 连接设备命令
        /// </summary>
        public ICommand ConnectDeviceCommand { get; private set; }

        /// <summary>
        /// 断开设备命令
        /// </summary>
        public ICommand DisconnectDeviceCommand { get; private set; }

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public DeviceManagerViewModel()
        {
            _deviceManager = new DeviceManager();

            // 初始化命令
            InitializeCommands();

            // 订阅设备管理器事件
            _deviceManager.PropertyChanged += DeviceManager_PropertyChanged;
            _deviceManager.ConfigurationUIChanged += DeviceManager_ConfigurationUIChanged;

            // 初始化支持的设备类型
            InitializeSupportedDeviceTypes();

            // 添加操作日志
            AddOperationLog("设备管理器视图模型已创建");
        }

        /// <summary>
        /// 带设备管理器实例的构造函数
        /// </summary>
        /// <param name="deviceManager">设备管理器实例</param>
        public DeviceManagerViewModel(DeviceManager deviceManager)
        {
            _deviceManager = deviceManager ?? throw new ArgumentNullException(nameof(deviceManager));

            // 初始化命令
            InitializeCommands();

            // 订阅设备管理器事件
            _deviceManager.PropertyChanged += DeviceManager_PropertyChanged;
            _deviceManager.ConfigurationUIChanged += DeviceManager_ConfigurationUIChanged;

            // 初始化支持的设备类型
            InitializeSupportedDeviceTypes();

            // 添加操作日志
            AddOperationLog("设备管理器视图模型已创建（使用外部设备管理器实例）");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化命令
        /// </summary>
        private void InitializeCommands()
        {
            InitializeDeviceManagerCommand = new RelayCommand(async () => await InitializeDeviceManagerAsync());
            RemoveSelectedDeviceCommand = new RelayCommand(RemoveSelectedDevice);
            ConnectAllDevicesCommand = new RelayCommand(async () => await ConnectAllDevicesAsync());
            DisconnectAllDevicesCommand = new RelayCommand(async () => await DisconnectAllDevicesAsync());
            StartMonitoringCommand = new RelayCommand(StartMonitoring);
            StopMonitoringCommand = new RelayCommand(StopMonitoring);
            SaveConfigurationCommand = new RelayCommand(SaveConfiguration);
            LoadConfigurationCommand = new RelayCommand(LoadConfiguration);
            ClearLogCommand = new RelayCommand(ClearLog);
            SearchDevicesCommand = new RelayCommand(SearchDevices);
            AddDeviceToGroupCommand = new RelayCommand<DeviceCategoryGroup>(AddDeviceToGroup);
            RemoveDeviceFromGroupCommand = new RelayCommand<IDevice>(RemoveDeviceFromGroup);
            RefreshDeviceGroupsCommand = new RelayCommand(RefreshDeviceGroups);
            ExportLogCommand = new RelayCommand(ExportLog);
            ConnectDeviceCommand = new RelayCommand<IDevice>(ConnectDevice);
            DisconnectDeviceCommand = new RelayCommand<IDevice>(DisconnectDevice);
        }

        /// <summary>
        /// 初始化支持的设备类型
        /// </summary>
        private void InitializeSupportedDeviceTypes()
        {
            var deviceTypes = DeviceFactory.GetSupportedDeviceTypes();
            foreach (var deviceType in deviceTypes)
            {
                SupportedDeviceTypes.Add(deviceType);
            }

            if (SupportedDeviceTypes.Count > 0)
            {
                SelectedDeviceType = SupportedDeviceTypes[0];
            }

            // 初始化设备类型分类
            InitializeDeviceTypeCategories();
        }

        /// <summary>
        /// 初始化设备类型分类
        /// </summary>
        private void InitializeDeviceTypeCategories()
        {
            try
            {
                DeviceTypeCategories.Clear();

                // 获取所有设备类型元数据
                var allMetadata = DeviceFactory.GetDeviceItems();

                // 按类别分组
                var categories = allMetadata.GroupBy(m => m.Category).ToList();

                foreach (var categoryGroup in categories)
                {
                    var categoryName = categoryGroup.Key;
                    var categoryViewModel = new DeviceCategoryGroup(categoryName);

                    foreach (var metadata in categoryGroup)
                    {
                        categoryViewModel.Items.Add(metadata);
                    }

                    DeviceTypeCategories.Add(categoryViewModel);
                }

                AddOperationLog($"已加载 {DeviceTypeCategories.Count} 个设备类别，共 {allMetadata.Count} 种设备类型");
            }
            catch (Exception ex)
            {
                AddOperationLog($"初始化设备类型分类失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 添加操作日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void AddOperationLog(string message)
        {
            string timestamp = DateTime.Now.ToString("HH:mm:ss");
            OperationLog += $"[{timestamp}] {message}\n";
        }

        /// <summary>
        /// 公开的属性变更通知方法，供外部调用
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        public void NotifyPropertyChanged(string propertyName)
        {
            OnPropertyChanged(propertyName);
        }

        #endregion

        #region 命令实现

        /// <summary>
        /// 初始化设备管理器命令
        /// </summary>
        private async Task InitializeDeviceManagerAsync()
        {
            try
            {
                AddOperationLog("正在初始化设备管理器...");
                bool result = await Task.Run(() => _deviceManager.Initialize());

                if (result)
                {
                    AddOperationLog("设备管理器初始化成功");
                    OnPropertyChanged(nameof(IsInitialized));
                }
                else
                {
                    AddOperationLog("设备管理器初始化失败");
                }
            }
            catch (Exception ex)
            {
                AddOperationLog($"初始化设备管理器异常：{ex.Message}");
            }
        }

 

        /// <summary>
        /// 移除选中设备命令
        /// </summary>
        private void RemoveSelectedDevice()
        {
            try
            {
                if (SelectedDevice == null)
                {
                    AddOperationLog("请选择要移除的设备");
                    return;
                }

                string deviceName = SelectedDevice.Name;
                string deviceId = SelectedDevice.Id;

                _deviceManager.RemoveDevice(deviceId);
                AddOperationLog($"成功移除设备：{deviceName}");

                // 更新属性通知
                OnPropertyChanged(nameof(TotalDeviceCount));
                OnPropertyChanged(nameof(ConnectedDeviceCount));
            }
            catch (Exception ex)
            {
                AddOperationLog($"移除设备异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 连接所有设备命令
        /// </summary>
        private async Task ConnectAllDevicesAsync()
        {
            try
            {
                AddOperationLog("正在连接所有设备...");
                bool result = await _deviceManager.ConnectAllDevicesAsync();

                if (result)
                {
                    AddOperationLog("所有设备连接成功");
                }
                else
                {
                    AddOperationLog("部分设备连接失败");
                }

                OnPropertyChanged(nameof(ConnectedDeviceCount));
            }
            catch (Exception ex)
            {
                AddOperationLog($"连接设备异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 断开所有设备命令
        /// </summary>
        private async Task DisconnectAllDevicesAsync()
        {
            try
            {
                AddOperationLog("正在断开所有设备...");
                bool result = await _deviceManager.DisconnectAllDevicesAsync();

                if (result)
                {
                    AddOperationLog("所有设备断开成功");
                }
                else
                {
                    AddOperationLog("部分设备断开失败");
                }

                OnPropertyChanged(nameof(ConnectedDeviceCount));
            }
            catch (Exception ex)
            {
                AddOperationLog($"断开设备异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 启动监控命令
        /// </summary>
        private void StartMonitoring()
        {
            try
            {
                bool result = _deviceManager.StartMonitoring();
                if (result)
                {
                    AddOperationLog("设备监控已启动");
                    OnPropertyChanged(nameof(IsMonitoring));
                }
                else
                {
                    AddOperationLog("启动设备监控失败");
                }
            }
            catch (Exception ex)
            {
                AddOperationLog($"启动监控异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 停止监控命令
        /// </summary>
        private void StopMonitoring()
        {
            try
            {
                bool result = _deviceManager.StopMonitoring();
                if (result)
                {
                    AddOperationLog("设备监控已停止");
                    OnPropertyChanged(nameof(IsMonitoring));
                }
                else
                {
                    AddOperationLog("停止设备监控失败");
                }
            }
            catch (Exception ex)
            {
                AddOperationLog($"停止监控异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 保存配置命令
        /// </summary>
        private void SaveConfiguration()
        {
            try
            {
                // 创建保存文件对话框
                var saveFileDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Title = "保存设备配置",
                    Filter = "JSON配置文件 (*.json)|*.json|所有文件 (*.*)|*.*",
                    DefaultExt = "json",
                    FileName = $"DeviceConfig_{DateTime.Now:yyyyMMdd_HHmmss}.json"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    _deviceManager.SaveDeviceConfiguration(saveFileDialog.FileName);
                    AddOperationLog($"设备配置已保存到：{saveFileDialog.FileName}");
                }
                else
                {
                    AddOperationLog("保存配置操作已取消");
                }
            }
            catch (Exception ex)
            {
                AddOperationLog($"保存配置异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 加载配置命令
        /// </summary>
        private void LoadConfiguration()
        {
            try
            {
                // 创建打开文件对话框
                var openFileDialog = new Microsoft.Win32.OpenFileDialog
                {
                    Title = "加载设备配置",
                    Filter = "JSON配置文件 (*.json)|*.json|所有文件 (*.*)|*.*",
                    DefaultExt = "json"
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    _deviceManager.LoadDeviceConfiguration(openFileDialog.FileName);
                    AddOperationLog($"设备配置已从文件加载：{openFileDialog.FileName}");

                    // 更新属性通知
                    OnPropertyChanged(nameof(TotalDeviceCount));
                    OnPropertyChanged(nameof(ConnectedDeviceCount));
                    OnPropertyChanged(nameof(DeviceGroups));
                }
                else
                {
                    AddOperationLog("加载配置操作已取消");
                }
            }
            catch (Exception ex)
            {
                AddOperationLog($"加载配置异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 清空日志命令
        /// </summary>
        private void ClearLog()
        {
            OperationLog = "设备管理器操作日志：\n";
        }

        /// <summary>
        /// 搜索设备命令
        /// </summary>
        private void SearchDevices()
        {
            try
            {
                if (!_deviceManager.IsInitialized)
                {
                    AddOperationLog("请先初始化设备管理器");
                    return;
                }

                AddOperationLog("开始搜索可用设备...");
                int foundDevices = _deviceManager.SearchAvailableDevices();
                AddOperationLog($"搜索完成，找到 {foundDevices} 个可用设备");

                // 更新属性通知
                OnPropertyChanged(nameof(TotalDeviceCount));
                OnPropertyChanged(nameof(ConnectedDeviceCount));
            }
            catch (Exception ex)
            {
                AddOperationLog($"搜索设备异常：{ex.Message}");
            }
        }

       
        /// <summary>
        /// 添加设备到分组命令
        /// </summary>
        /// <param name="group">设备分组</param>
        private void AddDeviceToGroup(DeviceCategoryGroup group)
        {
            try
            {
                if (group == null)
                {
                    AddOperationLog("设备分组为空");
                    return;
                }

                // 这里可以弹出设备选择对话框，暂时使用简单的逻辑
                AddOperationLog($"请在设备类型列表中选择要添加到 {group.CategoryName} 分组的设备类型");
            }
            catch (Exception ex)
            {
                AddOperationLog($"添加设备到分组异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 从分组移除设备命令
        /// </summary>
        /// <param name="device">要移除的设备</param>
        private void RemoveDeviceFromGroup(IDevice device)
        {
            try
            {
                if (device == null)
                {
                    AddOperationLog("设备为空");
                    return;
                }

                string deviceName = device.Name;
                string deviceId = device.Id;

                _deviceManager.RemoveDevice(deviceId);
                AddOperationLog($"成功从分组移除设备：{deviceName}");

                // 更新属性通知
                OnPropertyChanged(nameof(TotalDeviceCount));
                OnPropertyChanged(nameof(ConnectedDeviceCount));
            }
            catch (Exception ex)
            {
                AddOperationLog($"从分组移除设备异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 刷新设备分组命令
        /// </summary>
        private void RefreshDeviceGroups()
        {
            try
            {
                AddOperationLog("正在刷新设备分组...");

                // 重新初始化设备类型分类
                InitializeDeviceTypeCategories();

                // 通知设备分组更新
                OnPropertyChanged(nameof(DeviceGroups));

                AddOperationLog("设备分组刷新完成");
            }
            catch (Exception ex)
            {
                AddOperationLog($"刷新设备分组异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 导出日志命令
        /// </summary>
        private void ExportLog()
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var fileName = $"DeviceManager_Log_{timestamp}.txt";

                // 这里应该弹出保存文件对话框，暂时使用简单的逻辑
                AddOperationLog($"日志导出功能：将保存为 {fileName}");
                AddOperationLog("注意：实际导出功能需要实现文件保存对话框");
            }
            catch (Exception ex)
            {
                AddOperationLog($"导出日志异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 连接设备命令
        /// </summary>
        /// <param name="device">要连接的设备</param>
        private void ConnectDevice(IDevice device)
        {
            try
            {
                if (device == null)
                {
                    AddOperationLog("设备为空");
                    return;
                }

                AddOperationLog($"正在连接设备：{device.Name}");
                bool result = device.Open();

                if (result)
                {
                    AddOperationLog($"设备连接成功：{device.Name}");
                }
                else
                {
                    AddOperationLog($"设备连接失败：{device.Name}");
                }

                // 更新属性通知
                OnPropertyChanged(nameof(ConnectedDeviceCount));
            }
            catch (Exception ex)
            {
                AddOperationLog($"连接设备异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 断开设备命令
        /// </summary>
        /// <param name="device">要断开的设备</param>
        private void DisconnectDevice(IDevice device)
        {
            try
            {
                if (device == null)
                {
                    AddOperationLog("设备为空");
                    return;
                }

                AddOperationLog($"正在断开设备：{device.Name}");
                bool result = device.Close();

                if (result)
                {
                    AddOperationLog($"设备断开成功：{device.Name}");
                }
                else
                {
                    AddOperationLog($"设备断开失败：{device.Name}");
                }

                // 更新属性通知
                OnPropertyChanged(nameof(ConnectedDeviceCount));
            }
            catch (Exception ex)
            {
                AddOperationLog($"断开设备异常：{ex.Message}");
            }
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 设备管理器属性变化事件处理
        /// </summary>
        private void DeviceManager_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            switch (e.PropertyName)
            {
                case nameof(DeviceManager.StatusMessage):
                    OnPropertyChanged(nameof(StatusMessage));
                    break;
                case nameof(DeviceManager.IsInitialized):
                    OnPropertyChanged(nameof(IsInitialized));
                    break;
                case nameof(DeviceManager.IsMonitoring):
                    OnPropertyChanged(nameof(IsMonitoring));
                    break;
                case nameof(DeviceManager.ConnectedDeviceCount):
                    OnPropertyChanged(nameof(ConnectedDeviceCount));
                    break;
                case nameof(DeviceManager.TotalDeviceCount):
                    OnPropertyChanged(nameof(TotalDeviceCount));
                    break;
            }
        }

        /// <summary>
        /// 配置界面变更事件处理
        /// </summary>
        private void DeviceManager_ConfigurationUIChanged(object sender, EventArgs e)
        {
            OnPropertyChanged(nameof(CurrentConfigurationUI));
        }

        #endregion
    }
}
