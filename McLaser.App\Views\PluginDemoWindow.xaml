<Window x:Class="McLaser.App.Views.PluginDemoWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="插件管理演示" Height="800" Width="1200"
        WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <!-- 状态颜色转换器 -->
        <Style x:Key="StatusTextStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding StatusColor}" Value="Green">
                    <Setter Property="Foreground" Value="Green"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding StatusColor}" Value="Red">
                    <Setter Property="Foreground" Value="Red"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding StatusColor}" Value="Orange">
                    <Setter Property="Foreground" Value="Orange"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding StatusColor}" Value="Blue">
                    <Setter Property="Foreground" Value="Blue"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding StatusColor}" Value="Gray">
                    <Setter Property="Foreground" Value="Gray"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- 按钮样式 -->
        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="MinWidth" Value="80"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 顶部工具栏 -->
        <ToolBar Grid.Row="0">
            <Button Content="初始化" Command="{Binding InitializePluginManagerCommand}" 
                    Style="{StaticResource ActionButtonStyle}"/>
            <Button Content="扫描插件" Command="{Binding ScanPluginsCommand}" 
                    Style="{StaticResource ActionButtonStyle}"/>
            <Button Content="创建示例" Command="{Binding CreateSamplePluginsCommand}" 
                    Style="{StaticResource ActionButtonStyle}"/>
            <Separator/>
            <Button Content="加载插件" Command="{Binding LoadPluginCommand}" 
                    Style="{StaticResource ActionButtonStyle}"/>
            <Button Content="卸载插件" Command="{Binding UnloadPluginCommand}" 
                    Style="{StaticResource ActionButtonStyle}"/>
            <Button Content="启动插件" Command="{Binding StartPluginCommand}" 
                    Style="{StaticResource ActionButtonStyle}"/>
            <Button Content="停止插件" Command="{Binding StopPluginCommand}" 
                    Style="{StaticResource ActionButtonStyle}"/>
            <Separator/>
            <Button Content="刷新统计" Command="{Binding RefreshStatisticsCommand}" 
                    Style="{StaticResource ActionButtonStyle}"/>
            <Button Content="清空日志" Command="{Binding ClearLogsCommand}" 
                    Style="{StaticResource ActionButtonStyle}"/>
            <Separator/>
            <TextBlock Text="插件目录:" VerticalAlignment="Center" Margin="5,0"/>
            <TextBox Text="{Binding PluginDirectory}" Width="200" VerticalAlignment="Center"/>
        </ToolBar>

        <!-- 主内容区域 -->
        <TabControl Grid.Row="1" Margin="5">
            <!-- 可用插件标签页 -->
            <TabItem Header="可用插件">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 插件列表 -->
                    <DataGrid Grid.Row="0" ItemsSource="{Binding AvailablePlugins}" 
                              SelectedItem="{Binding SelectedPlugin}"
                              AutoGenerateColumns="False" IsReadOnly="True"
                              GridLinesVisibility="Horizontal" AlternatingRowBackground="LightGray">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="名称" Binding="{Binding Name}" Width="150"/>
                            <DataGridTextColumn Header="ID" Binding="{Binding Id}" Width="200"/>
                            <DataGridTextColumn Header="版本" Binding="{Binding Version}" Width="80"/>
                            <DataGridTextColumn Header="作者" Binding="{Binding Author}" Width="100"/>
                            <DataGridTextColumn Header="类别" Binding="{Binding Category}" Width="100"/>
                            <DataGridTemplateColumn Header="状态" Width="80">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding StatusText}" 
                                                   Style="{StaticResource StatusTextStyle}"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTextColumn Header="文件名" Binding="{Binding FileName}" Width="150"/>
                            <DataGridTextColumn Header="大小" Binding="{Binding FileSize}" Width="80"/>
                            <DataGridTextColumn Header="描述" Binding="{Binding Description}" Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- 插件详细信息 -->
                    <GroupBox Grid.Row="1" Header="插件详细信息" Margin="0,5,0,0" Height="120">
                        <Grid DataContext="{Binding SelectedPlugin}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="{Binding Name, StringFormat='名称: {0}'}" Margin="5"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding Version, StringFormat='版本: {0}'}" Margin="5"/>
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="{Binding Author, StringFormat='作者: {0}'}" Margin="5"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding Category, StringFormat='类别: {0}'}" Margin="5"/>
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="{Binding FilePath, StringFormat='路径: {0}'}" Margin="5"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding RunTime, StringFormat='运行时间: {0}'}" Margin="5"/>
                            <TextBlock Grid.Row="3" Grid.ColumnSpan="2" Text="{Binding Description, StringFormat='描述: {0}'}" 
                                       Margin="5" TextWrapping="Wrap"/>
                        </Grid>
                    </GroupBox>
                </Grid>
            </TabItem>

            <!-- 已加载插件标签页 -->
            <TabItem Header="已加载插件">
                <DataGrid ItemsSource="{Binding LoadedPlugins}" 
                          SelectedItem="{Binding SelectedPlugin}"
                          AutoGenerateColumns="False" IsReadOnly="True"
                          GridLinesVisibility="Horizontal" AlternatingRowBackground="LightGray">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="名称" Binding="{Binding Name}" Width="150"/>
                        <DataGridTextColumn Header="ID" Binding="{Binding Id}" Width="200"/>
                        <DataGridTextColumn Header="版本" Binding="{Binding Version}" Width="80"/>
                        <DataGridTemplateColumn Header="状态" Width="80">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding StatusText}" 
                                               Style="{StaticResource StatusTextStyle}"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTextColumn Header="运行时间" Binding="{Binding RunTime}" Width="100"/>
                        <DataGridTextColumn Header="描述" Binding="{Binding Description}" Width="*"/>
                    </DataGrid.Columns>
                </DataGrid>
            </TabItem>

            <!-- 统计信息标签页 -->
            <TabItem Header="统计信息">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 统计数据 -->
                    <GroupBox Grid.Column="0" Header="插件统计" Margin="5">
                        <DataGrid ItemsSource="{Binding PluginStatistics}" 
                                  AutoGenerateColumns="False" IsReadOnly="True"
                                  GridLinesVisibility="None" HeadersVisibility="None">
                            <DataGrid.Columns>
                                <DataGridTextColumn Binding="{Binding Name}" Width="150"/>
                                <DataGridTextColumn Binding="{Binding Value}" Width="*"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </GroupBox>

                    <!-- 系统信息 -->
                    <GroupBox Grid.Column="1" Header="系统信息" Margin="5">
                        <StackPanel>
                            <TextBlock Text="{Binding IsInitialized, StringFormat='管理器状态: {0}'}" Margin="5"/>
                            <TextBlock Text="{Binding IsLoading, StringFormat='正在加载: {0}'}" Margin="5"/>
                            <TextBlock Text="{Binding PluginDirectory, StringFormat='插件目录: {0}'}" Margin="5"/>
                            <TextBlock Text="{Binding AvailablePlugins.Count, StringFormat='可用插件数: {0}'}" Margin="5"/>
                            <TextBlock Text="{Binding LoadedPlugins.Count, StringFormat='已加载插件数: {0}'}" Margin="5"/>
                        </StackPanel>
                    </GroupBox>
                </Grid>
            </TabItem>

            <!-- 日志标签页 -->
            <TabItem Header="日志">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="200"/>
                    </Grid.RowDefinitions>

                    <!-- 日志列表 -->
                    <DataGrid Grid.Row="0" ItemsSource="{Binding PluginLogs}" 
                              AutoGenerateColumns="False" IsReadOnly="True"
                              GridLinesVisibility="Horizontal" AlternatingRowBackground="LightGray">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="时间" Binding="{Binding Timestamp, StringFormat='HH:mm:ss.fff'}" Width="100"/>
                            <DataGridTextColumn Header="级别" Binding="{Binding Level}" Width="80"/>
                            <DataGridTextColumn Header="来源" Binding="{Binding Source}" Width="100"/>
                            <DataGridTextColumn Header="消息" Binding="{Binding Message}" Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- 日志文本 -->
                    <GroupBox Grid.Row="1" Header="日志输出" Margin="0,5,0,0">
                        <ScrollViewer VerticalScrollBarVisibility="Auto">
                            <TextBox Text="{Binding LogMessages}" IsReadOnly="True" 
                                     TextWrapping="Wrap" FontFamily="Consolas" FontSize="10"/>
                        </ScrollViewer>
                    </GroupBox>
                </Grid>
            </TabItem>
        </TabControl>

        <!-- 底部状态栏 -->
        <StatusBar Grid.Row="2">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="加载状态: "/>
                    <TextBlock Text="{Binding IsLoading}" FontWeight="Bold"/>
                    <TextBlock Text=" | 初始化状态: " Margin="10,0,0,0"/>
                    <TextBlock Text="{Binding IsInitialized}" FontWeight="Bold"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
