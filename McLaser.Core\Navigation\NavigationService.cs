#nullable enable
using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Linq;
using System.Windows;
using McLaser.Core.Framework.Logging;

namespace McLaser.Core.Navigation
{

    [Export(typeof(INavigationService))]   
    public class NavigationService : INavigationService
    {
        private readonly ILogger? _logger;
        private readonly List<PageInfo> _registeredPages = new List<PageInfo>();
        private readonly List<PageInfo> _navigationHistory = new List<PageInfo>();
        private readonly Dictionary<string, FrameworkElement> _pageInstances = new Dictionary<string, FrameworkElement>();
        private int _currentHistoryIndex = -1;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志服务</param>
        [ImportingConstructor]
        public NavigationService(ILogger? logger = null)
        {
            _logger = logger;
        }

        /// <summary>
        /// 当前页面信息
        /// </summary>
        public PageInfo? CurrentPage { get; private set; }

        /// <summary>
        /// 当前页面实例
        /// </summary>
        public FrameworkElement? CurrentPageInstance { get; private set; }

        /// <summary>
        /// 所有注册的页面
        /// </summary>
        public IReadOnlyList<PageInfo> RegisteredPages => _registeredPages.AsReadOnly();

        /// <summary>
        /// 导航历史
        /// </summary>
        public IReadOnlyList<PageInfo> NavigationHistory => _navigationHistory.AsReadOnly();

        /// <summary>
        /// 页面变更事件
        /// </summary>
        public event EventHandler<PageChangedEventArgs>? PageChanged;

        /// <summary>
        /// 注册页面
        /// </summary>
        /// <param name="pageInfo">页面信息</param>
        public void RegisterPage(PageInfo pageInfo)
        {
            if (pageInfo == null)
                throw new ArgumentNullException(nameof(pageInfo));

            if (string.IsNullOrEmpty(pageInfo.Id))
                throw new ArgumentException("页面ID不能为空", nameof(pageInfo));

            // 检查是否已存在相同ID的页面
            if (_registeredPages.Any(p => p.Id == pageInfo.Id))
            {
                _logger?.LogWarning($"页面ID '{pageInfo.Id}' 已存在，将被替换");
                _registeredPages.RemoveAll(p => p.Id == pageInfo.Id);
            }

            _registeredPages.Add(pageInfo);
            _logger?.LogInfo($"页面已注册: {pageInfo.Id} - {pageInfo.Title}");
        }

        /// <summary>
        /// 批量注册页面
        /// </summary>
        /// <param name="pages">页面信息列表</param>
        public void RegisterPages(IEnumerable<PageInfo> pages)
        {
            if (pages == null)
                throw new ArgumentNullException(nameof(pages));

            foreach (var page in pages)
            {
                RegisterPage(page);
            }
        }

        /// <summary>
        /// 导航到指定页面
        /// </summary>
        /// <param name="pageId">页面ID</param>
        /// <param name="parameters">导航参数</param>
        /// <returns>是否导航成功</returns>
        public bool NavigateTo(string pageId, object? parameters = null)
        {
            if (string.IsNullOrEmpty(pageId))
            {
                _logger?.LogWarning("导航失败: 页面ID为空");
                return false;
            }

            _logger?.LogInfo($"尝试导航到页面: {pageId}");

            var pageInfo = _registeredPages.FirstOrDefault(p => p.Id == pageId);
            if (pageInfo == null)
            {
                _logger?.LogError($"页面未找到: {pageId}，已注册页面: {string.Join(", ", _registeredPages.Select(p => p.Id))}");
                return false;
            }

            _logger?.LogInfo($"找到页面: {pageInfo.Title}");
            return NavigateTo(pageInfo, parameters);
        }

        /// <summary>
        /// 导航到指定页面
        /// </summary>
        /// <param name="pageInfo">页面信息</param>
        /// <param name="parameters">导航参数</param>
        /// <returns>是否导航成功</returns>
        public bool NavigateTo(PageInfo pageInfo, object? parameters = null)
        {
            if (pageInfo == null)
            {
                _logger?.LogWarning("导航失败: 页面信息为空");
                return false;
            }

            try
            {
                _logger?.LogInfo($"开始导航到页面: {pageInfo.Id} - {pageInfo.Title}");

                var oldPage = CurrentPage;
                var pageInstance = GetOrCreatePageInstance(pageInfo);

                if (pageInstance == null)
                {
                    _logger?.LogError($"无法创建页面实例: {pageInfo.Id}");
                    return false;
                }

                _logger?.LogInfo($"页面实例创建成功: {pageInstance.GetType().Name}");

                // 更新当前页面
                CurrentPage = pageInfo;
                CurrentPageInstance = pageInstance;

                // 更新所有页面的当前状态
                UpdateAllPagesCurrentStatus(pageInfo.Id);

                // 更新导航历史
                UpdateNavigationHistory(pageInfo);

                // 触发页面变更事件
                PageChanged?.Invoke(this, new PageChangedEventArgs(oldPage, pageInfo, parameters));

                _logger?.LogInfo($"导航成功: {pageInfo.Id} - {pageInfo.Title}");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"导航失败: {ex.Message}\n堆栈跟踪: {ex.StackTrace}");
                return false;
            }
        }

        /// <summary>
        /// 返回上一页
        /// </summary>
        /// <returns>是否返回成功</returns>
        public bool GoBack()
        {
            if (_currentHistoryIndex <= 0)
                return false;

            _currentHistoryIndex--;
            var pageInfo = _navigationHistory[_currentHistoryIndex];
            return NavigateToHistoryPage(pageInfo);
        }

        /// <summary>
        /// 前进到下一页
        /// </summary>
        /// <returns>是否前进成功</returns>
        public bool GoForward()
        {
            if (_currentHistoryIndex >= _navigationHistory.Count - 1)
                return false;

            _currentHistoryIndex++;
            var pageInfo = _navigationHistory[_currentHistoryIndex];
            return NavigateToHistoryPage(pageInfo);
        }

        /// <summary>
        /// 清除导航历史
        /// </summary>
        public void ClearHistory()
        {
            _navigationHistory.Clear();
            _currentHistoryIndex = -1;
            _logger?.LogInfo("导航历史已清除");
        }

        /// <summary>
        /// 获取页面实例
        /// </summary>
        /// <param name="pageId">页面ID</param>
        /// <returns>页面实例</returns>
        public FrameworkElement? GetPageInstance(string pageId)
        {
            if (string.IsNullOrEmpty(pageId))
                return null;

            _pageInstances.TryGetValue(pageId, out var instance);
            return instance;
        }

        /// <summary>
        /// 释放页面实例
        /// </summary>
        /// <param name="pageId">页面ID</param>
        public void ReleasePage(string pageId)
        {
            if (string.IsNullOrEmpty(pageId))
                return;

            if (_pageInstances.TryGetValue(pageId, out var instance))
            {
                _pageInstances.Remove(pageId);
                
                // 如果实现了IDisposable，则释放资源
                if (instance is IDisposable disposable)
                {
                    disposable.Dispose();
                }

                _logger?.LogInfo($"页面实例已释放: {pageId}");
            }
        }

        /// <summary>
        /// 检查页面是否存在
        /// </summary>
        /// <param name="pageId">页面ID</param>
        /// <returns>是否存在</returns>
        public bool PageExists(string pageId)
        {
            if (string.IsNullOrEmpty(pageId))
                return false;

            return _registeredPages.Any(p => p.Id == pageId);
        }

        #region 私有方法

        /// <summary>
        /// 获取或创建页面实例
        /// </summary>
        /// <param name="pageInfo">页面信息</param>
        /// <returns>页面实例</returns>
        private FrameworkElement? GetOrCreatePageInstance(PageInfo pageInfo)
        {
            // 如果是单例且已存在实例，直接返回
            if (pageInfo.IsSingleton && _pageInstances.TryGetValue(pageInfo.Id, out var existingInstance))
            {
                return existingInstance;
            }

            FrameworkElement? instance = null;

            // 使用工厂方法创建
            if (pageInfo.PageFactory != null)
            {
                instance = pageInfo.PageFactory();
            }
            // 使用类型创建
            else if (pageInfo.PageType != null)
            {
                instance = Activator.CreateInstance(pageInfo.PageType) as FrameworkElement;
            }

            if (instance != null)
            {
                // 设置DataContext
                if (pageInfo.ViewModelFactory != null)
                {
                    instance.DataContext = pageInfo.ViewModelFactory();
                }
                else if (pageInfo.ViewModelType != null)
                {
                    instance.DataContext = Activator.CreateInstance(pageInfo.ViewModelType);
                }

                // 如果是单例，缓存实例
                if (pageInfo.IsSingleton)
                {
                    _pageInstances[pageInfo.Id] = instance;
                }
            }

            return instance;
        }

        /// <summary>
        /// 更新导航历史
        /// </summary>
        /// <param name="pageInfo">页面信息</param>
        private void UpdateNavigationHistory(PageInfo pageInfo)
        {
            // 如果当前不在历史末尾，移除后续历史
            if (_currentHistoryIndex < _navigationHistory.Count - 1)
            {
                _navigationHistory.RemoveRange(_currentHistoryIndex + 1, 
                    _navigationHistory.Count - _currentHistoryIndex - 1);
            }

            // 添加新页面到历史
            _navigationHistory.Add(pageInfo);
            _currentHistoryIndex = _navigationHistory.Count - 1;

            // 限制历史记录数量
            const int maxHistoryCount = 50;
            if (_navigationHistory.Count > maxHistoryCount)
            {
                _navigationHistory.RemoveAt(0);
                _currentHistoryIndex--;
            }
        }

        /// <summary>
        /// 更新所有页面的当前状态
        /// </summary>
        /// <param name="currentPageId">当前页面ID</param>
        private void UpdateAllPagesCurrentStatus(string currentPageId)
        {
            foreach (var page in _registeredPages)
            {
                page.IsCurrentPage = page.Id == currentPageId;
            }
        }

        /// <summary>
        /// 导航到历史页面（不更新历史）
        /// </summary>
        /// <param name="pageInfo">页面信息</param>
        /// <returns>是否导航成功</returns>
        private bool NavigateToHistoryPage(PageInfo pageInfo)
        {
            try
            {
                var oldPage = CurrentPage;
                var pageInstance = GetOrCreatePageInstance(pageInfo);
                
                if (pageInstance == null)
                {
                    _logger?.LogError($"无法创建页面实例: {pageInfo.Id}");
                    return false;
                }

                // 更新当前页面
                CurrentPage = pageInfo;
                CurrentPageInstance = pageInstance;

                // 更新所有页面的当前状态
                UpdateAllPagesCurrentStatus(pageInfo.Id);

                // 触发页面变更事件
                PageChanged?.Invoke(this, new PageChangedEventArgs(oldPage, pageInfo));

                _logger?.LogInfo($"历史导航到页面: {pageInfo.Id} - {pageInfo.Title}");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"历史导航失败: {ex.Message}");
                return false;
            }
        }

        #endregion
    }
}
