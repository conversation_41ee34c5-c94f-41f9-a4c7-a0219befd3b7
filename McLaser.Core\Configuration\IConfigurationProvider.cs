using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace McLaser.Core.Configuration
{
    /// <summary>
    /// 配置提供者接口
    /// 定义配置数据的读取、写入和监控功能
    /// </summary>
    public interface IConfigurationProvider : IDisposable
    {
        #region 基本属性

        /// <summary>
        /// 提供者名称
        /// </summary>
        string Name { get; }

        /// <summary>
        /// 提供者描述
        /// </summary>
        string Description { get; }

        /// <summary>
        /// 是否支持写入
        /// </summary>
        bool SupportsWrite { get; }

        /// <summary>
        /// 是否支持监控变更
        /// </summary>
        bool SupportsWatch { get; }

        /// <summary>
        /// 是否已初始化
        /// </summary>
        bool IsInitialized { get; }

        /// <summary>
        /// 优先级（数值越大优先级越高）
        /// </summary>
        int Priority { get; set; }

        #endregion

        #region 初始化和生命周期

        /// <summary>
        /// 初始化配置提供者
        /// </summary>
        /// <param name="parameters">初始化参数</param>
        /// <returns>异步任务</returns>
        Task InitializeAsync(IDictionary<string, object> parameters = null);

        /// <summary>
        /// 关闭配置提供者
        /// </summary>
        /// <returns>异步任务</returns>
        Task ShutdownAsync();

        #endregion

        #region 配置数据操作

        /// <summary>
        /// 加载所有配置数据
        /// </summary>
        /// <returns>配置数据字典</returns>
        Task<IDictionary<string, object>> LoadAsync();

        /// <summary>
        /// 获取配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>配置值</returns>
        Task<object> GetValueAsync(string key);

        /// <summary>
        /// 设置配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        /// <returns>异步任务</returns>
        Task SetValueAsync(string key, object value);

        /// <summary>
        /// 删除配置项
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>是否删除成功</returns>
        Task<bool> RemoveValueAsync(string key);

        /// <summary>
        /// 检查配置键是否存在
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>是否存在</returns>
        Task<bool> ContainsKeyAsync(string key);

        /// <summary>
        /// 获取所有配置键
        /// </summary>
        /// <returns>配置键集合</returns>
        Task<IEnumerable<string>> GetKeysAsync();

        #endregion

        #region 批量操作

        /// <summary>
        /// 批量设置配置值
        /// </summary>
        /// <param name="values">配置值字典</param>
        /// <returns>异步任务</returns>
        Task SetValuesAsync(IDictionary<string, object> values);

        /// <summary>
        /// 批量删除配置项
        /// </summary>
        /// <param name="keys">配置键集合</param>
        /// <returns>删除成功的键集合</returns>
        Task<IEnumerable<string>> RemoveValuesAsync(IEnumerable<string> keys);

        #endregion

        #region 配置持久化

        /// <summary>
        /// 保存配置到持久化存储
        /// </summary>
        /// <returns>异步任务</returns>
        Task SaveAsync();

        /// <summary>
        /// 重新加载配置
        /// </summary>
        /// <returns>异步任务</returns>
        Task ReloadAsync();

        #endregion

        #region 配置监控

        /// <summary>
        /// 配置变更事件
        /// </summary>
        event EventHandler<ConfigurationChangedEventArgs> ConfigurationChanged;

        /// <summary>
        /// 开始监控配置变更
        /// </summary>
        void StartWatching();

        /// <summary>
        /// 停止监控配置变更
        /// </summary>
        void StopWatching();

        /// <summary>
        /// 是否正在监控
        /// </summary>
        bool IsWatching { get; }

        #endregion

        #region 配置验证

        /// <summary>
        /// 验证配置数据
        /// </summary>
        /// <param name="data">配置数据</param>
        /// <returns>验证结果</returns>
        Task<ConfigurationValidationResult> ValidateAsync(IDictionary<string, object> data);

        #endregion

        #region 配置备份和恢复

        /// <summary>
        /// 创建配置备份
        /// </summary>
        /// <param name="backupName">备份名称</param>
        /// <returns>备份标识</returns>
        Task<string> CreateBackupAsync(string backupName = null);

        /// <summary>
        /// 恢复配置备份
        /// </summary>
        /// <param name="backupId">备份标识</param>
        /// <returns>异步任务</returns>
        Task RestoreBackupAsync(string backupId);

        /// <summary>
        /// 获取所有备份
        /// </summary>
        /// <returns>备份信息集合</returns>
        Task<IEnumerable<ConfigurationBackupInfo>> GetBackupsAsync();

        /// <summary>
        /// 删除配置备份
        /// </summary>
        /// <param name="backupId">备份标识</param>
        /// <returns>是否删除成功</returns>
        Task<bool> DeleteBackupAsync(string backupId);

        #endregion

        #region 配置统计

        /// <summary>
        /// 获取提供者统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        ConfigurationProviderStatistics GetStatistics();

        #endregion
    }

    /// <summary>
    /// 配置变更事件参数
    /// </summary>
    public class ConfigurationChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="oldValue">旧值</param>
        /// <param name="newValue">新值</param>
        /// <param name="changeType">变更类型</param>
        public ConfigurationChangedEventArgs(string key, object oldValue, object newValue, ConfigurationChangeType changeType)
        {
            Key = key;
            OldValue = oldValue;
            NewValue = newValue;
            ChangeType = changeType;
            Timestamp = DateTime.UtcNow;
        }

        /// <summary>
        /// 配置键
        /// </summary>
        public string Key { get; }

        /// <summary>
        /// 旧值
        /// </summary>
        public object OldValue { get; }

        /// <summary>
        /// 新值
        /// </summary>
        public object NewValue { get; }

        /// <summary>
        /// 变更类型
        /// </summary>
        public ConfigurationChangeType ChangeType { get; }

        /// <summary>
        /// 变更时间戳
        /// </summary>
        public DateTime Timestamp { get; }
    }

    /// <summary>
    /// 配置变更类型
    /// </summary>
    public enum ConfigurationChangeType
    {
        /// <summary>
        /// 添加
        /// </summary>
        Added,

        /// <summary>
        /// 修改
        /// </summary>
        Modified,

        /// <summary>
        /// 删除
        /// </summary>
        Removed,

        /// <summary>
        /// 重新加载
        /// </summary>
        Reloaded
    }

    /// <summary>
    /// 配置备份信息
    /// </summary>
    public class ConfigurationBackupInfo
    {
        /// <summary>
        /// 备份标识
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 备份名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 备份大小（字节）
        /// </summary>
        public long Size { get; set; }

        /// <summary>
        /// 备份描述
        /// </summary>
        public string Description { get; set; }
    }

    /// <summary>
    /// 配置提供者统计信息
    /// </summary>
    public class ConfigurationProviderStatistics
    {
        /// <summary>
        /// 读取次数
        /// </summary>
        public long ReadCount { get; set; }

        /// <summary>
        /// 写入次数
        /// </summary>
        public long WriteCount { get; set; }

        /// <summary>
        /// 错误次数
        /// </summary>
        public long ErrorCount { get; set; }

        /// <summary>
        /// 最后访问时间
        /// </summary>
        public DateTime LastAccessTime { get; set; }

        /// <summary>
        /// 平均响应时间（毫秒）
        /// </summary>
        public double AverageResponseTime { get; set; }

        /// <summary>
        /// 配置项数量
        /// </summary>
        public int ItemCount { get; set; }
    }
}
