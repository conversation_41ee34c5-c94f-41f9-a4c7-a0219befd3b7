using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using McLaser.Core.Framework.Logging;

namespace McLaser.Core.Framework.Security
{
    /// <summary>
    /// 默认角色服务实现
    /// 提供完整的角色管理和权限分配功能
    /// </summary>
    public class DefaultRoleService : IRoleService
    {
        #region 字段和属性

        private readonly ILogger? _logger;
        private readonly ConcurrentDictionary<string, Role> _roles;
        private readonly ConcurrentDictionary<string, HashSet<string>> _rolePermissions; // roleId -> permissionIds
        private readonly ConcurrentDictionary<string, HashSet<string>> _roleHierarchy; // roleId -> parentRoleIds

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化默认角色服务
        /// </summary>
        /// <param name="logger">日志服务</param>
        public DefaultRoleService(ILogger? logger = null)
        {
            _logger = logger;
            _roles = new ConcurrentDictionary<string, Role>();
            _rolePermissions = new ConcurrentDictionary<string, HashSet<string>>();
            _roleHierarchy = new ConcurrentDictionary<string, HashSet<string>>();

            // 初始化默认角色
            InitializeDefaultRoles();
        }

        /// <summary>
        /// 初始化默认角色
        /// </summary>
        private void InitializeDefaultRoles()
        {
            try
            {
                // 创建管理员角色
                var adminRole = new Role
                {
                    Id = "admin",
                    Name = "管理员",
                    Description = "系统管理员，拥有所有权限",
                    IsSystemRole = true,
                    IsEnabled = true
                };

                var adminResult = CreateRoleAsync(adminRole).Result;
                if (adminResult.Success)
                {
                    _logger?.LogInfo("默认管理员角色已创建");
                }

                // 创建普通用户角色
                var userRole = new Role
                {
                    Id = "user",
                    Name = "普通用户",
                    Description = "普通用户，拥有基本权限",
                    IsSystemRole = true,
                    IsEnabled = true
                };

                var userResult = CreateRoleAsync(userRole).Result;
                if (userResult.Success)
                {
                    _logger?.LogInfo("默认普通用户角色已创建");
                }

                // 创建访客角色
                var guestRole = new Role
                {
                    Id = "guest",
                    Name = "访客",
                    Description = "访客用户，只有查看权限",
                    IsSystemRole = true,
                    IsEnabled = true
                };

                var guestResult = CreateRoleAsync(guestRole).Result;
                if (guestResult.Success)
                {
                    _logger?.LogInfo("默认访客角色已创建");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("初始化默认角色失败", ex);
            }
        }

        #endregion

        #region 角色管理

        /// <summary>
        /// 创建角色
        /// </summary>
        /// <param name="role">角色信息</param>
        /// <returns>创建结果</returns>
        public async Task<RoleOperationResult> CreateRoleAsync(Role role)
        {
            if (role == null)
                return RoleOperationResult.CreateFailure("角色信息不能为空");

            if (string.IsNullOrWhiteSpace(role.Id))
                return RoleOperationResult.CreateFailure("角色ID不能为空");

            if (string.IsNullOrWhiteSpace(role.Name))
                return RoleOperationResult.CreateFailure("角色名称不能为空");

            try
            {
                // 检查角色是否已存在
                if (_roles.ContainsKey(role.Id))
                {
                    return RoleOperationResult.CreateFailure("角色ID已存在", "ROLE_EXISTS");
                }

                // 设置创建时间
                role.CreatedAt = DateTime.UtcNow;
                role.UpdatedAt = DateTime.UtcNow;

                // 添加角色
                if (_roles.TryAdd(role.Id, role))
                {
                    // 初始化角色权限集合
                    _rolePermissions.TryAdd(role.Id, new HashSet<string>());
                    _roleHierarchy.TryAdd(role.Id, new HashSet<string>());

                    _logger?.LogInfo($"角色已创建: {role.Name} (ID: {role.Id})");
                    
                    // 触发事件
                    OnRoleCreated(new RoleCreatedEventArgs(role));
                    
                    return RoleOperationResult.CreateSuccess();
                }
                else
                {
                    return RoleOperationResult.CreateFailure("添加角色失败");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"创建角色失败: {role.Name}", ex);
                return RoleOperationResult.CreateFailure($"创建角色失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新角色
        /// </summary>
        /// <param name="role">角色信息</param>
        /// <returns>更新结果</returns>
        public async Task<RoleOperationResult> UpdateRoleAsync(Role role)
        {
            if (role == null)
                return RoleOperationResult.CreateFailure("角色信息不能为空");

            if (string.IsNullOrWhiteSpace(role.Id))
                return RoleOperationResult.CreateFailure("角色ID不能为空");

            try
            {
                if (_roles.TryGetValue(role.Id, out var existingRole))
                {
                    // 检查是否为系统角色
                    if (existingRole.IsSystemRole)
                    {
                        return RoleOperationResult.CreateFailure("系统角色不能修改", "SYSTEM_ROLE_READONLY");
                    }

                    // 更新角色信息
                    role.CreatedAt = existingRole.CreatedAt; // 保持创建时间不变
                    role.UpdatedAt = DateTime.UtcNow;

                    if (_roles.TryUpdate(role.Id, role, existingRole))
                    {
                        _logger?.LogInfo($"角色已更新: {role.Name} (ID: {role.Id})");
                        
                        // 触发事件
                        OnRoleUpdated(new RoleUpdatedEventArgs(role));
                        
                        return RoleOperationResult.CreateSuccess();
                    }
                    else
                    {
                        return RoleOperationResult.CreateFailure("更新角色失败");
                    }
                }
                else
                {
                    return RoleOperationResult.CreateFailure("角色不存在", "ROLE_NOT_FOUND");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"更新角色失败: {role.Id}", ex);
                return RoleOperationResult.CreateFailure($"更新角色失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 删除角色
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>删除结果</returns>
        public async Task<RoleOperationResult> DeleteRoleAsync(string roleId)
        {
            if (string.IsNullOrWhiteSpace(roleId))
                return RoleOperationResult.CreateFailure("角色ID不能为空");

            try
            {
                if (_roles.TryGetValue(roleId, out var role))
                {
                    // 检查是否为系统角色
                    if (role.IsSystemRole)
                    {
                        return RoleOperationResult.CreateFailure("系统角色不能删除", "SYSTEM_ROLE_READONLY");
                    }

                    // 删除角色
                    if (_roles.TryRemove(roleId, out var removedRole))
                    {
                        // 清理相关数据
                        _rolePermissions.TryRemove(roleId, out _);
                        _roleHierarchy.TryRemove(roleId, out _);

                        // 清理其他角色对此角色的父级引用
                        foreach (var kvp in _roleHierarchy)
                        {
                            kvp.Value.Remove(roleId);
                        }

                        _logger?.LogInfo($"角色已删除: {removedRole.Name} (ID: {roleId})");
                        
                        // 触发事件
                        OnRoleDeleted(new RoleDeletedEventArgs(roleId, removedRole.Name));
                        
                        return RoleOperationResult.CreateSuccess();
                    }
                    else
                    {
                        return RoleOperationResult.CreateFailure("删除角色失败");
                    }
                }
                else
                {
                    return RoleOperationResult.CreateFailure("角色不存在", "ROLE_NOT_FOUND");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"删除角色失败: {roleId}", ex);
                return RoleOperationResult.CreateFailure($"删除角色失败: {ex.Message}");
            }
        }

        #endregion

        #region 角色查询

        /// <summary>
        /// 根据ID获取角色
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>角色信息</returns>
        public async Task<Role?> GetRoleByIdAsync(string roleId)
        {
            if (string.IsNullOrWhiteSpace(roleId))
                return null;

            _roles.TryGetValue(roleId, out var role);
            return role;
        }

        /// <summary>
        /// 根据名称获取角色
        /// </summary>
        /// <param name="roleName">角色名称</param>
        /// <returns>角色信息</returns>
        public async Task<Role?> GetRoleByNameAsync(string roleName)
        {
            if (string.IsNullOrWhiteSpace(roleName))
                return null;

            return _roles.Values.FirstOrDefault(r => r.Name.Equals(roleName, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 获取所有角色
        /// </summary>
        /// <returns>角色列表</returns>
        public async Task<IEnumerable<Role>> GetAllRolesAsync()
        {
            return _roles.Values.ToList();
        }

        /// <summary>
        /// 搜索角色
        /// </summary>
        /// <param name="searchTerm">搜索条件</param>
        /// <returns>角色列表</returns>
        public async Task<IEnumerable<Role>> SearchRolesAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllRolesAsync();

            var term = searchTerm.ToLowerInvariant();
            return _roles.Values.Where(r =>
                r.Name.ToLowerInvariant().Contains(term) ||
                (r.Description?.ToLowerInvariant().Contains(term) ?? false)
            ).ToList();
        }

        #endregion

        #region 权限管理

        /// <summary>
        /// 为角色分配权限
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <param name="permissionId">权限ID</param>
        /// <returns>分配结果</returns>
        public async Task<RoleOperationResult> AssignPermissionToRoleAsync(string roleId, string permissionId)
        {
            if (string.IsNullOrWhiteSpace(roleId))
                return RoleOperationResult.CreateFailure("角色ID不能为空");

            if (string.IsNullOrWhiteSpace(permissionId))
                return RoleOperationResult.CreateFailure("权限ID不能为空");

            try
            {
                if (!_roles.ContainsKey(roleId))
                {
                    return RoleOperationResult.CreateFailure("角色不存在", "ROLE_NOT_FOUND");
                }

                var permissions = _rolePermissions.GetOrAdd(roleId, _ => new HashSet<string>());
                if (permissions.Add(permissionId))
                {
                    _logger?.LogInfo($"权限已分配给角色: {permissionId} -> {roleId}");
                    return RoleOperationResult.CreateSuccess();
                }
                else
                {
                    return RoleOperationResult.CreateFailure("权限已存在", "PERMISSION_EXISTS");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"分配权限失败: {permissionId} -> {roleId}", ex);
                return RoleOperationResult.CreateFailure($"分配权限失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 为角色分配多个权限
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <param name="permissionIds">权限ID列表</param>
        /// <returns>分配结果</returns>
        public async Task<RoleOperationResult> AssignPermissionsToRoleAsync(string roleId, IEnumerable<string> permissionIds)
        {
            if (string.IsNullOrWhiteSpace(roleId))
                return RoleOperationResult.CreateFailure("角色ID不能为空");

            if (permissionIds == null || !permissionIds.Any())
                return RoleOperationResult.CreateFailure("权限ID列表不能为空");

            try
            {
                if (!_roles.ContainsKey(roleId))
                {
                    return RoleOperationResult.CreateFailure("角色不存在", "ROLE_NOT_FOUND");
                }

                var permissions = _rolePermissions.GetOrAdd(roleId, _ => new HashSet<string>());
                var addedCount = 0;

                foreach (var permissionId in permissionIds)
                {
                    if (!string.IsNullOrWhiteSpace(permissionId) && permissions.Add(permissionId))
                    {
                        addedCount++;
                    }
                }

                _logger?.LogInfo($"已为角色 {roleId} 分配 {addedCount} 个权限");
                return RoleOperationResult.CreateSuccess();
            }
            catch (Exception ex)
            {
                _logger?.LogError($"批量分配权限失败: {roleId}", ex);
                return RoleOperationResult.CreateFailure($"批量分配权限失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从角色移除权限
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <param name="permissionId">权限ID</param>
        /// <returns>移除结果</returns>
        public async Task<RoleOperationResult> RemovePermissionFromRoleAsync(string roleId, string permissionId)
        {
            if (string.IsNullOrWhiteSpace(roleId))
                return RoleOperationResult.CreateFailure("角色ID不能为空");

            if (string.IsNullOrWhiteSpace(permissionId))
                return RoleOperationResult.CreateFailure("权限ID不能为空");

            try
            {
                if (_rolePermissions.TryGetValue(roleId, out var permissions))
                {
                    if (permissions.Remove(permissionId))
                    {
                        _logger?.LogInfo($"权限已从角色移除: {permissionId} <- {roleId}");
                        return RoleOperationResult.CreateSuccess();
                    }
                    else
                    {
                        return RoleOperationResult.CreateFailure("权限不存在", "PERMISSION_NOT_FOUND");
                    }
                }
                else
                {
                    return RoleOperationResult.CreateFailure("角色不存在", "ROLE_NOT_FOUND");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"移除权限失败: {permissionId} <- {roleId}", ex);
                return RoleOperationResult.CreateFailure($"移除权限失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 移除角色的所有权限
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>移除结果</returns>
        public async Task<RoleOperationResult> RemoveAllPermissionsFromRoleAsync(string roleId)
        {
            if (string.IsNullOrWhiteSpace(roleId))
                return RoleOperationResult.CreateFailure("角色ID不能为空");

            try
            {
                if (_rolePermissions.TryGetValue(roleId, out var permissions))
                {
                    var count = permissions.Count;
                    permissions.Clear();
                    _logger?.LogInfo($"已清除角色 {roleId} 的所有权限 ({count} 个)");
                    return RoleOperationResult.CreateSuccess();
                }
                else
                {
                    return RoleOperationResult.CreateFailure("角色不存在", "ROLE_NOT_FOUND");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"清除角色权限失败: {roleId}", ex);
                return RoleOperationResult.CreateFailure($"清除角色权限失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取角色的权限
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>权限列表</returns>
        public async Task<IEnumerable<Permission>> GetRolePermissionsAsync(string roleId)
        {
            if (string.IsNullOrWhiteSpace(roleId))
                return Enumerable.Empty<Permission>();

            if (_rolePermissions.TryGetValue(roleId, out var permissionIds))
            {
                var permissions = new List<Permission>();
                // 注意：这里需要通过IPermissionService来获取Permission对象
                // 简化实现，返回空列表
                return permissions;
            }

            return Enumerable.Empty<Permission>();
        }

        /// <summary>
        /// 检查角色是否拥有指定权限
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <param name="permissionId">权限ID</param>
        /// <returns>是否拥有</returns>
        public async Task<bool> RoleHasPermissionAsync(string roleId, string permissionId)
        {
            if (string.IsNullOrWhiteSpace(roleId) || string.IsNullOrWhiteSpace(permissionId))
                return false;

            if (_rolePermissions.TryGetValue(roleId, out var permissions))
            {
                return permissions.Contains(permissionId);
            }

            return false;
        }

        #endregion

        #region 角色层次结构

        /// <summary>
        /// 设置父角色
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <param name="parentRoleId">父角色ID</param>
        /// <returns>设置结果</returns>
        public async Task<RoleOperationResult> SetParentRoleAsync(string roleId, string parentRoleId)
        {
            if (string.IsNullOrWhiteSpace(roleId))
                return RoleOperationResult.CreateFailure("角色ID不能为空");

            if (string.IsNullOrWhiteSpace(parentRoleId))
                return RoleOperationResult.CreateFailure("父角色ID不能为空");

            if (roleId == parentRoleId)
                return RoleOperationResult.CreateFailure("角色不能设置自己为父角色");

            try
            {
                // 检查角色是否存在
                if (!_roles.ContainsKey(roleId) || !_roles.ContainsKey(parentRoleId))
                {
                    return RoleOperationResult.CreateFailure("角色不存在", "ROLE_NOT_FOUND");
                }

                // 检查是否会形成循环引用
                if (await WouldCreateCircularReferenceAsync(roleId, parentRoleId))
                {
                    return RoleOperationResult.CreateFailure("设置父角色会形成循环引用", "CIRCULAR_REFERENCE");
                }

                var hierarchy = _roleHierarchy.GetOrAdd(roleId, _ => new HashSet<string>());
                hierarchy.Add(parentRoleId);

                _logger?.LogInfo($"父角色已设置: {roleId} -> {parentRoleId}");
                return RoleOperationResult.CreateSuccess();
            }
            catch (Exception ex)
            {
                _logger?.LogError($"设置父角色失败: {roleId} -> {parentRoleId}", ex);
                return RoleOperationResult.CreateFailure($"设置父角色失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 移除父角色
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>移除结果</returns>
        public async Task<RoleOperationResult> RemoveParentRoleAsync(string roleId)
        {
            if (string.IsNullOrWhiteSpace(roleId))
                return RoleOperationResult.CreateFailure("角色ID不能为空");

            try
            {
                if (_roleHierarchy.TryGetValue(roleId, out var hierarchy))
                {
                    var count = hierarchy.Count;
                    hierarchy.Clear();
                    _logger?.LogInfo($"已移除角色 {roleId} 的所有父角色 ({count} 个)");
                    return RoleOperationResult.CreateSuccess();
                }
                else
                {
                    return RoleOperationResult.CreateFailure("角色不存在", "ROLE_NOT_FOUND");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"移除父角色失败: {roleId}", ex);
                return RoleOperationResult.CreateFailure($"移除父角色失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取子角色
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>子角色列表</returns>
        public async Task<IEnumerable<Role>> GetChildRolesAsync(string roleId)
        {
            if (string.IsNullOrWhiteSpace(roleId))
                return Enumerable.Empty<Role>();

            var childRoles = new List<Role>();

            foreach (var kvp in _roleHierarchy)
            {
                if (kvp.Value.Contains(roleId))
                {
                    if (_roles.TryGetValue(kvp.Key, out var childRole))
                    {
                        childRoles.Add(childRole);
                    }
                }
            }

            return childRoles;
        }

        /// <summary>
        /// 获取所有继承的权限（包括父角色权限）
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>权限列表</returns>
        public async Task<IEnumerable<Permission>> GetInheritedPermissionsAsync(string roleId)
        {
            // 注意：这里返回权限ID列表，实际的Permission对象需要通过IPermissionService获取
            // 这是一个简化实现，实际项目中可能需要注入IPermissionService
            var allPermissionIds = new HashSet<string>();

            await CollectPermissionsRecursiveAsync(roleId, allPermissionIds, new HashSet<string>());

            // 返回空的Permission列表，实际实现中应该通过IPermissionService解析
            return Enumerable.Empty<Permission>();
        }

        /// <summary>
        /// 递归收集权限
        /// </summary>
        private async Task CollectPermissionsRecursiveAsync(string roleId, HashSet<string> allPermissions, HashSet<string> visited)
        {
            if (string.IsNullOrWhiteSpace(roleId) || visited.Contains(roleId))
                return;

            visited.Add(roleId);

            // 添加当前角色的权限
            if (_rolePermissions.TryGetValue(roleId, out var permissions))
            {
                foreach (var permission in permissions)
                {
                    allPermissions.Add(permission);
                }
            }

            // 递归添加父角色的权限
            if (_roleHierarchy.TryGetValue(roleId, out var parentRoles))
            {
                foreach (var parentRoleId in parentRoles)
                {
                    await CollectPermissionsRecursiveAsync(parentRoleId, allPermissions, visited);
                }
            }
        }

        /// <summary>
        /// 检查是否会形成循环引用
        /// </summary>
        private async Task<bool> WouldCreateCircularReferenceAsync(string roleId, string parentRoleId)
        {
            var visited = new HashSet<string>();
            return await CheckCircularReferenceRecursiveAsync(parentRoleId, roleId, visited);
        }

        /// <summary>
        /// 递归检查循环引用
        /// </summary>
        private async Task<bool> CheckCircularReferenceRecursiveAsync(string currentRoleId, string targetRoleId, HashSet<string> visited)
        {
            if (currentRoleId == targetRoleId)
                return true;

            if (visited.Contains(currentRoleId))
                return false;

            visited.Add(currentRoleId);

            if (_roleHierarchy.TryGetValue(currentRoleId, out var parentRoles))
            {
                foreach (var parentRoleId in parentRoles)
                {
                    if (await CheckCircularReferenceRecursiveAsync(parentRoleId, targetRoleId, visited))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        #endregion

        #region 用户角色管理

        private readonly ConcurrentDictionary<string, HashSet<string>> _userRoles = new(); // userId -> roleIds

        /// <summary>
        /// 为用户分配角色
        /// </summary>
        public async Task<RoleOperationResult> AssignRoleToUserAsync(string userId, string roleId)
        {
            if (string.IsNullOrWhiteSpace(userId))
                return RoleOperationResult.CreateFailure("用户ID不能为空");

            if (string.IsNullOrWhiteSpace(roleId))
                return RoleOperationResult.CreateFailure("角色ID不能为空");

            try
            {
                if (!_roles.ContainsKey(roleId))
                {
                    return RoleOperationResult.CreateFailure("角色不存在", "ROLE_NOT_FOUND");
                }

                var userRoles = _userRoles.GetOrAdd(userId, _ => new HashSet<string>());
                if (userRoles.Add(roleId))
                {
                    _logger?.LogInfo($"角色已分配给用户: {roleId} -> {userId}");
                    OnRoleAssigned(new RoleAssignedEventArgs(userId, roleId));
                    return RoleOperationResult.CreateSuccess();
                }
                else
                {
                    return RoleOperationResult.CreateFailure("用户已拥有该角色", "ROLE_EXISTS");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"分配角色失败: {roleId} -> {userId}", ex);
                return RoleOperationResult.CreateFailure($"分配角色失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 为用户分配多个角色
        /// </summary>
        public async Task<RoleOperationResult> AssignRolesToUserAsync(string userId, IEnumerable<string> roleIds)
        {
            if (string.IsNullOrWhiteSpace(userId))
                return RoleOperationResult.CreateFailure("用户ID不能为空");

            if (roleIds == null || !roleIds.Any())
                return RoleOperationResult.CreateFailure("角色ID列表不能为空");

            try
            {
                var userRoles = _userRoles.GetOrAdd(userId, _ => new HashSet<string>());
                var addedCount = 0;

                foreach (var roleId in roleIds)
                {
                    if (!string.IsNullOrWhiteSpace(roleId) &&
                        _roles.ContainsKey(roleId) &&
                        userRoles.Add(roleId))
                    {
                        addedCount++;
                        OnRoleAssigned(new RoleAssignedEventArgs(userId, roleId));
                    }
                }

                _logger?.LogInfo($"已为用户 {userId} 分配 {addedCount} 个角色");
                return RoleOperationResult.CreateSuccess();
            }
            catch (Exception ex)
            {
                _logger?.LogError($"批量分配角色失败: {userId}", ex);
                return RoleOperationResult.CreateFailure($"批量分配角色失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从用户移除角色
        /// </summary>
        public async Task<RoleOperationResult> RemoveRoleFromUserAsync(string userId, string roleId)
        {
            if (string.IsNullOrWhiteSpace(userId))
                return RoleOperationResult.CreateFailure("用户ID不能为空");

            if (string.IsNullOrWhiteSpace(roleId))
                return RoleOperationResult.CreateFailure("角色ID不能为空");

            try
            {
                if (_userRoles.TryGetValue(userId, out var userRoles))
                {
                    if (userRoles.Remove(roleId))
                    {
                        _logger?.LogInfo($"角色已从用户移除: {roleId} <- {userId}");
                        OnRoleRemoved(new RoleRemovedEventArgs(userId, roleId));
                        return RoleOperationResult.CreateSuccess();
                    }
                    else
                    {
                        return RoleOperationResult.CreateFailure("用户没有该角色", "ROLE_NOT_FOUND");
                    }
                }
                else
                {
                    return RoleOperationResult.CreateFailure("用户没有任何角色", "USER_NO_ROLES");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"移除角色失败: {roleId} <- {userId}", ex);
                return RoleOperationResult.CreateFailure($"移除角色失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 移除用户的所有角色
        /// </summary>
        public async Task<RoleOperationResult> RemoveAllRolesFromUserAsync(string userId)
        {
            if (string.IsNullOrWhiteSpace(userId))
                return RoleOperationResult.CreateFailure("用户ID不能为空");

            try
            {
                if (_userRoles.TryGetValue(userId, out var userRoles))
                {
                    var roleIds = userRoles.ToList();
                    userRoles.Clear();

                    foreach (var roleId in roleIds)
                    {
                        OnRoleRemoved(new RoleRemovedEventArgs(userId, roleId));
                    }

                    _logger?.LogInfo($"已移除用户 {userId} 的所有角色 ({roleIds.Count} 个)");
                    return RoleOperationResult.CreateSuccess();
                }
                else
                {
                    return RoleOperationResult.CreateFailure("用户没有任何角色", "USER_NO_ROLES");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"移除所有角色失败: {userId}", ex);
                return RoleOperationResult.CreateFailure($"移除所有角色失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取用户的角色
        /// </summary>
        public async Task<IEnumerable<Role>> GetUserRolesAsync(string userId)
        {
            if (string.IsNullOrWhiteSpace(userId))
                return Enumerable.Empty<Role>();

            if (_userRoles.TryGetValue(userId, out var roleIds))
            {
                var roles = new List<Role>();
                foreach (var roleId in roleIds)
                {
                    if (_roles.TryGetValue(roleId, out var role))
                    {
                        roles.Add(role);
                    }
                }
                return roles;
            }

            return Enumerable.Empty<Role>();
        }

        /// <summary>
        /// 获取角色的用户
        /// </summary>
        public async Task<IEnumerable<User>> GetRoleUsersAsync(string roleId)
        {
            if (string.IsNullOrWhiteSpace(roleId))
                return Enumerable.Empty<User>();

            var users = new List<User>();
            foreach (var kvp in _userRoles)
            {
                if (kvp.Value.Contains(roleId))
                {
                    // 注意：这里需要通过IUserService来获取User对象
                    // 简化实现，创建一个基本的User对象
                    var user = new User
                    {
                        Id = kvp.Key,
                        Username = kvp.Key, // 简化处理
                        IsEnabled = true
                    };
                    users.Add(user);
                }
            }

            return users;
        }

        /// <summary>
        /// 检查用户是否拥有指定角色
        /// </summary>
        public async Task<bool> UserHasRoleAsync(string userId, string roleId)
        {
            if (string.IsNullOrWhiteSpace(userId) || string.IsNullOrWhiteSpace(roleId))
                return false;

            if (_userRoles.TryGetValue(userId, out var userRoles))
            {
                return userRoles.Contains(roleId);
            }

            return false;
        }

        /// <summary>
        /// 检查用户是否拥有指定名称的角色
        /// </summary>
        public async Task<bool> UserHasRoleByNameAsync(string userId, string roleName)
        {
            if (string.IsNullOrWhiteSpace(userId) || string.IsNullOrWhiteSpace(roleName))
                return false;

            var role = await GetRoleByNameAsync(roleName);
            if (role == null)
                return false;

            return await UserHasRoleAsync(userId, role.Id);
        }

        #endregion

        #region 事件

        /// <summary>
        /// 角色创建事件
        /// </summary>
        public event EventHandler<RoleCreatedEventArgs>? RoleCreated;

        /// <summary>
        /// 角色更新事件
        /// </summary>
        public event EventHandler<RoleUpdatedEventArgs>? RoleUpdated;

        /// <summary>
        /// 角色删除事件
        /// </summary>
        public event EventHandler<RoleDeletedEventArgs>? RoleDeleted;

        /// <summary>
        /// 角色分配事件
        /// </summary>
        public event EventHandler<RoleAssignedEventArgs>? RoleAssigned;

        /// <summary>
        /// 角色移除事件
        /// </summary>
        public event EventHandler<RoleRemovedEventArgs>? RoleRemoved;

        /// <summary>
        /// 触发角色创建事件
        /// </summary>
        protected virtual void OnRoleCreated(RoleCreatedEventArgs e)
        {
            RoleCreated?.Invoke(this, e);
        }

        /// <summary>
        /// 触发角色更新事件
        /// </summary>
        protected virtual void OnRoleUpdated(RoleUpdatedEventArgs e)
        {
            RoleUpdated?.Invoke(this, e);
        }

        /// <summary>
        /// 触发角色删除事件
        /// </summary>
        protected virtual void OnRoleDeleted(RoleDeletedEventArgs e)
        {
            RoleDeleted?.Invoke(this, e);
        }

        /// <summary>
        /// 触发角色分配事件
        /// </summary>
        protected virtual void OnRoleAssigned(RoleAssignedEventArgs e)
        {
            RoleAssigned?.Invoke(this, e);
        }

        /// <summary>
        /// 触发角色移除事件
        /// </summary>
        protected virtual void OnRoleRemoved(RoleRemovedEventArgs e)
        {
            RoleRemoved?.Invoke(this, e);
        }

        #endregion
    }
}
