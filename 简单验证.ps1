# McLaser_V1 错误修复验证脚本
Write-Host "=== McLaser_V1 错误修复验证 ===" -ForegroundColor Cyan
Write-Host ""

# 1. 编译验证
Write-Host "1. 编译验证..." -ForegroundColor Yellow
Write-Host "  编译 McLaser.Core..." -ForegroundColor Gray
dotnet build McLaser.Core --verbosity quiet
if ($LASTEXITCODE -eq 0) {
    Write-Host "  ✅ McLaser.Core 编译成功" -ForegroundColor Green
} else {
    Write-Host "  ❌ McLaser.Core 编译失败" -ForegroundColor Red
}

Write-Host "  编译 McLaser.App..." -ForegroundColor Gray
dotnet build McLaser.App --verbosity quiet
if ($LASTEXITCODE -eq 0) {
    Write-Host "  ✅ McLaser.App 编译成功" -ForegroundColor Green
} else {
    Write-Host "  ❌ McLaser.App 编译失败" -ForegroundColor Red
}

Write-Host ""

# 2. 文件检查
Write-Host "2. 关键文件检查..." -ForegroundColor Yellow
$files = @(
    "McLaser.App\Core\AppCore.cs",
    "McLaser.App\Core\ConsoleLogger.cs",
    "错误修复总结.md"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "  ✅ $file" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $file" -ForegroundColor Red
    }
}

Write-Host ""

# 3. 关键修复点验证
Write-Host "3. 关键修复点验证..." -ForegroundColor Yellow

# 检查AppCore.cs
if (Test-Path "McLaser.App\Core\AppCore.cs") {
    $content = Get-Content "McLaser.App\Core\AppCore.cs" -Raw
    if ($content -match "RegisterFactory") {
        Write-Host "  ✅ 使用工厂模式注册服务" -ForegroundColor Green
    }
    if ($content -match "TryResolve") {
        Write-Host "  ✅ 使用安全解析方法" -ForegroundColor Green
    }
}

# 检查ConsoleLogger
if (Test-Path "McLaser.App\Core\ConsoleLogger.cs") {
    Write-Host "  ✅ ConsoleLogger后备实现已添加" -ForegroundColor Green
}

# 检查DefaultServiceRegistry
if (Test-Path "McLaser.Core\Framework\Bootstrapper\DefaultServiceRegistry.cs") {
    $content = Get-Content "McLaser.Core\Framework\Bootstrapper\DefaultServiceRegistry.cs" -Raw
    if ($content -match "_resolvingTypes") {
        Write-Host "  ✅ 循环依赖检测已添加" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "=== 验证完成 ===" -ForegroundColor Cyan
Write-Host "主要修复内容:" -ForegroundColor Yellow
Write-Host "• 优化服务注册顺序" -ForegroundColor White
Write-Host "• 添加循环依赖检测" -ForegroundColor White
Write-Host "• 实现后备日志器" -ForegroundColor White
Write-Host "• 使用安全解析方法" -ForegroundColor White
