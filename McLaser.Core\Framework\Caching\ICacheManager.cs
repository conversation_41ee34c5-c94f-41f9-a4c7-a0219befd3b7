using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace McLaser.Core.Framework.Caching
{
    /// <summary>
    /// 缓存管理器接口
    /// 提供统一的缓存操作接口
    /// </summary>
    public interface ICacheManager
    {
        /// <summary>
        /// 缓存项添加事件
        /// </summary>
        event EventHandler<CacheItemEventArgs>? ItemAdded;

        /// <summary>
        /// 缓存项更新事件
        /// </summary>
        event EventHandler<CacheItemEventArgs>? ItemUpdated;

        /// <summary>
        /// 缓存项移除事件
        /// </summary>
        event EventHandler<CacheItemEventArgs>? ItemRemoved;

        /// <summary>
        /// 缓存项过期事件
        /// </summary>
        event EventHandler<CacheItemEventArgs>? ItemExpired;

        /// <summary>
        /// 缓存统计信息
        /// </summary>
        CacheStatistics Statistics { get; }

        // 基本操作
        /// <summary>
        /// 获取缓存项
        /// </summary>
        /// <typeparam name="T">值类型</typeparam>
        /// <param name="key">缓存键</param>
        /// <returns>缓存值</returns>
        T? Get<T>(string key);

        /// <summary>
        /// 异步获取缓存项
        /// </summary>
        /// <typeparam name="T">值类型</typeparam>
        /// <param name="key">缓存键</param>
        /// <returns>缓存值</returns>
        Task<T?> GetAsync<T>(string key);

        /// <summary>
        /// 设置缓存项
        /// </summary>
        /// <typeparam name="T">值类型</typeparam>
        /// <param name="key">缓存键</param>
        /// <param name="value">缓存值</param>
        /// <param name="expiration">过期时间</param>
        void Set<T>(string key, T value, TimeSpan? expiration = null);

        /// <summary>
        /// 异步设置缓存项
        /// </summary>
        /// <typeparam name="T">值类型</typeparam>
        /// <param name="key">缓存键</param>
        /// <param name="value">缓存值</param>
        /// <param name="expiration">过期时间</param>
        /// <returns>任务</returns>
        Task SetAsync<T>(string key, T value, TimeSpan? expiration = null);

        /// <summary>
        /// 设置缓存项（带选项）
        /// </summary>
        /// <typeparam name="T">值类型</typeparam>
        /// <param name="key">缓存键</param>
        /// <param name="value">缓存值</param>
        /// <param name="options">缓存选项</param>
        void Set<T>(string key, T value, CacheItemOptions options);

        /// <summary>
        /// 异步设置缓存项（带选项）
        /// </summary>
        /// <typeparam name="T">值类型</typeparam>
        /// <param name="key">缓存键</param>
        /// <param name="value">缓存值</param>
        /// <param name="options">缓存选项</param>
        /// <returns>任务</returns>
        Task SetAsync<T>(string key, T value, CacheItemOptions options);

        /// <summary>
        /// 移除缓存项
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <returns>是否成功移除</returns>
        bool Remove(string key);

        /// <summary>
        /// 异步移除缓存项
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <returns>是否成功移除</returns>
        Task<bool> RemoveAsync(string key);

        /// <summary>
        /// 检查缓存项是否存在
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <returns>是否存在</returns>
        bool Exists(string key);

        /// <summary>
        /// 异步检查缓存项是否存在
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <returns>是否存在</returns>
        Task<bool> ExistsAsync(string key);

        // 高级操作
        /// <summary>
        /// 获取或设置缓存项
        /// </summary>
        /// <typeparam name="T">值类型</typeparam>
        /// <param name="key">缓存键</param>
        /// <param name="factory">值工厂函数</param>
        /// <param name="expiration">过期时间</param>
        /// <returns>缓存值</returns>
        T GetOrSet<T>(string key, Func<T> factory, TimeSpan? expiration = null);

        /// <summary>
        /// 异步获取或设置缓存项
        /// </summary>
        /// <typeparam name="T">值类型</typeparam>
        /// <param name="key">缓存键</param>
        /// <param name="factory">异步值工厂函数</param>
        /// <param name="expiration">过期时间</param>
        /// <returns>缓存值</returns>
        Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null);

        /// <summary>
        /// 获取或设置缓存项（带选项）
        /// </summary>
        /// <typeparam name="T">值类型</typeparam>
        /// <param name="key">缓存键</param>
        /// <param name="factory">值工厂函数</param>
        /// <param name="options">缓存选项</param>
        /// <returns>缓存值</returns>
        T GetOrSet<T>(string key, Func<T> factory, CacheItemOptions options);

        /// <summary>
        /// 异步获取或设置缓存项（带选项）
        /// </summary>
        /// <typeparam name="T">值类型</typeparam>
        /// <param name="key">缓存键</param>
        /// <param name="factory">异步值工厂函数</param>
        /// <param name="options">缓存选项</param>
        /// <returns>缓存值</returns>
        Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, CacheItemOptions options);

        /// <summary>
        /// 批量获取缓存项
        /// </summary>
        /// <typeparam name="T">值类型</typeparam>
        /// <param name="keys">缓存键列表</param>
        /// <returns>缓存项字典</returns>
        IDictionary<string, T?> GetMany<T>(IEnumerable<string> keys);

        /// <summary>
        /// 异步批量获取缓存项
        /// </summary>
        /// <typeparam name="T">值类型</typeparam>
        /// <param name="keys">缓存键列表</param>
        /// <returns>缓存项字典</returns>
        Task<IDictionary<string, T?>> GetManyAsync<T>(IEnumerable<string> keys);

        /// <summary>
        /// 批量设置缓存项
        /// </summary>
        /// <typeparam name="T">值类型</typeparam>
        /// <param name="items">缓存项字典</param>
        /// <param name="expiration">过期时间</param>
        void SetMany<T>(IDictionary<string, T> items, TimeSpan? expiration = null);

        /// <summary>
        /// 异步批量设置缓存项
        /// </summary>
        /// <typeparam name="T">值类型</typeparam>
        /// <param name="items">缓存项字典</param>
        /// <param name="expiration">过期时间</param>
        /// <returns>任务</returns>
        Task SetManyAsync<T>(IDictionary<string, T> items, TimeSpan? expiration = null);

        /// <summary>
        /// 批量移除缓存项
        /// </summary>
        /// <param name="keys">缓存键列表</param>
        /// <returns>成功移除的键列表</returns>
        IEnumerable<string> RemoveMany(IEnumerable<string> keys);

        /// <summary>
        /// 异步批量移除缓存项
        /// </summary>
        /// <param name="keys">缓存键列表</param>
        /// <returns>成功移除的键列表</returns>
        Task<IEnumerable<string>> RemoveManyAsync(IEnumerable<string> keys);

        // 模式操作
        /// <summary>
        /// 根据模式获取键列表
        /// </summary>
        /// <param name="pattern">键模式</param>
        /// <returns>匹配的键列表</returns>
        IEnumerable<string> GetKeys(string pattern = "*");

        /// <summary>
        /// 根据模式移除缓存项
        /// </summary>
        /// <param name="pattern">键模式</param>
        /// <returns>移除的项数量</returns>
        int RemoveByPattern(string pattern);

        /// <summary>
        /// 异步根据模式移除缓存项
        /// </summary>
        /// <param name="pattern">键模式</param>
        /// <returns>移除的项数量</returns>
        Task<int> RemoveByPatternAsync(string pattern);

        // 管理操作
        /// <summary>
        /// 清空所有缓存
        /// </summary>
        void Clear();

        /// <summary>
        /// 异步清空所有缓存
        /// </summary>
        /// <returns>任务</returns>
        Task ClearAsync();

        /// <summary>
        /// 刷新缓存
        /// </summary>
        void Flush();

        /// <summary>
        /// 异步刷新缓存
        /// </summary>
        /// <returns>任务</returns>
        Task FlushAsync();

        /// <summary>
        /// 清理过期项
        /// </summary>
        /// <returns>清理的项数量</returns>
        int CleanupExpired();

        /// <summary>
        /// 异步清理过期项
        /// </summary>
        /// <returns>清理的项数量</returns>
        Task<int> CleanupExpiredAsync();

        /// <summary>
        /// 获取缓存项信息
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <returns>缓存项信息</returns>
        CacheItemInfo? GetItemInfo(string key);

        /// <summary>
        /// 更新缓存项过期时间
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <param name="expiration">新的过期时间</param>
        /// <returns>是否成功更新</returns>
        bool UpdateExpiration(string key, TimeSpan expiration);
    }

    /// <summary>
    /// 内存缓存接口
    /// </summary>
    public interface IMemoryCache : ICacheManager
    {
        /// <summary>
        /// 最大缓存项数量
        /// </summary>
        int MaxItems { get; set; }

        /// <summary>
        /// 内存使用限制（字节）
        /// </summary>
        long MemoryLimit { get; set; }

        /// <summary>
        /// 当前内存使用量（字节）
        /// </summary>
        long MemoryUsage { get; }

        /// <summary>
        /// 缓存项数量
        /// </summary>
        int Count { get; }

        /// <summary>
        /// 压缩缓存
        /// </summary>
        /// <param name="percentage">压缩百分比</param>
        /// <returns>释放的项数量</returns>
        int Compact(double percentage = 0.1);
    }

    /// <summary>
    /// 缓存项选项
    /// </summary>
    public class CacheItemOptions
    {
        /// <summary>
        /// 绝对过期时间
        /// </summary>
        public DateTimeOffset? AbsoluteExpiration { get; set; }

        /// <summary>
        /// 相对过期时间
        /// </summary>
        public TimeSpan? SlidingExpiration { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        public CacheItemPriority Priority { get; set; } = CacheItemPriority.Normal;

        /// <summary>
        /// 大小
        /// </summary>
        public long? Size { get; set; }

        /// <summary>
        /// 移除回调
        /// </summary>
        public Action<string, object?, CacheItemRemovedReason>? RemovedCallback { get; set; }

        /// <summary>
        /// 标签
        /// </summary>
        public ISet<string> Tags { get; set; } = new HashSet<string>();

        /// <summary>
        /// 依赖项
        /// </summary>
        public IList<ICacheDependency> Dependencies { get; set; } = new List<ICacheDependency>();
    }

    /// <summary>
    /// 缓存项优先级
    /// </summary>
    public enum CacheItemPriority
    {
        /// <summary>
        /// 低优先级
        /// </summary>
        Low,

        /// <summary>
        /// 正常优先级
        /// </summary>
        Normal,

        /// <summary>
        /// 高优先级
        /// </summary>
        High,

        /// <summary>
        /// 永不移除
        /// </summary>
        NeverRemove
    }

    /// <summary>
    /// 缓存项移除原因
    /// </summary>
    public enum CacheItemRemovedReason
    {
        /// <summary>
        /// 手动移除
        /// </summary>
        Removed,

        /// <summary>
        /// 过期
        /// </summary>
        Expired,

        /// <summary>
        /// 容量不足
        /// </summary>
        Capacity,

        /// <summary>
        /// 依赖项变更
        /// </summary>
        DependencyChanged,

        /// <summary>
        /// 被替换
        /// </summary>
        Replaced
    }

    /// <summary>
    /// 缓存项事件参数
    /// </summary>
    public class CacheItemEventArgs : EventArgs
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <param name="value">缓存值</param>
        /// <param name="reason">原因</param>
        public CacheItemEventArgs(string key, object? value, CacheItemRemovedReason? reason = null)
        {
            Key = key;
            Value = value;
            Reason = reason;
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// 缓存键
        /// </summary>
        public string Key { get; }

        /// <summary>
        /// 缓存值
        /// </summary>
        public object? Value { get; }

        /// <summary>
        /// 移除原因
        /// </summary>
        public CacheItemRemovedReason? Reason { get; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; }
    }

    /// <summary>
    /// 缓存统计信息
    /// </summary>
    public class CacheStatistics
    {
        /// <summary>
        /// 命中次数
        /// </summary>
        public long HitCount { get; set; }

        /// <summary>
        /// 未命中次数
        /// </summary>
        public long MissCount { get; set; }

        /// <summary>
        /// 总请求次数
        /// </summary>
        public long TotalRequests => HitCount + MissCount;

        /// <summary>
        /// 命中率
        /// </summary>
        public double HitRatio => TotalRequests > 0 ? (double)HitCount / TotalRequests : 0;

        /// <summary>
        /// 添加次数
        /// </summary>
        public long AddCount { get; set; }

        /// <summary>
        /// 更新次数
        /// </summary>
        public long UpdateCount { get; set; }

        /// <summary>
        /// 移除次数
        /// </summary>
        public long RemoveCount { get; set; }

        /// <summary>
        /// 过期次数
        /// </summary>
        public long ExpiredCount { get; set; }

        /// <summary>
        /// 当前项数量
        /// </summary>
        public int CurrentItemCount { get; set; }

        /// <summary>
        /// 内存使用量（字节）
        /// </summary>
        public long MemoryUsage { get; set; }

        /// <summary>
        /// 重置统计信息
        /// </summary>
        public void Reset()
        {
            HitCount = 0;
            MissCount = 0;
            AddCount = 0;
            UpdateCount = 0;
            RemoveCount = 0;
            ExpiredCount = 0;
        }
    }

    /// <summary>
    /// 缓存项信息
    /// </summary>
    public class CacheItemInfo
    {
        /// <summary>
        /// 缓存键
        /// </summary>
        public string Key { get; set; } = string.Empty;

        /// <summary>
        /// 值类型
        /// </summary>
        public Type? ValueType { get; set; }

        /// <summary>
        /// 大小（字节）
        /// </summary>
        public long Size { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 最后访问时间
        /// </summary>
        public DateTime LastAccessTime { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? ExpirationTime { get; set; }

        /// <summary>
        /// 访问次数
        /// </summary>
        public long AccessCount { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        public CacheItemPriority Priority { get; set; }

        /// <summary>
        /// 标签
        /// </summary>
        public ISet<string> Tags { get; set; } = new HashSet<string>();

        /// <summary>
        /// 是否已过期
        /// </summary>
        public bool IsExpired => ExpirationTime.HasValue && ExpirationTime.Value <= DateTime.Now;
    }

    /// <summary>
    /// 缓存依赖项接口
    /// </summary>
    public interface ICacheDependency
    {
        /// <summary>
        /// 依赖项变更事件
        /// </summary>
        event EventHandler? Changed;

        /// <summary>
        /// 是否已变更
        /// </summary>
        bool HasChanged { get; }

        /// <summary>
        /// 开始监控
        /// </summary>
        void StartMonitoring();

        /// <summary>
        /// 停止监控
        /// </summary>
        void StopMonitoring();
    }
}
