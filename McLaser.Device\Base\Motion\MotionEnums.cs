using System;
using System.ComponentModel;

namespace McLaser.Devices
{
    /// <summary>
    /// 控制卡类型枚举
    /// </summary>
    [Serializable]
    public enum CardType
    {
        /// <summary>
        /// 虚拟卡
        /// </summary>
        Virtual,
        
        /// <summary>
        /// PMAC卡
        /// </summary>
        PMAC,
        
        /// <summary>
        /// 固高GSN卡
        /// </summary>
        GSN,
        
        /// <summary>
        /// 固高GTS卡
        /// </summary>
        GTS,
        
        /// <summary>
        /// A3200卡
        /// </summary>
        A3200,
        
        /// <summary>
        /// Automation1卡
        /// </summary>
        Automation1,
    }

    /// <summary>
    /// 速度类型枚举
    /// </summary>
    [Serializable]
    public enum VelType
    {
        /// <summary>
        /// 毫秒
        /// </summary>
        Millisecond,
        
        /// <summary>
        /// 秒
        /// </summary>
        Second,
    }

    /// <summary>
    /// 步进类型枚举
    /// </summary>
    [Serializable]
    public enum StepType
    {
        /// <summary>
        /// Jog运动
        /// </summary>
        Jog,
        
        /// <summary>
        /// 相对运动
        /// </summary>
        Step,
    }

    /// <summary>
    /// 运动方向枚举
    /// </summary>
    [Serializable]
    public enum Direction
    {
        /// <summary>
        /// 正向
        /// </summary>
        Positive = 1,
        
        /// <summary>
        /// 负向
        /// </summary>
        Negative = -1
    }

    /// <summary>
    /// IO值枚举
    /// </summary>
    [Serializable]
    public enum IOValue
    {
        /// <summary>
        /// 无值
        /// </summary>
        NotValue = -1,
        
        /// <summary>
        /// 假
        /// </summary>
        False,
        
        /// <summary>
        /// 真
        /// </summary>
        True
    }

    /// <summary>
    /// 回零模式枚举
    /// </summary>
    [Serializable]
    public enum HomeMode
    {
        /// <summary>
        /// 负极限+原点
        /// </summary>
        负极限_原点,
        
        /// <summary>
        /// 正极限+原点
        /// </summary>
        正极限_原点,
        
        /// <summary>
        /// 原点
        /// </summary>
        原点,
        
        /// <summary>
        /// 负极限+Index
        /// </summary>
        负极限_Index,
        
        /// <summary>
        /// 正极限+Index
        /// </summary>
        正极限_Index,
        
        /// <summary>
        /// 零位置预设
        /// </summary>
        零位置预设,
        
        /// <summary>
        /// 负极限
        /// </summary>
        负极限,
        
        /// <summary>
        /// 正极限
        /// </summary>
        正极限
    }
}
