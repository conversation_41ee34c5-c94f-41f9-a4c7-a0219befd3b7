using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Windows.Controls;

namespace McLaser.Device
{
    /// <summary>
    /// 设备接口
    /// 定义所有设备必须实现的基本功能
    /// </summary>
    [InheritedExport]
    public interface IDevice
    {
        /// <summary>
        /// 设备唯一标识符
        /// </summary>
        string Id { get; }

        /// <summary>
        /// 设备名称
        /// </summary>
        string Name { get; set; }

        /// <summary>
        /// 设备类别
        /// </summary>
        DeviceCategory Category { get; }

        /// <summary>
        /// 设备类型
        /// </summary>
        DevicesType DeviceType { get; }

        /// <summary>
        /// 设备状态
        /// </summary>
        StatusDevice Status { get; }

        /// <summary>
        /// 是否启用
        /// </summary>
        bool IsEnabled { get; set; }

        /// <summary>
        /// 是否已连接
        /// </summary>
        bool IsConnected { get; set; }

        /// <summary>
        /// 设备配置参数
        /// </summary>
        Dictionary<string, object> Configuration { get; }

        /// <summary>
        /// 打开设备
        /// </summary>
        /// <returns>操作是否成功</returns>
        bool Open();

        /// <summary>
        /// 检查设备是否已打开
        /// </summary>
        /// <returns>设备是否已打开</returns>
        bool IsOpen();

        /// <summary>
        /// 关闭设备
        /// </summary>
        /// <returns>操作是否成功</returns>
        bool Close();

        /// <summary>
        /// 初始化设备
        /// </summary>
        void Initialize();

        /// <summary>
        /// 关闭设备并释放资源
        /// </summary>
        void Shutdown();

        /// <summary>
        /// 应用配置参数
        /// </summary>
        /// <param name="config">配置参数</param>
        void ApplyConfiguration(Dictionary<string, object> config);

        /// <summary>
        /// 获取设备配置界面
        /// </summary>
        /// <returns>配置界面控件</returns>
        UserControl GetConfigurationView();

        /// <summary>
        /// 设备状态变更事件
        /// </summary>
        event EventHandler DeviceStatusChanged;
    }
}
