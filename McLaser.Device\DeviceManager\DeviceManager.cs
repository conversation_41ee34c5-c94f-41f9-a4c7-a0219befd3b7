using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel.Composition;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using McLaser.Core.Common;
using Newtonsoft.Json;

namespace McLaser.Device
{
    /// <summary>
    /// 统一设备管理器
    /// 整合了MVVM模式和MEF导出功能，提供完整的设备管理服务
    /// </summary>
    [Export(typeof(IDeviceManager))]
    public class DeviceManager : ObservableObject, IDeviceManager
    {
        #region 私有字段

        private readonly Dictionary<string, IDevice> _devices = new Dictionary<string, IDevice>();
        private const string ConfigFileName = "DeviceConfig.json";
        private IDevice _selectedDevice;
        private FrameworkElement _currentConfigurationUI;
        private bool _isInitialized = false;
        private bool _isMonitoring = false;
        private string _statusMessage = "设备管理器未初始化";

        #endregion

        #region 属性

        /// <summary>
        /// 设备分类组集合
        /// </summary>
        public ObservableCollection<DeviceCategoryGroup> DeviceGroups { get; private set; } = new ObservableCollection<DeviceCategoryGroup>();

        /// <summary>
        /// 所有设备列表
        /// </summary>
        public ObservableCollection<DeviceBase> Devices { get; } = new ObservableCollection<DeviceBase>();

        /// <summary>
        /// 相机设备列表
        /// </summary>
        public ObservableCollection<DeviceBase> Cameras { get; } = new ObservableCollection<DeviceBase>();

        /// <summary>
        /// 运动控制卡列表
        /// </summary>
        public ObservableCollection<DeviceBase> MotionCards { get; } = new ObservableCollection<DeviceBase>();

        /// <summary>
        /// 激光器设备列表
        /// </summary>
        public ObservableCollection<DeviceBase> Lasers { get; } = new ObservableCollection<DeviceBase>();

        /// <summary>
        /// 传感器设备列表
        /// </summary>
        public ObservableCollection<DeviceBase> Sensors { get; } = new ObservableCollection<DeviceBase>();

        /// <summary>
        /// 当前选中的设备
        /// </summary>
        public IDevice SelectedDevice
        {
            get { return _selectedDevice; }
            set
            {
                Set(ref _selectedDevice, value);
                UpdateConfigurationUI();

            }
        }

        /// <summary>
        /// 当前配置界面
        /// </summary>
        public FrameworkElement CurrentConfigurationUI
        {
            get { return _currentConfigurationUI; }
            set
            {
                Set(ref _currentConfigurationUI, value);
                ConfigurationUIChanged?.Invoke(this, EventArgs.Empty);

            }
        }

        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized
        {
            get { return _isInitialized; }
            set { Set(ref _isInitialized, value); }
        }

        /// <summary>
        /// 是否正在监控
        /// </summary>
        public bool IsMonitoring
        {
            get { return _isMonitoring; }
            set { Set(ref _isMonitoring, value); }
        }

        /// <summary>
        /// 状态消息
        /// </summary>
        public string StatusMessage
        {
            get { return _statusMessage; }
            set { Set(ref _statusMessage, value); }
        }

        /// <summary>
        /// 已连接设备数量
        /// </summary>
        public int ConnectedDeviceCount => Devices.Count(d => d.Status.IsConnected);

        /// <summary>
        /// 设备总数
        /// </summary>
        public int TotalDeviceCount => Devices.Count;

        /// <summary>
        /// 配置界面变更事件
        /// </summary>
        public event EventHandler ConfigurationUIChanged;

        #endregion

        #region 事件

        /// <summary>
        /// 设备添加事件
        /// </summary>
        public event EventHandler<DeviceEventArgs> DeviceAdded;

        /// <summary>
        /// 设备移除事件
        /// </summary>
        public event EventHandler<DeviceEventArgs> DeviceRemoved;

        /// <summary>
        /// 设备状态变更事件
        /// </summary>
        public event EventHandler<DeviceStatusChangedEventArgs> DeviceStatusChanged;

        /// <summary>
        /// 设备管理器状态变更事件
        /// </summary>
        public event EventHandler<ManagerStatusChangedEventArgs> ManagerStatusChanged;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public DeviceManager()
        {
            StatusMessage = "设备管理器已创建";
            InitializeDeviceGroups();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化设备分类组
        /// </summary>
        private void InitializeDeviceGroups()
        {
            DeviceGroups.Clear();
            DeviceGroups.Add(new DeviceCategoryGroup(DeviceCategory.Camera));
            DeviceGroups.Add(new DeviceCategoryGroup(DeviceCategory.MotionController));
            DeviceGroups.Add(new DeviceCategoryGroup(DeviceCategory.Laser));
            DeviceGroups.Add(new DeviceCategoryGroup(DeviceCategory.Sensor));
            DeviceGroups.Add(new DeviceCategoryGroup(DeviceCategory.NetworkDevice));
            DeviceGroups.Add(new DeviceCategoryGroup(DeviceCategory.SerialDevice));
        }

        /// <summary>
        /// 初始化设备管理器
        /// </summary>
        /// <returns>初始化是否成功</returns>
        public bool Initialize()
        {
            try
            {
                if (IsInitialized)
                {
                    StatusMessage = "设备管理器已初始化";
                    return true;
                }

                // 初始化设备工厂
                DeviceFactory.Initialize();

                // 加载设备配置
                LoadDeviceConfiguration();

                // 初始化各种设备类型的默认配置
                InitializeDefaultDevices();

                IsInitialized = true;
                StatusMessage = "设备管理器初始化成功";

                // 触发状态变更事件
                OnManagerStatusChanged();

                return true;
            }
            catch (Exception ex)
            {
                StatusMessage = $"设备管理器初始化异常：{ex.Message}";
                System.Diagnostics.Debug.WriteLine($"设备管理器初始化失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 关闭设备管理器
        /// </summary>
        public void Shutdown()
        {
            try
            {
                // 停止监控
                StopMonitoring();

                // 保存设备配置
                SaveDeviceConfiguration();

                // 关闭所有设备
                CloseAllDevices();

                // 清空设备列表
                _devices.Clear();
                foreach (var group in DeviceGroups)
                {
                    group.ClearDevices();
                }

                ClearAllDevices();
                IsInitialized = false;
                StatusMessage = "设备管理器已关闭";

                // 触发状态变更事件
                OnManagerStatusChanged();
            }
            catch (Exception ex)
            {
                StatusMessage = $"设备管理器关闭异常：{ex.Message}";
                System.Diagnostics.Debug.WriteLine($"设备管理器关闭失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化默认设备
        /// </summary>
        private void InitializeDefaultDevices()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始初始化默认设备...");

                // 添加一些测试设备用于演示
                AddTestDevices();

                // 这里可以添加设备搜索逻辑
                // SearchAndAddAvailableDevices();

                System.Diagnostics.Debug.WriteLine($"默认设备初始化完成，共添加 {_devices.Count} 个设备");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化默认设备失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加测试设备
        /// </summary>
        private void AddTestDevices()
        {
            try
            {
                // 添加相机设备
                var hikCamera = DeviceFactory.CreateTestDevice(DevicesType.Camera, "海康威视相机");
                if (hikCamera != null)
                {
                    AddDevice(hikCamera);
                    System.Diagnostics.Debug.WriteLine("添加海康威视相机设备");
                }

                var baslerCamera = DeviceFactory.CreateTestDevice(DevicesType.Camera, "巴斯勒相机");
                if (baslerCamera != null)
                {
                    AddDevice(baslerCamera);
                    System.Diagnostics.Debug.WriteLine("添加巴斯勒相机设备");
                }

                var dahengCamera = DeviceFactory.CreateTestDevice(DevicesType.Camera, "大恒图像相机");
                if (dahengCamera != null)
                {
                    AddDevice(dahengCamera);
                    System.Diagnostics.Debug.WriteLine("添加大恒图像相机设备");
                }

                // 添加运动控制卡设备
                var pmacCard = DeviceFactory.CreateTestDevice(DevicesType.MotionCard, "PMAC运动控制卡");
                if (pmacCard != null)
                {
                    AddDevice(pmacCard);
                    System.Diagnostics.Debug.WriteLine("添加PMAC运动控制卡设备");
                }

                var gtsCard = DeviceFactory.CreateTestDevice(DevicesType.MotionCard, "固高GTS运动控制卡");
                if (gtsCard != null)
                {
                    AddDevice(gtsCard);
                    System.Diagnostics.Debug.WriteLine("添加固高GTS运动控制卡设备");
                }

                // 添加激光器设备
                var ipgLaser = DeviceFactory.CreateTestDevice(DevicesType.Laser, "IPG激光器");
                if (ipgLaser != null)
                {
                    AddDevice(ipgLaser);
                    System.Diagnostics.Debug.WriteLine("添加IPG激光器设备");
                }

                var coherentLaser = DeviceFactory.CreateTestDevice(DevicesType.Laser, "Coherent激光器");
                if (coherentLaser != null)
                {
                    AddDevice(coherentLaser);
                    System.Diagnostics.Debug.WriteLine("添加Coherent激光器设备");
                }

                // 添加传感器设备
                var tempSensor = DeviceFactory.CreateTestDevice(DevicesType.Sensor, "温度传感器");
                if (tempSensor != null)
                {
                    AddDevice(tempSensor);
                    System.Diagnostics.Debug.WriteLine("添加温度传感器设备");
                }

                var dispSensor = DeviceFactory.CreateTestDevice(DevicesType.Sensor, "位移传感器");
                if (dispSensor != null)
                {
                    AddDevice(dispSensor);
                    System.Diagnostics.Debug.WriteLine("添加位移传感器设备");
                }

                // 添加网络设备
                var networkDevice = DeviceFactory.CreateTestDevice(DevicesType.NetworkDevice, "网络设备");
                if (networkDevice != null)
                {
                    AddDevice(networkDevice);
                    System.Diagnostics.Debug.WriteLine("添加网络设备");
                }

                // 添加串口设备
                var serialDevice = DeviceFactory.CreateTestDevice(DevicesType.SerialDevice, "串口设备");
                if (serialDevice != null)
                {
                    AddDevice(serialDevice);
                    System.Diagnostics.Debug.WriteLine("添加串口设备");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"添加测试设备失败: {ex.Message}");
            }
        }

        #endregion

        #region 设备查询方法

        /// <summary>
        /// 根据ID获取设备
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>找到的设备，如果没找到返回null</returns>
        public IDevice GetDeviceById(string deviceId)
        {
            if (string.IsNullOrEmpty(deviceId)) return null;
            return _devices.ContainsKey(deviceId) ? _devices[deviceId] : null;
        }

        /// <summary>
        /// 根据名称获取设备
        /// </summary>
        /// <param name="deviceName">设备名称</param>
        /// <returns>找到的设备，如果没找到返回null</returns>
        public IDevice GetDeviceByName(string deviceName)
        {
            if (string.IsNullOrEmpty(deviceName)) return null;
            return _devices.Values.FirstOrDefault(d => d.Name == deviceName);
        }

        /// <summary>
        /// 获取指定类型的设备列表
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <returns>指定类型的设备列表</returns>
        public List<IDevice> GetDevicesByType(DevicesType deviceType)
        {
            return _devices.Values.Where(d => d.DeviceType == deviceType).ToList();
        }

        /// <summary>
        /// 检查设备名称是否唯一
        /// </summary>
        /// <param name="name">设备名称</param>
        /// <param name="excludeDeviceId">排除的设备ID（用于重命名时排除自身）</param>
        /// <returns>名称是否唯一</returns>
        public bool IsDeviceNameUnique(string name, string excludeDeviceId = null)
        {
            if (string.IsNullOrEmpty(name)) return false;

            return !_devices.Values.Any(d => d.Name == name && d.Id != excludeDeviceId);
        }

        /// <summary>
        /// 获取唯一的设备名称
        /// </summary>
        /// <param name="baseName">基础名称</param>
        /// <returns>唯一的设备名称</returns>
        public string GetUniqueDeviceName(string baseName)
        {
            if (string.IsNullOrEmpty(baseName)) baseName = "设备";

            if (IsDeviceNameUnique(baseName)) return baseName;

            int counter = 1;
            string uniqueName;
            do
            {
                uniqueName = $"{baseName}_{counter}";
                counter++;
            } while (!IsDeviceNameUnique(uniqueName));

            return uniqueName;
        }

        #endregion

        #region 设备管理方法

        /// <summary>
        /// 添加设备
        /// </summary>
        /// <param name="device">要添加的设备</param>
        public void AddDevice(IDevice device)
        {
            if (device == null) throw new ArgumentNullException(nameof(device));

            // 确保名称唯一
            if (string.IsNullOrEmpty(device.Name))
            {
                device.Name = GetDefaultDeviceName(device.Category);
            }

            device.Name = GetUniqueDeviceName(device.Name);

            // 添加到设备字典
            _devices[device.Id] = device;

            // 不再将设备添加到对应的分类组
            // var group = DeviceGroups.FirstOrDefault(g => g.Category == device.Category);
            // group?.AddDevice(device);

            // 如果是DeviceBase类型，也添加到对应的类型列表
            if (device is DeviceBase deviceBase)
            {
                AddDeviceToTypedCollections(deviceBase);

                // 订阅设备状态变化事件
                deviceBase.PropertyChanged += Device_PropertyChanged;
            }

            // 设置为当前选中设备
            SelectedDevice = device;
            StatusMessage = $"设备 {device.Name} 添加成功";
            OnPropertyChanged(nameof(ConnectedDeviceCount));
            OnPropertyChanged(nameof(TotalDeviceCount));

            // 触发设备添加事件
            DeviceAdded?.Invoke(this, new DeviceEventArgs(device));
        }

        /// <summary>
        /// 移除设备
        /// </summary>
        /// <param name="deviceId">要移除的设备ID</param>
        public void RemoveDevice(string deviceId)
        {
            if (string.IsNullOrEmpty(deviceId) || !_devices.ContainsKey(deviceId)) return;

            var device = _devices[deviceId];

            // 关闭设备
            try
            {
                if (device.IsOpen())
                {
                    device.Close();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"关闭设备失败: {ex.Message}");
            }

            // 不再从分类组中移除
            // var group = DeviceGroups.FirstOrDefault(g => g.Category == device.Category);
            // group?.RemoveDevice(device);

            // 如果是DeviceBase类型，从类型化集合中移除
            if (device is DeviceBase deviceBase)
            {
                RemoveDeviceFromTypedCollections(deviceBase);

                // 取消订阅事件
                deviceBase.PropertyChanged -= Device_PropertyChanged;
            }

            // 从设备字典中移除
            _devices.Remove(deviceId);

            // 如果删除的是当前选中的设备，清空选择
            if (SelectedDevice?.Id == deviceId)
            {
                SelectedDevice = null;
            }

            StatusMessage = $"设备 {device.Name} 移除成功";
            OnPropertyChanged(nameof(ConnectedDeviceCount));
            OnPropertyChanged(nameof(TotalDeviceCount));

            // 触发设备移除事件
            DeviceRemoved?.Invoke(this, new DeviceEventArgs(device));
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 获取默认设备名称
        /// </summary>
        /// <param name="category">设备类别</param>
        /// <returns>默认设备名称</returns>
        private string GetDefaultDeviceName(DeviceCategory category)
        {
            return category switch
            {
                DeviceCategory.Camera => "相机设备",
                DeviceCategory.MotionController => "运动控制卡",
                DeviceCategory.Laser => "激光器",
                DeviceCategory.Sensor => "传感器",
                DeviceCategory.NetworkDevice => "网络设备",
                DeviceCategory.SerialDevice => "串口设备",
                _ => "未知设备"
            };
        }

        /// <summary>
        /// 添加设备到类型化集合
        /// </summary>
        /// <param name="device">设备对象</param>
        private void AddDeviceToTypedCollections(DeviceBase device)
        {
            // 添加到总列表
            Devices.Add(device);

            // 根据设备类型添加到对应列表
            switch (device.DeviceType)
            {
                case DevicesType.Camera:
                    Cameras.Add(device);
                    break;
                case DevicesType.MotionCard:
                    MotionCards.Add(device);
                    break;
                case DevicesType.Laser:
                    Lasers.Add(device);
                    break;
                case DevicesType.Sensor:
                    Sensors.Add(device);
                    break;
            }
        }

        /// <summary>
        /// 从类型化集合中移除设备
        /// </summary>
        /// <param name="device">设备对象</param>
        private void RemoveDeviceFromTypedCollections(DeviceBase device)
        {
            // 从总列表移除
            Devices.Remove(device);

            // 从对应类型列表移除
            switch (device.DeviceType)
            {
                case DevicesType.Camera:
                    Cameras.Remove(device);
                    break;
                case DevicesType.MotionCard:
                    MotionCards.Remove(device);
                    break;
                case DevicesType.Laser:
                    Lasers.Remove(device);
                    break;
                case DevicesType.Sensor:
                    Sensors.Remove(device);
                    break;
            }
        }

        /// <summary>
        /// 更新配置界面
        /// </summary>
        private void UpdateConfigurationUI()
        {
            if (SelectedDevice != null)
            {
                CurrentConfigurationUI = SelectedDevice.GetConfigurationView();
            }
            else
            {
                CurrentConfigurationUI = null;
            }
        }

        /// <summary>
        /// 设备属性变更事件处理
        /// </summary>
        /// <param name="sender">发送者</param>
        /// <param name="e">事件参数</param>
        private void Device_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (sender is IDevice device)
            {
                // 如果是连接状态变更，更新连接数量
                if (e.PropertyName == nameof(DeviceBase.Status) || e.PropertyName == nameof(IDevice.IsConnected))
                {
                    OnPropertyChanged(nameof(ConnectedDeviceCount));
                }

                // 触发设备状态变更事件
                DeviceStatusChanged?.Invoke(this, new DeviceStatusChangedEventArgs(device, "", ""));
            }
        }

        /// <summary>
        /// 触发管理器状态变更事件
        /// </summary>
        private void OnManagerStatusChanged()
        {
            ManagerStatusChanged?.Invoke(this, new ManagerStatusChangedEventArgs(StatusMessage, IsInitialized, IsMonitoring));
        }

        /// <summary>
        /// 搜索可用设备
        /// </summary>
        /// <returns>搜索到的设备数量</returns>
        public int SearchAvailableDevices()
        {
            try
            {
                StatusMessage = "正在搜索可用设备...";
                int foundDevices = 0;

                // 搜索相机设备
                foundDevices += SearchCameraDevices();

                // 搜索运动控制卡设备
                foundDevices += SearchMotionDevices();

                // 搜索激光器设备
                foundDevices += SearchLaserDevices();

                // 搜索传感器设备
                foundDevices += SearchSensorDevices();

                StatusMessage = $"设备搜索完成，找到 {foundDevices} 个可用设备";
                return foundDevices;
            }
            catch (Exception ex)
            {
                StatusMessage = $"搜索设备异常：{ex.Message}";
                System.Diagnostics.Debug.WriteLine($"搜索设备失败: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 搜索相机设备
        /// </summary>
        /// <returns>找到的相机数量</returns>
        private int SearchCameraDevices()
        {
            int count = 0;
            try
            {
                // 这里可以调用具体的相机搜索逻辑
                // 例如：海康威视、巴斯勒、大恒图像相机的搜索
                System.Diagnostics.Debug.WriteLine("搜索相机设备...");

                // 暂时添加一个模拟的相机设备
                var simulatedCamera = DeviceFactory.CreateTestDevice(DevicesType.Camera, "模拟相机");
                if (simulatedCamera != null && !_devices.ContainsKey(simulatedCamera.Id))
                {
                    AddDevice(simulatedCamera);
                    count++;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"搜索相机设备失败: {ex.Message}");
            }
            return count;
        }

        /// <summary>
        /// 搜索运动控制卡设备
        /// </summary>
        /// <returns>找到的运动控制卡数量</returns>
        private int SearchMotionDevices()
        {
            int count = 0;
            try
            {
                System.Diagnostics.Debug.WriteLine("搜索运动控制卡设备...");

                // 暂时添加一个模拟的运动控制卡设备
                var simulatedMotionCard = DeviceFactory.CreateTestDevice(DevicesType.MotionCard, "模拟运动控制卡");
                if (simulatedMotionCard != null && !_devices.ContainsKey(simulatedMotionCard.Id))
                {
                    AddDevice(simulatedMotionCard);
                    count++;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"搜索运动控制卡设备失败: {ex.Message}");
            }
            return count;
        }

        /// <summary>
        /// 搜索激光器设备
        /// </summary>
        /// <returns>找到的激光器数量</returns>
        private int SearchLaserDevices()
        {
            int count = 0;
            try
            {
                System.Diagnostics.Debug.WriteLine("搜索激光器设备...");

                // 暂时添加一个模拟的激光器设备
                var simulatedLaser = DeviceFactory.CreateTestDevice(DevicesType.Laser, "模拟激光器");
                if (simulatedLaser != null && !_devices.ContainsKey(simulatedLaser.Id))
                {
                    AddDevice(simulatedLaser);
                    count++;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"搜索激光器设备失败: {ex.Message}");
            }
            return count;
        }

        /// <summary>
        /// 搜索传感器设备
        /// </summary>
        /// <returns>找到的传感器数量</returns>
        private int SearchSensorDevices()
        {
            int count = 0;
            try
            {
                System.Diagnostics.Debug.WriteLine("搜索传感器设备...");

                // 暂时添加一个模拟的传感器设备
                var simulatedSensor = DeviceFactory.CreateTestDevice(DevicesType.Sensor, "模拟传感器");
                if (simulatedSensor != null && !_devices.ContainsKey(simulatedSensor.Id))
                {
                    AddDevice(simulatedSensor);
                    count++;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"搜索传感器设备失败: {ex.Message}");
            }
            return count;
        }

        #endregion

        #region 设备操作方法

        /// <summary>
        /// 连接所有设备
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> ConnectAllDevicesAsync()
        {
            try
            {
                StatusMessage = "正在连接所有设备...";
                int successCount = 0;
                int totalCount = Devices.Count;

                foreach (var device in Devices)
                {
                    if (device.IsEnabled && !device.Status.IsConnected)
                    {
                        if (await Task.Run(() => device.Open()))
                        {
                            successCount++;
                        }
                    }
                }

                StatusMessage = $"设备连接完成：{successCount}/{totalCount} 个设备连接成功";
                OnPropertyChanged(nameof(ConnectedDeviceCount));

                return successCount == totalCount;
            }
            catch (Exception ex)
            {
                StatusMessage = $"连接所有设备异常：{ex.Message}";
                return false;
            }
        }

        /// <summary>
        /// 断开所有设备
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> DisconnectAllDevicesAsync()
        {
            try
            {
                StatusMessage = "正在断开所有设备...";
                int successCount = 0;
                int totalCount = Devices.Count;

                foreach (var device in Devices)
                {
                    if (device.Status.IsConnected)
                    {
                        if (await Task.Run(() => device.Close()))
                        {
                            successCount++;
                        }
                    }
                }

                StatusMessage = $"设备断开完成：{successCount}/{totalCount} 个设备断开成功";
                OnPropertyChanged(nameof(ConnectedDeviceCount));

                return successCount == totalCount;
            }
            catch (Exception ex)
            {
                StatusMessage = $"断开所有设备异常：{ex.Message}";
                return false;
            }
        }

        /// <summary>
        /// 开始监控所有设备
        /// </summary>
        /// <returns>是否成功</returns>
        public bool StartMonitoring()
        {
            try
            {
                if (IsMonitoring)
                {
                    StatusMessage = "设备监控已在运行";
                    return true;
                }

                IsMonitoring = true;
                StatusMessage = "设备监控已启动";

                // 启动监控循环
                Task.Run(MonitoringLoop);

                OnManagerStatusChanged();
                return true;
            }
            catch (Exception ex)
            {
                StatusMessage = $"启动设备监控异常：{ex.Message}";
                return false;
            }
        }

        /// <summary>
        /// 停止监控所有设备
        /// </summary>
        /// <returns>是否成功</returns>
        public bool StopMonitoring()
        {
            try
            {
                IsMonitoring = false;
                StatusMessage = "设备监控已停止";

                OnManagerStatusChanged();
                return true;
            }
            catch (Exception ex)
            {
                StatusMessage = $"停止设备监控异常：{ex.Message}";
                return false;
            }
        }

        /// <summary>
        /// 监控循环
        /// </summary>
        private async Task MonitoringLoop()
        {
            while (IsMonitoring)
            {
                try
                {
                    // 检查设备连接状态
                    foreach (var device in Devices)
                    {
                        if (device.IsEnabled && device.Status.IsConnected)
                        {
                            // 这里可以添加设备状态检查逻辑
                        }
                    }

                    await Task.Delay(1000); // 每秒检查一次
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"设备监控循环异常：{ex.Message}");
                }
            }
        }

        #endregion

        #region 配置管理方法

        /// <summary>
        /// 保存设备配置
        /// </summary>
        /// <returns>是否成功</returns>
        public bool SaveDeviceConfiguration()
        {
            return SaveDeviceConfiguration(ConfigFileName);
        }

        /// <summary>
        /// 保存设备配置到指定文件
        /// </summary>
        /// <param name="fileName">配置文件路径</param>
        /// <returns>是否成功</returns>
        public bool SaveDeviceConfiguration(string fileName)
        {
            try
            {
                if (string.IsNullOrEmpty(fileName))
                {
                    fileName = ConfigFileName;
                }

               _= JsonHelper.SerialObject(fileName,Devices);
               // File.WriteAllText(fileName, json);

                StatusMessage = $"设备配置保存成功：{fileName}";
                return true;
            }
            catch (Exception ex)
            {
                StatusMessage = $"保存设备配置异常：{ex.Message}";
                System.Diagnostics.Debug.WriteLine($"保存设备配置失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 加载设备配置
        /// </summary>
        /// <returns>是否成功</returns>
        public bool LoadDeviceConfiguration()
        {
            return LoadDeviceConfiguration(ConfigFileName);
        }

        /// <summary>
        /// 从指定文件加载设备配置
        /// </summary>
        /// <param name="fileName">配置文件路径</param>
        /// <returns>是否成功</returns>
        public bool LoadDeviceConfiguration(string fileName)
        {
            try
            {
                if (string.IsNullOrEmpty(fileName))
                {
                    fileName = ConfigFileName;
                }

                if (!File.Exists(fileName))
                {
                    StatusMessage = $"配置文件不存在：{fileName}";
                    return false;
                }

                var settings = new JsonSerializerSettings
                {
                    TypeNameHandling = TypeNameHandling.Auto
                };

                string json = File.ReadAllText(fileName);
                var deserializedGroups = JsonHelper.DeserializeObject<ObservableCollection<DeviceBase>>(json);

                Devices.Clear();
                foreach ( DeviceBase device in deserializedGroups )
                {
                    Devices.Add(device);    
                }

                //if (deserializedGroups != null)
                //{
                //    // 清除现有设备
                //    _devices.Clear();
                //    foreach (var group in DeviceGroups)
                //    {
                //        group.ClearDevices();
                //    }

                //    // 重建设备
                //    foreach (var deviceGroup in deserializedGroups)
                //    {
                //        foreach (var device in deviceGroup.Devices)
                //        {
                //            if (device != null)
                //            {
                //                _devices[device.Id] = device;
                //                var targetGroup = DeviceGroups.FirstOrDefault(g => g.Category == device.Category);
                //                targetGroup?.AddDevice(device);
                //            }
                //        }
                //    }
                //}
                StatusMessage = $"设备配置加载成功：{fileName}";
                return true;
            }
            catch (Exception ex)
            {
                StatusMessage = $"加载设备配置异常：{ex.Message}";
                System.Diagnostics.Debug.WriteLine($"加载设备配置失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 清除所有设备
        /// </summary>
        public void ClearAllDevices()
        {
            var devicesToRemove = Devices.ToList();
            foreach (var device in devicesToRemove)
            {
                if (device is IDevice iDevice)
                {
                    RemoveDevice(iDevice.Id);
                }
            }
        }

        /// <summary>
        /// 关闭所有设备
        /// </summary>
        public void CloseAllDevices()
        {
            foreach (var device in _devices.Values)
            {
                try
                {
                    if (device.IsOpen())
                    {
                        device.Close();
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"关闭设备异常: {device.Name}, {ex.Message}");
                }
            }
        }

        #endregion
    }
}
