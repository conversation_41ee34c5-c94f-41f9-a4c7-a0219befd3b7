using System;

namespace McLaser.Devices
{
    /// <summary>
    /// 相机触发模式枚举
    /// 从MoonLight项目迁移的触发模式定义
    /// </summary>
    [Serializable]
    public enum TrigMode
    {
        /// <summary>
        /// 连续采集模式
        /// </summary>
        连续采集 = 0,

        /// <summary>
        /// 软触发模式
        /// </summary>
        软触发 = 1,

        /// <summary>
        /// 上升沿触发模式
        /// </summary>
        上升沿 = 2,

        /// <summary>
        /// 下降沿触发模式
        /// </summary>
        下降沿 = 3
    }
}
