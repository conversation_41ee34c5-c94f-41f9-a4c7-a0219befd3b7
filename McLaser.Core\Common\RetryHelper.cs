using System;
using System.Threading;
using System.Threading.Tasks;

namespace McLaser.Core.Common
{
    /// <summary>
    /// 重试策略枚举
    /// </summary>
    public enum RetryStrategy
    {
        /// <summary>
        /// 固定间隔
        /// </summary>
        FixedInterval,

        /// <summary>
        /// 指数退避
        /// </summary>
        ExponentialBackoff,

        /// <summary>
        /// 线性增长
        /// </summary>
        LinearBackoff
    }

    /// <summary>
    /// 重试配置
    /// </summary>
    public class RetryOptions
    {
        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetries { get; set; } = 3;

        /// <summary>
        /// 重试间隔
        /// </summary>
        public TimeSpan Interval { get; set; } = TimeSpan.FromSeconds(1);

        /// <summary>
        /// 重试策略
        /// </summary>
        public RetryStrategy Strategy { get; set; } = RetryStrategy.FixedInterval;

        /// <summary>
        /// 最大间隔时间（用于退避策略）
        /// </summary>
        public TimeSpan MaxInterval { get; set; } = TimeSpan.FromMinutes(1);

        /// <summary>
        /// 退避倍数（用于指数退避）
        /// </summary>
        public double BackoffMultiplier { get; set; } = 2.0;

        /// <summary>
        /// 异常过滤器，返回 true 表示需要重试
        /// </summary>
        public Func<Exception, bool>? ExceptionFilter { get; set; }
    }

    /// <summary>
    /// 重试帮助类
    /// 提供各种重试机制的实现
    /// </summary>
    public static class RetryHelper
    {
        /// <summary>
        /// 执行带重试的操作
        /// </summary>
        /// <param name="action">要执行的操作</param>
        /// <param name="options">重试配置</param>
        public static void Execute(Action action, RetryOptions? options = null)
        {
            if (action == null)
                throw new ArgumentNullException(nameof(action));

            options ??= new RetryOptions();
            var attempt = 0;
            Exception? lastException = null;

            while (attempt <= options.MaxRetries)
            {
                try
                {
                    action();
                    return; // 成功执行，退出重试循环
                }
                catch (Exception ex)
                {
                    lastException = ex;

                    // 检查是否需要重试
                    if (attempt >= options.MaxRetries ||
                        (options.ExceptionFilter != null && !options.ExceptionFilter(ex)))
                    {
                        throw; // 达到最大重试次数或异常不需要重试
                    }

                    // 计算等待时间
                    var delay = CalculateDelay(attempt, options);
                    if (delay > TimeSpan.Zero)
                    {
                        Thread.Sleep(delay);
                    }

                    attempt++;
                }
            }

            // 如果到这里，说明所有重试都失败了
            throw lastException ?? new InvalidOperationException("重试失败");
        }

        /// <summary>
        /// 执行带重试的操作并返回结果
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="func">要执行的函数</param>
        /// <param name="options">重试配置</param>
        /// <returns>操作结果</returns>
        public static T Execute<T>(Func<T> func, RetryOptions? options = null)
        {
            if (func == null)
                throw new ArgumentNullException(nameof(func));

            options ??= new RetryOptions();
            var attempt = 0;
            Exception? lastException = null;

            while (attempt <= options.MaxRetries)
            {
                try
                {
                    return func(); // 成功执行，返回结果
                }
                catch (Exception ex)
                {
                    lastException = ex;

                    // 检查是否需要重试
                    if (attempt >= options.MaxRetries ||
                        (options.ExceptionFilter != null && !options.ExceptionFilter(ex)))
                    {
                        throw; // 达到最大重试次数或异常不需要重试
                    }

                    // 计算等待时间
                    var delay = CalculateDelay(attempt, options);
                    if (delay > TimeSpan.Zero)
                    {
                        Thread.Sleep(delay);
                    }

                    attempt++;
                }
            }

            // 如果到这里，说明所有重试都失败了
            throw lastException ?? new InvalidOperationException("重试失败");
        }

        /// <summary>
        /// 异步执行带重试的操作
        /// </summary>
        /// <param name="action">要执行的异步操作</param>
        /// <param name="options">重试配置</param>
        /// <param name="cancellationToken">取消令牌</param>
        public static async Task ExecuteAsync(Func<Task> action, RetryOptions? options = null, CancellationToken cancellationToken = default)
        {
            if (action == null)
                throw new ArgumentNullException(nameof(action));

            options ??= new RetryOptions();
            var attempt = 0;
            Exception? lastException = null;

            while (attempt <= options.MaxRetries)
            {
                try
                {
                    await action();
                    return; // 成功执行，退出重试循环
                }
                catch (Exception ex)
                {
                    lastException = ex;

                    // 检查是否需要重试
                    if (attempt >= options.MaxRetries ||
                        (options.ExceptionFilter != null && !options.ExceptionFilter(ex)))
                    {
                        throw; // 达到最大重试次数或异常不需要重试
                    }

                    // 计算等待时间
                    var delay = CalculateDelay(attempt, options);
                    if (delay > TimeSpan.Zero)
                    {
                        await Task.Delay(delay, cancellationToken);
                    }

                    attempt++;
                }
            }

            // 如果到这里，说明所有重试都失败了
            throw lastException ?? new InvalidOperationException("重试失败");
        }

        /// <summary>
        /// 异步执行带重试的操作并返回结果
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="func">要执行的异步函数</param>
        /// <param name="options">重试配置</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>操作结果</returns>
        public static async Task<T> ExecuteAsync<T>(Func<Task<T>> func, RetryOptions? options = null, CancellationToken cancellationToken = default)
        {
            if (func == null)
                throw new ArgumentNullException(nameof(func));

            options ??= new RetryOptions();
            var attempt = 0;
            Exception? lastException = null;

            while (attempt <= options.MaxRetries)
            {
                try
                {
                    return await func(); // 成功执行，返回结果
                }
                catch (Exception ex)
                {
                    lastException = ex;

                    // 检查是否需要重试
                    if (attempt >= options.MaxRetries ||
                        (options.ExceptionFilter != null && !options.ExceptionFilter(ex)))
                    {
                        throw; // 达到最大重试次数或异常不需要重试
                    }

                    // 计算等待时间
                    var delay = CalculateDelay(attempt, options);
                    if (delay > TimeSpan.Zero)
                    {
                        await Task.Delay(delay, cancellationToken);
                    }

                    attempt++;
                }
            }

            // 如果到这里，说明所有重试都失败了
            throw lastException ?? new InvalidOperationException("重试失败");
        }

        /// <summary>
        /// 计算延迟时间
        /// </summary>
        /// <param name="attempt">当前尝试次数</param>
        /// <param name="options">重试配置</param>
        /// <returns>延迟时间</returns>
        private static TimeSpan CalculateDelay(int attempt, RetryOptions options)
        {
            if (attempt == 0)
                return TimeSpan.Zero;

            TimeSpan delay;

            switch (options.Strategy)
            {
                case RetryStrategy.FixedInterval:
                    delay = options.Interval;
                    break;

                case RetryStrategy.ExponentialBackoff:
                    var exponentialDelay = TimeSpan.FromMilliseconds(
                        options.Interval.TotalMilliseconds * Math.Pow(options.BackoffMultiplier, attempt));
                    delay = exponentialDelay > options.MaxInterval ? options.MaxInterval : exponentialDelay;
                    break;

                case RetryStrategy.LinearBackoff:
                    delay = TimeSpan.FromMilliseconds(options.Interval.TotalMilliseconds * (attempt + 1));
                    delay = delay > options.MaxInterval ? options.MaxInterval : delay;
                    break;

                default:
                    delay = options.Interval;
                    break;
            }

            return delay;
        }
    }
}
