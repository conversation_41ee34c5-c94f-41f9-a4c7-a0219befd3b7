using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using McLaser.Core.Framework.Logging;

namespace McLaser.Core.Framework.Security
{
    /// <summary>
    /// 默认权限服务实现
    /// 提供完整的权限管理和授权检查功能
    /// </summary>
    public class DefaultPermissionService : IPermissionService
    {
        #region 字段和属性

        private readonly ILogger? _logger;
        private readonly ConcurrentDictionary<string, Permission> _permissions;
        private readonly ConcurrentDictionary<string, HashSet<string>> _userPermissions; // userId -> permissionIds
        private readonly ConcurrentDictionary<string, HashSet<string>> _permissionHierarchy; // permissionId -> parentPermissionIds

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化默认权限服务
        /// </summary>
        /// <param name="logger">日志服务</param>
        public DefaultPermissionService(ILogger? logger = null)
        {
            _logger = logger;
            _permissions = new ConcurrentDictionary<string, Permission>();
            _userPermissions = new ConcurrentDictionary<string, HashSet<string>>();
            _permissionHierarchy = new ConcurrentDictionary<string, HashSet<string>>();

            // 初始化默认权限
            InitializeDefaultPermissions();
        }

        /// <summary>
        /// 初始化默认权限
        /// </summary>
        private void InitializeDefaultPermissions()
        {
            try
            {
                // 系统管理权限
                var systemPermissions = new[]
                {
                    new Permission
                    {
                        Id = "system.admin",
                        Name = "系统管理",
                        Description = "系统管理员权限，拥有所有操作权限",
                        Category = "系统",
                        IsSystemPermission = true
                    },
                    new Permission
                    {
                        Id = "system.config",
                        Name = "系统配置",
                        Description = "系统配置管理权限",
                        Category = "系统",
                        IsSystemPermission = true
                    },
                    new Permission
                    {
                        Id = "system.log",
                        Name = "日志查看",
                        Description = "系统日志查看权限",
                        Category = "系统",
                        IsSystemPermission = true
                    }
                };

                // 用户管理权限
                var userPermissions = new[]
                {
                    new Permission
                    {
                        Id = "user.view",
                        Name = "用户查看",
                        Description = "查看用户信息权限",
                        Category = "用户管理",
                        IsSystemPermission = true
                    },
                    new Permission
                    {
                        Id = "user.create",
                        Name = "用户创建",
                        Description = "创建用户权限",
                        Category = "用户管理",
                        IsSystemPermission = true
                    },
                    new Permission
                    {
                        Id = "user.edit",
                        Name = "用户编辑",
                        Description = "编辑用户信息权限",
                        Category = "用户管理",
                        IsSystemPermission = true
                    },
                    new Permission
                    {
                        Id = "user.delete",
                        Name = "用户删除",
                        Description = "删除用户权限",
                        Category = "用户管理",
                        IsSystemPermission = true
                    }
                };

                // 角色管理权限
                var rolePermissions = new[]
                {
                    new Permission
                    {
                        Id = "role.view",
                        Name = "角色查看",
                        Description = "查看角色信息权限",
                        Category = "角色管理",
                        IsSystemPermission = true
                    },
                    new Permission
                    {
                        Id = "role.create",
                        Name = "角色创建",
                        Description = "创建角色权限",
                        Category = "角色管理",
                        IsSystemPermission = true
                    },
                    new Permission
                    {
                        Id = "role.edit",
                        Name = "角色编辑",
                        Description = "编辑角色信息权限",
                        Category = "角色管理",
                        IsSystemPermission = true
                    },
                    new Permission
                    {
                        Id = "role.delete",
                        Name = "角色删除",
                        Description = "删除角色权限",
                        Category = "角色管理",
                        IsSystemPermission = true
                    }
                };

                // 数据权限
                var dataPermissions = new[]
                {
                    new Permission
                    {
                        Id = "data.read",
                        Name = "数据读取",
                        Description = "读取数据权限",
                        Category = "数据操作",
                        IsSystemPermission = true
                    },
                    new Permission
                    {
                        Id = "data.write",
                        Name = "数据写入",
                        Description = "写入数据权限",
                        Category = "数据操作",
                        IsSystemPermission = true
                    },
                    new Permission
                    {
                        Id = "data.export",
                        Name = "数据导出",
                        Description = "导出数据权限",
                        Category = "数据操作",
                        IsSystemPermission = true
                    },
                    new Permission
                    {
                        Id = "data.import",
                        Name = "数据导入",
                        Description = "导入数据权限",
                        Category = "数据操作",
                        IsSystemPermission = true
                    }
                };

                // 创建所有默认权限
                var allPermissions = systemPermissions
                    .Concat(userPermissions)
                    .Concat(rolePermissions)
                    .Concat(dataPermissions);

                foreach (var permission in allPermissions)
                {
                    var result = CreatePermissionAsync(permission).Result;
                    if (result.Success)
                    {
                        _logger?.LogInfo($"默认权限已创建: {permission.Name} (ID: {permission.Id})");
                    }
                }

                _logger?.LogInfo("默认权限初始化完成");
            }
            catch (Exception ex)
            {
                _logger?.LogError("初始化默认权限失败", ex);
            }
        }

        #endregion

        #region 权限管理

        /// <summary>
        /// 创建权限
        /// </summary>
        /// <param name="permission">权限信息</param>
        /// <returns>创建结果</returns>
        public async Task<PermissionOperationResult> CreatePermissionAsync(Permission permission)
        {
            if (permission == null)
                return PermissionOperationResult.CreateFailure("权限信息不能为空");

            if (string.IsNullOrWhiteSpace(permission.Id))
                return PermissionOperationResult.CreateFailure("权限ID不能为空");

            if (string.IsNullOrWhiteSpace(permission.Name))
                return PermissionOperationResult.CreateFailure("权限名称不能为空");

            try
            {
                // 检查权限是否已存在
                if (_permissions.ContainsKey(permission.Id))
                {
                    return PermissionOperationResult.CreateFailure("权限ID已存在", "PERMISSION_EXISTS");
                }

                // 设置创建时间
                permission.CreatedAt = DateTime.UtcNow;
                permission.UpdatedAt = DateTime.UtcNow;

                // 添加权限
                if (_permissions.TryAdd(permission.Id, permission))
                {
                    // 初始化权限层次结构
                    _permissionHierarchy.TryAdd(permission.Id, new HashSet<string>());

                    _logger?.LogInfo($"权限已创建: {permission.Name} (ID: {permission.Id})");
                    
                    // 触发事件
                    OnPermissionCreated(new PermissionCreatedEventArgs(permission));
                    
                    return PermissionOperationResult.CreateSuccess();
                }
                else
                {
                    return PermissionOperationResult.CreateFailure("添加权限失败");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"创建权限失败: {permission.Name}", ex);
                return PermissionOperationResult.CreateFailure($"创建权限失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新权限
        /// </summary>
        /// <param name="permission">权限信息</param>
        /// <returns>更新结果</returns>
        public async Task<PermissionOperationResult> UpdatePermissionAsync(Permission permission)
        {
            if (permission == null)
                return PermissionOperationResult.CreateFailure("权限信息不能为空");

            if (string.IsNullOrWhiteSpace(permission.Id))
                return PermissionOperationResult.CreateFailure("权限ID不能为空");

            try
            {
                if (_permissions.TryGetValue(permission.Id, out var existingPermission))
                {
                    // 检查是否为系统权限
                    if (existingPermission.IsSystemPermission)
                    {
                        return PermissionOperationResult.CreateFailure("系统权限不能修改", "SYSTEM_PERMISSION_READONLY");
                    }

                    // 更新权限信息
                    permission.CreatedAt = existingPermission.CreatedAt; // 保持创建时间不变
                    permission.UpdatedAt = DateTime.UtcNow;

                    if (_permissions.TryUpdate(permission.Id, permission, existingPermission))
                    {
                        _logger?.LogInfo($"权限已更新: {permission.Name} (ID: {permission.Id})");
                        
                        // 触发事件
                        OnPermissionUpdated(new PermissionUpdatedEventArgs(permission));
                        
                        return PermissionOperationResult.CreateSuccess();
                    }
                    else
                    {
                        return PermissionOperationResult.CreateFailure("更新权限失败");
                    }
                }
                else
                {
                    return PermissionOperationResult.CreateFailure("权限不存在", "PERMISSION_NOT_FOUND");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"更新权限失败: {permission.Id}", ex);
                return PermissionOperationResult.CreateFailure($"更新权限失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 删除权限
        /// </summary>
        /// <param name="permissionId">权限ID</param>
        /// <returns>删除结果</returns>
        public async Task<PermissionOperationResult> DeletePermissionAsync(string permissionId)
        {
            if (string.IsNullOrWhiteSpace(permissionId))
                return PermissionOperationResult.CreateFailure("权限ID不能为空");

            try
            {
                if (_permissions.TryGetValue(permissionId, out var permission))
                {
                    // 检查是否为系统权限
                    if (permission.IsSystemPermission)
                    {
                        return PermissionOperationResult.CreateFailure("系统权限不能删除", "SYSTEM_PERMISSION_READONLY");
                    }

                    // 删除权限
                    if (_permissions.TryRemove(permissionId, out var removedPermission))
                    {
                        // 清理相关数据
                        _permissionHierarchy.TryRemove(permissionId, out _);

                        // 清理用户权限中的引用
                        foreach (var kvp in _userPermissions)
                        {
                            kvp.Value.Remove(permissionId);
                        }

                        // 清理其他权限对此权限的父级引用
                        foreach (var kvp in _permissionHierarchy)
                        {
                            kvp.Value.Remove(permissionId);
                        }

                        _logger?.LogInfo($"权限已删除: {removedPermission.Name} (ID: {permissionId})");
                        
                        // 触发事件
                        OnPermissionDeleted(new PermissionDeletedEventArgs(permissionId, removedPermission.Name));
                        
                        return PermissionOperationResult.CreateSuccess();
                    }
                    else
                    {
                        return PermissionOperationResult.CreateFailure("删除权限失败");
                    }
                }
                else
                {
                    return PermissionOperationResult.CreateFailure("权限不存在", "PERMISSION_NOT_FOUND");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"删除权限失败: {permissionId}", ex);
                return PermissionOperationResult.CreateFailure($"删除权限失败: {ex.Message}");
            }
        }

        #endregion

        #region 事件

        /// <summary>
        /// 权限创建事件
        /// </summary>
        public event EventHandler<PermissionCreatedEventArgs>? PermissionCreated;

        /// <summary>
        /// 权限更新事件
        /// </summary>
        public event EventHandler<PermissionUpdatedEventArgs>? PermissionUpdated;

        /// <summary>
        /// 权限删除事件
        /// </summary>
        public event EventHandler<PermissionDeletedEventArgs>? PermissionDeleted;

        /// <summary>
        /// 触发权限创建事件
        /// </summary>
        protected virtual void OnPermissionCreated(PermissionCreatedEventArgs e)
        {
            PermissionCreated?.Invoke(this, e);
        }

        /// <summary>
        /// 触发权限更新事件
        /// </summary>
        protected virtual void OnPermissionUpdated(PermissionUpdatedEventArgs e)
        {
            PermissionUpdated?.Invoke(this, e);
        }

        /// <summary>
        /// 触发权限删除事件
        /// </summary>
        protected virtual void OnPermissionDeleted(PermissionDeletedEventArgs e)
        {
            PermissionDeleted?.Invoke(this, e);
        }



        #endregion

        #region 权限查询

        /// <summary>
        /// 根据ID获取权限
        /// </summary>
        /// <param name="permissionId">权限ID</param>
        /// <returns>权限信息</returns>
        public async Task<Permission?> GetPermissionByIdAsync(string permissionId)
        {
            if (string.IsNullOrWhiteSpace(permissionId))
                return null;

            _permissions.TryGetValue(permissionId, out var permission);
            return permission;
        }

        /// <summary>
        /// 根据名称获取权限
        /// </summary>
        /// <param name="permissionName">权限名称</param>
        /// <returns>权限信息</returns>
        public async Task<Permission?> GetPermissionByNameAsync(string permissionName)
        {
            if (string.IsNullOrWhiteSpace(permissionName))
                return null;

            return _permissions.Values.FirstOrDefault(p => p.Name.Equals(permissionName, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 获取所有权限
        /// </summary>
        /// <returns>权限列表</returns>
        public async Task<IEnumerable<Permission>> GetAllPermissionsAsync()
        {
            return _permissions.Values.ToList();
        }

        /// <summary>
        /// 根据分类获取权限
        /// </summary>
        /// <param name="category">权限分类</param>
        /// <returns>权限列表</returns>
        public async Task<IEnumerable<Permission>> GetPermissionsByCategoryAsync(string category)
        {
            if (string.IsNullOrWhiteSpace(category))
                return Enumerable.Empty<Permission>();

            return _permissions.Values.Where(p =>
                string.Equals(p.Category, category, StringComparison.OrdinalIgnoreCase)
            ).ToList();
        }

        /// <summary>
        /// 搜索权限
        /// </summary>
        /// <param name="searchTerm">搜索条件</param>
        /// <returns>权限列表</returns>
        public async Task<IEnumerable<Permission>> SearchPermissionsAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllPermissionsAsync();

            var term = searchTerm.ToLowerInvariant();
            return _permissions.Values.Where(p =>
                p.Name.ToLowerInvariant().Contains(term) ||
                (p.Description?.ToLowerInvariant().Contains(term) ?? false) ||
                (p.Category?.ToLowerInvariant().Contains(term) ?? false)
            ).ToList();
        }

        #endregion

        #region 授权检查

        /// <summary>
        /// 检查用户是否拥有指定权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="permissionName">权限名称</param>
        /// <returns>是否拥有权限</returns>
        public async Task<bool> UserHasPermissionAsync(string userId, string permissionName)
        {
            if (string.IsNullOrWhiteSpace(userId) || string.IsNullOrWhiteSpace(permissionName))
                return false;

            // 根据权限名称查找权限ID
            var permission = await GetPermissionByNameAsync(permissionName);
            if (permission == null)
                return false;

            return await UserHasPermissionByIdAsync(userId, permission.Id);
        }

        /// <summary>
        /// 检查用户是否拥有指定权限（按ID）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="permissionId">权限ID</param>
        /// <returns>是否拥有权限</returns>
        public async Task<bool> UserHasPermissionByIdAsync(string userId, string permissionId)
        {
            if (string.IsNullOrWhiteSpace(userId) || string.IsNullOrWhiteSpace(permissionId))
                return false;

            if (_userPermissions.TryGetValue(userId, out var permissions))
            {
                return permissions.Contains(permissionId);
            }

            return false;
        }

        /// <summary>
        /// 检查用户是否拥有任一权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="permissionNames">权限名称列表</param>
        /// <returns>是否拥有任一权限</returns>
        public async Task<bool> UserHasAnyPermissionAsync(string userId, IEnumerable<string> permissionNames)
        {
            if (string.IsNullOrWhiteSpace(userId) || permissionNames == null || !permissionNames.Any())
                return false;

            foreach (var permissionName in permissionNames)
            {
                if (await UserHasPermissionAsync(userId, permissionName))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 检查用户是否拥有所有权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="permissionNames">权限名称列表</param>
        /// <returns>是否拥有所有权限</returns>
        public async Task<bool> UserHasAllPermissionsAsync(string userId, IEnumerable<string> permissionNames)
        {
            if (string.IsNullOrWhiteSpace(userId) || permissionNames == null || !permissionNames.Any())
                return false;

            foreach (var permissionName in permissionNames)
            {
                if (!await UserHasPermissionAsync(userId, permissionName))
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 获取用户的直接权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>权限列表</returns>
        public async Task<IEnumerable<Permission>> GetUserDirectPermissionsAsync(string userId)
        {
            if (string.IsNullOrWhiteSpace(userId))
                return Enumerable.Empty<Permission>();

            if (_userPermissions.TryGetValue(userId, out var permissionIds))
            {
                var permissions = new List<Permission>();
                foreach (var permissionId in permissionIds)
                {
                    if (_permissions.TryGetValue(permissionId, out var permission))
                    {
                        permissions.Add(permission);
                    }
                }
                return permissions;
            }

            return Enumerable.Empty<Permission>();
        }

        /// <summary>
        /// 获取用户的有效权限（包括通过角色继承的权限）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>权限列表</returns>
        public async Task<IEnumerable<Permission>> GetUserEffectivePermissionsAsync(string userId)
        {
            // 注意：这里只返回直接权限，角色继承的权限需要通过IRoleService获取
            // 实际项目中可能需要注入IRoleService和IUserService来获取完整的有效权限
            return await GetUserDirectPermissionsAsync(userId);
        }

        #endregion

        #region 权限授予和撤销

        /// <summary>
        /// 直接授予用户权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="permissionId">权限ID</param>
        /// <returns>授予结果</returns>
        public async Task<PermissionOperationResult> GrantPermissionToUserAsync(string userId, string permissionId)
        {
            if (string.IsNullOrWhiteSpace(userId))
                return PermissionOperationResult.CreateFailure("用户ID不能为空");

            if (string.IsNullOrWhiteSpace(permissionId))
                return PermissionOperationResult.CreateFailure("权限ID不能为空");

            try
            {
                // 检查权限是否存在
                if (!_permissions.ContainsKey(permissionId))
                {
                    return PermissionOperationResult.CreateFailure("权限不存在", "PERMISSION_NOT_FOUND");
                }

                var userPermissions = _userPermissions.GetOrAdd(userId, _ => new HashSet<string>());
                if (userPermissions.Add(permissionId))
                {
                    _logger?.LogInfo($"权限已授予用户: {permissionId} -> {userId}");
                    return PermissionOperationResult.CreateSuccess();
                }
                else
                {
                    return PermissionOperationResult.CreateFailure("用户已拥有该权限", "PERMISSION_EXISTS");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"授予权限失败: {permissionId} -> {userId}", ex);
                return PermissionOperationResult.CreateFailure($"授予权限失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 直接授予用户多个权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="permissionIds">权限ID列表</param>
        /// <returns>授予结果</returns>
        public async Task<PermissionOperationResult> GrantPermissionsToUserAsync(string userId, IEnumerable<string> permissionIds)
        {
            if (string.IsNullOrWhiteSpace(userId))
                return PermissionOperationResult.CreateFailure("用户ID不能为空");

            if (permissionIds == null || !permissionIds.Any())
                return PermissionOperationResult.CreateFailure("权限ID列表不能为空");

            try
            {
                var userPermissions = _userPermissions.GetOrAdd(userId, _ => new HashSet<string>());
                var addedCount = 0;

                foreach (var permissionId in permissionIds)
                {
                    if (!string.IsNullOrWhiteSpace(permissionId) &&
                        _permissions.ContainsKey(permissionId) &&
                        userPermissions.Add(permissionId))
                    {
                        addedCount++;
                    }
                }

                _logger?.LogInfo($"已为用户 {userId} 授予 {addedCount} 个权限");
                return PermissionOperationResult.CreateSuccess();
            }
            catch (Exception ex)
            {
                _logger?.LogError($"批量授予权限失败: {userId}", ex);
                return PermissionOperationResult.CreateFailure($"批量授予权限失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 撤销用户权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="permissionId">权限ID</param>
        /// <returns>撤销结果</returns>
        public async Task<PermissionOperationResult> RevokePermissionFromUserAsync(string userId, string permissionId)
        {
            if (string.IsNullOrWhiteSpace(userId))
                return PermissionOperationResult.CreateFailure("用户ID不能为空");

            if (string.IsNullOrWhiteSpace(permissionId))
                return PermissionOperationResult.CreateFailure("权限ID不能为空");

            try
            {
                if (_userPermissions.TryGetValue(userId, out var userPermissions))
                {
                    if (userPermissions.Remove(permissionId))
                    {
                        _logger?.LogInfo($"权限已从用户撤销: {permissionId} <- {userId}");
                        return PermissionOperationResult.CreateSuccess();
                    }
                    else
                    {
                        return PermissionOperationResult.CreateFailure("用户没有该权限", "PERMISSION_NOT_FOUND");
                    }
                }
                else
                {
                    return PermissionOperationResult.CreateFailure("用户没有任何权限", "USER_NO_PERMISSIONS");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"撤销权限失败: {permissionId} <- {userId}", ex);
                return PermissionOperationResult.CreateFailure($"撤销权限失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 撤销用户的所有直接权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>撤销结果</returns>
        public async Task<PermissionOperationResult> RevokeAllPermissionsFromUserAsync(string userId)
        {
            if (string.IsNullOrWhiteSpace(userId))
                return PermissionOperationResult.CreateFailure("用户ID不能为空");

            try
            {
                if (_userPermissions.TryGetValue(userId, out var userPermissions))
                {
                    var count = userPermissions.Count;
                    userPermissions.Clear();
                    _logger?.LogInfo($"已撤销用户 {userId} 的所有直接权限 ({count} 个)");
                    return PermissionOperationResult.CreateSuccess();
                }
                else
                {
                    return PermissionOperationResult.CreateFailure("用户没有任何权限", "USER_NO_PERMISSIONS");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"撤销所有权限失败: {userId}", ex);
                return PermissionOperationResult.CreateFailure($"撤销所有权限失败: {ex.Message}");
            }
        }

        #endregion

        #region 权限层次结构

        /// <summary>
        /// 设置权限的父权限
        /// </summary>
        /// <param name="permissionId">权限ID</param>
        /// <param name="parentPermissionId">父权限ID</param>
        /// <returns>设置结果</returns>
        public async Task<PermissionOperationResult> SetParentPermissionAsync(string permissionId, string parentPermissionId)
        {
            if (string.IsNullOrWhiteSpace(permissionId))
                return PermissionOperationResult.CreateFailure("权限ID不能为空");

            if (string.IsNullOrWhiteSpace(parentPermissionId))
                return PermissionOperationResult.CreateFailure("父权限ID不能为空");

            if (permissionId == parentPermissionId)
                return PermissionOperationResult.CreateFailure("权限不能设置自己为父权限");

            try
            {
                // 检查权限是否存在
                if (!_permissions.ContainsKey(permissionId) || !_permissions.ContainsKey(parentPermissionId))
                {
                    return PermissionOperationResult.CreateFailure("权限不存在", "PERMISSION_NOT_FOUND");
                }

                // 检查是否会形成循环引用
                if (await WouldCreateCircularReferenceAsync(permissionId, parentPermissionId))
                {
                    return PermissionOperationResult.CreateFailure("设置父权限会形成循环引用", "CIRCULAR_REFERENCE");
                }

                var hierarchy = _permissionHierarchy.GetOrAdd(permissionId, _ => new HashSet<string>());
                hierarchy.Add(parentPermissionId);

                _logger?.LogInfo($"父权限已设置: {permissionId} -> {parentPermissionId}");
                return PermissionOperationResult.CreateSuccess();
            }
            catch (Exception ex)
            {
                _logger?.LogError($"设置父权限失败: {permissionId} -> {parentPermissionId}", ex);
                return PermissionOperationResult.CreateFailure($"设置父权限失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 移除权限的父权限
        /// </summary>
        /// <param name="permissionId">权限ID</param>
        /// <returns>移除结果</returns>
        public async Task<PermissionOperationResult> RemoveParentPermissionAsync(string permissionId)
        {
            if (string.IsNullOrWhiteSpace(permissionId))
                return PermissionOperationResult.CreateFailure("权限ID不能为空");

            try
            {
                if (_permissionHierarchy.TryGetValue(permissionId, out var hierarchy))
                {
                    var count = hierarchy.Count;
                    hierarchy.Clear();
                    _logger?.LogInfo($"已移除权限 {permissionId} 的所有父权限 ({count} 个)");
                    return PermissionOperationResult.CreateSuccess();
                }
                else
                {
                    return PermissionOperationResult.CreateFailure("权限不存在", "PERMISSION_NOT_FOUND");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"移除父权限失败: {permissionId}", ex);
                return PermissionOperationResult.CreateFailure($"移除父权限失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取子权限
        /// </summary>
        /// <param name="permissionId">权限ID</param>
        /// <returns>子权限列表</returns>
        public async Task<IEnumerable<Permission>> GetChildPermissionsAsync(string permissionId)
        {
            if (string.IsNullOrWhiteSpace(permissionId))
                return Enumerable.Empty<Permission>();

            var childPermissions = new List<Permission>();

            foreach (var kvp in _permissionHierarchy)
            {
                if (kvp.Value.Contains(permissionId))
                {
                    if (_permissions.TryGetValue(kvp.Key, out var childPermission))
                    {
                        childPermissions.Add(childPermission);
                    }
                }
            }

            return childPermissions;
        }

        /// <summary>
        /// 检查是否会形成循环引用
        /// </summary>
        private async Task<bool> WouldCreateCircularReferenceAsync(string permissionId, string parentPermissionId)
        {
            var visited = new HashSet<string>();
            return await CheckCircularReferenceRecursiveAsync(parentPermissionId, permissionId, visited);
        }

        /// <summary>
        /// 递归检查循环引用
        /// </summary>
        private async Task<bool> CheckCircularReferenceRecursiveAsync(string currentPermissionId, string targetPermissionId, HashSet<string> visited)
        {
            if (currentPermissionId == targetPermissionId)
                return true;

            if (visited.Contains(currentPermissionId))
                return false;

            visited.Add(currentPermissionId);

            if (_permissionHierarchy.TryGetValue(currentPermissionId, out var parentPermissions))
            {
                foreach (var parentPermissionId in parentPermissions)
                {
                    if (await CheckCircularReferenceRecursiveAsync(parentPermissionId, targetPermissionId, visited))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        #endregion

        #region 缺失的接口方法实现

        /// <summary>
        /// 获取用户权限
        /// </summary>
        public async Task<IEnumerable<Permission>> GetUserPermissionsAsync(string userId)
        {
            return await GetUserDirectPermissionsAsync(userId);
        }

        /// <summary>
        /// 获取子权限
        /// </summary>
        public async Task<IEnumerable<Permission>> GetDescendantPermissionsAsync(string permissionId)
        {
            return await GetChildPermissionsAsync(permissionId);
        }

        /// <summary>
        /// 验证权限名称
        /// </summary>
        public bool ValidatePermissionName(string permissionName)
        {
            return !string.IsNullOrWhiteSpace(permissionName) && permissionName.Length <= 100;
        }

        /// <summary>
        /// 检查权限是否存在（按名称）
        /// </summary>
        public async Task<bool> PermissionExistsAsync(string permissionName)
        {
            var permission = await GetPermissionByNameAsync(permissionName);
            return permission != null;
        }

        /// <summary>
        /// 检查权限是否存在（按ID）
        /// </summary>
        public async Task<bool> PermissionExistsByIdAsync(string permissionId)
        {
            var permission = await GetPermissionByIdAsync(permissionId);
            return permission != null;
        }

        /// <summary>
        /// 清理权限缓存
        /// </summary>
        public void ClearPermissionCache()
        {
            // 这里可以实现权限缓存清理逻辑
        }

        /// <summary>
        /// 清理用户权限缓存
        /// </summary>
        public void ClearUserPermissionCache(string userId)
        {
            // 这里可以实现用户权限缓存清理逻辑
        }

        /// <summary>
        /// 预加载用户权限
        /// </summary>
        public async Task PreloadUserPermissionsAsync(string userId)
        {
            // 这里可以实现权限预加载逻辑
            await GetUserDirectPermissionsAsync(userId);
        }

        /// <summary>
        /// 权限授予事件
        /// </summary>
        public event EventHandler<PermissionGrantedEventArgs>? PermissionGranted;

        /// <summary>
        /// 权限撤销事件
        /// </summary>
        public event EventHandler<PermissionRevokedEventArgs>? PermissionRevoked;

        /// <summary>
        /// 权限检查事件
        /// </summary>
        public event EventHandler<PermissionCheckedEventArgs>? PermissionChecked;

        #endregion


    }
}
