using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace McLaser.Core.Framework.IO
{
    /// <summary>
    /// 文件系统服务接口
    /// 提供文件和目录操作的统一接口
    /// </summary>
    public interface IFileSystemService
    {
        /// <summary>
        /// 文件变更事件
        /// </summary>
        event EventHandler<FileSystemEventArgs>? FileChanged;

        /// <summary>
        /// 目录变更事件
        /// </summary>
        event EventHandler<FileSystemEventArgs>? DirectoryChanged;

        // 文件操作
        /// <summary>
        /// 检查文件是否存在
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否存在</returns>
        bool FileExists(string filePath);

        /// <summary>
        /// 读取文件内容
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件内容</returns>
        string ReadAllText(string filePath);

        /// <summary>
        /// 异步读取文件内容
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件内容</returns>
        Task<string> ReadAllTextAsync(string filePath);

        /// <summary>
        /// 读取文件字节
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件字节数组</returns>
        byte[] ReadAllBytes(string filePath);

        /// <summary>
        /// 异步读取文件字节
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件字节数组</returns>
        Task<byte[]> ReadAllBytesAsync(string filePath);

        /// <summary>
        /// 写入文件内容
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="content">文件内容</param>
        void WriteAllText(string filePath, string content);

        /// <summary>
        /// 异步写入文件内容
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="content">文件内容</param>
        /// <returns>任务</returns>
        Task WriteAllTextAsync(string filePath, string content);

        /// <summary>
        /// 写入文件字节
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="bytes">字节数组</param>
        void WriteAllBytes(string filePath, byte[] bytes);

        /// <summary>
        /// 异步写入文件字节
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="bytes">字节数组</param>
        /// <returns>任务</returns>
        Task WriteAllBytesAsync(string filePath, byte[] bytes);

        /// <summary>
        /// 复制文件
        /// </summary>
        /// <param name="sourceFilePath">源文件路径</param>
        /// <param name="destinationFilePath">目标文件路径</param>
        /// <param name="overwrite">是否覆盖</param>
        void CopyFile(string sourceFilePath, string destinationFilePath, bool overwrite = false);

        /// <summary>
        /// 移动文件
        /// </summary>
        /// <param name="sourceFilePath">源文件路径</param>
        /// <param name="destinationFilePath">目标文件路径</param>
        void MoveFile(string sourceFilePath, string destinationFilePath);

        /// <summary>
        /// 删除文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        void DeleteFile(string filePath);

        /// <summary>
        /// 获取文件信息
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件信息</returns>
        FileInfo GetFileInfo(string filePath);

        // 目录操作
        /// <summary>
        /// 检查目录是否存在
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <returns>是否存在</returns>
        bool DirectoryExists(string directoryPath);

        /// <summary>
        /// 创建目录
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        void CreateDirectory(string directoryPath);

        /// <summary>
        /// 删除目录
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="recursive">是否递归删除</param>
        void DeleteDirectory(string directoryPath, bool recursive = false);

        /// <summary>
        /// 获取目录中的文件
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="searchPattern">搜索模式</param>
        /// <param name="searchOption">搜索选项</param>
        /// <returns>文件路径列表</returns>
        IEnumerable<string> GetFiles(string directoryPath, string searchPattern = "*", 
                                   SearchOption searchOption = SearchOption.TopDirectoryOnly);

        /// <summary>
        /// 获取目录中的子目录
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="searchPattern">搜索模式</param>
        /// <param name="searchOption">搜索选项</param>
        /// <returns>目录路径列表</returns>
        IEnumerable<string> GetDirectories(string directoryPath, string searchPattern = "*", 
                                         SearchOption searchOption = SearchOption.TopDirectoryOnly);

        /// <summary>
        /// 获取目录信息
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <returns>目录信息</returns>
        DirectoryInfo GetDirectoryInfo(string directoryPath);

        // 路径操作
        /// <summary>
        /// 组合路径
        /// </summary>
        /// <param name="paths">路径部分</param>
        /// <returns>组合后的路径</returns>
        string CombinePath(params string[] paths);

        /// <summary>
        /// 获取文件名
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件名</returns>
        string GetFileName(string filePath);

        /// <summary>
        /// 获取不带扩展名的文件名
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>不带扩展名的文件名</returns>
        string GetFileNameWithoutExtension(string filePath);

        /// <summary>
        /// 获取文件扩展名
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件扩展名</returns>
        string GetExtension(string filePath);

        /// <summary>
        /// 获取目录名
        /// </summary>
        /// <param name="path">路径</param>
        /// <returns>目录名</returns>
        string? GetDirectoryName(string path);

        /// <summary>
        /// 获取绝对路径
        /// </summary>
        /// <param name="path">相对路径</param>
        /// <returns>绝对路径</returns>
        string GetFullPath(string path);

        /// <summary>
        /// 获取相对路径
        /// </summary>
        /// <param name="relativeTo">相对于的路径</param>
        /// <param name="path">目标路径</param>
        /// <returns>相对路径</returns>
        string GetRelativePath(string relativeTo, string path);

        // 监控操作
        /// <summary>
        /// 开始监控目录
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="filter">过滤器</param>
        /// <param name="includeSubdirectories">是否包含子目录</param>
        void StartWatching(string directoryPath, string filter = "*.*", bool includeSubdirectories = false);

        /// <summary>
        /// 停止监控目录
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        void StopWatching(string directoryPath);

        /// <summary>
        /// 停止所有监控
        /// </summary>
        void StopAllWatching();

        // 临时文件操作
        /// <summary>
        /// 创建临时文件
        /// </summary>
        /// <param name="extension">文件扩展名</param>
        /// <returns>临时文件路径</returns>
        string CreateTempFile(string extension = ".tmp");

        /// <summary>
        /// 创建临时目录
        /// </summary>
        /// <returns>临时目录路径</returns>
        string CreateTempDirectory();

        /// <summary>
        /// 清理临时文件
        /// </summary>
        void CleanupTempFiles();

        // 压缩操作
        /// <summary>
        /// 压缩文件
        /// </summary>
        /// <param name="sourceFilePath">源文件路径</param>
        /// <param name="compressedFilePath">压缩文件路径</param>
        void CompressFile(string sourceFilePath, string compressedFilePath);

        /// <summary>
        /// 解压文件
        /// </summary>
        /// <param name="compressedFilePath">压缩文件路径</param>
        /// <param name="extractPath">解压路径</param>
        void DecompressFile(string compressedFilePath, string extractPath);

        /// <summary>
        /// 压缩目录
        /// </summary>
        /// <param name="sourceDirectoryPath">源目录路径</param>
        /// <param name="compressedFilePath">压缩文件路径</param>
        void CompressDirectory(string sourceDirectoryPath, string compressedFilePath);

        /// <summary>
        /// 解压到目录
        /// </summary>
        /// <param name="compressedFilePath">压缩文件路径</param>
        /// <param name="extractDirectoryPath">解压目录路径</param>
        void DecompressToDirectory(string compressedFilePath, string extractDirectoryPath);

        // 加密操作
        /// <summary>
        /// 加密文件
        /// </summary>
        /// <param name="sourceFilePath">源文件路径</param>
        /// <param name="encryptedFilePath">加密文件路径</param>
        /// <param name="password">密码</param>
        void EncryptFile(string sourceFilePath, string encryptedFilePath, string password);

        /// <summary>
        /// 解密文件
        /// </summary>
        /// <param name="encryptedFilePath">加密文件路径</param>
        /// <param name="decryptedFilePath">解密文件路径</param>
        /// <param name="password">密码</param>
        void DecryptFile(string encryptedFilePath, string decryptedFilePath, string password);

        // 实用方法
        /// <summary>
        /// 计算文件哈希值
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="algorithm">哈希算法</param>
        /// <returns>哈希值</returns>
        string CalculateFileHash(string filePath, HashAlgorithmType algorithm = HashAlgorithmType.MD5);

        /// <summary>
        /// 获取目录大小
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="includeSubdirectories">是否包含子目录</param>
        /// <returns>目录大小（字节）</returns>
        long GetDirectorySize(string directoryPath, bool includeSubdirectories = true);

        /// <summary>
        /// 格式化文件大小
        /// </summary>
        /// <param name="bytes">字节数</param>
        /// <returns>格式化的文件大小</returns>
        string FormatFileSize(long bytes);
    }

    /// <summary>
    /// 哈希算法类型
    /// </summary>
    public enum HashAlgorithmType
    {
        /// <summary>
        /// MD5算法
        /// </summary>
        MD5,

        /// <summary>
        /// SHA1算法
        /// </summary>
        SHA1,

        /// <summary>
        /// SHA256算法
        /// </summary>
        SHA256,

        /// <summary>
        /// SHA512算法
        /// </summary>
        SHA512
    }

    /// <summary>
    /// 文件系统事件参数
    /// </summary>
    public class FileSystemEventArgs : EventArgs
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="changeType">变更类型</param>
        /// <param name="fullPath">完整路径</param>
        /// <param name="name">名称</param>
        public FileSystemEventArgs(WatcherChangeTypes changeType, string fullPath, string? name = null)
        {
            ChangeType = changeType;
            FullPath = fullPath;
            Name = name;
        }

        /// <summary>
        /// 变更类型
        /// </summary>
        public WatcherChangeTypes ChangeType { get; }

        /// <summary>
        /// 完整路径
        /// </summary>
        public string FullPath { get; }

        /// <summary>
        /// 名称
        /// </summary>
        public string? Name { get; }
    }
}
