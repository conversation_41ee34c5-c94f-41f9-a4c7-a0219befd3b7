using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace McLaser.Devices
{
    /// <summary>
    /// 布尔值转颜色转换器
    /// 用于状态指示器的颜色显示
    /// </summary>
    [ValueConversion(typeof(bool), typeof(Color))]
    public class BoolToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool isTrue = false;
            
            // 处理null值
            if (value == null)
                return Colors.Gray;
                
            // 尝试获取布尔值
            if (value is bool boolValue)
                isTrue = boolValue;
                
            // 解析参数
            string[] colors = parameter != null ? parameter.ToString().Split(':') : new[] { "Green", "Gray" };
            string trueColor = colors.Length > 0 ? colors[0] : "Green";
            string falseColor = colors.Length > 1 ? colors[1] : "Gray";
            
            // 根据布尔值选择颜色
            return isTrue ? GetColorFromName(trueColor) : GetColorFromName(falseColor);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
        
        /// <summary>
        /// 从颜色名称获取颜色
        /// </summary>
        private Color GetColorFromName(string colorName)
        {
            switch (colorName.ToLower())
            {
                case "red": return Colors.Red;
                case "green": return Colors.Green;
                case "blue": return Colors.Blue;
                case "yellow": return Colors.Yellow;
                case "orange": return Colors.Orange;
                case "gray": return Colors.Gray;
                case "lightgray": return Colors.LightGray;
                default:
                    try
                    {
                        // 尝试从系统颜色获取
                        var systemColor = (Color)ColorConverter.ConvertFromString(colorName);
                        return systemColor;
                    }
                    catch
                    {
                        return Colors.Gray;
                    }
            }
        }
    }
} 