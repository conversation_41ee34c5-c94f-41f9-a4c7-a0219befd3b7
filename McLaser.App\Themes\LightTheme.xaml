<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <!-- 浅色主题资源定义 -->
    
    <!-- 主要颜色 -->
    <Color x:Key="PrimaryColor">#0078D4</Color>
    <Color x:Key="SecondaryColor">#F3F2F1</Color>
    <Color x:Key="AccentColor">#005A9E</Color>
    
    <!-- 背景颜色 -->
    <Color x:Key="WindowBackgroundColor">White</Color>
    <Color x:Key="PanelBackgroundColor">#FAFAFA</Color>
    <Color x:Key="ControlBackgroundColor">White</Color>
    
    <!-- 前景颜色 -->
    <Color x:Key="WindowForegroundColor">#323130</Color>
    <Color x:Key="ControlForegroundColor">#323130</Color>
    <Color x:Key="DisabledForegroundColor">#A19F9D</Color>
    
    <!-- 边框颜色 -->
    <Color x:Key="BorderColor">#D2D0CE</Color>
    <Color x:Key="FocusBorderColor">#0078D4</Color>
    
    <!-- 按钮颜色 -->
    <Color x:Key="ButtonBackgroundColor">#F3F2F1</Color>
    <Color x:Key="ButtonHoverBackgroundColor">#EDEBE9</Color>
    <Color x:Key="ButtonPressedBackgroundColor">#E1DFDD</Color>
    <Color x:Key="ButtonBorderColor">#D2D0CE</Color>
    
    <!-- 文本框颜色 -->
    <Color x:Key="TextBoxBackgroundColor">White</Color>
    <Color x:Key="TextBoxBorderColor">#D2D0CE</Color>
    <Color x:Key="TextBoxFocusBorderColor">#0078D4</Color>
    
    <!-- 菜单颜色 -->
    <Color x:Key="MenuBackgroundColor">#F8F8F8</Color>
    <Color x:Key="MenuItemHoverBackgroundColor">#E1DFDD</Color>
    
    <!-- 工具栏颜色 -->
    <Color x:Key="ToolBarBackgroundColor">#F0F0F0</Color>
    
    <!-- 状态栏颜色 -->
    <Color x:Key="StatusBarBackgroundColor">#F0F0F0</Color>
    
    <!-- 画刷资源 -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource PrimaryColor}" />
    <SolidColorBrush x:Key="SecondaryBrush" Color="{StaticResource SecondaryColor}" />
    <SolidColorBrush x:Key="AccentBrush" Color="{StaticResource AccentColor}" />
    
    <SolidColorBrush x:Key="WindowBackgroundBrush" Color="{StaticResource WindowBackgroundColor}" />
    <SolidColorBrush x:Key="PanelBackgroundBrush" Color="{StaticResource PanelBackgroundColor}" />
    <SolidColorBrush x:Key="ControlBackgroundBrush" Color="{StaticResource ControlBackgroundColor}" />
    
    <SolidColorBrush x:Key="WindowForegroundBrush" Color="{StaticResource WindowForegroundColor}" />
    <SolidColorBrush x:Key="ControlForegroundBrush" Color="{StaticResource ControlForegroundColor}" />
    <SolidColorBrush x:Key="DisabledForegroundBrush" Color="{StaticResource DisabledForegroundColor}" />
    
    <SolidColorBrush x:Key="BorderBrush" Color="{StaticResource BorderColor}" />
    <SolidColorBrush x:Key="FocusBorderBrush" Color="{StaticResource FocusBorderColor}" />
    
    <SolidColorBrush x:Key="ButtonBackgroundBrush" Color="{StaticResource ButtonBackgroundColor}" />
    <SolidColorBrush x:Key="ButtonHoverBackgroundBrush" Color="{StaticResource ButtonHoverBackgroundColor}" />
    <SolidColorBrush x:Key="ButtonPressedBackgroundBrush" Color="{StaticResource ButtonPressedBackgroundColor}" />
    <SolidColorBrush x:Key="ButtonBorderBrush" Color="{StaticResource ButtonBorderColor}" />
    <SolidColorBrush x:Key="ButtonForegroundBrush" Color="{StaticResource ControlForegroundColor}" />
    
    <SolidColorBrush x:Key="TextBoxBackgroundBrush" Color="{StaticResource TextBoxBackgroundColor}" />
    <SolidColorBrush x:Key="TextBoxBorderBrush" Color="{StaticResource TextBoxBorderColor}" />
    <SolidColorBrush x:Key="TextBoxFocusBorderBrush" Color="{StaticResource TextBoxFocusBorderColor}" />
    <SolidColorBrush x:Key="TextBoxForegroundBrush" Color="{StaticResource ControlForegroundColor}" />
    
    <SolidColorBrush x:Key="LabelForegroundBrush" Color="{StaticResource ControlForegroundColor}" />
    
    <SolidColorBrush x:Key="MenuBackgroundBrush" Color="{StaticResource MenuBackgroundColor}" />
    <SolidColorBrush x:Key="MenuForegroundBrush" Color="{StaticResource ControlForegroundColor}" />
    <SolidColorBrush x:Key="MenuItemHoverBackgroundBrush" Color="{StaticResource MenuItemHoverBackgroundColor}" />
    
    <SolidColorBrush x:Key="ToolBarBackgroundBrush" Color="{StaticResource ToolBarBackgroundColor}" />
    <SolidColorBrush x:Key="ToolBarForegroundBrush" Color="{StaticResource ControlForegroundColor}" />
    
    <SolidColorBrush x:Key="StatusBarBackgroundBrush" Color="{StaticResource StatusBarBackgroundColor}" />
    <SolidColorBrush x:Key="StatusBarForegroundBrush" Color="{StaticResource ControlForegroundColor}" />
    
    <!-- 主题标记 -->
    <sys:String x:Key="ThemeName" xmlns:sys="clr-namespace:System;assembly=mscorlib">Light</sys:String>
    <sys:String x:Key="ThemeDisplayName" xmlns:sys="clr-namespace:System;assembly=mscorlib">浅色主题</sys:String>
    <sys:Boolean x:Key="IsDarkTheme" xmlns:sys="clr-namespace:System;assembly=mscorlib">False</sys:Boolean>
    
</ResourceDictionary>
