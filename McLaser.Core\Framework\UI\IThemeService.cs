using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Windows;

namespace McLaser.Core.Framework.UI
{
    /// <summary>
    /// 主题服务接口
    /// 提供应用程序主题管理功能
    /// </summary>

    public interface IThemeService
    {
        /// <summary>
        /// 当前主题名称
        /// </summary>
        string CurrentTheme { get; }

        /// <summary>
        /// 可用主题列表
        /// </summary>
        IReadOnlyList<string> AvailableThemes { get; }

        /// <summary>
        /// 主题变更事件
        /// </summary>
        event EventHandler<ThemeChangedEventArgs>? ThemeChanged;

        /// <summary>
        /// 应用主题
        /// </summary>
        /// <param name="themeName">主题名称</param>
        /// <returns>是否成功应用</returns>
        bool ApplyTheme(string themeName);

        /// <summary>
        /// 获取主题资源
        /// </summary>
        /// <param name="themeName">主题名称</param>
        /// <returns>主题资源字典</returns>
        ResourceDictionary? GetThemeResources(string themeName);

        /// <summary>
        /// 注册主题
        /// </summary>
        /// <param name="themeName">主题名称</param>
        /// <param name="resourceUri">资源URI</param>
        void RegisterTheme(string themeName, Uri resourceUri);

        /// <summary>
        /// 注册主题
        /// </summary>
        /// <param name="themeName">主题名称</param>
        /// <param name="resources">资源字典</param>
        void RegisterTheme(string themeName, ResourceDictionary resources);

        /// <summary>
        /// 移除主题
        /// </summary>
        /// <param name="themeName">主题名称</param>
        /// <returns>是否成功移除</returns>
        bool RemoveTheme(string themeName);

        /// <summary>
        /// 保存当前主题设置
        /// </summary>
        void SaveCurrentTheme();

        /// <summary>
        /// 加载保存的主题设置
        /// </summary>
        void LoadSavedTheme();

        /// <summary>
        /// 重置为默认主题
        /// </summary>
        void ResetToDefault();

        /// <summary>
        /// 检查主题是否存在
        /// </summary>
        /// <param name="themeName">主题名称</param>
        /// <returns>是否存在</returns>
        bool ThemeExists(string themeName);

        /// <summary>
        /// 获取主题预览
        /// </summary>
        /// <param name="themeName">主题名称</param>
        /// <returns>预览信息</returns>
        ThemePreview? GetThemePreview(string themeName);
    }

    /// <summary>
    /// 主题变更事件参数
    /// </summary>
    public class ThemeChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="oldTheme">旧主题名称</param>
        /// <param name="newTheme">新主题名称</param>
        public ThemeChangedEventArgs(string oldTheme, string newTheme)
        {
            OldTheme = oldTheme;
            NewTheme = newTheme;
        }

        /// <summary>
        /// 旧主题名称
        /// </summary>
        public string OldTheme { get; }

        /// <summary>
        /// 新主题名称
        /// </summary>
        public string NewTheme { get; }
    }

    /// <summary>
    /// 主题预览信息
    /// </summary>
    public class ThemePreview
    {
        /// <summary>
        /// 主题名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 主题显示名称
        /// </summary>
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// 主题描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 主题作者
        /// </summary>
        public string Author { get; set; } = string.Empty;

        /// <summary>
        /// 主题版本
        /// </summary>
        public string Version { get; set; } = string.Empty;

        /// <summary>
        /// 预览图片URI
        /// </summary>
        public Uri? PreviewImageUri { get; set; }

        /// <summary>
        /// 主要颜色
        /// </summary>
        public string PrimaryColor { get; set; } = string.Empty;

        /// <summary>
        /// 次要颜色
        /// </summary>
        public string SecondaryColor { get; set; } = string.Empty;

        /// <summary>
        /// 背景颜色
        /// </summary>
        public string BackgroundColor { get; set; } = string.Empty;

        /// <summary>
        /// 前景颜色
        /// </summary>
        public string ForegroundColor { get; set; } = string.Empty;

        /// <summary>
        /// 是否为深色主题
        /// </summary>
        public bool IsDarkTheme { get; set; }
    }

    /// <summary>
    /// 主题类型枚举
    /// </summary>
    public enum ThemeType
    {
        /// <summary>
        /// 浅色主题
        /// </summary>
        Light,

        /// <summary>
        /// 深色主题
        /// </summary>
        Dark,

        /// <summary>
        /// 自动主题（跟随系统）
        /// </summary>
        Auto,

        /// <summary>
        /// 自定义主题
        /// </summary>
        Custom
    }

    /// <summary>
    /// 主题资源类型
    /// </summary>
    public enum ThemeResourceType
    {
        /// <summary>
        /// 颜色资源
        /// </summary>
        Color,

        /// <summary>
        /// 画刷资源
        /// </summary>
        Brush,

        /// <summary>
        /// 样式资源
        /// </summary>
        Style,

        /// <summary>
        /// 模板资源
        /// </summary>
        Template,

        /// <summary>
        /// 字体资源
        /// </summary>
        Font,

        /// <summary>
        /// 图像资源
        /// </summary>
        Image
    }
}
