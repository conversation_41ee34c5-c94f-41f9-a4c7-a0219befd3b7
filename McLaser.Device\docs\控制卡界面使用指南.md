# 控制卡界面使用指南

## 概述

McLaser.Device提供了现代化工业控制风格的控制卡配置界面，支持轴管理、IO管理和参数配置等功能。界面采用了卡片式设计，提供了直观的操作体验和实时状态反馈。

## 主要功能

1. **轴管理**：显示和控制所有轴的状态，包括使能、回零、点动等操作
2. **参数配置**：通过PropertyGridView编辑轴参数和卡参数
3. **IO管理**：监控和控制数字输入输出状态
4. **扩展功能**：支持不同控制卡类型的特定功能扩展

## 界面组件

### 1. 轴管理页面

轴管理页面提供了对控制卡所有轴的管理功能，包括：

- **轴状态列表**：显示所有轴的当前状态，包括位置、使能状态、回零状态等
- **轴参数编辑**：选中轴后，可在右侧PropertyGridView中编辑轴参数
- **轴控制面板**：提供使能、禁用、回零、停止、清除报警、设零点等常用操作
- **点动控制**：提供正向和负向点动控制

### 2. IO管理页面

IO管理页面提供了对控制卡数字输入输出的管理功能，包括：

- **数字输入监控**：显示所有数字输入的当前状态
- **数字输出控制**：显示和控制所有数字输出的状态

### 3. 卡参数页面

卡参数页面提供了对控制卡全局参数的配置功能，通过PropertyGridView实现参数的分类显示和编辑。

### 4. 扩展功能页面

扩展功能页面可由特定控制卡类型的实现类添加，用于提供该类型控制卡特有的功能。

## 使用方法

### 基本使用

```csharp
// 创建控制卡实例
CardBase card = new CardGTS();

// 创建控制卡视图
CardView cardView = new CardView();

// 设置关联的控制卡
cardView.SetCard(card);

// 获取UI控件并添加到界面
UserControl control = cardView.GetControl();
contentPanel.Content = control;
```

### 添加自定义Tab页

```csharp
// 创建自定义内容
UserControl customContent = new CustomControl();

// 添加到控制卡视图
cardView.AddCustomTab("自定义功能", customContent);
```

### 设置轴扩展内容

```csharp
// 创建轴扩展内容
UIElement axisExtension = CreateAxisExtensionContent();

// 设置到控制卡视图
cardView.SetAxisExtensionContent(axisExtension);
```

## 自定义和扩展

### 创建特定控制卡的配置界面

1. 创建特定控制卡类型的视图模型类，继承自CardViewModel
2. 创建特定控制卡类型的视图类，继承自CardView
3. 在视图类中重写CreateViewModel方法，返回特定控制卡类型的视图模型实例
4. 在视图类中重写GetSupportCardType方法，返回支持的控制卡类型

```csharp
// 特定控制卡类型的视图类
public class GTSCardView : CardView
{
    public override CardViewModel CreateViewModel(CardBase card)
    {
        return new GTSCardViewModel(card as CardGTS);
    }

    public override CardType GetSupportCardType()
    {
        return CardType.GTS;
    }
}
```

### 注册控制卡配置界面

在应用程序启动时，注册所有控制卡配置界面：

```csharp
// 注册PMAC控制卡配置界面
CardConfigControlFactory.RegisterCreator(CardType.PMAC, () => new PmacCardConfigControl());

// 注册GTS控制卡配置界面
CardConfigControlFactory.RegisterCreator(CardType.GTS, () => new GTSCardConfigControl());
```

## 界面样式说明

控制卡界面采用了现代化工业控制风格设计，主要特点包括：

1. **卡片式布局**：使用卡片式布局组织内容，提供清晰的视觉层次
2. **状态指示**：使用颜色和图标直观显示各种状态信息
3. **分组显示**：将相关功能分组显示，提高操作效率
4. **响应式设计**：界面可根据窗口大小自适应调整布局
5. **直观的操作反馈**：操作按钮提供明确的视觉反馈

## 最佳实践

1. **定期更新状态**：使用定时器定期更新轴状态和IO状态，确保显示的信息是最新的
2. **参数验证**：在修改参数时进行有效性验证，避免设置无效参数
3. **错误处理**：处理可能的异常情况，提供友好的错误提示
4. **资源释放**：在控件卸载时释放资源，如停止计时器、取消事件订阅等
5. **UI线程安全**：确保UI更新操作在UI线程中执行 