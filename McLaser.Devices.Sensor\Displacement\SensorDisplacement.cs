using McLaser.Device;
using System;
using System.ComponentModel;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Xml.Serialization;

namespace McLaser.Devices.Sensor.Displacement
{
    /// <summary>
    /// 位移传感器驱动实现
    /// 支持TCP/IP通信的激光位移传感器设备
    /// </summary>
    [Category("传感器")]
    [DisplayName("位移传感器")]
    [Serializable]
    public class SensorDisplacement : SensorBase
    {
        #region 私有字段

        /// <summary>
        /// TCP客户端
        /// </summary>
        private TcpClient tcpClient;

        /// <summary>
        /// 网络流
        /// </summary>
        private NetworkStream networkStream;

        /// <summary>
        /// 线程锁对象
        /// </summary>
        private static readonly object objLock = new object();

        /// <summary>
        /// 校准偏移量
        /// </summary>
        private double calibrationOffset = 0;

        /// <summary>
        /// 校准系数
        /// </summary>
        private double calibrationScale = 1.0;

        #endregion

        #region 属性

        /// <summary>
        /// IP地址
        /// </summary>
        [Category("位移传感器连接"), DisplayName("IP地址")]
        public string IPAddress { get; set; } = "*************";

        /// <summary>
        /// 端口号
        /// </summary>
        [Category("位移传感器连接"), DisplayName("端口")]
        public int Port { get; set; } = 10002;

        /// <summary>
        /// 连接超时时间(毫秒)
        /// </summary>
        [Category("位移传感器连接"), DisplayName("连接超时(ms)")]
        public int ConnectionTimeout { get; set; } = 5000;

        /// <summary>
        /// 测量模式
        /// </summary>
        [Category("位移传感器参数"), DisplayName("测量模式")]
        public DisplacementMode MeasurementMode { get; set; } = DisplacementMode.Distance;

        /// <summary>
        /// 激光功率等级
        /// </summary>
        [Category("位移传感器参数"), DisplayName("激光功率等级")]
        public int LaserPowerLevel { get; set; } = 50;

        /// <summary>
        /// 传感器类型
        /// </summary>
        [Category("位移传感器信息"), DisplayName("传感器类型")]
        public override SensorType SensorType => SensorType.Displacement;

        /// <summary>
        /// 测量单位
        /// </summary>
        [Category("位移传感器信息"), DisplayName("测量单位")]
        public override string Unit => "mm";

        /// <summary>
        /// 测量范围最小值
        /// </summary>
        [Category("位移传感器信息"), DisplayName("测量范围最小值")]
        public override double MinValue => 0.0;

        /// <summary>
        /// 测量范围最大值
        /// </summary>
        [Category("位移传感器信息"), DisplayName("测量范围最大值")]
        public override double MaxValue => 100.0;

        /// <summary>
        /// 测量精度
        /// </summary>
        [Category("位移传感器信息"), DisplayName("测量精度")]
        public override double Accuracy => 0.001;

        /// <summary>
        /// 传感器状态
        /// </summary>
        [Category("位移传感器状态"), DisplayName("传感器状态")]
        public override SensorStatus SensorStatus
        {
            get
            {
                if (Status is StatusDisplacement dispStatus)
                    return dispStatus.SensorStatus;
                return SensorStatus.Unknown;
            }
        }

        /// <summary>
        /// 设备状态
        /// </summary>
        [XmlIgnore, Browsable(false)]
        public override StatusBase Status { get; set; } = new StatusDisplacement();

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public SensorDisplacement()
        {
            Name = "位移传感器";
            DeviceType = DeviceType.Sensor;
        }

        #endregion

        #region 设备基本操作

        /// <summary>
        /// 打开设备连接
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool Open()
        {
            try
            {
                if (!IsEnabled) return false;
                if (Status.IsConnected) Close();

                // 创建TCP连接
                tcpClient = new TcpClient();
                var connectTask = tcpClient.ConnectAsync(IPAddress, Port);
                
                if (!connectTask.Wait(ConnectionTimeout))
                {
                    tcpClient?.Close();
                    return false;
                }

                networkStream = tcpClient.GetStream();
                Status.IsConnected = true;

                // 初始化传感器
                if (!InitializeSensor())
                {
                    Close();
                    return false;
                }

                if (Status is StatusDisplacement dispStatus)
                {
                    dispStatus.SensorStatus = SensorStatus.Ready;
                }

                return IsOpen();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"打开位移传感器异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查设备是否已打开
        /// </summary>
        /// <returns>是否已打开</returns>
        public override bool IsOpen()
        {
            return Status.IsConnected && tcpClient != null && tcpClient.Connected;
        }

        /// <summary>
        /// 关闭设备连接
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool Close()
        {
            try
            {
                // 停止采样
                StopSampling();

                // 关闭网络连接
                networkStream?.Close();
                tcpClient?.Close();

                Status.IsConnected = false;
                if (Status is StatusDisplacement dispStatus)
                {
                    dispStatus.SensorStatus = SensorStatus.Offline;
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"关闭位移传感器异常：{ex.Message}");
                return false;
            }
        }

        #endregion

        #region 传感器操作

        /// <summary>
        /// 读取传感器值
        /// </summary>
        /// <param name="value">测量值</param>
        /// <returns>是否成功</returns>
        public override bool ReadValue(ref double value)
        {
            try
            {
                if (!IsOpen()) return false;

                string command = "READ_DISTANCE\r\n";
                string response = string.Empty;

                if (SendCommand(command, out response))
                {
                    if (double.TryParse(response.Trim(), out double rawValue))
                    {
                        // 应用校准
                        value = ApplyCalibration(rawValue);
                        CurrentValue = value;

                        if (Status is StatusDisplacement dispStatus)
                        {
                            dispStatus.UpdateValue(value);
                        }

                        return true;
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"读取位移传感器值异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 读取多个传感器值
        /// </summary>
        /// <param name="values">测量值数组</param>
        /// <param name="count">读取数量</param>
        /// <returns>是否成功</returns>
        public override bool ReadValues(ref double[] values, int count)
        {
            try
            {
                if (!IsOpen() || count <= 0) return false;

                string command = $"READ_MULTI {count}\r\n";
                string response = string.Empty;

                if (SendCommand(command, out response))
                {
                    string[] parts = response.Split(',');
                    if (parts.Length >= count)
                    {
                        values = new double[count];
                        for (int i = 0; i < count; i++)
                        {
                            if (double.TryParse(parts[i].Trim(), out double rawValue))
                            {
                                values[i] = ApplyCalibration(rawValue);
                            }
                            else
                            {
                                return false;
                            }
                        }
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"读取位移传感器多个值异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 校准传感器
        /// </summary>
        /// <param name="referenceValue">参考值</param>
        /// <returns>是否成功</returns>
        public override bool Calibrate(double referenceValue)
        {
            try
            {
                if (!IsOpen()) return false;

                // 读取当前原始值
                double currentValue = 0;
                if (!ReadRawValue(ref currentValue))
                    return false;

                // 计算校准偏移量
                calibrationOffset = referenceValue - currentValue;
                IsCalibrated = true;

                if (Status is StatusDisplacement dispStatus)
                {
                    dispStatus.UpdateCalibrationStatus(true);
                }

                OnCalibrationCompleted(CalibrationType.TwoPoint, true, $"校准完成，偏移量: {calibrationOffset:F6}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"校准位移传感器异常：{ex.Message}");
                OnCalibrationCompleted(CalibrationType.TwoPoint, false, $"校准失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 零点校准
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool ZeroCalibration()
        {
            return Calibrate(0.0);
        }

        /// <summary>
        /// 满量程校准
        /// </summary>
        /// <param name="fullScaleValue">满量程值</param>
        /// <returns>是否成功</returns>
        public override bool FullScaleCalibration(double fullScaleValue)
        {
            try
            {
                if (!IsOpen()) return false;

                // 读取当前原始值
                double currentValue = 0;
                if (!ReadRawValue(ref currentValue))
                    return false;

                // 计算校准系数
                if (Math.Abs(currentValue) > 0.001)
                {
                    calibrationScale = fullScaleValue / currentValue;
                    IsCalibrated = true;

                    if (Status is StatusDisplacement dispStatus)
                    {
                        dispStatus.UpdateCalibrationStatus(true);
                    }

                    OnCalibrationCompleted(CalibrationType.FullScale, true, $"满量程校准完成，系数: {calibrationScale:F6}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"满量程校准位移传感器异常：{ex.Message}");
                OnCalibrationCompleted(CalibrationType.FullScale, false, $"满量程校准失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 重置校准
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool ResetCalibration()
        {
            try
            {
                calibrationOffset = 0;
                calibrationScale = 1.0;
                IsCalibrated = false;

                if (Status is StatusDisplacement dispStatus)
                {
                    dispStatus.UpdateCalibrationStatus(false);
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"重置位移传感器校准异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 传感器自检
        /// </summary>
        /// <returns>自检结果</returns>
        public override bool SelfTest()
        {
            try
            {
                if (!IsOpen()) return false;

                string command = "SELF_TEST\r\n";
                string response = string.Empty;

                if (SendCommand(command, out response))
                {
                    return response.Trim().ToUpper().Contains("PASS");
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"位移传感器自检异常：{ex.Message}");
                return false;
            }
        }

        #endregion

        #region 特有方法

        /// <summary>
        /// 设置激光功率等级
        /// </summary>
        /// <param name="powerLevel">功率等级(0-100)</param>
        /// <returns>是否成功</returns>
        public bool SetLaserPowerLevel(int powerLevel)
        {
            try
            {
                if (!IsOpen()) return false;

                powerLevel = Math.Max(0, Math.Min(100, powerLevel));
                string command = $"SET_LASER_POWER {powerLevel}\r\n";
                
                if (SendCommand(command))
                {
                    LaserPowerLevel = powerLevel;
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置位移传感器激光功率异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置测量模式
        /// </summary>
        /// <param name="mode">测量模式</param>
        /// <returns>是否成功</returns>
        public bool SetMeasurementMode(DisplacementMode mode)
        {
            try
            {
                if (!IsOpen()) return false;

                string command = $"SET_MODE {mode}\r\n";
                
                if (SendCommand(command))
                {
                    MeasurementMode = mode;
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置位移传感器测量模式异常：{ex.Message}");
                return false;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化传感器
        /// </summary>
        /// <returns>是否成功</returns>
        private bool InitializeSensor()
        {
            try
            {
                // 发送初始化命令
                string command = "INIT\r\n";
                string response = string.Empty;

                if (SendCommand(command, out response))
                {
                    if (response.Trim().ToUpper().Contains("OK"))
                    {
                        // 设置默认参数
                        SetLaserPowerLevel(LaserPowerLevel);
                        SetMeasurementMode(MeasurementMode);
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化位移传感器异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 发送命令
        /// </summary>
        /// <param name="command">命令字符串</param>
        /// <returns>是否成功</returns>
        private bool SendCommand(string command)
        {
            string response;
            return SendCommand(command, out response);
        }

        /// <summary>
        /// 发送命令并获取响应
        /// </summary>
        /// <param name="command">命令字符串</param>
        /// <param name="response">响应字符串</param>
        /// <returns>是否成功</returns>
        private bool SendCommand(string command, out string response)
        {
            response = string.Empty;
            try
            {
                lock (objLock)
                {
                    if (networkStream == null || !networkStream.CanWrite)
                        return false;

                    // 发送命令
                    byte[] data = Encoding.ASCII.GetBytes(command);
                    networkStream.Write(data, 0, data.Length);

                    // 读取响应
                    byte[] buffer = new byte[1024];
                    int bytesRead = networkStream.Read(buffer, 0, buffer.Length);
                    response = Encoding.ASCII.GetString(buffer, 0, bytesRead);

                    return true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"位移传感器发送命令异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 读取原始值（未校准）
        /// </summary>
        /// <param name="value">原始值</param>
        /// <returns>是否成功</returns>
        private bool ReadRawValue(ref double value)
        {
            try
            {
                string command = "READ_RAW\r\n";
                string response = string.Empty;

                if (SendCommand(command, out response))
                {
                    return double.TryParse(response.Trim(), out value);
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"读取位移传感器原始值异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 应用校准
        /// </summary>
        /// <param name="rawValue">原始值</param>
        /// <returns>校准后的值</returns>
        private double ApplyCalibration(double rawValue)
        {
            return (rawValue * calibrationScale) + calibrationOffset;
        }

        #endregion
    }

    /// <summary>
    /// 位移测量模式枚举
    /// </summary>
    public enum DisplacementMode
    {
        /// <summary>
        /// 距离测量
        /// </summary>
        Distance,
        
        /// <summary>
        /// 厚度测量
        /// </summary>
        Thickness,
        
        /// <summary>
        /// 高度测量
        /// </summary>
        Height
    }
}
