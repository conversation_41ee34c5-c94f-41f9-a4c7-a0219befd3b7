using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using McLaser.Core.Plugins;

namespace McLaser.Plugins.Samples
{
    /// <summary>
    /// 示例日志插件
    /// 演示文件日志记录功能
    /// </summary>
    [PluginMetadata(
        Id = "McLaser.Plugins.Logger",
        Name = "示例日志记录器",
        Version = "1.0.0",
        Description = "提供文件日志记录功能的示例插件",
        Author = "McLaser Team",
        Category = "系统"
    )]
    public class LoggerPlugin : PluginBase
    {
        #region 私有字段

        private string _logFilePath = "";
        private readonly object _lockObject = new object();
        private int _logCount = 0;
        private Timer _flushTimer;
        private readonly List<string> _logBuffer = new List<string>();

        #endregion

        #region 属性

        /// <summary>
        /// 日志文件路径
        /// </summary>
        public string LogFilePath => _logFilePath;

        /// <summary>
        /// 日志记录数量
        /// </summary>
        public int LogCount => _logCount;

        /// <summary>
        /// 是否启用缓冲
        /// </summary>
        public bool BufferingEnabled { get; set; } = true;

        /// <summary>
        /// 缓冲刷新间隔（毫秒）
        /// </summary>
        public int FlushInterval { get; set; } = 5000;

        #endregion

        #region 插件生命周期

        /// <summary>
        /// 初始化插件
        /// </summary>
        public override async Task InitializeAsync(IPluginContext context, CancellationToken cancellationToken = default)
        {
            await base.InitializeAsync(context, cancellationToken);
            
            OnStatusChanged(PluginStatus.Initializing);
            
            // 创建日志文件路径
            var logDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "McLaser", "Logs");
            if (!Directory.Exists(logDirectory))
            {
                Directory.CreateDirectory(logDirectory);
            }
            
            _logFilePath = Path.Combine(logDirectory, $"Plugin_{DateTime.Now:yyyyMMdd_HHmmss}.log");
            
            // 写入初始日志
            await WriteLogAsync("日志插件初始化", LogLevel.Info);
            
            OnStatusChanged(PluginStatus.Initialized);
            await WriteLogAsync("日志插件初始化完成", LogLevel.Info);
        }

        /// <summary>
        /// 启动插件
        /// </summary>
        public override async Task StartAsync(CancellationToken cancellationToken = default)
        {
            await base.StartAsync(cancellationToken);
            
            OnStatusChanged(PluginStatus.Starting);
            
            // 启动缓冲刷新定时器
            if (BufferingEnabled)
            {
                _flushTimer = new Timer(FlushBuffer, null, FlushInterval, FlushInterval);
            }
            
            OnStatusChanged(PluginStatus.Running);
            await WriteLogAsync("日志插件已启动", LogLevel.Info);
        }

        /// <summary>
        /// 停止插件
        /// </summary>
        public override async Task StopAsync(CancellationToken cancellationToken = default)
        {
            OnStatusChanged(PluginStatus.Stopping);
            
            // 停止定时器
            if (_flushTimer != null)
            {
                _flushTimer.Dispose();
                _flushTimer = null;
            }
            
            // 刷新缓冲区
            FlushBuffer(null);
            
            await WriteLogAsync($"日志插件已停止，共记录 {_logCount} 条日志", LogLevel.Info);
            
            await base.StopAsync(cancellationToken);
            OnStatusChanged(PluginStatus.Stopped);
        }

        #endregion

        #region 日志功能

        /// <summary>
        /// 写入日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="level">日志级别</param>
        /// <param name="source">日志来源</param>
        public async Task WriteLogAsync(string message, LogLevel level = LogLevel.Info, string source = "LoggerPlugin")
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                var logEntry = $"[{timestamp}] [{level}] [{source}] {message}";
                
                if (BufferingEnabled)
                {
                    lock (_lockObject)
                    {
                        _logBuffer.Add(logEntry);
                        _logCount++;
                    }
                }
                else
                {
                    using (var writer = new StreamWriter(_logFilePath, true))
                    {
                        await writer.WriteLineAsync(logEntry);
                    }
                    Interlocked.Increment(ref _logCount);
                }
                
                // 输出到调试窗口
                System.Diagnostics.Debug.WriteLine($"[LoggerPlugin] {logEntry}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[LoggerPlugin] 写入日志失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 批量写入日志
        /// </summary>
        /// <param name="messages">日志消息列表</param>
        /// <param name="level">日志级别</param>
        /// <param name="source">日志来源</param>
        public async Task WriteLogsAsync(IEnumerable<string> messages, LogLevel level = LogLevel.Info, string source = "LoggerPlugin")
        {
            foreach (var message in messages)
            {
                await WriteLogAsync(message, level, source);
            }
        }

        /// <summary>
        /// 刷新缓冲区
        /// </summary>
        private void FlushBuffer(object state)
        {
            try
            {
                List<string> logsToWrite;
                lock (_lockObject)
                {
                    if (_logBuffer.Count == 0) return;
                    
                    logsToWrite = new List<string>(_logBuffer);
                    _logBuffer.Clear();
                }
                
                // 写入文件
                File.AppendAllLines(_logFilePath, logsToWrite);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[LoggerPlugin] 刷新缓冲区失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 手动刷新缓冲区
        /// </summary>
        public void Flush()
        {
            FlushBuffer(null);
        }

        /// <summary>
        /// 清空日志文件
        /// </summary>
        public async Task ClearLogAsync()
        {
            try
            {
                if (File.Exists(_logFilePath))
                {
                    using (var writer = new StreamWriter(_logFilePath, false))
                    {
                        await writer.WriteAsync("");
                    }
                    _logCount = 0;
                    await WriteLogAsync("日志文件已清空", LogLevel.Info);
                }
            }
            catch (Exception ex)
            {
                await WriteLogAsync($"清空日志文件失败: {ex.Message}", LogLevel.Error);
            }
        }

        /// <summary>
        /// 读取日志内容
        /// </summary>
        /// <param name="maxLines">最大行数</param>
        /// <returns>日志内容</returns>
        public async Task<string[]> ReadLogAsync(int maxLines = 100)
        {
            try
            {
                if (!File.Exists(_logFilePath))
                {
                    return Array.Empty<string>();
                }
                
                var lines = File.ReadAllLines(_logFilePath);
                if (lines.Length <= maxLines)
                {
                    return lines;
                }
                
                // 返回最后的指定行数
                var result = new string[maxLines];
                Array.Copy(lines, lines.Length - maxLines, result, 0, maxLines);
                return result;
            }
            catch (Exception ex)
            {
                await WriteLogAsync($"读取日志失败: {ex.Message}", LogLevel.Error);
                return Array.Empty<string>();
            }
        }

        /// <summary>
        /// 获取日志统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public Dictionary<string, object> GetStatistics()
        {
            var fileSize = 0L;
            try
            {
                if (File.Exists(_logFilePath))
                {
                    fileSize = new FileInfo(_logFilePath).Length;
                }
            }
            catch
            {
                // 忽略错误
            }
            
            return new Dictionary<string, object>
            {
                { "LogCount", _logCount },
                { "LogFilePath", _logFilePath },
                { "FileSize", fileSize },
                { "FileSizeFormatted", FormatFileSize(fileSize) },
                { "BufferingEnabled", BufferingEnabled },
                { "FlushInterval", FlushInterval },
                { "BufferCount", _logBuffer.Count },
                { "Status", Status.ToString() },
                { "StartTime", StartTime }
            };
        }

        #endregion

        #region 配置管理

        /// <summary>
        /// 获取默认配置
        /// </summary>
        public override Dictionary<string, object> GetDefaultConfiguration()
        {
            return new Dictionary<string, object>
            {
                { "BufferingEnabled", true },
                { "FlushInterval", 5000 },
                { "MaxLogFileSize", 10 * 1024 * 1024 }, // 10MB
                { "LogLevel", "Info" },
                { "DateFormat", "yyyy-MM-dd HH:mm:ss.fff" }
            };
        }

        /// <summary>
        /// 验证配置
        /// </summary>
        public override bool ValidateConfiguration(Dictionary<string, object> configuration)
        {
            try
            {
                if (configuration.TryGetValue("FlushInterval", out var interval))
                {
                    var intervalValue = Convert.ToInt32(interval);
                    if (intervalValue < 1000 || intervalValue > 60000)
                    {
                        return false;
                    }
                }
                
                if (configuration.TryGetValue("MaxLogFileSize", out var maxSize))
                {
                    var maxSizeValue = Convert.ToInt64(maxSize);
                    if (maxSizeValue < 1024 || maxSizeValue > 100 * 1024 * 1024)
                    {
                        return false;
                    }
                }
                
                return true;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 格式化文件大小
        /// </summary>
        private static string FormatFileSize(long bytes)
        {
            if (bytes < 1024) return $"{bytes} B";
            if (bytes < 1024 * 1024) return $"{bytes / 1024.0:F1} KB";
            if (bytes < 1024 * 1024 * 1024) return $"{bytes / (1024.0 * 1024.0):F1} MB";
            return $"{bytes / (1024.0 * 1024.0 * 1024.0):F1} GB";
        }

        #endregion

        #region 资源清理

        /// <summary>
        /// 释放资源
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                if (_flushTimer != null)
                    _flushTimer.Dispose();
                FlushBuffer(null);
            }
            base.Dispose(disposing);
        }

        #endregion
    }
}
