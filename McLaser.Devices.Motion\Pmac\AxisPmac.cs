using System;
using System.ComponentModel;
using System.Xml.Serialization;
using McLaser.Device;
using McLaser.Devices.Motion.Pmac;

namespace McLaser.Devices.Motion
{

    [Serializable]
    public class AxisPmac : AxisBase
    {


        [Category("回零参数"), DisplayName("回零程序名")]
        public string HomeProgram { get; set; } = "";


        [Category("回零参数"), DisplayName("回零完成标志")]
        public string HomeOK { get; set; } = "";

        public AxisPmacStatus AxisPmacStatus { get; set; } = new AxisPmacStatus();

        public AxisPmac()
        {
            Name = "PMAC轴";
        }


        public AxisPmac(int id, string name) : this()
        {
            ID = id;
            Name = name;
        }

        uint[] Masks = {
        0x80000000, // TriggerMove
        0x40000000, // HomeInProgress
        0x20000000, // MinusLimit
        0x10000000, // PlusLimit
        0x08000000, // FeWarn
        0x04000000, // FeFatal
        0x02000000, // LimitStop
        0x01000000, // AmpFault
        0x00800000, // SoftMinusLimit
        0x00400000, // SoftPlusLimit
        0x00200000, // I2tFault
        0x00100000, // TriggerNotFound
        0x00080000, // AmpWarn
        0x00040000, // EncLoss
        0x00020000, // AuxFault
        0x00010000, // Reserved (Future Use)
        0x00008000, // HomeComplete
        0x00004000, // DesVelZero
        0x00002000, // ClosedLoop
        0x00001000, // AmpEna
        0x00000800, // InPos
        0x00000400, // Reserved (Future Use)
        0x00000200, // BlockRequest
        0x00000100, // PhaseFound
        0x00000080, // TriggerSpeedSel
        0x00000040, // GantryHomed
        0x00000020, // SpindleMotor (bit 1)
        0x00000010, // SpindleMotor (bit 0)
        0x0000000F  // Reserved (Future Use) -- Bits 0-3
    };

        readonly uint[] Masks1 = {
        0x80000000, // Csolve
        0x40000000, // SoftLimit
        0x20000000, // DacLimit
        0x10000000, // BlDir
        0x08000000, // SoftLimitDir
    };

        public void ParseStatus(string strStatus)
        {
            var status = Convert.ToUInt32(strStatus.Substring(0, 8), 16);
            var status2 = Convert.ToUInt32(strStatus.Substring(8), 16);

            //高8位
            AxisPmacStatus.TriggerMove = (status & Masks[0]) != 0;
            AxisPmacStatus.HomeInProgress = (status & Masks[1]) != 0;
            AxisPmacStatus.MinusLimit = (status & Masks[2]) != 0;
            AxisPmacStatus.PlusLimit = (status & Masks[3]) != 0;
            AxisPmacStatus.FeWarn = (status & Masks[4]) != 0;
            AxisPmacStatus.FeFatal = (status & Masks[5]) != 0;
            AxisPmacStatus.LimitStop = (status & Masks[6]) != 0;
            AxisPmacStatus.AmpFault = (status & Masks[7]) != 0;
            AxisPmacStatus.SoftMinusLimit = (status & Masks[8]) != 0;
            AxisPmacStatus.SoftPlusLimit = (status & Masks[9]) != 0;
            AxisPmacStatus.I2tFault = (status & Masks[10]) != 0;
            AxisPmacStatus.TriggerNotFound = (status & Masks[11]) != 0;
            AxisPmacStatus.AmpWarn = (status & Masks[12]) != 0;
            AxisPmacStatus.EncLoss = (status & Masks[13]) != 0;
            AxisPmacStatus.AuxFault = (status & Masks[14]) != 0;
            AxisPmacStatus.Reserved1 = (status & Masks[15]) != 0;
            AxisPmacStatus.HomeComplete = (status & Masks[16]) != 0;
            AxisPmacStatus.DesVelZero = (status & Masks[17]) != 0;
            AxisPmacStatus.ClosedLoop = (status & Masks[18]) != 0;
            AxisPmacStatus.AmpEna = (status & Masks[19]) != 0;
            AxisPmacStatus.InPos = (status & Masks[20]) != 0;
            AxisPmacStatus.Reserved2 = (status & Masks[21]) != 0;
            AxisPmacStatus.BlockRequest = (status & Masks[22]) != 0;
            AxisPmacStatus.PhaseFound = (status & Masks[23]) != 0;
            AxisPmacStatus.TriggerSpeedSel = (status & Masks[24]) != 0;
            AxisPmacStatus.GantryHomed = (status & Masks[25]) != 0;
            AxisPmacStatus.SpindleMotorBit1 = (status & Masks[26]) != 0;
            AxisPmacStatus.SpindleMotorBit0 = (status & Masks[27]) != 0;
            AxisPmacStatus.Reserved3 = (status & Masks[28]) != 0;

            //低8位
            AxisPmacStatus.Csolve = (status2 & Masks1[0]) != 0;
            AxisPmacStatus.SoftLimit = (status2 & Masks1[1]) != 0;
            AxisPmacStatus.DacLimit = (status2 & Masks1[2]) != 0;
            AxisPmacStatus.BlDir = (status2 & Masks1[3]) != 0;
            AxisPmacStatus.SoftLimitDir = (status2 & Masks1[4]) != 0;
        }

    }
}
