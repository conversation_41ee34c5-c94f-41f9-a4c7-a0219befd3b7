﻿#pragma checksum "..\..\..\..\..\Base\Motion\Views\CardConfigTestWindow.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "32D08A465513AAF8D23650562D1A4798C2370AC0BEF569DBE101A0717C97FB80"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using McLaser.Devices.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace McLaser.Devices.Views {
    
    
    /// <summary>
    /// CardConfigTestWindow
    /// </summary>
    public partial class CardConfigTestWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 17 "..\..\..\..\..\Base\Motion\Views\CardConfigTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnLoadBasic;
        
        #line default
        #line hidden
        
        
        #line 18 "..\..\..\..\..\Base\Motion\Views\CardConfigTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnLoadPmac;
        
        #line default
        #line hidden
        
        
        #line 19 "..\..\..\..\..\Base\Motion\Views\CardConfigTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnLoadGTS;
        
        #line default
        #line hidden
        
        
        #line 21 "..\..\..\..\..\Base\Motion\Views\CardConfigTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddAxis;
        
        #line default
        #line hidden
        
        
        #line 26 "..\..\..\..\..\Base\Motion\Views\CardConfigTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContentControl ConfigContainer;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/McLaser.Device;component/base/motion/views/cardconfigtestwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Base\Motion\Views\CardConfigTestWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BtnLoadBasic = ((System.Windows.Controls.Button)(target));
            
            #line 17 "..\..\..\..\..\Base\Motion\Views\CardConfigTestWindow.xaml"
            this.BtnLoadBasic.Click += new System.Windows.RoutedEventHandler(this.BtnLoadBasic_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.BtnLoadPmac = ((System.Windows.Controls.Button)(target));
            
            #line 18 "..\..\..\..\..\Base\Motion\Views\CardConfigTestWindow.xaml"
            this.BtnLoadPmac.Click += new System.Windows.RoutedEventHandler(this.BtnLoadPmac_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.BtnLoadGTS = ((System.Windows.Controls.Button)(target));
            
            #line 19 "..\..\..\..\..\Base\Motion\Views\CardConfigTestWindow.xaml"
            this.BtnLoadGTS.Click += new System.Windows.RoutedEventHandler(this.BtnLoadGTS_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.BtnAddAxis = ((System.Windows.Controls.Button)(target));
            
            #line 21 "..\..\..\..\..\Base\Motion\Views\CardConfigTestWindow.xaml"
            this.BtnAddAxis.Click += new System.Windows.RoutedEventHandler(this.BtnAddAxis_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ConfigContainer = ((System.Windows.Controls.ContentControl)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

