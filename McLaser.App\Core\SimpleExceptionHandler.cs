using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using McLaser.Core.ExceptionHandling;
using McLaser.App.Events;

namespace McLaser.App.Core
{
    /// <summary>
    /// 简单异常处理器实现
    /// 用于演示异常处理功能
    /// </summary>
    public class SimpleExceptionHandler : IExceptionHandler
    {
        private readonly Dictionary<ExceptionLevel, List<Action<Exception>>> _handlers;

        #region IExceptionHandler 实现

        /// <summary>
        /// 处理器名称
        /// </summary>
        public string Name => "简单异常处理器";

        /// <summary>
        /// 处理器优先级
        /// </summary>
        public int Priority => 0;

        /// <summary>
        /// 支持的异常类型
        /// </summary>
        public Type[] SupportedExceptionTypes => new[] { typeof(Exception) };

        #endregion

        /// <summary>
        /// 构造函数
        /// </summary>
        public SimpleExceptionHandler()
        {
            _handlers = new Dictionary<ExceptionLevel, List<Action<Exception>>>();
        }

        /// <summary>
        /// 注册异常处理器
        /// </summary>
        /// <param name="level">异常级别</param>
        /// <param name="handler">处理器</param>
        public void RegisterHandler(ExceptionLevel level, Action<Exception> handler)
        {
            if (!_handlers.ContainsKey(level))
            {
                _handlers[level] = new List<Action<Exception>>();
            }
            _handlers[level].Add(handler);
        }

        /// <summary>
        /// 处理异常
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="level">异常级别</param>
        public void HandleException(Exception exception, ExceptionLevel level)
        {
            if (_handlers.ContainsKey(level))
            {
                foreach (var handler in _handlers[level])
                {
                    try
                    {
                        handler(exception);
                    }
                    catch (Exception handlerException)
                    {
                        // 处理器本身出现异常时的处理
                        Console.WriteLine($"异常处理器出现异常: {handlerException.Message}");
                    }
                }
            }
        }

        /// <summary>
        /// 取消注册处理器
        /// </summary>
        /// <param name="level">异常级别</param>
        /// <param name="handler">处理器</param>
        public void UnregisterHandler(ExceptionLevel level, Action<Exception> handler)
        {
            if (_handlers.ContainsKey(level))
            {
                _handlers[level].Remove(handler);
            }
        }

        /// <summary>
        /// 清理所有处理器
        /// </summary>
        public void ClearHandlers()
        {
            _handlers.Clear();
        }

        /// <summary>
        /// 获取处理器数量
        /// </summary>
        /// <param name="level">异常级别</param>
        /// <returns>处理器数量</returns>
        public int GetHandlerCount(ExceptionLevel level)
        {
            return _handlers.ContainsKey(level) ? _handlers[level].Count : 0;
        }

        /// <summary>
        /// 获取总处理器数量
        /// </summary>
        /// <returns>总处理器数量</returns>
        public int GetTotalHandlerCount()
        {
            int total = 0;
            foreach (var handlers in _handlers.Values)
            {
                total += handlers.Count;
            }
            return total;
        }

        #region IExceptionHandler 接口实现

        /// <summary>
        /// 是否可以处理指定异常
        /// </summary>
        /// <param name="exception">异常</param>
        /// <returns>是否可以处理</returns>
        public bool CanHandle(Exception exception)
        {
            return true; // 简单实现，处理所有异常
        }

        /// <summary>
        /// 处理异常
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="context">异常上下文</param>
        /// <returns>处理结果</returns>
        public ExceptionHandlingResult Handle(Exception exception, ExceptionContext context)
        {
            try
            {
                // 根据异常类型确定级别
                var level = GetExceptionLevel(exception);

                // 调用自定义处理逻辑
                HandleException(exception, level);

                return new ExceptionHandlingResult
                {
                    IsHandled = true,
                    ShouldRethrow = false,
                    UserMessage = $"异常已被 {Name} 处理",
                    HandlerName = Name
                };
            }
            catch (Exception handlerException)
            {
                return new ExceptionHandlingResult
                {
                    IsHandled = false,
                    ShouldRethrow = true,
                    TechnicalMessage = $"处理器异常: {handlerException.Message}",
                    HandlerName = Name
                };
            }
        }

        /// <summary>
        /// 异步处理异常
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="context">异常上下文</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>处理任务</returns>
        public Task<ExceptionHandlingResult> HandleAsync(Exception exception, ExceptionContext context, CancellationToken cancellationToken = default)
        {
            return Task.FromResult(Handle(exception, context));
        }

        /// <summary>
        /// 根据异常类型获取异常级别
        /// </summary>
        /// <param name="exception">异常</param>
        /// <returns>异常级别</returns>
        private ExceptionLevel GetExceptionLevel(Exception exception)
        {
            return exception switch
            {
                InformationException => ExceptionLevel.Info,
                WarningException => ExceptionLevel.Warning,
                ErrorException => ExceptionLevel.Error,
                CriticalException => ExceptionLevel.Critical,
                _ => ExceptionLevel.Error
            };
        }

        #endregion
    }

    /// <summary>
    /// 信息异常
    /// </summary>
    public class InformationException : Exception
    {
        public InformationException(string message) : base(message) { }
        public InformationException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// 警告异常
    /// </summary>
    public class WarningException : Exception
    {
        public WarningException(string message) : base(message) { }
        public WarningException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// 错误异常
    /// </summary>
    public class ErrorException : Exception
    {
        public ErrorException(string message) : base(message) { }
        public ErrorException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// 严重异常
    /// </summary>
    public class CriticalException : Exception
    {
        public CriticalException(string message) : base(message) { }
        public CriticalException(string message, Exception innerException) : base(message, innerException) { }
    }
}
