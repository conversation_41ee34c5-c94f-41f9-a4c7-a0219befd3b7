<UserControl x:Class="McLaser.Device.Views.DeviceManagerControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:McLaser.Device.Converters"
             xmlns:device="clr-namespace:McLaser.Device"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000"
             Loaded="DeviceManagerControl_Loaded"
             Unloaded="DeviceManagerControl_Unloaded">

    <UserControl.Resources>
        <!-- 转换器 -->
        <local:BoolToRotateTransformConverter x:Key="BoolToRotateTransformConverter"/>

        <!-- 现代工业设计样式 -->

        <!-- 主要按钮样式 -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF2D3748"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#FF4A5568"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FF4A5568"/>
                                <Setter Property="BorderBrush" Value="#FF718096"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#FF1A202C"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#FFE2E8F0"/>
                                <Setter Property="Foreground" Value="#FFA0AEC0"/>
                                <Setter Property="BorderBrush" Value="#FFCBD5E0"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 危险操作按钮样式 -->
        <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#FFE53E3E"/>
            <Setter Property="BorderBrush" Value="#FFC53030"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FFC53030"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#FF9B2C2C"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 成功操作按钮样式 -->
        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#FF38A169"/>
            <Setter Property="BorderBrush" Value="#FF2F855A"/>
        </Style>

        <!-- 信息按钮样式 -->
        <Style x:Key="InfoButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#FF3182CE"/>
            <Setter Property="BorderBrush" Value="#FF2C5282"/>
        </Style>

        <!-- 面板样式 -->
        <Style x:Key="ModernPanelStyle" TargetType="Border">
            <Setter Property="Background" Value="#FFF7FAFC"/>
            <Setter Property="BorderBrush" Value="#FFE2E8F0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="6"/>
        </Style>

        <!-- 标题样式 -->
        <Style x:Key="PanelTitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#FF2D3748"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
        </Style>

        <!-- 设备项样式 -->
        <Style x:Key="DeviceItemStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#FFE2E8F0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="4"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Padding" Value="8,6"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#FFF7FAFC"/>
                    <Setter Property="BorderBrush" Value="#FFCBD5E0"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- TreeView样式 -->
        <Style x:Key="ModernTreeViewStyle" TargetType="TreeView">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="11"/>
        </Style>

        <!-- TreeViewItem样式 -->
        <Style x:Key="ModernTreeViewItemStyle" TargetType="TreeViewItem">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="0"/>
            <Setter Property="Margin" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TreeViewItem">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- 项目内容 -->
                            <Border Grid.Row="0"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    Padding="{TemplateBinding Padding}">
                                <ContentPresenter Name="PART_Header"
                                                  ContentSource="Header"
                                                  HorizontalAlignment="Stretch"/>
                            </Border>

                            <!-- 子项容器 -->
                            <ItemsPresenter Grid.Row="1"
                                            Name="ItemsHost"
                                            Visibility="Collapsed"/>
                        </Grid>

                        <ControlTemplate.Triggers>
                            <!-- 展开状态 -->
                            <Trigger Property="IsExpanded" Value="True">
                                <Setter TargetName="ItemsHost" Property="Visibility" Value="Visible"/>
                            </Trigger>

                            <!-- 选中状态 -->
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Background" Value="#FF3182CE"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>

                            <!-- 鼠标悬停状态 -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FFF7FAFC"/>
                            </Trigger>

                            <!-- 选中且鼠标悬停 -->
                            <MultiTrigger>
                                <MultiTrigger.Conditions>
                                    <Condition Property="IsSelected" Value="True"/>
                                    <Condition Property="IsMouseOver" Value="True"/>
                                </MultiTrigger.Conditions>
                                <Setter Property="Background" Value="#FF2C5282"/>
                                <Setter Property="Foreground" Value="White"/>
                            </MultiTrigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid Background="#FFF8FAFC">
        <Grid.ColumnDefinitions>
            <ColumnDefinition x:Name="DeviceTypeColumn" Width="280"/>
            <ColumnDefinition Width="8"/>
            <ColumnDefinition Width="380"/>
            <ColumnDefinition Width="8"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- 左侧设备类型面板（现代化设计） -->
        <Expander x:Name="DeviceTypeExpander"
                  Grid.Column="0"
                  IsExpanded="True"
                  ExpandDirection="Right"
                  Background="Transparent"
                  BorderThickness="0"
                  Expanded="DeviceTypeExpander_Expanded"
                  Collapsed="DeviceTypeExpander_Collapsed">
            <Expander.Header>
                <Border Background="#2196F3" 
                        CornerRadius="4" 
                        Width="32" 
                        Height="32" 
                        ToolTip="设备类型库">
                    <TextBlock Text="📚" 
                               FontSize="16" 
                               HorizontalAlignment="Center" 
                               VerticalAlignment="Center" 
                               Foreground="White"/>
                </Border>
            </Expander.Header>

            <Border Background="#F5F5F5" 
                    BorderBrush="#E0E0E0" 
                    BorderThickness="1" 
                    CornerRadius="8" 
                    Padding="16" 
                    Width="264">
                <Border.Effect>
                    <DropShadowEffect ShadowDepth="2" BlurRadius="8" Color="#20000000" Opacity="0.2"/>
                </Border.Effect>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 标题栏 -->
                    <Border Grid.Row="0" 
                            Background="#2196F3" 
                            CornerRadius="4" 
                            Padding="12,8" 
                            Margin="0,0,0,12">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📚" 
                                       FontSize="16" 
                                       Foreground="White" 
                                       VerticalAlignment="Center" 
                                       Margin="0,0,8,0"/>
                            <TextBlock Text="设备类型库" 
                                       FontSize="14" 
                                       FontWeight="SemiBold" 
                                       Foreground="White"/>
                        </StackPanel>
                    </Border>

                <!-- 设备类型列表 -->
                    <Border Grid.Row="1" 
                            Background="White" 
                            CornerRadius="6" 
                            Margin="0,0,0,12">
                        <ScrollViewer VerticalScrollBarVisibility="Auto" 
                                      Padding="2">
                    <TreeView ItemsSource="{Binding DeviceTypeCategories}"
                              Style="{StaticResource ModernTreeViewStyle}">
                        <TreeView.ItemContainerStyle>
                                    <Style TargetType="TreeViewItem">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Setter Property="BorderThickness" Value="0"/>
                                        <Setter Property="Padding" Value="4"/>
                                        <Setter Property="Margin" Value="0,2"/>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="TreeViewItem">
                                                    <Grid>
                                                        <Grid.RowDefinitions>
                                                            <RowDefinition Height="Auto"/>
                                                            <RowDefinition Height="*"/>
                                                        </Grid.RowDefinitions>

                                                        <!-- 项目内容 -->
                                                        <Border Grid.Row="0"
                                                                x:Name="ItemBorder"
                                                                Background="{TemplateBinding Background}"
                                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                                CornerRadius="4"
                                                                Padding="{TemplateBinding Padding}">
                                                            <ContentPresenter Name="PART_Header"
                                                                            ContentSource="Header"
                                                                            HorizontalAlignment="Stretch"/>
                                                        </Border>

                                                        <!-- 子项容器 -->
                                                        <ItemsPresenter Grid.Row="1"
                                                                        Name="ItemsHost"
                                                                        Margin="12,2,0,0"
                                                                        Visibility="Collapsed"/>
                                                    </Grid>

                                                    <ControlTemplate.Triggers>
                                                        <!-- 展开状态 -->
                                                        <Trigger Property="IsExpanded" Value="True">
                                                            <Setter TargetName="ItemsHost" Property="Visibility" Value="Visible"/>
                                                        </Trigger>

                                                        <!-- 选中状态 -->
                                                        <Trigger Property="IsSelected" Value="True">
                                                            <Setter TargetName="ItemBorder" Property="Background" Value="#E3F2FD"/>
                                                            <Setter TargetName="ItemBorder" Property="BorderBrush" Value="#2196F3"/>
                                                        </Trigger>

                                                        <!-- 鼠标悬停状态 -->
                                                        <Trigger Property="IsMouseOver" Value="True">
                                                            <Setter TargetName="ItemBorder" Property="Background" Value="#F5F9FF"/>
                                                        </Trigger>
                                                    </ControlTemplate.Triggers>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                        </TreeView.ItemContainerStyle>
                        <TreeView.ItemTemplate>
                            <HierarchicalDataTemplate ItemsSource="{Binding Items}">
                                        <Border Background="#F5F9FF" 
                                                BorderBrush="#BBDEFB" 
                                                BorderThickness="1" 
                                                CornerRadius="4" 
                                                Padding="8,6">
                                            <StackPanel Orientation="Horizontal">
                                                <Border Background="#2196F3" 
                                                        CornerRadius="4" 
                                                        Width="24" 
                                                        Height="24" 
                                                        Margin="0,0,8,0">
                                                    <TextBlock Text="{Binding CategoryIcon}" 
                                                               FontSize="14" 
                                                               Foreground="White" 
                                                               HorizontalAlignment="Center" 
                                                               VerticalAlignment="Center"/>
                                                </Border>
                                                <StackPanel>
                                                    <TextBlock Text="{Binding CategoryName}" 
                                                               FontSize="12" 
                                                               FontWeight="SemiBold" 
                                                               Foreground="#212121"/>
                                                    <TextBlock Text="{Binding DeviceCount, StringFormat='包含 {0} 种设备'}"
                                                               FontSize="10" 
                                                               Foreground="#757575" 
                                                               Margin="0,2,0,0"/>
                                                </StackPanel>
                                            </StackPanel>
                                        </Border>
                                        <HierarchicalDataTemplate.ItemTemplate>
                                            <DataTemplate>
                                                <Border Background="White" 
                                                        BorderBrush="#E0E0E0" 
                                                        BorderThickness="1" 
                                                        CornerRadius="4" 
                                                        Padding="8,6" 
                                                        Margin="0,2"
                                                        MouseLeftButtonDown="DeviceType_MouseLeftButtonDown"
                                                        MouseMove="DeviceType_MouseMove"
                                                        Cursor="Hand">
                                                    <Grid>
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="*"/>
                                                        </Grid.ColumnDefinitions>
                                                        
                                                        <Border Grid.Column="0" 
                                                                Background="#E3F2FD" 
                                                                CornerRadius="4" 
                                                                Width="28" 
                                                                Height="28" 
                                                                Margin="0,0,10,0">
                                                            <TextBlock Text="{Binding IconSource}" 
                                                                       FontSize="14" 
                                                                       Foreground="#2196F3" 
                                                                       HorizontalAlignment="Center" 
                                                                       VerticalAlignment="Center"/>
                                                        </Border>
                                                        
                                                        <StackPanel Grid.Column="1">
                                                            <TextBlock Text="{Binding DisplayName}" 
                                                                       FontSize="12" 
                                                                       FontWeight="Medium" 
                                                                       Foreground="#212121"/>
                                                            <TextBlock Text="{Binding Manufacturer}" 
                                                                       FontSize="10" 
                                                                       Foreground="#757575" 
                                                                       Margin="0,2,0,0"/>
                                                        </StackPanel>
                                                    </Grid>
                                        </Border>
                                    </DataTemplate>
                                </HierarchicalDataTemplate.ItemTemplate>
                            </HierarchicalDataTemplate>
                        </TreeView.ItemTemplate>
                    </TreeView>
                </ScrollViewer>
                    </Border>

                    <!-- 拖拽提示 -->
                    <Border Grid.Row="2" 
                            Background="#E8F5E9" 
                            BorderBrush="#A5D6A7" 
                            BorderThickness="1" 
                            CornerRadius="4" 
                            Padding="12,8">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" 
                                       Text="💡" 
                                       FontSize="18" 
                                       Margin="0,0,8,0" 
                                       VerticalAlignment="Center"/>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="拖拽提示" 
                                           FontSize="11" 
                                           FontWeight="SemiBold" 
                                           Foreground="#2E7D32"/>
                                <TextBlock Text="将设备类型拖拽到右侧区域即可添加设备"
                                           FontSize="10" 
                                           Foreground="#388E3C" 
                                           TextWrapping="Wrap" 
                                           Margin="0,2,0,0"/>
                    </StackPanel>
                        </Grid>
                </Border>
            </Grid>
        </Border>
        </Expander>

        <!-- 分隔线 -->
        <Rectangle Grid.Column="1" Fill="#FFE2E8F0" Width="1" HorizontalAlignment="Center"/>

        <!-- 中间设备分组区域 -->
        <Border Grid.Column="2" Style="{StaticResource ModernPanelStyle}" Padding="16">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 标题栏 -->
                <Grid Grid.Row="0" Margin="0,0,0,12">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="设备分组管理" Style="{StaticResource PanelTitleStyle}"/>

                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button Content="💾" FontSize="12" Padding="8,4" Margin="0,0,4,0"
                                Style="{StaticResource InfoButtonStyle}"
                                Command="{Binding SaveConfigurationCommand}"
                                ToolTip="保存配置"/>
                        <Button Content="📁" FontSize="12" Padding="8,4"
                                Style="{StaticResource ModernButtonStyle}"
                                Command="{Binding LoadConfigurationCommand}"
                                ToolTip="加载配置"/>
                    </StackPanel>
                </Grid>

                <!-- 设备列表 - 平铺显示所有设备 -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <ListView x:Name="DevicesListView"
                              ItemsSource="{Binding Devices}"
                              Background="Transparent"
                              BorderThickness="0"
                              AllowDrop="True"
                              Drop="DeviceGroup_Drop"
                              DragOver="DeviceGroup_DragOver"
                              SelectionChanged="DevicesListView_SelectionChanged">
                        <ListView.ItemContainerStyle>
                            <Style TargetType="ListViewItem">
                                <Setter Property="Margin" Value="3"/>
                                <Setter Property="Padding" Value="8"/>
                                <Setter Property="Background" Value="White"/>
                                <Setter Property="BorderThickness" Value="1"/>
                                <Setter Property="BorderBrush" Value="#FFE2E8F0"/>
                                <Setter Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect ShadowDepth="1" BlurRadius="4" Color="#20000000" Opacity="0.2"/>
                                    </Setter.Value>
                                </Setter>
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="ListViewItem">
                                            <Border
                                                x:Name="Border"
                                                Background="{TemplateBinding Background}"
                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                CornerRadius="4"
                                                Padding="{TemplateBinding Padding}">
                                                <ContentPresenter/>
                                </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsSelected" Value="True">
                                                    <Setter TargetName="Border" Property="Background" Value="#E3F2FD"/>
                                                    <Setter TargetName="Border" Property="BorderBrush" Value="#2196F3"/>
                                                </Trigger>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter TargetName="Border" Property="Background" Value="#F5F9FF"/>
                                                    <Setter TargetName="Border" Property="BorderBrush" Value="#90CAF9"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </ListView.ItemContainerStyle>
                        <ListView.ItemTemplate>
                                    <DataTemplate>
                                <Grid MouseRightButtonUp="DeviceItem_RightClick">
                                    <Grid.ContextMenu>
                                                <ContextMenu>
                                                    <MenuItem Header="删除设备"
                                                      Command="{Binding DataContext.RemoveSelectedDeviceCommand,
                                                                       RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                              CommandParameter="{Binding}">
                                                        <MenuItem.Icon>
                                                            <TextBlock Text="🗑️" FontSize="12"/>
                                                        </MenuItem.Icon>
                                                    </MenuItem>
                                                    <Separator/>
                                                    <MenuItem Header="连接设备"
                                                              Command="{Binding DataContext.ConnectDeviceCommand,
                                                                       RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                              CommandParameter="{Binding}">
                                                        <MenuItem.Icon>
                                                            <TextBlock Text="🔗" FontSize="12"/>
                                                        </MenuItem.Icon>
                                                    </MenuItem>
                                                    <MenuItem Header="断开设备"
                                                              Command="{Binding DataContext.DisconnectDeviceCommand,
                                                                       RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                              CommandParameter="{Binding}">
                                                        <MenuItem.Icon>
                                                            <TextBlock Text="🔌" FontSize="12"/>
                                                        </MenuItem.Icon>
                                                    </MenuItem>
                                                    <Separator/>
                                                    <MenuItem Header="设备属性" IsEnabled="False">
                                                        <MenuItem.Icon>
                                                            <TextBlock Text="⚙️" FontSize="12"/>
                                                        </MenuItem.Icon>
                                                    </MenuItem>
                                                </ContextMenu>
                                    </Grid.ContextMenu>

                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                        <!-- 状态指示器（改进版）-->
                                        <Border Grid.Column="0" Width="20" Height="20" CornerRadius="10" Margin="0,0,12,0" VerticalAlignment="Center">
                                            <Border.Style>
                                                <Style TargetType="Border">
                                                    <Setter Property="Background" Value="#f44336"/>
                                                    <Setter Property="Effect">
                                                        <Setter.Value>
                                                            <DropShadowEffect ShadowDepth="0" BlurRadius="4" Color="#f44336" Opacity="0.3"/>
                                                        </Setter.Value>
                                                    </Setter>
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding IsConnected}" Value="True">
                                                            <Setter Property="Background" Value="#4caf50"/>
                                                            <Setter Property="Effect">
                                                                <Setter.Value>
                                                                    <DropShadowEffect ShadowDepth="0" BlurRadius="4" Color="#4caf50" Opacity="0.3"/>
                                                                </Setter.Value>
                                                            </Setter>
                                                                </DataTrigger>
                                                                <DataTrigger Binding="{Binding IsEnabled}" Value="False">
                                                            <Setter Property="Background" Value="#9e9e9e"/>
                                                            <Setter Property="Effect">
                                                                <Setter.Value>
                                                                    <DropShadowEffect ShadowDepth="0" BlurRadius="4" Color="#9e9e9e" Opacity="0.3"/>
                                                                </Setter.Value>
                                                            </Setter>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                            </Border.Style>
                                        </Border>

                                        <!-- 设备图标 -->
                                        <TextBlock Grid.Column="1" Text="📦" 
                                                  FontSize="20" Margin="0,0,10,0" VerticalAlignment="Center"/>

                                                <!-- 设备信息 -->
                                        <StackPanel Grid.Column="2" VerticalAlignment="Center">
                                            <TextBlock Text="{Binding Name}" FontSize="12" FontWeight="SemiBold" Foreground="#212121"/>
                                            <TextBlock Text="{Binding Status.StatusMessage}" FontSize="10" Foreground="#757575" Margin="0,2,0,0"/>
                                                </StackPanel>

                                                <!-- 连接状态 -->
                                        <Border Grid.Column="3" CornerRadius="4" Padding="8,2" VerticalAlignment="Center" Margin="4,0,0,0">
                                            <Border.Style>
                                                <Style TargetType="Border">
                                                    <Setter Property="Background" Value="#ffebee"/>
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding IsConnected}" Value="True">
                                                            <Setter Property="Background" Value="#e8f5e9"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding IsEnabled}" Value="False">
                                                            <Setter Property="Background" Value="#f5f5f5"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Border.Style>
                                            <TextBlock VerticalAlignment="Center" FontSize="10" FontWeight="Medium">
                                                    <TextBlock.Style>
                                                        <Style TargetType="TextBlock">
                                                            <Setter Property="Text" Value="离线"/>
                                                        <Setter Property="Foreground" Value="#d32f2f"/>
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding IsConnected}" Value="True">
                                                                    <Setter Property="Text" Value="在线"/>
                                                                <Setter Property="Foreground" Value="#2e7d32"/>
                                                                </DataTrigger>
                                                                <DataTrigger Binding="{Binding IsEnabled}" Value="False">
                                                                    <Setter Property="Text" Value="禁用"/>
                                                                <Setter Property="Foreground" Value="#616161"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </TextBlock.Style>
                                                </TextBlock>
                                        </Border>
                                    </Grid>
                                </Grid>
                                    </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
                </ScrollViewer>
                
                <!-- 设备基本信息卡片 -->
                <Border Grid.Row="2" Background="White" BorderBrush="#FFE2E8F0" BorderThickness="1" CornerRadius="6" Padding="12" Margin="0,0,0,12">
                    <StackPanel>
                        <TextBlock Text="基本信息" FontSize="12" FontWeight="SemiBold" Foreground="#FF2D3748" Margin="0,0,0,8"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="设备名称" FontSize="10" Foreground="#FF4A5568" Margin="0,4,12,4" VerticalAlignment="Center"/>
                            <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding SelectedDevice.Name, FallbackValue='-'}"
                       FontSize="10" Margin="0,2" Padding="8,4" BorderBrush="#FFCBD5E0"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="设备类型" FontSize="10" Foreground="#FF4A5568" Margin="0,4,12,4" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding SelectedDevice.DeviceType, FallbackValue='-'}"
                         FontSize="10" Margin="0,4" Foreground="#FF2D3748"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="设备ID" FontSize="10" Foreground="#FF4A5568" Margin="0,4,12,4" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding SelectedDevice.Id, FallbackValue='-'}"
                         FontSize="10" Margin="0,4" Foreground="#FF718096" FontFamily="Consolas"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="连接状态" FontSize="10" Foreground="#FF4A5568" Margin="0,4,12,4" VerticalAlignment="Center"/>
                            <StackPanel Grid.Row="3" Grid.Column="1" Orientation="Horizontal" Margin="0,4">
                                <Ellipse Width="10" Height="10" Margin="0,0,8,0" VerticalAlignment="Center">
                                    <Ellipse.Style>
                                        <Style TargetType="Ellipse">
                                            <Setter Property="Fill" Value="#FFE53E3E"/>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding SelectedDevice.IsConnected}" Value="True">
                                                    <Setter Property="Fill" Value="#FF38A169"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Ellipse.Style>
                                </Ellipse>
                                <TextBlock VerticalAlignment="Center" FontSize="10">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Text" Value="离线"/>
                                            <Setter Property="Foreground" Value="#FFE53E3E"/>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding SelectedDevice.IsConnected}" Value="True">
                                                    <Setter Property="Text" Value="在线"/>
                                                    <Setter Property="Foreground" Value="#FF38A169"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </StackPanel>

                            <TextBlock Grid.Row="4" Grid.Column="0" Text="设备状态" FontSize="10" Foreground="#FF4A5568" Margin="0,4,12,4" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding SelectedDevice.Status.StatusMessage, FallbackValue='无状态信息'}"
                         FontSize="10" Margin="0,4" Foreground="#FF2D3748"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 底部状态栏 -->
                <Border Grid.Row="3" Background="#FFF0F4F8" BorderBrush="#FFCBD5E0" BorderThickness="1" CornerRadius="4" Padding="12,8">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Orientation="Horizontal">
                            <TextBlock Text="设备总数：" FontSize="10" Foreground="#FF4A5568" VerticalAlignment="Center"/>
                            <TextBlock Text="{Binding TotalDeviceCount}" FontSize="10" FontWeight="SemiBold" Foreground="#FF2D3748" VerticalAlignment="Center"/>
                            <TextBlock Text=" | 已连接：" FontSize="10" Foreground="#FF4A5568" VerticalAlignment="Center" Margin="8,0,0,0"/>
                            <TextBlock Text="{Binding ConnectedDeviceCount}" FontSize="10" FontWeight="SemiBold" Foreground="#FF38A169" VerticalAlignment="Center"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <Button Content="连接全部" FontSize="9" Padding="8,4" Margin="0,0,4,0"
                                    Style="{StaticResource SuccessButtonStyle}"
                                    Command="{Binding ConnectAllDevicesCommand}"/>
                            <Button Content="断开全部" FontSize="9" Padding="8,4"
                                    Style="{StaticResource DangerButtonStyle}"
                                    Command="{Binding DisconnectAllDevicesCommand}"/>
                        </StackPanel>
                    </Grid>
                </Border>
            </Grid>
        </Border>

        <!-- 分隔线 -->
        <Rectangle Grid.Column="3" Fill="#FFE2E8F0" Width="1" HorizontalAlignment="Center"/>

        <!-- 右侧配置和监控区域 -->
        <Border Grid.Column="4" Style="{StaticResource ModernPanelStyle}" Padding="16">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="2*"/>
                    <RowDefinition Height="8"/>
                    <RowDefinition Height="1" MinHeight="1"/>
                </Grid.RowDefinitions>

                <!-- 标题栏 -->
                <Grid Grid.Row="0" Margin="0,0,0,12">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <TextBlock Text="设备配置" Style="{StaticResource PanelTitleStyle}"/>
                        <TextBlock Text="{Binding SelectedDevice.Name, StringFormat='当前选中: {0}', FallbackValue='未选中设备'}"
                                   FontSize="10" Foreground="#FF718096"/>
                    </StackPanel>
                </Grid>

                <!-- 设备配置界面 -->
                <Grid Grid.Row="1" >
                        <!-- 动态配置界面卡片 -->
                        <Border Background="White" BorderBrush="#FFE2E8F0" BorderThickness="1" CornerRadius="6" Padding="12">
                            <Grid>
                           
                                <ContentPresenter Content="{Binding CurrentConfigurationUI}" >
                                    <ContentPresenter.Style>
                                        <Style TargetType="ContentPresenter">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding CurrentConfigurationUI}" Value="{x:Null}">
                                                    <Setter Property="Content">
                                                        <Setter.Value>
                                                            <TextBlock Text="请选择设备以显示配置选项"
                                                                       FontSize="10"
                                                                       Foreground="#FF718096"
                                                                       HorizontalAlignment="Center"
                                                                       Margin="0,20"/>
                                                        </Setter.Value>
                                                    </Setter>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </ContentPresenter.Style>
                                </ContentPresenter>
                            </Grid>
                        </Border>
                </Grid>

                <!-- 分隔线 -->
                <Rectangle Grid.Row="2" Fill="#FFE2E8F0" Height="1" VerticalAlignment="Center"/>

                <!-- 操作日志 -->
                <Border Grid.Row="3" Background="White" BorderBrush="#FFE2E8F0" BorderThickness="1" CornerRadius="6" Padding="12" Visibility="Hidden">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Text="操作日志" FontSize="12" FontWeight="SemiBold" Foreground="#FF2D3748" Margin="0,0,0,8"/>

                        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
                            <TextBox Text="{Binding OperationLog}"
                                     IsReadOnly="True"
                                     TextWrapping="Wrap"
                                     Background="#FF1A202C"
                                     Foreground="#FF68D391"
                                     FontFamily="Consolas"
                                     FontSize="9"
                                     BorderThickness="0"
                                     Padding="8"/>
                        </ScrollViewer>

                        <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="0,8,0,0">
                            <Button Content="清空日志" FontSize="9" Padding="8,4" Margin="0,0,4,0"
                                    Style="{StaticResource InfoButtonStyle}"
                                    Command="{Binding ClearLogCommand}"/>
                            <Button Content="导出日志" FontSize="9" Padding="8,4"
                                    Style="{StaticResource ModernButtonStyle}"
                                    Command="{Binding ExportLogCommand}"/>
                        </StackPanel>
                    </Grid>
                </Border>
            </Grid>
        </Border>
    </Grid>
</UserControl>
