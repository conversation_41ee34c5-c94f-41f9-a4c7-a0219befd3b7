using System.Windows.Controls;

namespace McLaser.Devices
{
    /// <summary>
    /// 控制卡配置界面接口
    /// 定义不同控制卡配置界面需要实现的共同功能
    /// </summary>
    public interface ICardConfigControl
    {
        /// <summary>
        /// 设置关联的控制卡
        /// </summary>
        /// <param name="card">控制卡</param>
        void SetCard(CardBase card);
        
        /// <summary>
        /// 获取配置界面的UI控件
        /// </summary>
        /// <returns>配置界面控件</returns>
        UserControl GetControl();
        
        /// <summary>
        /// 获取配置界面的卡类型
        /// </summary>
        /// <returns>支持的控制卡类型</returns>
        CardType GetSupportCardType();
        
        /// <summary>
        /// 更新所有轴的状态
        /// </summary>
        void UpdateAllAxisStatus();
    }
} 