﻿using HalconDotNet;
using System;
using System.Collections.Generic;
using System.Linq;

namespace McLaser.Modules.Vision
{
    public class Gen
    {
        public static void GenArrow(out HXLDCont ho_Arrow, HTuple hv_Row1, HTuple hv_Column1, HTuple hv_Row2, HTuple hv_Column2, HTuple hv_HeadLength, HTuple hv_HeadWidth)
        {
            HTuple distance = null;
            HTuple hTuple = null;
            HTuple hTuple2 = null;
            HTuple hTuple3 = null;
            HTuple hTuple4 = null;
            HTuple hTuple5 = null;
            HTuple hTuple6 = null;
            HTuple hTuple7 = null;
            HTuple hTuple8 = null;
            HTuple hTuple9 = null;
            ho_Arrow = new HXLDCont();
            HXLDCont hXLDCont = new HXLDCont();
            HOperatorSet.DistancePp(hv_Row1, hv_Column1, hv_Row2, hv_Column2, out distance);
            hTuple = distance.TupleFind(0);
            if ((int)new HTuple(hTuple.TupleNotEqual(-1)) != 0)
            {
                if (distance == null)
                {
                    distance = new HTuple();
                }

                distance[hTuple] = -1;
            }

            hTuple2 = 1.0 * (hv_Row2 - hv_Row1) / distance;
            hTuple3 = 1.0 * (hv_Column2 - hv_Column1) / distance;
            hTuple4 = hv_HeadWidth / 2.0;
            hTuple5 = hv_Row1 + (distance - hv_HeadLength) * hTuple2 + hTuple4 * hTuple3;
            hTuple6 = hv_Column1 + (distance - hv_HeadLength) * hTuple3 - hTuple4 * hTuple2;
            hTuple7 = hv_Row1 + (distance - hv_HeadLength) * hTuple2 - hTuple4 * hTuple3;
            hTuple8 = hv_Column1 + (distance - hv_HeadLength) * hTuple3 + hTuple4 * hTuple2;
            hTuple9 = 0;
            while ((int)hTuple9 <= (int)(new HTuple(distance.TupleLength()) - 1))
            {
                if ((int)new HTuple(distance.TupleSelect(hTuple9).TupleEqual(-1)) != 0)
                {
                    hXLDCont.Dispose();
                    hXLDCont.GenContourPolygonXld(hv_Row1.TupleSelect(hTuple9), hv_Column1.TupleSelect(hTuple9));
                }
                else
                {
                    hXLDCont.Dispose();
                    hXLDCont.GenContourPolygonXld(hv_Row1.TupleSelect(hTuple9).TupleConcat(hv_Row2.TupleSelect(hTuple9)).TupleConcat(hTuple5.TupleSelect(hTuple9))
                        .TupleConcat(hv_Row2.TupleSelect(hTuple9))
                        .TupleConcat(hTuple7.TupleSelect(hTuple9))
                        .TupleConcat(hv_Row2.TupleSelect(hTuple9)), hv_Column1.TupleSelect(hTuple9).TupleConcat(hv_Column2.TupleSelect(hTuple9)).TupleConcat(hTuple6.TupleSelect(hTuple9))
                        .TupleConcat(hv_Column2.TupleSelect(hTuple9))
                        .TupleConcat(hTuple8.TupleSelect(hTuple9))
                        .TupleConcat(hv_Column2.TupleSelect(hTuple9)));
                }

                if (!ho_Arrow.IsInitialized())
                {
                    ho_Arrow = hXLDCont;
                }

                ho_Arrow = ho_Arrow.ConcatObj(hXLDCont);
                hTuple9 = (int)hTuple9 + 1;
            }

            hXLDCont.Dispose();
        }

        public static void SortPairs(ref HTuple hv_T1, ref HTuple hv_T2)
        {
            try
            {
                HTuple hTuple = new HTuple();
                HTuple hTuple2 = new HTuple();
                HTuple hTuple3 = new HTuple();
                HTuple indices = new HTuple();
                HTuple indices2 = new HTuple();
                hTuple3 = ((!(hv_T1.TupleMax().D - hv_T1.TupleMin().D > hv_T2.TupleMax().D - hv_T2.TupleMin().D)) ? new HTuple("2") : new HTuple("1"));
                if ((int)new HTuple(hTuple3.TupleEqual("1")).TupleOr(new HTuple(hTuple3.TupleEqual(1))) != 0)
                {
                    HOperatorSet.TupleSortIndex(hv_T1, out indices);
                    hTuple = hv_T1.TupleSelect(indices);
                    hTuple2 = hv_T2.TupleSelect(indices);
                }
                else if ((int)new HTuple(new HTuple(hTuple3.TupleEqual("column")).TupleOr(new HTuple(hTuple3.TupleEqual("2")))).TupleOr(new HTuple(hTuple3.TupleEqual(2))) != 0)
                {
                    HOperatorSet.TupleSortIndex(hv_T2, out indices2);
                    hTuple = hv_T1.TupleSelect(indices2);
                    hTuple2 = hv_T2.TupleSelect(indices2);
                }

                hv_T1 = hTuple;
                hv_T2 = hTuple2;
            }
            catch (Exception)
            {
            }
        }

        public static void SortPairs(ref List<double> rows, ref List<double> cols)
        {
            try
            {
                HTuple hv_T1 = new HTuple(rows.ToArray());
                HTuple hv_T2 = new HTuple(cols.ToArray());
                //相同的方法 直接使用htuple返回结果
                SortPairs(ref hv_T1, ref hv_T2);
                rows = hv_T1.ToDArr().ToList();
                cols = hv_T2.ToDArr().ToList();
                return;
            }
            catch (Exception)
            {
            }
        }
    }

}
