using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace McLaser.Core.Framework.Validation
{
    /// <summary>
    /// 验证引擎接口
    /// </summary>
    public interface IValidationEngine
    {
        /// <summary>
        /// 验证对象
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要验证的对象</param>
        /// <returns>验证结果</returns>
        ValidationResult Validate<T>(T obj);

        /// <summary>
        /// 异步验证对象
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要验证的对象</param>
        /// <returns>验证结果</returns>
        Task<ValidationResult> ValidateAsync<T>(T obj);

        /// <summary>
        /// 验证属性
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">对象实例</param>
        /// <param name="propertyName">属性名称</param>
        /// <returns>验证结果</returns>
        ValidationResult ValidateProperty<T>(T obj, string propertyName);

        /// <summary>
        /// 注册验证规则
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="rule">验证规则</param>
        void RegisterRule<T>(IValidationRule<T> rule);

        /// <summary>
        /// 移除验证规则
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="rule">验证规则</param>
        /// <returns>是否成功移除</returns>
        bool RemoveRule<T>(IValidationRule<T> rule);

        /// <summary>
        /// 获取类型的所有验证规则
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <returns>验证规则列表</returns>
        IReadOnlyList<IValidationRule<T>> GetRules<T>();

        /// <summary>
        /// 清除类型的所有验证规则
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        void ClearRules<T>();
    }

    /// <summary>
    /// 验证引擎实现
    /// </summary>
    public class ValidationEngine : IValidationEngine
    {
        private readonly Dictionary<Type, List<object>> _rules;
        private readonly object _lock = new object();

        /// <summary>
        /// 构造函数
        /// </summary>
        public ValidationEngine()
        {
            _rules = new Dictionary<Type, List<object>>();
        }

        /// <summary>
        /// 验证对象
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要验证的对象</param>
        /// <returns>验证结果</returns>
        public ValidationResult Validate<T>(T obj)
        {
            if (obj == null)
                return ValidationResult.Failure("对象不能为空");

            var rules = GetRules<T>();
            if (rules.Count == 0)
                return ValidationResult.Success();

            var errors = new List<ValidationError>();

            foreach (var rule in rules.OrderBy(r => r.Priority))
            {
                if (!rule.IsEnabled)
                    continue;

                // 检查条件验证规则
                if (rule is IConditionalValidationRule<T> conditionalRule && 
                    !conditionalRule.ShouldValidate(obj))
                    continue;

                try
                {
                    var result = rule.Validate(obj);
                    if (!result.IsValid)
                    {
                        errors.AddRange(result.Errors);
                    }
                }
                catch (Exception ex)
                {
                    errors.Add(new ValidationError($"验证规则 '{rule.RuleName}' 执行失败: {ex.Message}"));
                }
            }

            return errors.Count == 0 ? ValidationResult.Success() : ValidationResult.Failure(errors);
        }

        /// <summary>
        /// 异步验证对象
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要验证的对象</param>
        /// <returns>验证结果</returns>
        public async Task<ValidationResult> ValidateAsync<T>(T obj)
        {
            if (obj == null)
                return ValidationResult.Failure("对象不能为空");

            var rules = GetRules<T>();
            if (rules.Count == 0)
                return ValidationResult.Success();

            var errors = new List<ValidationError>();
            var tasks = new List<Task<ValidationResult>>();

            foreach (var rule in rules.OrderBy(r => r.Priority))
            {
                if (!rule.IsEnabled)
                    continue;

                // 检查条件验证规则
                if (rule is IConditionalValidationRule<T> conditionalRule && 
                    !conditionalRule.ShouldValidate(obj))
                    continue;

                try
                {
                    tasks.Add(rule.ValidateAsync(obj));
                }
                catch (Exception ex)
                {
                    errors.Add(new ValidationError($"验证规则 '{rule.RuleName}' 执行失败: {ex.Message}"));
                }
            }

            if (tasks.Count > 0)
            {
                var results = await Task.WhenAll(tasks);
                foreach (var result in results)
                {
                    if (!result.IsValid)
                    {
                        errors.AddRange(result.Errors);
                    }
                }
            }

            return errors.Count == 0 ? ValidationResult.Success() : ValidationResult.Failure(errors);
        }

        /// <summary>
        /// 验证属性
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">对象实例</param>
        /// <param name="propertyName">属性名称</param>
        /// <returns>验证结果</returns>
        public ValidationResult ValidateProperty<T>(T obj, string propertyName)
        {
            if (obj == null)
                return ValidationResult.Failure("对象不能为空");

            if (string.IsNullOrWhiteSpace(propertyName))
                return ValidationResult.Failure("属性名称不能为空");

            var rules = GetRules<T>();
            var propertyRules = rules.OfType<IPropertyValidationRule<T, object>>()
                                   .Where(r => r.PropertyName == propertyName)
                                   .ToList();

            if (propertyRules.Count == 0)
                return ValidationResult.Success();

            var errors = new List<ValidationError>();

            foreach (var rule in propertyRules.OrderBy(r => r.Priority))
            {
                if (!rule.IsEnabled)
                    continue;

                try
                {
                    var propertyValue = rule.PropertySelector(obj);
                    var result = rule.ValidateProperty(obj, propertyValue);
                    
                    if (!result.IsValid)
                    {
                        errors.AddRange(result.Errors);
                    }
                }
                catch (Exception ex)
                {
                    errors.Add(new ValidationError($"属性 '{propertyName}' 验证失败: {ex.Message}", propertyName));
                }
            }

            return errors.Count == 0 ? ValidationResult.Success() : ValidationResult.Failure(errors);
        }

        /// <summary>
        /// 注册验证规则
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="rule">验证规则</param>
        public void RegisterRule<T>(IValidationRule<T> rule)
        {
            if (rule == null)
                throw new ArgumentNullException(nameof(rule));

            lock (_lock)
            {
                var type = typeof(T);
                if (!_rules.ContainsKey(type))
                {
                    _rules[type] = new List<object>();
                }

                _rules[type].Add(rule);
            }
        }

        /// <summary>
        /// 移除验证规则
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="rule">验证规则</param>
        /// <returns>是否成功移除</returns>
        public bool RemoveRule<T>(IValidationRule<T> rule)
        {
            if (rule == null)
                return false;

            lock (_lock)
            {
                var type = typeof(T);
                if (!_rules.ContainsKey(type))
                    return false;

                return _rules[type].Remove(rule);
            }
        }

        /// <summary>
        /// 获取类型的所有验证规则
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <returns>验证规则列表</returns>
        public IReadOnlyList<IValidationRule<T>> GetRules<T>()
        {
            lock (_lock)
            {
                var type = typeof(T);
                if (!_rules.ContainsKey(type))
                    return new List<IValidationRule<T>>();

                return _rules[type].Cast<IValidationRule<T>>().ToList();
            }
        }

        /// <summary>
        /// 清除类型的所有验证规则
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        public void ClearRules<T>()
        {
            lock (_lock)
            {
                var type = typeof(T);
                if (_rules.ContainsKey(type))
                {
                    _rules[type].Clear();
                }
            }
        }
    }

    /// <summary>
    /// 验证规则基类
    /// </summary>
    /// <typeparam name="T">验证对象类型</typeparam>
    public abstract class ValidationRuleBase<T> : IValidationRule<T>
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="ruleName">规则名称</param>
        /// <param name="errorMessage">错误消息</param>
        protected ValidationRuleBase(string ruleName, string errorMessage)
        {
            RuleName = ruleName ?? throw new ArgumentNullException(nameof(ruleName));
            ErrorMessage = errorMessage ?? throw new ArgumentNullException(nameof(errorMessage));
            Priority = 0;
            IsEnabled = true;
        }

        /// <summary>
        /// 规则名称
        /// </summary>
        public string RuleName { get; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; protected set; }

        /// <summary>
        /// 验证优先级
        /// </summary>
        public int Priority { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 验证对象
        /// </summary>
        /// <param name="value">要验证的值</param>
        /// <returns>验证结果</returns>
        public abstract ValidationResult Validate(T value);

        /// <summary>
        /// 异步验证对象
        /// </summary>
        /// <param name="value">要验证的值</param>
        /// <returns>验证结果</returns>
        public virtual Task<ValidationResult> ValidateAsync(T value)
        {
            return Task.FromResult(Validate(value));
        }

        /// <summary>
        /// 创建验证错误
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="propertyName">属性名称</param>
        /// <returns>验证错误</returns>
        protected ValidationError CreateError(string? message = null, string? propertyName = null)
        {
            return new ValidationError(message ?? ErrorMessage, propertyName);
        }
    }

    /// <summary>
    /// 属性验证规则基类
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <typeparam name="TProperty">属性类型</typeparam>
    public abstract class PropertyValidationRuleBase<T, TProperty> : ValidationRuleBase<T>, IPropertyValidationRule<T, TProperty>
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        /// <param name="propertySelector">属性选择器</param>
        /// <param name="ruleName">规则名称</param>
        /// <param name="errorMessage">错误消息</param>
        protected PropertyValidationRuleBase(string propertyName, Func<T, TProperty> propertySelector, 
                                           string ruleName, string errorMessage)
            : base(ruleName, errorMessage)
        {
            PropertyName = propertyName ?? throw new ArgumentNullException(nameof(propertyName));
            PropertySelector = propertySelector ?? throw new ArgumentNullException(nameof(propertySelector));
        }

        /// <summary>
        /// 属性名称
        /// </summary>
        public string PropertyName { get; }

        /// <summary>
        /// 属性选择器
        /// </summary>
        public Func<T, TProperty> PropertySelector { get; }

        /// <summary>
        /// 验证对象
        /// </summary>
        /// <param name="value">要验证的值</param>
        /// <returns>验证结果</returns>
        public override ValidationResult Validate(T value)
        {
            if (value == null)
                return ValidationResult.Failure("对象不能为空", PropertyName);

            try
            {
                var propertyValue = PropertySelector(value);
                return ValidateProperty(value, propertyValue);
            }
            catch (Exception ex)
            {
                return ValidationResult.Failure($"获取属性值失败: {ex.Message}", PropertyName);
            }
        }

        /// <summary>
        /// 验证属性值
        /// </summary>
        /// <param name="obj">对象实例</param>
        /// <param name="propertyValue">属性值</param>
        /// <returns>验证结果</returns>
        public abstract ValidationResult ValidateProperty(T obj, TProperty propertyValue);
    }
}
