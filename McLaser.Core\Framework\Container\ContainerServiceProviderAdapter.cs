using System;

namespace McLaser.Core.Framework
{
    /// <summary>
    /// 容器服务提供者适配器
    /// 将IContainer适配为IServiceProvider接口
    /// </summary>
    public class ContainerServiceProviderAdapter : IServiceProvider
    {
        private readonly Container.IContainer _container;

        /// <summary>
        /// 初始化容器服务提供者适配器
        /// </summary>
        /// <param name="container">容器实例</param>
        public ContainerServiceProviderAdapter(Container.IContainer container)
        {
            _container = container ?? throw new ArgumentNullException(nameof(container));
        }

        /// <summary>
        /// 获取服务
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <returns>服务实例</returns>
        public T GetService<T>() where T : class
        {
            return _container.Resolve<T>();
        }

        /// <summary>
        /// 获取可选服务
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <returns>服务实例，如果未找到则返回null</returns>
        public T? GetOptionalService<T>() where T : class
        {
            return _container.TryResolve<T>();
        }

        /// <summary>
        /// 获取服务
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <returns>服务实例</returns>
        public object GetService(Type serviceType)
        {
            return _container.Resolve(serviceType);
        }

        /// <summary>
        /// 获取可选服务
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <returns>服务实例，如果未找到则返回null</returns>
        public object? GetOptionalService(Type serviceType)
        {
            return _container.TryResolve(serviceType);
        }
    }
}
