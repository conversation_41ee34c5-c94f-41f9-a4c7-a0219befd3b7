using System;
using McLaser.App.Events;
using McLaser.Core.Common;

namespace McLaser.App.Models
{
    /// <summary>
    /// 处理器信息
    /// 用于异常处理演示界面的处理器显示
    /// </summary>
    public class HandlerInfo : ViewModelBase
    {
        #region 私有字段

        private string _name = string.Empty;
        private ExceptionLevel _level = ExceptionLevel.Error;
        private int _priority = 0;
        private bool _isEnabled = true;
        private int _handledCount = 0;
        private DateTime? _lastHandledTime;
        private double _averageHandlingTime = 0;
        private int _exceptionCount = 0;

        #endregion

        #region 属性

        /// <summary>
        /// 处理器名称
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 处理的异常级别
        /// </summary>
        public ExceptionLevel Level
        {
            get => _level;
            set => SetProperty(ref _level, value);
        }

        /// <summary>
        /// 处理器优先级
        /// </summary>
        public int Priority
        {
            get => _priority;
            set => SetProperty(ref _priority, value);
        }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled
        {
            get => _isEnabled;
            set => SetProperty(ref _isEnabled, value);
        }

        /// <summary>
        /// 已处理次数
        /// </summary>
        public int HandledCount
        {
            get => _handledCount;
            set => SetProperty(ref _handledCount, value);
        }

        /// <summary>
        /// 最后处理时间
        /// </summary>
        public DateTime? LastHandledTime
        {
            get => _lastHandledTime;
            set => SetProperty(ref _lastHandledTime, value);
        }

        /// <summary>
        /// 平均处理时间（毫秒）
        /// </summary>
        public double AverageHandlingTime
        {
            get => _averageHandlingTime;
            set => SetProperty(ref _averageHandlingTime, value);
        }

        /// <summary>
        /// 异常次数
        /// </summary>
        public int ExceptionCount
        {
            get => _exceptionCount;
            set => SetProperty(ref _exceptionCount, value);
        }

        /// <summary>
        /// 成功率
        /// </summary>
        public double SuccessRate
        {
            get
            {
                if (HandledCount == 0) return 100.0;
                return ((double)(HandledCount - ExceptionCount) / HandledCount) * 100.0;
            }
        }

        /// <summary>
        /// 状态文本
        /// </summary>
        public string StatusText
        {
            get
            {
                if (!IsEnabled) return "已禁用";
                if (HandledCount == 0) return "等待中";
                return $"已处理 {HandledCount} 次";
            }
        }

        /// <summary>
        /// 级别显示文本
        /// </summary>
        public string LevelDisplayText
        {
            get
            {
                return Level switch
                {
                    ExceptionLevel.Info => "信息",
                    ExceptionLevel.Warning => "警告",
                    ExceptionLevel.Error => "错误",
                    ExceptionLevel.Critical => "严重",
                    _ => "未知"
                };
            }
        }

        /// <summary>
        /// 级别颜色
        /// </summary>
        public string LevelColor
        {
            get
            {
                return Level switch
                {
                    ExceptionLevel.Info => "#FF17A2B8",      // 蓝色
                    ExceptionLevel.Warning => "#FFFFC107",   // 黄色
                    ExceptionLevel.Error => "#FFDC3545",     // 红色
                    ExceptionLevel.Critical => "#FF6F42C1",  // 紫色
                    _ => "#FF6C757D"                         // 灰色
                };
            }
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public HandlerInfo()
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="name">处理器名称</param>
        /// <param name="level">异常级别</param>
        /// <param name="priority">优先级</param>
        public HandlerInfo(string name, ExceptionLevel level, int priority = 0)
        {
            Name = name;
            Level = level;
            Priority = priority;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 记录处理成功
        /// </summary>
        /// <param name="processingTime">处理时间（毫秒）</param>
        public void RecordSuccess(long processingTime = 0)
        {
            HandledCount++;
            LastHandledTime = DateTime.Now;
            
            if (processingTime > 0)
            {
                // 计算平均处理时间
                AverageHandlingTime = (AverageHandlingTime * (HandledCount - 1) + processingTime) / HandledCount;
            }

            // 通知属性变更
            OnPropertyChanged(nameof(SuccessRate));
            OnPropertyChanged(nameof(StatusText));
        }

        /// <summary>
        /// 记录处理异常
        /// </summary>
        public void RecordException()
        {
            HandledCount++;
            ExceptionCount++;
            LastHandledTime = DateTime.Now;

            // 通知属性变更
            OnPropertyChanged(nameof(SuccessRate));
            OnPropertyChanged(nameof(StatusText));
        }

        /// <summary>
        /// 重置统计信息
        /// </summary>
        public void ResetStatistics()
        {
            HandledCount = 0;
            ExceptionCount = 0;
            LastHandledTime = null;
            AverageHandlingTime = 0;

            // 通知属性变更
            OnPropertyChanged(nameof(SuccessRate));
            OnPropertyChanged(nameof(StatusText));
        }

        /// <summary>
        /// 转换为字符串
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"{Name} [{LevelDisplayText}] - {StatusText}";
        }

        #endregion
    }
}
