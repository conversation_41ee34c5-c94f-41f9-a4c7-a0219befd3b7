using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace McLaser.Core.Configuration
{
    /// <summary>
    /// 配置验证结果
    /// </summary>
    public class ConfigurationValidationResult
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public ConfigurationValidationResult()
        {
            Errors = new List<ConfigurationValidationError>();
            Warnings = new List<ConfigurationValidationWarning>();
        }

        /// <summary>
        /// 是否验证成功
        /// </summary>
        public bool IsValid => Errors.Count == 0;

        /// <summary>
        /// 验证错误集合
        /// </summary>
        public List<ConfigurationValidationError> Errors { get; }

        /// <summary>
        /// 验证警告集合
        /// </summary>
        public List<ConfigurationValidationWarning> Warnings { get; }

        /// <summary>
        /// 验证时间
        /// </summary>
        public DateTime ValidationTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 添加错误
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="message">错误消息</param>
        /// <param name="value">配置值</param>
        public void AddError(string key, string message, object value = null)
        {
            Errors.Add(new ConfigurationValidationError(key, message, value));
        }

        /// <summary>
        /// 添加警告
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="message">警告消息</param>
        /// <param name="value">配置值</param>
        public void AddWarning(string key, string message, object value = null)
        {
            Warnings.Add(new ConfigurationValidationWarning(key, message, value));
        }
    }

    /// <summary>
    /// 配置验证错误
    /// </summary>
    public class ConfigurationValidationError
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="message">错误消息</param>
        /// <param name="value">配置值</param>
        public ConfigurationValidationError(string key, string message, object value = null)
        {
            Key = key;
            Message = message;
            Value = value;
        }

        /// <summary>
        /// 配置键
        /// </summary>
        public string Key { get; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string Message { get; }

        /// <summary>
        /// 配置值
        /// </summary>
        public object Value { get; }
    }

    /// <summary>
    /// 配置验证警告
    /// </summary>
    public class ConfigurationValidationWarning
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="message">警告消息</param>
        /// <param name="value">配置值</param>
        public ConfigurationValidationWarning(string key, string message, object value = null)
        {
            Key = key;
            Message = message;
            Value = value;
        }

        /// <summary>
        /// 配置键
        /// </summary>
        public string Key { get; }

        /// <summary>
        /// 警告消息
        /// </summary>
        public string Message { get; }

        /// <summary>
        /// 配置值
        /// </summary>
        public object Value { get; }
    }

    /// <summary>
    /// 配置验证规则接口
    /// </summary>
    public interface IConfigurationValidationRule
    {
        /// <summary>
        /// 规则名称
        /// </summary>
        string Name { get; }

        /// <summary>
        /// 规则描述
        /// </summary>
        string Description { get; }

        /// <summary>
        /// 验证配置
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        /// <param name="context">验证上下文</param>
        /// <returns>验证结果</returns>
        ConfigurationValidationResult Validate(string key, object value, ConfigurationValidationContext context);
    }

    /// <summary>
    /// 配置验证上下文
    /// </summary>
    public class ConfigurationValidationContext
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="allConfigurations">所有配置数据</param>
        public ConfigurationValidationContext(IDictionary<string, object> allConfigurations)
        {
            AllConfigurations = allConfigurations ?? new Dictionary<string, object>();
            Properties = new Dictionary<string, object>();
        }

        /// <summary>
        /// 所有配置数据
        /// </summary>
        public IDictionary<string, object> AllConfigurations { get; }

        /// <summary>
        /// 验证上下文属性
        /// </summary>
        public IDictionary<string, object> Properties { get; }
    }

    /// <summary>
    /// 配置统计信息
    /// </summary>
    public class ConfigurationStatistics
    {
        /// <summary>
        /// 配置项总数
        /// </summary>
        public int TotalItems { get; set; }

        /// <summary>
        /// 配置提供者数量
        /// </summary>
        public int ProviderCount { get; set; }

        /// <summary>
        /// 读取次数
        /// </summary>
        public long ReadCount { get; set; }

        /// <summary>
        /// 写入次数
        /// </summary>
        public long WriteCount { get; set; }

        /// <summary>
        /// 缓存命中次数
        /// </summary>
        public long CacheHitCount { get; set; }

        /// <summary>
        /// 缓存未命中次数
        /// </summary>
        public long CacheMissCount { get; set; }

        /// <summary>
        /// 缓存命中率
        /// </summary>
        public double CacheHitRate => CacheHitCount + CacheMissCount > 0 
            ? (double)CacheHitCount / (CacheHitCount + CacheMissCount) 
            : 0;

        /// <summary>
        /// 平均读取时间（毫秒）
        /// </summary>
        public double AverageReadTime { get; set; }

        /// <summary>
        /// 平均写入时间（毫秒）
        /// </summary>
        public double AverageWriteTime { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime { get; set; }

        /// <summary>
        /// 错误次数
        /// </summary>
        public long ErrorCount { get; set; }
    }

    /// <summary>
    /// 配置诊断信息
    /// </summary>
    public class ConfigurationDiagnostics
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public ConfigurationDiagnostics()
        {
            ProviderDiagnostics = new List<ConfigurationProviderDiagnostic>();
            Issues = new List<ConfigurationIssue>();
        }

        /// <summary>
        /// 配置管理器状态
        /// </summary>
        public ConfigurationManagerStatus Status { get; set; }

        /// <summary>
        /// 提供者诊断信息
        /// </summary>
        public List<ConfigurationProviderDiagnostic> ProviderDiagnostics { get; }

        /// <summary>
        /// 配置问题
        /// </summary>
        public List<ConfigurationIssue> Issues { get; }

        /// <summary>
        /// 诊断时间
        /// </summary>
        public DateTime DiagnosticTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 内存使用量（字节）
        /// </summary>
        public long MemoryUsage { get; set; }

        /// <summary>
        /// 配置文件大小（字节）
        /// </summary>
        public long ConfigurationFileSize { get; set; }
    }

    /// <summary>
    /// 配置管理器状态
    /// </summary>
    public enum ConfigurationManagerStatus
    {
        /// <summary>
        /// 未初始化
        /// </summary>
        NotInitialized,

        /// <summary>
        /// 正常
        /// </summary>
        Healthy,

        /// <summary>
        /// 警告
        /// </summary>
        Warning,

        /// <summary>
        /// 错误
        /// </summary>
        Error,

        /// <summary>
        /// 关闭
        /// </summary>
        Shutdown
    }

    /// <summary>
    /// 配置提供者诊断信息
    /// </summary>
    public class ConfigurationProviderDiagnostic
    {
        /// <summary>
        /// 提供者名称
        /// </summary>
        public string ProviderName { get; set; }

        /// <summary>
        /// 提供者状态
        /// </summary>
        public ConfigurationProviderStatus Status { get; set; }

        /// <summary>
        /// 最后错误消息
        /// </summary>
        public string LastError { get; set; }

        /// <summary>
        /// 最后错误时间
        /// </summary>
        public DateTime? LastErrorTime { get; set; }

        /// <summary>
        /// 响应时间（毫秒）
        /// </summary>
        public double ResponseTime { get; set; }

        /// <summary>
        /// 配置项数量
        /// </summary>
        public int ItemCount { get; set; }
    }

    /// <summary>
    /// 配置提供者状态
    /// </summary>
    public enum ConfigurationProviderStatus
    {
        /// <summary>
        /// 未初始化
        /// </summary>
        NotInitialized,

        /// <summary>
        /// 正常
        /// </summary>
        Healthy,

        /// <summary>
        /// 警告
        /// </summary>
        Warning,

        /// <summary>
        /// 错误
        /// </summary>
        Error,

        /// <summary>
        /// 不可用
        /// </summary>
        Unavailable
    }

    /// <summary>
    /// 配置问题
    /// </summary>
    public class ConfigurationIssue
    {
        /// <summary>
        /// 问题类型
        /// </summary>
        public ConfigurationIssueType Type { get; set; }

        /// <summary>
        /// 问题描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 相关配置键
        /// </summary>
        public string Key { get; set; }

        /// <summary>
        /// 建议解决方案
        /// </summary>
        public string Recommendation { get; set; }

        /// <summary>
        /// 发现时间
        /// </summary>
        public DateTime DiscoveredAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 配置问题类型
    /// </summary>
    public enum ConfigurationIssueType
    {
        /// <summary>
        /// 缺少必需配置
        /// </summary>
        MissingRequired,

        /// <summary>
        /// 配置值无效
        /// </summary>
        InvalidValue,

        /// <summary>
        /// 配置冲突
        /// </summary>
        Conflict,

        /// <summary>
        /// 性能问题
        /// </summary>
        Performance,

        /// <summary>
        /// 安全问题
        /// </summary>
        Security,

        /// <summary>
        /// 已弃用配置
        /// </summary>
        Deprecated
    }
}
