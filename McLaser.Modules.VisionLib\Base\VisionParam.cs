﻿using HalconDotNet;
using McLaser.Core.Common;
using McLaser.Modules.Vision.Base;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace McLaser.Modules.Vision
{
    #region blob
    [Serializable]
    public class FeatureParam : ObservableObject
    {
        // 特征
        private Features _Name;
        public Features Name
        {
            get { return _Name; }
            set { Set(ref _Name, value); }
        }

        // 最小值
        private double _MinValue = 0;
        public double MinValue
        {
            get { return _MinValue; }
            set { Set(ref _MinValue, value); }
        }

        // 最大值
        private double _MaxValue = 999999;
        public double MaxValue
        {
            get { return _MaxValue; }
            set { Set(ref _MaxValue, value); }
        }
    }
    [Serializable]
    public class RegionAnalyParam : ObservableObject
    {
        private ObservableCollection<string> _UseIDs = new ObservableCollection<string>();
        public ObservableCollection<string> UseIDs
        {
            get { return _UseIDs; }
            set { Set(ref _UseIDs, value); }
        }

        private bool _IsFirstData = false;
        public bool IsFirstData
        {
            get { return _IsFirstData; }
            set { Set(ref _IsFirstData, value); }
        }

        // 启用
        private bool _Enable = true;
        public bool Enable
        {
            get { return _Enable; }
            set { Set(ref _Enable, value); }
        }

        // 数据类型
        private RegionAnalysis _Name;
        public RegionAnalysis Name
        {
            get { return _Name; }
            set { Set(ref _Name, value); }
        }

        // 使用
        private string _UsedID;
        public string UsedID
        {
            get { return _UsedID; }
            set
            {
                if (value == "上一个区域")
                {
                    UsedID = "0";
                }
                else
                {
                    Set(ref _UsedID, value);
                }
            }
        }

        // 索引
        private int _ID;
        public int ID
        {
            get { return _ID; }
            set { Set(ref _ID, value); }
        }
        // 显示数据
        private string _Value;
        public string Value
        {
            get
            {
                if (_Value == null)
                    _Value = string.Empty;
                switch (Name)
                {
                    // 区域操作
                    case RegionAnalysis.连通:
                    case RegionAnalysis.合并:
                    case RegionAnalysis.补集:
                    case RegionAnalysis.孔洞填充:
                    case RegionAnalysis.获取最大区域:
                        _Value = "占位";
                        break;
                    case RegionAnalysis.相减:
                    case RegionAnalysis.相交:
                        _Value = AreaAction.ToString();
                        break;
                    // 形态学
                    case RegionAnalysis.闭运算:
                    case RegionAnalysis.开运算:
                    case RegionAnalysis.腐蚀:
                    case RegionAnalysis.膨胀:
                        {
                            switch (BlobElementType)
                            {
                                case BlobElementType.圆形:
                                    {
                                        _Value = BlobElementType.ToString() + "," + Diameter.ToString();
                                    }
                                    break;
                                case BlobElementType.矩形:
                                    {
                                        _Value = BlobElementType.ToString() + "," + BlobWidth.ToString() + "," + BlobHeight.ToString();
                                    }
                                    break;
                            }
                        }
                        break;
                    // 高级操作
                    case RegionAnalysis.特征筛选:
                        {
                            _Value = ConditionalRelation.ToString() + ",";
                            foreach (var item in FeatureParams)
                            {
                                _Value += item.Name.ToString() + "_" + item.MinValue.ToString() + "_" + item.MaxValue.ToString() + " ";
                            }
                        }
                        break;
                    case RegionAnalysis.转换:
                        _Value = ConversionType.ToString();
                        break;
                    case RegionAnalysis.矩形分割:
                        _Value = RectWidth.ToString() + "," + RectHeight.ToString();
                        break;
                    case RegionAnalysis.动态分割:
                        _Value = ApproximateWidth.ToString() + "," + Percentage.ToString();
                        break;
                    case RegionAnalysis.亮度:
                        _Value = MaxLight.ToString() + "," + MinLight.ToString();
                        break;

                }
                return _Value;
            }
            set { Set(ref _Value, value); }
        }


        private string _UseID = "上一个区域";
        public string UseID
        {
            get { return _UseID; }
            set { Set(ref _UseID, value); UsedID = value; }
        }
        #region 区域操作
        // 相减 相交
        private string _AreaAction = "上一个区域";
        public string AreaAction
        {
            get { return _AreaAction; }
            set { Set(ref _AreaAction, value); Value = ""; }
        }
        #endregion

        #region 形态学
        private BlobElementType _BlobElementType = BlobElementType.圆形;
        public BlobElementType BlobElementType
        {
            get { return _BlobElementType; }
            set { Set(ref _BlobElementType, value); Value = ""; }
        }

        // 直径
        private double _Diameter = 5;
        public double Diameter
        {
            get { return _Diameter; }
            set { Set(ref _Diameter, value); Value = ""; }
        }

        // 宽度
        private double _BlobWidth = 5;
        public double BlobWidth
        {
            get { return _BlobWidth; }
            set { Set(ref _BlobWidth, value); Value = ""; }
        }

        // 高度
        private double _BlobHeight = 5;
        public double BlobHeight
        {
            get { return _BlobHeight; }
            set { Set(ref _BlobHeight, value); Value = ""; }
        }
        #endregion

        #region 高级操作
        // 特征筛选
        private ConditionalRelation _ConditionalRelation = ConditionalRelation.AND;
        public ConditionalRelation ConditionalRelation
        {
            get { return _ConditionalRelation; }
            set { Set(ref _ConditionalRelation, value); Value = ""; }
        }

        private FeatureParam _SelectedFeatureParam;
        public FeatureParam SelectedFeatureParam
        {
            get { return _SelectedFeatureParam; }
            set { Set(ref _SelectedFeatureParam, value); Value = ""; }
        }

        private int _SelectedFeatureIndex;
        public int SelectedFeatureIndex
        {
            get { return _SelectedFeatureIndex; }
            set { Set(ref _SelectedFeatureIndex, value); Value = ""; }
        }

        private ObservableCollection<FeatureParam> _FeatureParams;
        public ObservableCollection<FeatureParam> FeatureParams
        {
            get
            {
                if (_FeatureParams == null)
                    _FeatureParams = new ObservableCollection<FeatureParam>();
                return _FeatureParams;
            }
            set { Set(ref _FeatureParams, value); Value = ""; }
        }
        // 转换
        private ConversionType _ConversionType = ConversionType.最小外接矩形2;
        public ConversionType ConversionType
        {
            get { return _ConversionType; }
            set { Set(ref _ConversionType, value); Value = ""; }
        }

        // 矩形分割 
        private double _RectWidth = 40;
        public double RectWidth
        {
            get { return _RectWidth; }
            set { Set(ref _RectWidth, value); Value = ""; }
        }

        private double _RectHeight = 50;
        public double RectHeight
        {
            get { return _RectHeight; }
            set { Set(ref _RectHeight, value); Value = ""; }
        }

        // 动态分割
        private double _ApproximateWidth = 40;
        public double ApproximateWidth
        {
            get { return _ApproximateWidth; }
            set { Set(ref _ApproximateWidth, value); Value = ""; }
        }

        private double _Percentage = 20;
        public double Percentage
        {
            get { return _Percentage; }
            set { Set(ref _Percentage, value); Value = ""; }
        }

        // 亮度
        private double _MinLight = 0;
        public double MinLight
        {
            get { return _MinLight; }
            set { Set(ref _MinLight, value); Value = ""; }
        }

        private double _MaxLight = 255;
        public double MaxLight
        {
            get { return _MaxLight; }
            set { Set(ref _MaxLight, value); Value = ""; }
        }
        #endregion
    }
    #endregion
    #region 预处理
    [Serializable]
    public class PretreatParam : ObservableObject
    {
        public event EventHandler DataEventChanged;

        [NonSerialized]
        public Array TransImageTypes = Enum.GetValues(typeof(TransImageType));  //图像转换类型

        [NonSerialized]
        public Array TransImageChannels = Enum.GetValues(typeof(TransImageChannel));  //转换通道

        [NonSerialized]
        public Array MirrorImageTypes = Enum.GetValues(typeof(MirrorImageType));  //镜像类型

        [NonSerialized]
        public Array VarThresholdTypes = Enum.GetValues(typeof(VarThresholdType));   //比较类型

        [NonSerialized]
        public Array RotateImageAngles = Enum.GetValues(typeof(RotateImageAngle));   //旋转角度

        // 启用
        private bool _Enable = true;
        public bool Enable
        {
            get { return _Enable; }
            set { Set(ref _Enable, value); }
        }

        // 数据类型
        private OperatorType _Name;
        public OperatorType Name
        {
            get { return _Name; }
            set { Set(ref _Name, value); }
        }

        // 显示数据
        private string _Value;
        public string Value
        {
            get
            {
                if (_Value == null)
                    _Value = string.Empty;
                switch (Name)
                {
                    //图像调整
                    case OperatorType.彩色转灰:
                        _Value = TransImageType.ToString() + " ; " + TransImageChannel;
                        break;
                    case OperatorType.图像镜像:
                        _Value = MirrorImageType.ToString();
                        break;
                    case OperatorType.图像旋转:
                        _Value = RotateImageAngle.ToString();
                        break;
                    case OperatorType.修改图像尺寸:
                        _Value = ChangeImageWidth.ToString() + " ; " + ChangeImageHeight.ToString();
                        break;
                    //滤波
                    case OperatorType.均值滤波:
                        _Value = MeanImageWidth.ToString() + " ; " + MeanImageHeight.ToString();
                        break;
                    case OperatorType.中值滤波:
                        _Value = MedianImageWidth.ToString() + " ; " + MedianImageHeight.ToString();
                        break;
                    case OperatorType.高斯滤波:
                        _Value = GaussImageSize.ToString();
                        break;
                    //形态学运算
                    case OperatorType.灰度膨胀:
                        _Value = GrayDilationWidth.ToString() + " ; " + GrayDilationHeight.ToString();
                        break;
                    case OperatorType.灰度腐蚀:
                        _Value = GrayErosionWidth.ToString() + " ; " + GrayErosionHeight.ToString();
                        break;
                    //图像增强
                    case OperatorType.锐化:
                        _Value = EmphaSizeWidth.ToString() + " ; " + EmphaSizeHeight.ToString() + " ; " + EmphaSizeFactor.ToString();
                        break;
                    case OperatorType.对比度:
                        _Value = IlluminateWidth.ToString() + " ; " + IlluminateHeight.ToString() + " ; " + IlluminateFactor.ToString();
                        break;
                    case OperatorType.亮度调节:
                        _Value = ScaleImageMult.ToString() + " ; " + ScaleImageAdd.ToString();
                        break;
                    case OperatorType.灰度开运算:
                        _Value = OpeningWidth.ToString() + " ; " + OpeningHeight.ToString();
                        break;
                    case OperatorType.灰度闭运算:
                        _Value = ClosingWidth.ToString() + " ; " + ClosingHeight.ToString();
                        break;
                    case OperatorType.反色:
                        _Value = InvertImageLogic.ToString();
                        break;
                    //二值化
                    case OperatorType.二值化:
                        _Value = ThresholdLow.ToString() + " ; " + ThresholdHight.ToString() + " ; " +
                                   ThresholdReverse.ToString();
                        break;
                    case OperatorType.均值二值化:
                        _Value = VarThresholdWidth.ToString() + " ; " + VarThresholdHeight.ToString() + " ; " +
                                   VarThresholdSkew.ToString() + " ; " + VarThresholdType.ToString();
                        break;
                }
                return _Value;
            }
            set { Set(ref _Value, value); DataEventChanged?.Invoke(this, EventArgs.Empty); }
        }

        #region 图像调整
        // 彩色转灰,
        private TransImageType _TransImageType = TransImageType.通用比例转换; //转换类型：1.通用比例转换;2.RGB;3.HSV;4.HSI;5.YUV
        public TransImageType TransImageType
        {
            get { return _TransImageType; }
            set { Set(ref _TransImageType, value); Value = ""; }   //Value="" 是为了改变一下值，以便界面更新Value
        }
        private TransImageChannel _TransImageChannel = TransImageChannel.第一通道;  //通道：1.第一通道;2.第二通道;3.第三通道
        public TransImageChannel TransImageChannel
        {
            get { return _TransImageChannel; }
            set { Set(ref _TransImageChannel, value); Value = ""; }
        }

        // 图像镜像,
        private MirrorImageType _MirrorImageType = MirrorImageType.水平镜像; //镜像类型：1.水平镜像；2.垂直镜像；3.对角镜像
        public MirrorImageType MirrorImageType
        {
            get { return _MirrorImageType; }
            set { Set(ref _MirrorImageType, value); Value = ""; }
        }

        // 图像旋转，
        private RotateImageAngle _RotateImageAngle = RotateImageAngle._90; //旋转角度；1.90；180；270
        public RotateImageAngle RotateImageAngle
        {
            get { return _RotateImageAngle; }
            set { Set(ref _RotateImageAngle, value); Value = ""; }
        }

        // 修改图像尺寸
        private int _ChangeImageWidth = 500; //宽  1-999  step = 1
        public int ChangeImageWidth
        {
            get { return _ChangeImageWidth; }
            set { Set(ref _ChangeImageWidth, value); Value = ""; }
        }
        private int _ChangeImageHeight = 400; //高 1-999  step = 1
        public int ChangeImageHeight
        {
            get { return _ChangeImageHeight; }
            set { Set(ref _ChangeImageHeight, value); Value = ""; }
        }
        #endregion
        #region 滤波

        // 均值滤波
        private int _MeanImageWidth = 5; //宽度  1-999  step = 1
        public int MeanImageWidth
        {
            get { return _MeanImageWidth; }
            set { Set(ref _MeanImageWidth, value); Value = ""; }
        }
        private int _MeanImageHeight = 5; //高度 1-999  step = 1
        public int MeanImageHeight
        {
            get { return _MeanImageHeight; }
            set { Set(ref _MeanImageHeight, value); Value = ""; }
        }

        // 中值滤波
        private int _MedianImageWidth = 5; //宽度 1-999  step = 1
        public int MedianImageWidth
        {
            get { return _MedianImageWidth; }
            set { Set(ref _MedianImageWidth, value); Value = ""; }
        }
        private int _MedianImageHeight = 5; //高度 1-999  step = 1
        public int MedianImageHeight
        {
            get { return _MedianImageHeight; }
            set { Set(ref _MedianImageHeight, value); Value = ""; }
        }

        // 高斯滤波大小
        private int _GaussImageSize = 5;  //3-11  step = 2(奇数)
        public int GaussImageSize
        {
            get { return _GaussImageSize; }
            set { Set(ref _GaussImageSize, value); Value = ""; }
        }
        #endregion
        #region 形态学运算

        // 灰度膨胀
        private int _GrayDilationWidth = 5; //宽度 1-9999
        public int GrayDilationWidth
        {
            get { return _GrayDilationWidth; }
            set { Set(ref _GrayDilationWidth, value); Value = ""; }
        }
        private int _GrayDilationHeight = 5; //高度 1-9999
        public int GrayDilationHeight
        {
            get { return _GrayDilationHeight; }
            set { Set(ref _GrayDilationHeight, value); Value = ""; }
        }

        // 灰度腐蚀
        private int _GrayErosionWidth = 5; //宽度 1-9999
        public int GrayErosionWidth
        {
            get { return _GrayErosionWidth; }
            set { Set(ref _GrayErosionWidth, value); Value = ""; }
        }
        private int _GrayErosionHeight = 5; //高度 1-9999
        public int GrayErosionHeight
        {
            get { return _GrayErosionHeight; }
            set { Set(ref _GrayErosionHeight, value); Value = ""; }
        }
        #endregion
        #region 图像增强

        // 图像锐化
        private int _EmphaSizeWidth = 3; //宽度   3-201  奇数
        public int EmphaSizeWidth
        {
            get { return _EmphaSizeWidth; }
            set { Set(ref _EmphaSizeWidth, value); Value = ""; }
        }
        private int _EmphaSizeHeight = 3; //高度   3-201  奇数
        public int EmphaSizeHeight
        {
            get { return _EmphaSizeHeight; }
            set { Set(ref _EmphaSizeHeight, value); Value = ""; }
        }
        private double _EmphaSizeFactor = 0.3; //对比因子  0.3-20  step=0.1
        public double EmphaSizeFactor
        {
            get { return _EmphaSizeFactor; }
            set { Set(ref _EmphaSizeFactor, value); Value = ""; }
        }

        // 对比度
        private int _IlluminateWidth = 101; //宽度  3-299 奇数
        public int IlluminateWidth
        {
            get { return _IlluminateWidth; }
            set { Set(ref _IlluminateWidth, value); Value = ""; }
        }
        private int _IlluminateHeight = 101; //高度  3-299  奇数
        public int IlluminateHeight
        {
            get { return _IlluminateHeight; }
            set { Set(ref _IlluminateHeight, value); Value = ""; }
        }
        private double _IlluminateFactor = 0.7; //对比因子  0-0.5  step = 0.1
        public double IlluminateFactor
        {
            get { return _IlluminateFactor; }
            set { Set(ref _IlluminateFactor, value); Value = ""; }
        }

        // 亮度调节
        private double _ScaleImageMult = 0.1; //Mult  0.1-99999 step = 0.1
        public double ScaleImageMult
        {
            get { return _ScaleImageMult; }
            set { Set(ref _ScaleImageMult, value); Value = ""; }
        }
        private int _ScaleImageAdd = 1; //Add -999 - 999   step=1
        public int ScaleImageAdd
        {
            get { return _ScaleImageAdd; }
            set { Set(ref _ScaleImageAdd, value); Value = ""; }
        }

        // 灰度开运算
        private int _OpeningWidth = 5; //宽度 1-999 step=1
        public int OpeningWidth
        {
            get { return _OpeningWidth; }
            set { Set(ref _OpeningWidth, value); Value = ""; }
        }
        private int _OpeningHeight = 5; //高度  1-999 step=1
        public int OpeningHeight
        {
            get { return _OpeningHeight; }
            set { Set(ref _OpeningHeight, value); Value = ""; }
        }

        // 灰度闭运算
        private int _ClosingWidth = 5; //宽度  1-999 step=1
        public int ClosingWidth
        {
            get { return _ClosingWidth; }
            set { Set(ref _ClosingWidth, value); Value = ""; }
        }
        private int _ClosingHeight = 5; //高度 1-999 step=1
        public int ClosingHeight
        {
            get { return _ClosingHeight; }
            set { Set(ref _ClosingHeight, value); Value = ""; }
        }

        // 图像反色
        private bool _InvertImageLogic;
        public bool InvertImageLogic
        {
            get { return _InvertImageLogic; }
            set { Set(ref _InvertImageLogic, value); Value = ""; }
        }
        #endregion       
        #region 二值化
        // 二值化
        private int _ThresholdLow = 50; //低阈值  0-255  step=1
        public int ThresholdLow
        {
            get { return _ThresholdLow; }
            set
            {
                if (value >= ThresholdHight)
                {
                    ThresholdHight = value;
                }
                Set(ref _ThresholdLow, value); Value = "";
            }
        }
        private int _ThresholdHight = 200; //高阈值  0-255  step=1
        public int ThresholdHight
        {
            get { return _ThresholdHight; }
            set
            {
                if (value < ThresholdLow)
                {
                    ThresholdLow = value;
                }
                Set(ref _ThresholdHight, value); Value = "";
            }
        }
        private bool _ThresholdReverse = false; //黑白反转
        public bool ThresholdReverse
        {
            get { return _ThresholdReverse; }
            set { Set(ref _ThresholdReverse, value); Value = ""; }
        }

        // 均值二值化
        private int _VarThresholdWidth = 50; //宽度  1-999  step=1
        public int VarThresholdWidth
        {
            get { return _VarThresholdWidth; }
            set { Set(ref _VarThresholdWidth, value); Value = ""; }
        }
        private int _VarThresholdHeight = 50; //高度  1-999  step=1
        public int VarThresholdHeight
        {
            get { return _VarThresholdHeight; }
            set { Set(ref _VarThresholdHeight, value); Value = ""; }
        }
        private int _VarThresholdSkew = 30; //阈值偏移  1-999  step=1
        public int VarThresholdSkew
        {
            get { return _VarThresholdSkew; }
            set { Set(ref _VarThresholdSkew, value); Value = ""; }
        }
        private VarThresholdType _VarThresholdType = VarThresholdType.大于等于; //比较类型
        public VarThresholdType VarThresholdType
        {
            get { return _VarThresholdType; }
            set { Set(ref _VarThresholdType, value); Value = ""; }
        }
        #endregion

    }
    #endregion
    #region 定位
    [Serializable]
    public class MatchModelParam : ObservableObject
    {
        // 模板类型
        private ModelType _ModelType = ModelType.形状模板;
        public ModelType ModelType { get { return _ModelType; } set { Set(ref _ModelType, value); } }

        // 对比极性
        private CompType _CompType = CompType.黑白对比一致;
        public CompType CompType { get { return _CompType; } set { Set(ref _CompType, value); } }

        // 精细程度
        private Optimization _Optimization = Optimization.正常;
        public Optimization Optimization { get { return _Optimization; } set { Set(ref _Optimization, value); } }

        // 最小对比度
        private int _MinContrast = 10;
        public int MinContrast
        {
            get { return _MinContrast; }
            set
            {
                if (value >= Contrast)
                {
                    Contrast = value + 1;
                }
                Set(ref _MinContrast, value);
            }
        }

        // 对比度
        private double _Contrast = 30;
        public double Contrast
        {
            get { return _Contrast; }
            set
            {
                if (value <= MinContrast)
                {
                    MinContrast = (int)value - 1;
                }
                Set(ref _Contrast, value);
            }
        }

        // 金字塔层数
        public int Levels { get; set; } = 5;

        // 查找的最小金字塔层数
        private int _EndMatchLevel = 2;
        public int EndMatchLevel
        {
            get { return _EndMatchLevel; }
            set { Set(ref _EndMatchLevel, value); }
        }

        // 最小长度
        public double MinLength { get; set; } = 10;

        // 查找超时时间
        public double TimeOut { get; set; } = 5000;

        // 起始角度
        public double StartPhi { get; set; } = -10f;

        // 结束角度
        public double EndPhi { get; set; } = 10f;

        // 最小缩放比例
        public double MinScale { get; set; } = 0.9;

        // 最大缩放比例
        public double MaxScale { get; set; } = 1.1;

        // 最小分数
        public double MinScore { get; set; } = 0.6;

        // 最大匹配个数
        public int MathNum { get; set; } = 1;

        // 贪婪度
        public double GreedDeg { get; set; } = 0.9;

        // 最大重叠
        public double MaxOverlap { get; set; } = 0.2;

        // 执行时间
        public double RunTime { get; set; }

        // 跟随坐标系 -- 矩阵
        public HTuple homMat2D { get; set; }

        // 涂抹的最终区域
        public HRegion finalRegion = new HRegion();

        // 中心点
        public ROICoordinates CenterCoordinates { get; set; }

        public MatchModelParam()
        {
            finalRegion.GenEmptyObj();
        }
    }

    [Serializable]
    public class MatchModel
    {
        // 模板句柄
        public HHandle ModelImageHandle;

        // 匹配轮廓
        public HXLDCont ContourXld;

        // 截取模板图像
        public HImage OutImage;

        // 模板图像
        [NonSerialized]
        public HImage ModelImageReduce;
    }

    #endregion
    #region 标定
    [Serializable]
    public class NPointCalibParam : ObservableObject
    {
        // 点类型
        private PointType _PointType = PointType.Nine;
        public PointType PointType { get { return _PointType; } set { Set(ref _PointType, value); } }

        // 角度类型
        private AngleType _AngleType = AngleType.固定;
        public AngleType AngleType { get { return _AngleType; } set { Set(ref _AngleType, value); } }

        // 数据获取模式
        private CalType _CalType = CalType.全点读取;
        public CalType CalType { get { return _CalType; } set { Set(ref _CalType, value); } }

        // X移动间距
        public double SpaceX { get; set; } = 1;

        // Y移动间距
        public double SpaceY { get; set; } = 1;

        // 基准点X
        private double _BaseX = 100;
        public double BaseX { get { return _BaseX; } set { Set(ref _BaseX, value); } }

        // 基准点Y
        private double _BaseY = 50;
        public double BaseY { get { return _BaseY; } set { Set(ref _BaseY, value); } }
        // 旋转中心Y
        private double _BaseAngle = 0;
        public double BaseAngle { get { return _BaseAngle; } set { Set(ref _BaseAngle, value); } }

        // 相机与X轴的夹角
        private double _PhiSingle = 0;
        public double PhiSingle { get { return _PhiSingle; } set { Set(ref _PhiSingle, value); } }

        // 旋转中心X
        private double _RotateCenterX = 0;
        public double RotateCenterX { get { return _RotateCenterX; } set { Set(ref _RotateCenterX, value); } }

        // 旋转中心Y
        private double _RotateCenterY = 0;
        public double RotateCenterY { get { return _RotateCenterY; } set { Set(ref _RotateCenterY, value); } }

        // 平移矩阵
        public HHomMat2D HomMat2DTrans;

        // 旋转矩阵
        public HTuple HomMat2DRotate;
    }

    [Serializable]
    public class CameraCalParam : ObservableObject
    {
        // 两相邻圆心间距
        public double Distance { get; set; } = 2;

        // 低阈值
        public double LowThreshold { get; set; } = 20;

        // 高阈值
        public double HighThreshold { get; set; } = 40;

        // 最小圆度
        public double MinRound { get; set; } = 0.8;

        // 最大圆度
        public double MaxRound { get; set; } = 1.0;
    }
    #endregion
    #region 二维码
    [Serializable]
    public class Bcr2dParam : ObservableObject
    {
        public event EventHandler OnParamChanged;
        // 是否启用QR
        private bool _IsEnableQRCode = true;
        public bool IsEnableQRCode
        {
            get { return _IsEnableQRCode; }
            set { Set(ref _IsEnableQRCode, value); OnParamChanged?.Invoke("QRCode", null); }
        }

        // 是否启用DM码
        //private bool _IsEnableDMCode = true;
        //public bool IsEnableDMCode
        //{
        //    get { return _IsEnableDMCode; }
        //    set { Set(ref _IsEnableDMCode, value); OnParamChanged?.Invoke("DMCode", null); }
        //}

        // 码极性
        private CodePolarity _CodePolarity = CodePolarity.白底黑码;
        public CodePolarity CodePolarity
        {
            get { return _CodePolarity; }
            set { Set(ref _CodePolarity, value); OnParamChanged?.Invoke("polarity", null); }
        }

        // 边缘类型
        private CodeEdgeType _CodeEdgeType = CodeEdgeType.连续性;
        public CodeEdgeType CodeEdgeType
        {
            get { return _CodeEdgeType; }
            set { Set(ref _CodeEdgeType, value); }
        }

        // 降采样倍数
        private int _DownsamplingMultiple = 1;
        public int DownsamplingMultiple
        {
            get { return _DownsamplingMultiple; }
            set { Set(ref _DownsamplingMultiple, value); }
        }

        // 最小码宽
        private int _ModuleSizeMin = 6;
        public int ModuleSizeMin
        {
            get { return _ModuleSizeMin; }
            set { Set(ref _ModuleSizeMin, value); OnParamChanged?.Invoke("module_size_min", null); }
        }

        // 最大码宽
        private int _ModuleSizeMax = 100;
        public int ModuleSizeMax
        {
            get { return _ModuleSizeMax; }
            set { Set(ref _ModuleSizeMax, value); OnParamChanged?.Invoke("module_size_max", null); }
        }

        // 镜像
        private CodeMirrored _CodeMirrored = CodeMirrored.非镜像;
        public CodeMirrored CodeMirrored
        {
            get { return _CodeMirrored; }
            set { Set(ref _CodeMirrored, value); OnParamChanged?.Invoke("mirrored", null); }
        }

        // 非畸变
        private CodeAberration _CodeAberration = CodeAberration.非畸变;
        public CodeAberration CodeAberration
        {
            get { return _CodeAberration; }
            set { Set(ref _CodeAberration, value); }
        }

        // 应用模式
        private CodeApplyMode _CodeApplyMode = CodeApplyMode.普通模式;
        public CodeApplyMode CodeApplyMode
        {
            get { return _CodeApplyMode; }
            set { Set(ref _CodeApplyMode, value); OnParamChanged?.Invoke("default_parameters", null); }
        }

        // 应用模式
        private DMCodeType _DMCodeType = DMCodeType.正方形;
        public DMCodeType DMCodeType
        {
            get { return _DMCodeType; }
            set { Set(ref _DMCodeType, value); OnParamChanged?.Invoke("symbol_shape", null); }
        }

        // 超时时间
        public int TimeOut { get; set; } = 500;

        // 句柄 
        public HTuple DataCodeHandle;
    }
    #endregion
    #region 一维测量
    [Serializable]
    public class MeasureParam : ObservableObject
    {
        private double _CenterRow;
        public double CenterRow
        {
            get { return _CenterRow; }
            set { Set(ref _CenterRow, value); }//OnCaliper1DParamChanged?.Invoke(null, null); 
        }

        private double _CenterCol;
        public double CenterCol
        {
            get { return _CenterCol; }
            set { Set(ref _CenterCol, value); }//OnCaliper1DParamChanged?.Invoke(null, null); 
        }

        private double _Phi;
        public double Phi
        {
            get { return _Phi; }
            set { Set(ref _Phi, value); }//OnCaliper1DParamChanged?.Invoke(null, null); 
        }

        private double _Length1;
        public double Length1
        {
            get { return _Length1; }
            set { Set(ref _Length1, value); }//OnCaliper1DParamChanged?.Invoke(null, null); 
        }
        private double _Length2;
        public double Length2
        {
            get { return _Length2; }
            set { Set(ref _Length2, value); }//OnCaliper1DParamChanged?.Invoke(null, null); 
        }
        // 最小边缘弧度 
        private double _MinEdgeCurvature = 2;
        public double MinEdgeCurvature
        {
            get { return _MinEdgeCurvature; }
            set { Set(ref _MinEdgeCurvature, value); }//OnCaliper1DParamChanged?.Invoke(null, null); 
        }

        // 平滑 
        private double _Sigma = 2;
        public double Sigma
        {
            get { return _Sigma; }
            set { Set(ref _Sigma, value); }//OnCaliper1DParamChanged?.Invoke(null, null);
        }

        //边缘极性
        private MeasMode _MeasMode = MeasMode.所有信息;
        public MeasMode MeasMode
        {
            get { return _MeasMode; }
            set { Set(ref _MeasMode, value); }
        }

        //边缘选择
        private MeasSelect _MeasSelect = MeasSelect.所有点;
        public MeasSelect MeasSelect
        {
            get { return _MeasSelect; }
            set { Set(ref _MeasSelect, value); }
        }

        //插值方式
        private MeasInterpolation _MeasInterpolation = MeasInterpolation.nearest_neighbor;
        public MeasInterpolation MeasInterpolation
        {
            get { return _MeasInterpolation; }
            set { Set(ref _MeasInterpolation, value); }
        }
    }
    #endregion
    #region 二维测量
    [Serializable]
    public class MetrologyParam : ObservableObject
    {
        public HTuple MetrologyHandle { get; set; }
        public List<double> ShapeParams { get; set; } = new List<double>();
        // 长/2
        private double _Length1 = 20;
        public double Length1
        {
            get { return _Length1; }
            set { Set(ref _Length1, value); }
        }
        // 宽/2
        private double _Length2 = 5;
        public double Length2
        {
            get { return _Length2; }
            set { Set(ref _Length2, value); }
        }
        // 阈值
        private double _Threshold = 30;
        public double Threshold
        {
            get { return _Threshold; }
            set { Set(ref _Threshold, value); }
        }
        // 间隔
        private double _MeasDis = 10;
        public double MeasDis
        {
            get { return _MeasDis; }
            set { Set(ref _MeasDis, value); }
        }
        // 最小间距
        private double _MinDistance = 10;
        public double MinDistance
        {
            get { return _MinDistance; }
            set { Set(ref _MinDistance, value); }
        }
        // 最小分数
        private double _MinScore = 0.01;
        public double MinScore
        {
            get { return _MinScore; }
            set
            {
                if (value > 0.9)
                {
                    Set(ref _MinScore, 0.9); return;
                }
                else if (value < 0.001)
                {
                    Set(ref _MinScore, 0.001); return;
                }
                Set(ref _MinScore, value);
            }
        }
        // 个数
        private HTuple _MeasNum;
        public HTuple MeasNum
        {
            get { return _MeasNum; }
            set { Set(ref _MeasNum, value); }
        }
        // sigma
        private double _Sigma = 30;
        public double Sigma
        {
            get { return _Sigma; }
            set { Set(ref _Sigma, value); }
        }
        // 像素距离
        private double _PixelDistance = 2;
        public double PixelDistance
        {
            get { return _PixelDistance; }
            set { Set(ref _PixelDistance, value); }
        }

        // 边缘极性
        private MeasMode _MeasMode = MeasMode.所有信息;
        public MeasMode MeasMode
        {
            get { return _MeasMode; }
            set { Set(ref _MeasMode, value); }
        }
        // 边缘选择
        private MeasSelect _MeasSelect = MeasSelect.所有点;
        public MeasSelect MeasSelect
        {
            get { return _MeasSelect; }
            set { Set(ref _MeasSelect, value); }
        }

        // 剔除点像素距离
        private int _DistanceThreshold = 2;
        public int DistanceThreshold
        {
            get { return _DistanceThreshold; }
            set { Set(ref _DistanceThreshold, value); }
        }
    }
    #endregion
    #region 深度学习异常值检测
    [Serializable]
    public class DeepStudyParam : ObservableObject
    {
        public HTuple DLModelHandle { get; set; }

        public HTuple DLPreprocessParam { get; set; }

        private double _AnomalySegmentationThreshold = 1;
        public double AnomalySegmentationThreshold
        {
            get { return _AnomalySegmentationThreshold; }
            set { Set(ref _AnomalySegmentationThreshold, value); }
        }

        private double _AnomalyClassificationThresholds = 1;
        public double AnomalyClassificationThresholds
        {
            get { return _AnomalyClassificationThresholds; }
            set { Set(ref _AnomalyClassificationThresholds, value); }
        }

        private double _StandardDeviationFactor = 1;
        public double StandardDeviationFactor
        {
            get { return _StandardDeviationFactor; }
            set { Set(ref _StandardDeviationFactor, value); }
        }
    }
    #endregion
}
