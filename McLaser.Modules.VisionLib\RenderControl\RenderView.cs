﻿using HalconDotNet;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Rectangle = System.Drawing.Rectangle;

namespace McLaser.Modules.Vision
{

    //针对Halcon 编写的图像显示控价
    /// <summary>
    /// 图像控件具备的功能
    /// 1. ROI绘制、涂抹功能
    /// 2. 图像、图形渲染
    /// 3. 简易尺寸测量功能、一键测量功能
    /// </summary>
    public partial class RenderView : UserControl, IRenderView
    {

        [NonSerialized]
        public IRenderView Owner;
        public enum DrawingObjectsModifier
        {
            None,
            Shift,
            Ctrl,
            Alt
        }

        public enum MeasureType
        {
            None = 1,
            PointToPoint,
            PointToLine,
            LineToLine
        }

        private HWindow _hwindow;
        private HImage tmpImage = null;
        private HRegion MaskRegion = null;
        private HRegion BrushRegion = new HRegion();
        private ROI _selectedRoi = null;
        private HTuple _dump_params;
        //private CameraBase cam;  //相机源
        private double factor = 1;
        private double maskShape = 0; //默认圆形涂抹
        private double maskSize = 10; //默认10的涂抹尺寸
        private bool bEnableDrawMask = false;
        private bool bHideMaskRegion = false;
        private bool _left_button_down;
        private bool _right_button_down;
        private bool IsShowCross = false;
        private bool hideMenuBtn = false;
        private bool _IsShowGrayValue = true;
        private bool _IsMoveImage = true;
        private Point _last_position = new Point(0, 0);
        private Size _prevsize;
        private System.Drawing.Rectangle _part = new System.Drawing.Rectangle(0, 0, 640, 480);
        private DrawingObjectsModifier _drawingObjectsModifier;
        private MeasureType _measureType = MeasureType.None;
        private List<PointF> measurePoints = new List<PointF>();
        private ConcurrentDictionary<string, HRoi> dicHObjects = new ConcurrentDictionary<string, HRoi>();
        private Dictionary<string, HRoi> dicMeasureHObjects = new Dictionary<string, HRoi>();
        private Dictionary<string, ROI> dicHROIs = new Dictionary<string, ROI>();
        private readonly object m_lock = new object();
        private double piexlPrecision = 0;
        public IRenderViewGroupEx father = null;


        // 成员变量
        private HObject ho_CheckerboardImage = null;
        private int squareSize = 32;      // 方格子的大小 (像素)
                                          // 使用灰度值定义颜色 (0=黑, 255=白)


        // 或者使用颜色名称 (需要 Halcon 12 或更高版本支持大部分名称)
        private HTuple color1 = new HTuple(220); // 浅灰色 (数值)
        private HTuple color2 = new HTuple(180); // 深灰色 (数值)

        [NonSerialized]
        private List<IRenderView> _bindingViews;
        public List<IRenderView> BindingViews
        {
            get
            {
                if (_bindingViews == null)
                {
                    _bindingViews = new List<IRenderView>();
                }
                return _bindingViews;
            }
        }

        public HWindow HalconWindow
        {
            get
            {
                if (_hwindow == null && base.Width > 0 && base.Height > 0)
                {
                    CreateHWindow();
                }

                return _hwindow;
            }
        }

        private IntPtr hwnd = IntPtr.Zero;

        private static bool RunningInDesignerMode
        {
            get
            {
                bool flag = LicenseManager.UsageMode == LicenseUsageMode.Designtime;
                if (!flag)
                {
                    using (Process process = Process.GetCurrentProcess())
                    {
                        string fileDescription = process.MainModule.FileVersionInfo.FileDescription;
                        if (fileDescription != null && fileDescription.ToLowerInvariant().Contains("microsoft visual studio"))
                        {
                            return true;
                        }

                        return process.ProcessName.ToLowerInvariant().Contains("devenv");
                    }
                }

                return flag;
            }
        }

        public Size WindowSize
        {
            get
            {
                if (RunningInDesignerMode || _hwindow == null)
                {
                    return base.Size;
                }

                _hwindow.GetWindowExtents(out var _, out var _, out var width, out var height);
                return new Size(width, height);
            }
            set
            {
                if (value.Width > 0 && value.Height > 0)
                {
                    base.Size = new Size(value.Width, value.Height);
                }
            }
        }

        public System.Drawing.Rectangle HImagePart
        {
            get
            {
                if (_hwindow != null)
                {
                    _hwindow.GetPart(out int row, out int column, out int row2, out int column2);
                    return new System.Drawing.Rectangle(column, row, column2 - column + 1, row2 - row + 1);
                }

                return _part;
            }
            set
            {
                if (RunningInDesignerMode)
                {
                    _part = value;
                }
                else
                {
                    if (value.Right <= 0 || value.Width <= 0)
                    {
                        return;
                    }

                    if (_hwindow != null)
                    {
                        try
                        {
                            _hwindow.SetPart(value.Top, value.Left, value.Top + value.Height - 1, value.Left + value.Width - 1);
                            _part = value;
                        }
                        catch (HalconException)
                        {
                        }
                    }
                    else
                    {
                        _part = value;
                    }
                }
            }
        }

        public DrawingObjectsModifier HDrawingObjectsModifier
        {
            get
            {
                return _drawingObjectsModifier;
            }
            set
            {
                _drawingObjectsModifier = value;
            }
        }

        public event EventHandler RealTimeChanged;
        public event MouseEventHandler OnRenderViewMouseUp;
        public event MouseEventHandler OnRenderViewMouseDown;

        public RenderView()
        {
            InitializeComponent();
            if (_hwindow == null)
            {
                CreateHWindow();
            }
            base.Resize += RenderView_SizeChanged;
            base.MouseUp += WindowFrame_MouseUp;
            base.MouseMove += WindowFrame_MouseMove;
            base.MouseWheel += RenderView_MouseWheel;
            base.MouseDown += WindowFrame_MouseDown;
            hideMenuBtn = false;
            HideMenuBtn();
        }

        private int _tempbeginRow, _tempbeginCol, _tempendRow, _tempendCol;
        public bool SetFullImagePart()
        {
            //lock (m_lock)
            {
                if (tmpImage != null && tmpImage.IsInitialized())
                {
                    tmpImage.GetImageSize(out int width, out int height);
                    double ratio_win = (double)this.Width / (double)this.Height;
                    double ratio_img = (double)width / (double)height;
                    int _beginRow, _begin_Col, _endRow, _endCol;

                    if (ratio_win >= ratio_img)
                    {
                        _beginRow = 0;
                        _endRow = height - 1;
                        _begin_Col = (int)(-width * (ratio_win / ratio_img - 1d) / 2d);
                        _endCol = (int)(width + width * (ratio_win / ratio_img - 1d) / 2d);
                    }
                    else
                    {
                        _begin_Col = 0;
                        _endCol = width - 1;
                        _beginRow = (int)(-height * (ratio_img / ratio_win - 1d) / 2d);
                        _endRow = (int)(height + height * (ratio_img / ratio_win - 1d) / 2d);
                    }

                    if (!btnRealTime.Checked)
                    {
                        _hwindow.GetPart(out HTuple row1, out HTuple column1, out HTuple row2, out HTuple column2);
                        if (_beginRow != row1.D || _begin_Col != column1.D || _endRow != row2.D || _endCol != column2.D)
                        {
                            _hwindow.SetPart(_beginRow, _begin_Col, _endRow, _endCol);
                            _tempbeginRow = _beginRow;
                            _tempbeginCol = _begin_Col;
                            _tempendRow = _endRow;
                            _tempendCol = _endCol;
                            return false;
                        }
                    }
                    else
                    {
                        if (_beginRow != _tempbeginRow || _begin_Col != _tempbeginCol || _endRow != _tempendRow || _endCol != _tempendCol)
                        {
                            _hwindow.SetPart(_beginRow, _begin_Col, _endRow, _endCol);
                            _tempbeginRow = _beginRow;
                            _tempbeginCol = _begin_Col;
                            _tempendRow = _endRow;
                            _tempendCol = _endCol;
                            return false;
                        }
                    }

                }
            }
            return true;
        }

        public void SetCameraImagePart(int imageWidth, int imageHeight)
        {
            double ratio_win = (double)this.Width / (double)this.Height;
            double ratio_img = (double)imageWidth / (double)imageHeight;
            int _beginRow, _begin_Col, _endRow, _endCol;

            if (ratio_win >= ratio_img)
            {
                _beginRow = 0;
                _endRow = imageHeight - 1;
                _begin_Col = (int)(-imageWidth * (ratio_win / ratio_img - 1d) / 2d);
                _endCol = (int)(imageWidth + imageWidth * (ratio_win / ratio_img - 1d) / 2d);
            }
            else
            {
                _begin_Col = 0;
                _endCol = imageWidth - 1;
                _beginRow = (int)(-imageHeight * (ratio_img / ratio_win - 1d) / 2d);
                _endRow = (int)(imageHeight + imageHeight * (ratio_img / ratio_win - 1d) / 2d);
            }
            _hwindow.SetPart(_beginRow, _begin_Col, _endRow, _endCol);
        }

        [DllImport("user32.dll")]
        private static extern bool SetForegroundWindow(IntPtr hWnd);
        private void CreateHWindow()
        {
            try
            {
                if (!RunningInDesignerMode)
                {
                    HOperatorSet.SetCheck("~father");
                    if (_hwindow == null)
                    {
                        _hwindow = new HWindow();
                    }
                    else
                    {
                        _hwindow.GetPart(out int row, out int column, out int row2, out int column2);
                    }

                    hwnd = base.Handle;

                    _hwindow.OpenWindow(0, 0, base.Width, base.Height, hwnd, "visible", "");
                    _hwindow.SetPart(_part.Top, _part.Left, _part.Top + _part.Height - 1, _part.Left + _part.Width - 1);
                    _hwindow.SetWindowParam("graphics_stack", "true");
                    _hwindow.SetWindowParam("graphics_stack_max_element_num", 10000);


                    _prevsize.Width = base.Width;
                    _prevsize.Height = base.Height;
                    _dump_params = new HTuple(_hwindow);
                    _dump_params[1] = "interleaved";
                }
            }
            catch (HalconException)
            {
            }
        }

        private void RenderView_SizeChanged(object sender, EventArgs e)
        {
            try
            {
                HalconWindow.SetWindowExtents(0, 0, base.Width, base.Height);
                calculate_part(HalconWindow, base.Width, base.Height);
                SetFullImagePart();

                this.Controls.Remove(this.toolEasyMeasure);
                this.Controls.Remove(this.lb_GrayValue);
                this.Controls.Remove(this.lb_CurViewNum);
                this.Controls.Remove(this.lb_RealTime);
                this.Controls.Add(this.toolEasyMeasure);
                this.Controls.Add(this.lb_GrayValue);
                this.Controls.Add(this.lb_CurViewNum);
                this.Controls.Add(this.lb_RealTime);
                this.toolEasyMeasure.BringToFront();
                this.lb_CurViewNum.BringToFront();
                this.lb_GrayValue.BringToFront();
                this.lb_RealTime.BringToFront();
            }
            catch (HalconException)
            {
            }
        }

        private void RenderView_Load(object sender, EventArgs e)
        {

        }

        private void GetFloatPart(HWindow window, out double l1, out double c1, out double l2, out double c2)
        {
            window.GetPart(out HTuple row, out HTuple column, out HTuple row2, out HTuple column2);
            l1 = row;
            c1 = column;
            l2 = row2;
            c2 = column2;
        }

        public static Image HalconToWinFormsImage(HImage himage)
        {
            HImage hImage = himage.InterleaveChannels("argb", "match", 255);
            string type;
            int width;
            int height;
            IntPtr imagePointer = hImage.GetImagePointer1(out type, out width, out height);
            Bitmap bitmap = new Bitmap(width / 4, height, width, PixelFormat.Format32bppPArgb, imagePointer);
            Image result;
            using (MemoryStream memoryStream = new MemoryStream())
            {
                bitmap.Save(memoryStream, ImageFormat.Bmp);
                memoryStream.Position = 0L;
                result = Image.FromStream(memoryStream);
            }

            hImage.Dispose();
            bitmap.Dispose();
            return result;
        }

        private bool InteractingWithDrawingObjs()
        {
            switch (HDrawingObjectsModifier)
            {
                case DrawingObjectsModifier.Shift:
                    return Control.ModifierKeys == Keys.Shift;
                case DrawingObjectsModifier.Ctrl:
                    return Control.ModifierKeys == Keys.Shift;
                case DrawingObjectsModifier.Alt:
                    return Control.ModifierKeys == Keys.Alt;
                default:
                    return true;
            }
        }

        private void SetCursor(RoiCursor cursor)
        {
            switch (cursor)
            {
                case RoiCursor.Default:
                    Cursor = Cursors.Default;
                    break;
                case RoiCursor.Hand:
                    Cursor = Cursors.Hand;
                    break;
                case RoiCursor.SizeAll:
                    Cursor = Cursors.SizeAll;
                    break;
                case RoiCursor.SizeNESW:
                    Cursor = Cursors.SizeNESW;
                    break;
                case RoiCursor.SizeNWSE:
                    Cursor = Cursors.SizeNWSE;
                    break;
                case RoiCursor.SizeWE:
                    Cursor = Cursors.SizeWE;
                    break;
                case RoiCursor.SizeNS:
                    Cursor = Cursors.SizeNS;
                    break;
            }
        }


        bool flat = false;
        private void WindowFrame_MouseDown(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            try
            {
                OnRenderViewMouseDown?.Invoke(sender, e);
                if (e.Button == MouseButtons.Left)
                {
                    //双击图像适应窗口
                    if (e.Clicks == 2)
                    {
                        if (SetFullImagePart())
                        {
                            flat = !flat;
                            father?.SetFullScreenEx(this, flat);
                        }
                        return;
                    }

                    _left_button_down = true;
                    _last_position.X = e.X;
                    _last_position.Y = e.Y;

                    //左击绘制涂抹
                    if (bEnableDrawMask)
                    {
                        return;
                    }
                }


                else if (e.Button == MouseButtons.Right)
                {

                    _right_button_down = true;
                    //右击绘制擦除
                    if (bEnableDrawMask)
                    {
                        return;
                    }

                }


                if (InteractingWithDrawingObjs())
                {
                    _hwindow.ConvertCoordinatesWindowToImage((double)e.Y, (double)e.X, out double rowImage, out double columnImage);

                    _selectedRoi = null;
                    //鼠标左键按下的时候，判断是否选中图形
                    foreach (var item in dicHROIs)
                    {
                        if (item.Value.IsSelected(rowImage, columnImage, factor))
                        {
                            SetCursor(item.Value.Cursor);
                            _selectedRoi = item.Value;
                            return;
                        }
                    }
                    this.Cursor = Cursors.Default;
                }


            }
            catch (HalconException)
            {
            }

        }

        public void HShiftWindowContents(double dx, double dy)
        {
            if (_IsMoveImage)
            {
                GetFloatPart(_hwindow, out var l, out var c, out var l2, out var c2);
                _hwindow.GetWindowExtents(out var _, out var _, out var width, out var height);
                double num = (c2 - c + 1.0) / (double)width;
                double num2 = (l2 - l + 1.0) / (double)height;
                try
                {
                    _hwindow.SetPart(l + dy * num2, c + dx * num, l2 + dy * num2, c2 + dx * num);
                }
                catch (HalconException)
                {
                }
            }
        }

        private void WindowFrame_MouseMove(object sender, MouseEventArgs e)
        {
            try
            {
                GetGrayValue();
                //绘制涂抹
                if (bEnableDrawMask)
                {
                    _hwindow.ConvertCoordinatesWindowToImage((double)e.Y, (double)e.X, out double rowImage, out double columnImage);
                    if (maskShape == 0)
                    {
                        BrushRegion.GenCircle(rowImage, columnImage, maskSize);
                    }
                    else if (maskShape == 1)
                    {
                        BrushRegion.GenRectangle2(rowImage, columnImage, 0, maskSize, maskSize);
                    }
                    if (rowImage >= 0 && columnImage >= 0)
                    {
                        //显示
                        AddHObject(new HRoi(new HObject(BrushRegion), "red", false), "涂抹跟随");
                        //生成涂抹区域
                        if (MaskRegion == null)
                        {
                            MaskRegion = new HRegion();
                        }
                        if (!MaskRegion.IsInitialized())
                        {
                            MaskRegion.GenEmptyRegion();
                        }
                        if (_left_button_down) //左键涂抹
                        {
                            HRegion region = MaskRegion.Union2(BrushRegion);
                            MaskRegion.Dispose(); BrushRegion.Dispose(); MaskRegion = region;
                        }
                        else if (_right_button_down)//右键擦除
                        {
                            HRegion region = MaskRegion.Difference(BrushRegion);
                            MaskRegion.Dispose(); BrushRegion.Dispose(); MaskRegion = region;
                        }
                        AddHObject(new HRoi(new HObject(MaskRegion)), "涂抹");
                        Repaint();
                    }
                    return;
                }

                if (_measureType != MeasureType.None)
                {
                    if (measurePoints.Count % 2 != 0)
                    {
                        Repaint();

                        _hwindow.ConvertCoordinatesWindowToImage((double)e.Y, (double)e.X, out double row, out double column);
                        HOperatorSet.GenContourPolygonXld(out HObject tmpLine, new HTuple(new double[] { measurePoints[measurePoints.Count - 1].Y, row }), new HTuple(new double[] { measurePoints[measurePoints.Count - 1].X, column }));
                        _hwindow.SetColor("red");
                        _hwindow.DispObj(tmpLine);
                    }
                }

                bool flag = false;

                if (!flag && _left_button_down && _selectedRoi == null)
                {
                    HShiftWindowContents(_last_position.X - e.X, _last_position.Y - e.Y);
                }

                else if (!flag && _left_button_down && _selectedRoi != null)
                {

                    _hwindow.ConvertCoordinatesWindowToImage((double)e.Y, (double)e.X, out double rowImage, out double columnImage);
                    _selectedRoi.Shape(rowImage, columnImage);
                    Repaint();
                }
                else
                {
                    _hwindow.ConvertCoordinatesWindowToImage((double)e.Y, (double)e.X, out double rowImage, out double columnImage);
                    //鼠标左键按下的时候，判断是否选中图形
                    foreach (var item in dicHROIs)
                    {
                        item.Value.Selected = false;
                        if (item.Value.IsSelected(rowImage, columnImage, factor))
                        {
                            SetCursor(item.Value.Cursor);
                            item.Value.Selected = true;
                            Repaint();
                            return;
                        }
                    }
                    this.Cursor = Cursors.Default;
                    //Repaint(); //可以不刷新
                }

                _last_position.X = e.X;
                _last_position.Y = e.Y;
            }
            catch (HalconException)
            {
            }


        }

        private void WindowFrame_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                _left_button_down = false;
                if (_measureType != MeasureType.None)
                {
                    if (((int)_measureType) > measurePoints.Count)
                    {
                        _hwindow.ConvertCoordinatesWindowToImage((double)e.Y, (double)e.X, out double rowMeasure, out double columnMeasure);
                        measurePoints.Add(new PointF { X = (float)columnMeasure, Y = (float)rowMeasure });
                        //显示点
                        HOperatorSet.GenCrossContourXld(out HObject cross, rowMeasure, columnMeasure, 20, 0);
                        DispMeasure(new HRoi(cross), $"测量点{dicMeasureHObjects.Count}");
                        //显示线
                        if (measurePoints.Count % 2 == 0)
                        {
                            HOperatorSet.GenContourPolygonXld(out HObject Line, new HTuple(new double[] { measurePoints[measurePoints.Count - 2].Y, measurePoints[measurePoints.Count - 1].Y }), new HTuple(new double[] { measurePoints[measurePoints.Count - 2].X, measurePoints[measurePoints.Count - 1].X }));
                            DispMeasure(new HRoi(Line), $"测量线{dicMeasureHObjects.Count}");
                        }
                        //显示距离
                        if ((int)_measureType == measurePoints.Count)
                        {
                            HTuple distance = new HTuple();
                            //计算距离
                            switch (_measureType)
                            {
                                case MeasureType.PointToPoint:
                                    {
                                        HOperatorSet.DistancePp(measurePoints[0].Y, measurePoints[0].X, measurePoints[1].Y, measurePoints[1].X, out distance);
                                        _hwindow.SetTposition((int)((measurePoints[0].Y + measurePoints[1].Y) / 2 - 50), (int)((measurePoints[0].X + measurePoints[1].X) / 2 - 50));
                                        if (piexlPrecision == 0)
                                        {
                                            DispMeasure(new HText($"{Math.Round(distance.D, 4)}piexl", (int)((measurePoints[0].Y + measurePoints[1].Y) / 2), (int)((measurePoints[0].X + measurePoints[1].X) / 2), "cyan"), $"点间距{dicMeasureHObjects.Count}");
                                        }
                                        else
                                        {
                                            DispMeasure(new HText($"{Math.Round(distance.D * piexlPrecision, 4)}mm", (int)((measurePoints[0].Y + measurePoints[1].Y) / 2), (int)((measurePoints[0].X + measurePoints[1].X) / 2), "cyan"), $"点间距{dicMeasureHObjects.Count}");
                                        }
                                    }
                                    break;
                                case MeasureType.PointToLine:
                                    {
                                        HOperatorSet.DistancePl(measurePoints[0].Y, measurePoints[0].X, measurePoints[1].Y, measurePoints[1].X, measurePoints[2].Y, measurePoints[2].X, out distance);
                                        HOperatorSet.ProjectionPl(measurePoints[2].Y, measurePoints[2].X, measurePoints[0].Y, measurePoints[0].X, measurePoints[1].Y, measurePoints[1].X, out HTuple rowProj, out HTuple colProj);
                                        HOperatorSet.GenContourPolygonXld(out HObject Line, new HTuple(new double[] { measurePoints[2].Y, rowProj }), new HTuple(new double[] { measurePoints[2].X, colProj }));
                                        DispMeasure(new HRoi(Line, "green"), $"测量线{dicMeasureHObjects.Count}");
                                        if (piexlPrecision == 0)
                                        {
                                            DispMeasure(new HText($"{Math.Round(distance.D, 4)}piexl", (int)((measurePoints[2].Y + rowProj.D) / 2), (int)((measurePoints[2].X + colProj.D) / 2), "cyan"), $"点线间距{dicMeasureHObjects.Count}");
                                        }
                                        else
                                        {
                                            DispMeasure(new HText($"{Math.Round(distance.D * piexlPrecision, 4)}mm", (int)((measurePoints[2].Y + rowProj.D) / 2), (int)((measurePoints[2].X + colProj.D) / 2), "cyan"), $"点线间距{dicMeasureHObjects.Count}");
                                        }

                                    }
                                    break;
                                case MeasureType.LineToLine:
                                    break;
                            }
                            measurePoints.Clear();
                            _measureType = MeasureType.None;
                        }
                    }
                    Repaint();
                }
            }
            else if (e.Button == MouseButtons.Right)
            {
                _right_button_down = false;
            }
            OnRenderViewMouseUp?.Invoke(sender, e);
        }

        private void WindowFrame_MouseLeave(object sender, EventArgs e)
        {
            _left_button_down = false;
        }

        public void RenderView_MouseWheel(object sender, MouseEventArgs e)
        {
            try
            {
                HOperatorSet.HomMat2dIdentity(out var homMat2DIdentity);
                Point point = PointToClient(Cursor.Position);
                _hwindow.ConvertCoordinatesWindowToImage((double)point.Y, (double)point.X, out double rowImage, out double columnImage);
                double num = ((e.Delta < 0) ? Math.Sqrt(2.0) : (1.0 / Math.Sqrt(2.0)));
                for (int num2 = Math.Abs(e.Delta) / 120; num2 > 1; num2--)
                {
                    num *= ((e.Delta < 0) ? Math.Sqrt(2.0) : (1.0 / Math.Sqrt(2.0)));
                }

                HOperatorSet.HomMat2dScale(homMat2DIdentity, num, num, columnImage, rowImage, out var homMat2DScale);
                GetFloatPart(_hwindow, out var l, out var c, out var l2, out var c2);
                HOperatorSet.AffineTransPoint2d(homMat2DScale, c, l, out var qx, out var qy);
                HOperatorSet.AffineTransPoint2d(homMat2DScale, c2, l2, out var qx2, out var qy2);
                try
                {
                    if (/*(qy.D - qy2.D) * (qx.D - qx2.D) < 45000 * 45000  &&*/ (qy.D - qy2.D) * (qx.D - qx2.D) > 20)
                    {
                        _hwindow.SetPart(qy.D, qx.D, qy2.D, qx2.D);
                    }
                }
                catch (Exception)
                {
                    if ((l - l2) * (c - c2) < 46000 * 46000 && (l - l2) * (c - c2) > 20)
                    {
                        _hwindow.SetPart(l, c, l2, c2);
                    }
                }

            }
            catch (HalconException)
            {
            }
        }


        private void RenderView_Paint(object sender, PaintEventArgs e)
        {

            //HOperatorSet.GetPart(HalconWindow, out HTuple row1, out HTuple col1, out HTuple row2, out HTuple col2);
            //GenerateCheckerboardImage(base.Width, base.Height, squareSize, color1, color2, out ho_CheckerboardImage);
            //HOperatorSet.SetPart(HalconWindow, 0, 0, base.Height - 1, base.Width - 1);
            //HOperatorSet.DispObj(ho_CheckerboardImage, HalconWindow);
            //HOperatorSet.SetPart(HalconWindow, row1, col1, row2, col2);


            if (_hwindow == null || RunningInDesignerMode)
            {
                return;
            }
            //var bmpBackGround = new Bitmap(this.Size.Width, this.Size.Height);
            //var g = Graphics.FromImage(bmpBackGround);
            //g.FillRectangle(new SolidBrush(this.BackColor), 0, 0, this.Size.Width, this.Size.Height);
            //for (int i = 0; i < Size.Width; i++)
            //{
            //    for (int j = 0; j < Size.Height; j++)
            //    {
            //        g.FillRectangle((((i + j) % 2 == 0 ? Brushes.White : Brushes.DimGray)), new Rectangle(i * 20, j * 20, 20, 20));
            //    }
            //}
            //g.Dispose();
            //Bitmap2HObjectBpp8(bmpBackGround, out HObject img);
            //if (bmpBackGround != null)
            //{
            //    _hwindow.DetachBackgroundFromWindow();
            //    _hwindow.AttachBackgroundToWindow(new HImage(img));
            //}
        }


        public void Bitmap2HObjectBpp8(Bitmap bmp, out HObject image)
        {
            try
            {
                System.Drawing.Rectangle rect = new System.Drawing.Rectangle(0, 0, bmp.Width, bmp.Height);

                BitmapData srcBmpData = bmp.LockBits(rect, ImageLockMode.ReadOnly, PixelFormat.Format8bppIndexed);

                HOperatorSet.GenImage1(out image, "byte", bmp.Width, bmp.Height, srcBmpData.Scan0);
                bmp.UnlockBits(srcBmpData);
            }
            catch (Exception)
            {
                image = null;
            }
        }


        private bool calculate_part(HTuple hv_WindowHandle, HTuple hv_WindowWidth, HTuple hv_WindowHeight)
        {
            HTuple row = null;
            HTuple column = null;
            HTuple row2 = null;
            HTuple column2 = null;
            HTuple hTuple = null;
            HTuple row3 = null;
            HTuple column3 = null;
            HTuple width = null;
            HTuple height = null;
            HTuple hTuple2 = null;
            HTuple hTuple3 = null;
            HTuple hTuple4 = null;
            HTuple homMat2DIdentity = null;
            HTuple homMat2DScale = null;
            HTuple qx = null;
            HTuple qy = null;
            bool result = true;
            if (hv_WindowHandle.H == IntPtr.Zero) { return false; }
            HOperatorSet.GetPart(hv_WindowHandle, out row, out column, out row2, out column2);
            try
            {
                HTuple hTuple5 = column2 - column + 1;
                hTuple = row2 - row + 1;
                _ = hTuple5 / hTuple.TupleReal();
                HOperatorSet.GetWindowExtents(hv_WindowHandle, out row3, out column3, out width, out height);
                hTuple2 = width / hv_WindowWidth.TupleReal();
                hTuple3 = height / hv_WindowHeight.TupleReal();
                hTuple4 = new HTuple();
                hTuple4 = hTuple4.TupleConcat((row + row2) * 0.5);
                hTuple4 = hTuple4.TupleConcat((column + column2) * 0.5);
                HOperatorSet.HomMat2dIdentity(out homMat2DIdentity);
                HOperatorSet.HomMat2dScale(homMat2DIdentity, hTuple2, hTuple3, hTuple4.TupleSelect(1), hTuple4.TupleSelect(0), out homMat2DScale);
                HOperatorSet.AffineTransPoint2d(homMat2DScale, column.TupleConcat(column2), row.TupleConcat(row2), out qx, out qy);
                HOperatorSet.SetPart(hv_WindowHandle, qy.TupleSelect(0), qx.TupleSelect(0), qy.TupleSelect(1), qx.TupleSelect(1));
                return result;
            }
            catch (HalconException)
            {
                HOperatorSet.SetPart(hv_WindowHandle, row, column, row2, column2);
                return false;
            }
        }

        protected override void Dispose(bool disposing)
        {

            if (disposing)
            {
                if (_hwindow != null)
                {
                    _hwindow.Dispose();
                }

                if (_dump_params != null)
                {
                    _dump_params.Dispose();
                }

                if (components != null)
                {
                    components.Dispose();
                }
                dicHObjects.Clear();
            }

            base.Dispose(disposing);
        }

        private void GetGrayValue()
        {
            //lock (m_lock)
            {
                if (tmpImage != null && _IsShowGrayValue)
                {
                    try
                    {
                        string str_value;
                        double positionY = -1, positionX = -1;
                        HOperatorSet.CountChannels(tmpImage, out HTuple channel_count);
                        try
                        {
                            _hwindow.GetMpositionSubPix(out positionY, out positionX, out int button_state);
                        }
                        catch (HalconException)
                        {
                        }
                        tmpImage.GetImageSize(out int imgWidth, out int imgHeight);
                        string str_position = string.Format("X: {0:0000}, Y: {1:0000}", positionX, positionY);
                        bool _isXOut = (positionX < 0 || positionX >= imgWidth);
                        bool _isYOut = (positionY < 0 || positionY >= imgHeight);
                        if (!_isXOut && !_isYOut)
                        {
                            if (channel_count == 1)
                            {
                                double grayVal = tmpImage.GetGrayval((int)positionY, (int)positionX);
                                str_value = string.Format("Gray: {0:000.0}", grayVal);
                            }
                            else if (channel_count == 3)
                            {
                                HImage _RedChannel = tmpImage.AccessChannel(1);
                                HImage _GreenChannel = tmpImage.AccessChannel(2);
                                HImage _BlueChannel = tmpImage.AccessChannel(3);
                                double grayValRed = _RedChannel.GetGrayval((int)positionY, (int)positionX);
                                double grayValGreen = _GreenChannel.GetGrayval((int)positionY, (int)positionX);
                                double grayValBlue = _BlueChannel.GetGrayval((int)positionY, (int)positionX);
                                _RedChannel.Dispose();
                                _GreenChannel.Dispose();
                                _BlueChannel.Dispose();
                                str_value = String.Format("|  {0:000.0}, {1:000.0}, {2:000.0}", "R:" + grayValRed, "G:" + grayValGreen, "B:" + grayValBlue);
                            }
                            else
                            {
                                str_value = "";
                            }
                            lb_GrayValue.Text = str_position + "   " + str_value + $"   W:{imgWidth}, H:{imgHeight}";
                        }
                        else
                        {
                            lb_GrayValue.Text = "";
                        }
                    }
                    catch (Exception ex)
                    {
                        //LoggerFactory.Warn(ex.Message);
                        //Logger.AddLog(ex.Message, MoonLight.Core.Enums.MsgType.Warn);
                    }
                }
                else
                {
                    lb_GrayValue.Text = "";
                }
            }
        }

        internal void SetShowViewNum(string str)
        {
            if (!string.IsNullOrEmpty(str))
            {
                this.lb_CurViewNum.Text = str;
            }
        }


        public void DispImage(HImage hImage)
        {
            try
            {
                if (hImage == null || !hImage.IsInitialized())
                    return;


                tmpImage?.Dispose();
                tmpImage = new HImage(hImage);

                HalconWindow.DispObj(tmpImage);

                if (_IsShowGrayValue == false)
                {
                    _IsShowGrayValue = true;
                }
            }
            catch (Exception ex)
            {
                //LoggerFactory.Warn(ex.ToString());
                //Logger.AddLog(ex.Message, MoonLight.Core.Enums.MsgType.Warn);
            }

        }



        public void AddHObject(HRoi roi, string name)
        {
            if (!(roi is HText) && roi.DispHobject == null)
            {
                return;
            }

            for (int i = 0; i < dicHObjects.Count; i++)
            {
                if (dicHObjects.ContainsKey(name))
                {
                    dicHObjects[name].DispHobject?.Dispose();
                    dicHObjects[name] = roi;
                    return;
                }
            }

            dicHObjects.TryAdd(name, roi);
        }

        public void AddHROIs(Dictionary<string, HRoi> rois, bool isCopyMode = true)
        {
            if (rois == null)
            {
                return;
            }
            if (isCopyMode)
            {
                foreach (var item in rois)
                {
                    if (item.Value is HText text)
                    {
                        AddHObject(text, item.Key);
                    }
                    else
                    {
                        AddHObject(new HRoi(new HObject(item.Value.DispHobject), item.Value.DrawColor, item.Value.IsFillDisp), item.Key);
                    }
                }
            }
            else
            {
                foreach (var item in rois)
                {
                    if (item.Value is HText text)
                    {
                        AddHObject(text, item.Key);
                    }
                    else
                    {
                        AddHObject(item.Value, item.Key);
                    }
                }
            }
        }



        private void DispMeasure(HRoi roi, string name)
        {
            for (int i = 0; i < dicMeasureHObjects.Count; i++)
            {
                if (dicMeasureHObjects.ContainsKey(name))
                {
                    dicMeasureHObjects[name].DispHobject?.Dispose();
                    dicMeasureHObjects[name] = roi;
                    return;
                }
            }

            dicMeasureHObjects.Add(name, roi);
        }

        public void DispROI(ROI roi)
        {
            roi.Visiable = true;
            for (int i = 0; i < dicHROIs.Count; i++)
            {
                if (dicHROIs.ContainsKey(roi.Name))
                {

                    dicHROIs[roi.Name] = roi;
                    Repaint();
                    return;
                }
            }
            dicHROIs.Add(roi.Name, roi);
            Repaint();
        }

        public void HideROI(ROI roi)
        {
            for (int i = 0; i < dicHROIs.Count; i++)
            {
                if (dicHROIs.ContainsKey(roi.Name))
                {
                    dicHROIs[roi.Name].Visiable = false;
                    Repaint();
                    return;
                }
            }
        }

        public void SetMaskShape(int shape, int size)
        {
            if (size > 0)
            {
                maskSize = size;
            }
            if (shape == 0 || shape == 1)
            {
                maskShape = shape;
            }
        }

        public void DrawMask(HRegion region)
        {
            this.ContextMenuStrip = null;
            MaskRegion = region;
            bEnableDrawMask = true;
        }

        public HRegion EndDrawMask()
        {

            bEnableDrawMask = false;
            this.ContextMenuStrip = contextMenuStrip1;
            HideMenuBtn();

            if (dicHObjects.ContainsKey("涂抹跟随"))
            {
                dicHObjects.TryRemove("涂抹跟随", out HRoi roi);
                Repaint();
            }
            return MaskRegion;
        }

        public void HideMaskRegion(bool bHide)
        {

            if (bHide)
                bHideMaskRegion = true;
            else bHideMaskRegion = false;

            Repaint();
        }

        public HRegion GetMaskRegion()
        {
            return MaskRegion;
        }

        private void DisplayRoi(HRoi roi, string key)
        {
            HalconWindow.SetDraw(roi.IsFillDisp ? "fill" : "margin");
            if (roi.IsDashLine)
            {
                HalconWindow.SetLineStyle(10);
            }
            else
            {
                HalconWindow.SetLineStyle(new HTuple());
            }

            // Set color based on the key  
            if (key == "涂抹" && !bHideMaskRegion)
            {
                HalconWindow.SetRgba(255, 0, 0, 100);
            }
            else
            {
                HalconWindow.SetColor(roi.DrawColor);
            }


            // Display text or shape  
            if (roi is HText text)
            {
                HalconWindow.SetFont($"{text.Font}-{text.Size}");
                if (text.IsShowBox)
                {
                    HalconWindow.DispText(text.Text, "image", text.Row, text.Col, roi.DrawColor, "box_color", "#fffffff0");
                }
                else
                {
                    HalconWindow.SetTposition(text.Row, text.Col);
                    HalconWindow.WriteString(text.Text);
                }
            }
            else if (roi.DispHobject.IsInitialized())
            {
                HalconWindow.DispObj(roi.DispHobject);
            }
        }

        public void Repaint(HImage dispImage = null, ConcurrentDictionary<string, HRoi> hObjects = null)
        {
            try
            {
                //lock (m_lock)
                {
                    if (dispImage == null || !dispImage.IsInitialized())
                    {
                        if (tmpImage == null || !tmpImage.IsInitialized())
                        {
                            return;
                        }
                    }
                    else
                    {
                        tmpImage?.Dispose();
                        tmpImage = dispImage.CopyImage();
                    }
                    SetViewParam("flush_graphic", false);
                    HalconWindow.ClearWindow();
                    HalconWindow.DispObj(tmpImage);

                    HalconWindow.GetPart(out int row1, out int column1, out int row2, out int column2);
                    factor = Math.Abs(column1 - column2) / 100.0;
                    if (factor > 15) factor = 15;

                    if (hObjects != null)
                    {
                        dicHObjects = hObjects;
                    }

                    foreach (var item in dicHObjects)
                    {
                        DisplayRoi(item.Value, item.Key);
                    }
                    foreach (var item in dicHROIs)
                    {
                        if (item.Value.Visiable)
                            (item.Value as IDrawable).Draw(HalconWindow, factor);
                    }
                    foreach (var item in dicMeasureHObjects)
                    {
                        DisplayRoi(item.Value, item.Key);
                    }
                    PaintCross();
                    SetViewParam("flush_graphic", true);
                }
            }
            catch (Exception ex)
            {
                //LoggerFactory.Error(ex.Message);
                //Logger.AddLog(ex.Message, MoonLight.Core.Enums.MsgType.Warn);
                SetViewParam("flush_graphic", true);
            }

        }

        public void ClearWindow()
        {
            dicHROIs.Clear();
            dicHObjects.Clear();
            HalconWindow.ClearWindow();

            _IsShowGrayValue = false;

        }

        public void ClearAllROI()
        {
            dicHROIs.Clear();
            dicHObjects.Clear();
        }

        private void HideMenuBtn()
        {
            if (hideMenuBtn)
            {
                btnRealTime.Visible = false;
                lb_RealTime.Visible = false;
                //btnFullscreen.Visible = false;
            }
        }

        public void SetRealTimeEnable(bool bEnable)
        {
            this.btnRealTime.Enabled = bEnable;
            this.btnRealTime.CheckOnClick = bEnable;
        }

        public void SetRealTimeUI(bool isRealTime)
        {
            btnRealTime.Checked = isRealTime;
            btnEasyMeasure.Enabled = !isRealTime;
            lb_RealTime.Visible = isRealTime;
            //switch ((cam as CameraBase).TrigMode)
            //{
            //    case TrigMode.硬触发_线0:
            //    case TrigMode.硬触发_线2:
            //    case TrigMode.软触发:
            //        lb_RealTime.Text = "处于触发状态";
            //        break;
            //    case TrigMode.内触发:
            //        lb_RealTime.Text = "实时中";
            //        break;
            //}

            RealTimeChanged?.Invoke(isRealTime, null);
        }

        //private Thread dispImageThread = null;
        //private bool isRunning = false;
        public void StartRealTimeShow()
        {
            try
            {
                // 设置实时状态下的UI控件属性
                System.Windows.Application.Current.Dispatcher.Invoke(new Action(() =>
                {
                    SetRealTimeUI(true);
                }));

                //CameraBase camera = (CameraBase)cam;
                //if (cam != null && camera.Status.IsOpen && btnRealTime.Checked)
                //{
                //    // 使图像自适应窗口
                //    SetCameraImagePart((int)camera.CurWidth, (int)camera.CurHeight);

                //    // 开启线程
                //    dispImageThread = new Thread(new ThreadStart(() =>
                //    {
                //        while (isRunning)
                //        {
                //            SceneAlgorithms.Algorithms.Vision.MV_ImageGrab(cam, out HImage image);
                //            if (image != null && image.IsInitialized())
                //            {
                //                SetViewParam("flush_graphic", false);
                //                // 更新图像
                //                ClearAllROI();
                //                DispImage(image);
                //                image.Dispose();
                //                // 显示十字
                //                Repaint();
                //                SetViewParam("flush_graphic", true);
                //            }
                //            Thread.Sleep(1000 / camera.DisFrameRate);
                //        }
                //    }));
                //    isRunning = true;
                //    dispImageThread.IsBackground = true;
                //    dispImageThread.Start();

                //}
                //else
                //{
                //    // 设置实时状态下的UI控件属性
                //    System.Windows.Application.Current.Dispatcher.Invoke(new Action(() =>
                //    {
                //        SetRealTimeUI(false);
                //    }));
                //    LoggerFactory.Error($"{camera.Name} 实时失败");
                //}
            }
            catch (Exception ex)
            {
                //LoggerFactory.Error(ex.Message);
                //Logger.AddLog(ex.Message, MoonLight.Core.Enums.MsgType.Error);
            }
        }

        public HImage EndRealTimeShow()
        {
            try
            {
                System.Windows.Application.Current.Dispatcher.Invoke(new Action(() =>
                {
                    SetRealTimeUI(false);
                }));
                //if (cam != null && !btnRealTime.Checked && dispImageThread != null && dispImageThread.IsAlive)
                //{
                //    isRunning = false;
                //    dispImageThread.Join();
                //    return tmpImage?.CopyImage();
                //}
            }
            catch (Exception ex)
            {
                //LoggerFactory.Error(ex.Message);
                //Logger.AddLog(ex.Message, MoonLight.Core.Enums.MsgType.Error);
            }
            return null;
        }

        //显示十字线/隐藏十字线
        private void btnDispCross_Click(object sender, EventArgs e)
        {
            if (tmpImage == null)
            {
                MessageBox.Show("未采集图片!");
                btnDispCross.CheckState = CheckState.Unchecked;
                return;
            }
            IsShowCross = !IsShowCross;
            Repaint();
        }

        private void PaintCross()
        {
            if (IsShowCross)
            {
                //显示十字线
                HalconWindow.SetColor("green");
                tmpImage.GetImageSize(out int ImageWidth, out int ImageHeight);
                double _ROW = ImageHeight / 2;
                double _COL = ImageWidth / 2;
                //小十字
                HalconWindow.DispLine(_ROW - 5, _COL, _ROW + 5, _COL);
                HalconWindow.DispLine(_ROW, _COL - 5, _ROW, _COL + 5);
                //大十字-横
                HalconWindow.DispLine((double)_ROW, (double)_COL + 50, (double)_ROW, (double)_COL * 2);
                HalconWindow.DispLine((double)_ROW, 0, (double)_ROW, (double)_COL - 50);
                //大十字-竖
                HalconWindow.DispLine(0, (double)_COL, (double)_ROW - 50, (double)_COL);
                HalconWindow.DispLine((double)_ROW + 50, (double)_COL, (double)_ROW * 2, (double)_COL);
            }
        }

        //保存原图
        private void btnSaveRawImage_Click(object sender, EventArgs e)
        {
            if (tmpImage == null)
            {
                MessageBox.Show("未采集图片!");
                return;
            }
            SaveFileDialog sfd = new SaveFileDialog();
            sfd.Filter = "PNG图像|*.png|BMP图像|*.bmp|JPG图像|*.jpg";//|所有文件|*.*
            sfd.FilterIndex = 1;
            if (sfd.ShowDialog() == DialogResult.OK)
            {
                if (string.IsNullOrEmpty(sfd.FileName)) { return; }
                HOperatorSet.WriteImage(tmpImage, Path.GetExtension(sfd.FileName).Replace(".", ""), 0, sfd.FileName);
            }
        }

        //保存缩略图
        private void btnSaveDumpImage_Click(object sender, EventArgs e)
        {
            if (tmpImage == null)
            {
                MessageBox.Show("未采集图片!");
                return;
            }
            SaveFileDialog sfd = new SaveFileDialog();
            sfd.Filter = "PNG图像|*.png|BMP图像|*.bmp|JPG图像|*.jpg";//|所有文件|*.*
            sfd.FilterIndex = 1;
            if (sfd.ShowDialog() == DialogResult.OK)
            {
                if (string.IsNullOrEmpty(sfd.FileName)) { return; }
                DumpWindow(sfd.FileName);
            }
        }

        //简易测量
        private void btnEasyMeasure_Click(object sender, EventArgs e)
        {
            if (tmpImage == null)
            {
                MessageBox.Show("未采集图片!");
                return;
            }
            if (btnEasyMeasure.Text == "简易测量")
            {
                //piexlPrecision = (cam as CameraBase).PixelPrecision;
                toolEasyMeasure.Visible = true;
                btnEasyMeasure.Text = "关闭简易测量";
                SetRealTimeEnable(false);
            }
            else if (btnEasyMeasure.Text == "关闭简易测量")
            {
                toolEasyMeasure.Visible = false;
                btnEasyMeasure.Text = "简易测量";
                btnMeasure_clear_Click(sender, EventArgs.Empty);
                SetRealTimeEnable(true);
            }
        }

        //图像信息
        private void btnGetImageInfo_Click(object sender, EventArgs e)
        {
            if (tmpImage == null)
            {
                MessageBox.Show("未采集图片!");
                return;
            }
            //ImageInfoView imgInfoView = new ImageInfoView(tmpImage.CopyImage());
            //imgInfoView.ShowDialog();
        }

        private void btnMeasure_pt2pt_Click(object sender, EventArgs e)
        {
            _measureType = MeasureType.PointToPoint;
        }

        private void btnMeasure_pt2line_Click(object sender, EventArgs e)
        {
            _measureType = MeasureType.PointToLine;
        }

        private void btnMeasure_clear_Click(object sender, EventArgs e)
        {
            foreach (var item in dicMeasureHObjects)
            {
                if (item.Value.DispHobject != null && item.Value.DispHobject.IsInitialized())
                {
                    item.Value.DispHobject.Dispose();
                }
            }
            dicMeasureHObjects.Clear();
            Repaint();
        }

        public void DumpWindow(string filename)
        {
            if (tmpImage != null && !string.IsNullOrEmpty(filename))
            {
                HOperatorSet.DumpWindow(_hwindow, Path.GetExtension(filename).Replace(".", ""), filename);//截取窗口图 
            }
        }

        // 实时
        private void btnRealTime_Click(object sender, EventArgs e)
        {
            //if (cam != null)
            //{
            //    if (!btnRealTime.Checked)
            //    {
            //        EndRealTimeShow();
            //    }
            //    else
            //    {
            //        StartRealTimeShow();
            //    }
            //}
        }

        public void SetViewParam(string param, bool param_value)
        {
            switch (param)
            {
                case "ShowGrayValue":
                    lb_GrayValue.Visible = param_value;
                    break;
                case "IsMoveImage":
                    _IsMoveImage = param_value;
                    break;
                case "IsWheel":
                    if (param_value)
                    {
                        base.MouseWheel += RenderView_MouseWheel;
                    }
                    else
                    {
                        base.MouseWheel -= RenderView_MouseWheel;
                    }
                    break;
                case "flush_graphic":
                    {
                        string result = param_value ? "true" : "false";
                        HOperatorSet.SetSystem("flush_graphic", result);
                    }
                    break;
            }
        }

        public void ClearHWindow()
        {
            HalconWindow.ClearWindow();
            foreach (var view in BindingViews)
            {
                if (view is RenderView)
                    (view as RenderView).HalconWindow.ClearWindow();
                else if (view is RenderViewWpf)
                    (view as RenderViewWpf).HalconWindow.ClearWindow();
            }
        }

        public void DispObj(HObject obj)
        {
            if (obj == null || !obj.IsInitialized())
            {
                return;
            }
            HalconWindow.DispObj(obj);
            foreach (var view in BindingViews)
            {
                if (view is RenderView)
                    (view as RenderView).HalconWindow.DispObj(obj);
                else if (view is RenderViewWpf)
                    (view as RenderViewWpf).HalconWindow.DispObj(obj);
            }
        }


        public void RegisterView(IRenderView renderView)
        {
            BindingViews.Add(renderView);
        }
    }
}
