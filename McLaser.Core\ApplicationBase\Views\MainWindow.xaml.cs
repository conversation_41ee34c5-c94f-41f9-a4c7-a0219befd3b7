using McLaser.Core.Container;
using System;
using System.ComponentModel;
using System.Windows;

namespace McLaser.Core.ApplicationBase
{
    /// <summary>
    /// 主窗口
    /// 展示McLaser.Core框架的主要功能
    /// </summary>
    public partial class MainWindow : Window
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public MainWindow()
        {
            InitializeComponent();
            DataContext = IoC.Get<MainViewModel>();
            // 订阅窗口关闭事件
            Closing += MainWindow_Closing;
        }

        /// <summary>
        /// 窗口关闭事件处理
        /// </summary>
        private void MainWindow_Closing(object? sender, CancelEventArgs e)
        {
            try
            {
                // 清理DataContext中的资源
                if (DataContext is IDisposable disposableContext)
                {
                    disposableContext.Dispose();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清理窗口资源时发生错误: {ex.Message}");
            }
        }
    }
}
