# 视觉指示功能测试指南

## 测试目标

验证底部导航栏的视觉指示功能是否正常工作：
1. 上拉框中当前页面的高亮显示
2. 平铺按钮显示当前选中的子页面标题

## 测试环境

- 启动McLaser.App应用程序
- 确保底部导航栏正常显示
- 准备测试不同的导航场景

## 详细测试步骤

### 测试1: 上拉框高亮显示功能

#### 1.1 设备分类测试
1. **操作**: 点击底部导航栏的"设备"按钮（📱图标）
2. **预期**: 弹出上拉框，显示"设备管理器"和"设备状态"两个选项
3. **操作**: 点击"设备管理器"选项
4. **预期**: 
   - 页面切换到设备管理器
   - 设备按钮保持选中状态（高亮背景）
   - 再次点击设备按钮，上拉框中"设备管理器"项应该有高亮背景

5. **操作**: 在上拉框中点击"设备状态"选项
6. **预期**: 
   - 页面切换到设备状态
   - 再次点击设备按钮，上拉框中"设备状态"项应该有高亮背景
   - "设备管理器"项恢复正常背景

#### 1.2 系统分类测试
1. **操作**: 点击底部导航栏的"系统"按钮（🔧图标）
2. **预期**: 弹出上拉框，显示"事件总线"、"异常处理"、"插件管理"三个选项
3. **操作**: 点击"异常处理"选项
4. **预期**: 
   - 页面切换到异常处理演示
   - 系统按钮保持选中状态
   - 再次点击系统按钮，上拉框中"异常处理"项应该有高亮背景

5. **操作**: 在上拉框中点击"插件管理"选项
6. **预期**: 
   - 页面切换到插件管理演示
   - 再次点击系统按钮，上拉框中"插件管理"项应该有高亮背景
   - "异常处理"项恢复正常背景

### 测试2: 平铺按钮子页面指示功能

#### 2.1 初始状态检查
1. **操作**: 启动应用程序，观察底部导航栏
2. **预期**: 
   - "设备"和"系统"按钮显示下拉箭头（▼）
   - 没有显示子页面标题

#### 2.2 设备按钮状态指示
1. **操作**: 点击设备按钮，选择"设备管理器"
2. **预期**: 
   - 设备按钮不再显示下拉箭头（▼）
   - 设备按钮显示"设备管理器"作为子标题（小字体、斜体、灰色）

3. **操作**: 点击设备按钮，选择"设备状态"
4. **预期**: 
   - 设备按钮显示"设备状态"作为子标题
   - 子标题文字应该是小字体、斜体、灰色

#### 2.3 系统按钮状态指示
1. **操作**: 点击系统按钮，选择"事件总线"
2. **预期**: 
   - 系统按钮不再显示下拉箭头（▼）
   - 系统按钮显示"事件总线"作为子标题

3. **操作**: 切换到"异常处理"
4. **预期**: 
   - 系统按钮显示"异常处理"作为子标题

5. **操作**: 切换到"插件管理"
6. **预期**: 
   - 系统按钮显示"插件管理"作为子标题

#### 2.4 状态切换测试
1. **操作**: 从系统分类页面切换到"主页"
2. **预期**: 
   - 系统按钮恢复显示下拉箭头（▼）
   - 不再显示子页面标题
   - 主页按钮变为选中状态

2. **操作**: 从主页切换到"工具"页面
3. **预期**: 
   - 工具按钮变为选中状态
   - 分类按钮都显示下拉箭头

### 测试3: 主题兼容性测试

#### 3.1 浅色主题测试
1. **操作**: 确保应用程序使用浅色主题
2. **操作**: 测试上拉框高亮显示
3. **预期**: 
   - 高亮背景色为蓝色系
   - 高亮文字为白色或浅色
   - 视觉效果清晰可见

#### 3.2 深色主题测试
1. **操作**: 切换到深色主题（如果支持）
2. **操作**: 测试上拉框高亮显示
3. **预期**: 
   - 高亮背景色适配深色主题
   - 高亮文字颜色自动调整
   - 视觉效果在深色背景下清晰可见

### 测试4: 交互响应性测试

#### 4.1 快速切换测试
1. **操作**: 快速在不同页面间切换
2. **预期**: 
   - 视觉指示能够实时更新
   - 没有延迟或错误状态
   - 高亮显示始终正确

#### 4.2 多次点击测试
1. **操作**: 多次点击同一个分类按钮
2. **预期**: 
   - 上拉框正常弹出和关闭
   - 高亮状态保持正确
   - 没有界面异常

## 预期结果总结

### ✅ 正常工作的功能
- 上拉框中当前页面有明显的高亮背景色
- 分类按钮显示当前选中的子页面标题
- 下拉箭头和子页面标题互斥显示
- 所有视觉指示实时响应页面切换

### ❌ 需要注意的问题
如果发现以下问题，请报告：
- 高亮背景色不显示或显示错误
- 子页面标题不更新或显示错误
- 下拉箭头和子页面标题同时显示
- 主题切换后视觉效果异常
- 页面切换时视觉指示延迟更新

## 故障排除

### 问题1: 上拉框高亮不显示
**可能原因**: PageInfo的IsCurrentPage属性未正确更新
**检查方法**: 查看控制台日志，确认导航事件是否正常触发

### 问题2: 子页面标题不显示
**可能原因**: NavigationItemViewModel的CurrentSubPageTitle属性未正确设置
**检查方法**: 确认分类按钮是否正确识别为选中状态

### 问题3: 视觉效果在主题切换后异常
**可能原因**: 动态资源引用问题
**检查方法**: 重启应用程序，确认主题资源是否正确加载

## 测试完成标准

当以下所有项目都通过测试时，视觉指示功能验证完成：

- [ ] 设备分类上拉框高亮显示正常
- [ ] 系统分类上拉框高亮显示正常
- [ ] 设备按钮子页面标题显示正常
- [ ] 系统按钮子页面标题显示正常
- [ ] 下拉箭头和子页面标题正确互斥显示
- [ ] 页面切换时视觉指示实时更新
- [ ] 浅色主题下视觉效果正常
- [ ] 深色主题下视觉效果正常（如果支持）
- [ ] 快速切换时响应正常
- [ ] 多次操作无异常

## 测试报告模板

```
测试日期: ___________
测试人员: ___________
应用版本: ___________

测试结果:
□ 通过  □ 失败

具体问题描述:
_________________________
_________________________
_________________________

建议改进:
_________________________
_________________________
_________________________
```
