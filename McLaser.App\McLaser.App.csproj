<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A271967E-8208-422D-9A80-DA32C4B6E6EC}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>McLaser.App</RootNamespace>
    <AssemblyName>McLaser.App</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{60dc8134-eba5-43b8-bcc9-bb4bc16c2548};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <WarningLevel>4</WarningLevel>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <LangVersion>11.0</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject>
    </StartupObject>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="halcondotnetxl, Version=22.5.0.0, Culture=neutral, PublicKeyToken=4973bed59ddbf2b8, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\bin\halcondotnetxl.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Xaml.Behaviors, Version=1.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\bin\Microsoft.Xaml.Behaviors.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\bin\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xaml">
      <RequiredTargetFramework>4.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
  </ItemGroup>
  <ItemGroup>
    <ApplicationDefinition Include="App.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </ApplicationDefinition>
    <Compile Include="Program.cs" />
    <Compile Include="Services\INavigationService.cs" />
    <Compile Include="Services\NavigationService.cs" />
    <Compile Include="ViewModels\DataInputViewModel.cs" />
    <Compile Include="ViewModels\EventBusDemoViewModel.cs" />
    <Compile Include="ViewModels\ExceptionDemoViewModel.cs" />
    <Compile Include="ViewModels\ModuleDemoViewModel.cs" />
    <Compile Include="ViewModels\NavigationItemViewModel.cs" />
    <Compile Include="ViewModels\NavigationViewModel.cs" />
    <Compile Include="ViewModels\PluginDemoViewModel.cs" />
    <Compile Include="ViewModels\SettingsViewModel.cs" />
    <Compile Include="Views\DataInputWindow.xaml.cs">
      <DependentUpon>DataInputWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\EventBusDemoWindow.xaml.cs">
      <DependentUpon>EventBusDemoWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\ExceptionDemoWindow.xaml.cs">
      <DependentUpon>ExceptionDemoWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\ModuleDemoWindow.xaml.cs">
      <DependentUpon>ModuleDemoWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\PluginDemoWindow.xaml.cs">
      <DependentUpon>PluginDemoWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\SettingsWindow.xaml.cs">
      <DependentUpon>SettingsWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="App.xaml.cs">
      <DependentUpon>App.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Core\ConsoleLogger.cs" />
    <Compile Include="Core\SimpleExceptionHandler.cs" />
    <Compile Include="Events\DeviceStatusChangedEvent.cs" />
    <Compile Include="Events\ExceptionEvent.cs" />
    <Compile Include="Events\IEvent.cs" />
    <Compile Include="Events\SystemNotificationEvent.cs" />
    <Compile Include="Events\UserActionEvent.cs" />
    <Page Include="Themes\DarkTheme.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Themes\LightTheme.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\DataInputWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\EventBusDemoWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\ExceptionDemoWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\ModuleDemoWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\PluginDemoWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\SettingsWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Models\ExceptionLogEntry.cs" />
    <Compile Include="Models\HandlerInfo.cs" />
    <Compile Include="Models\NavigationCategory.cs" />
    <Compile Include="Models\NavigationItem.cs" />
    <Compile Include="Models\PageInfo.cs" />
    <Compile Include="Models\PluginDisplayInfo.cs" />
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <None Include="docs\README.md" />
    <None Include="docs\上拉框自动收起功能实现总结.md" />
    <None Include="docs\使用指南.md" />
    <None Include="docs\修复报告.md" />
    <None Include="docs\功能演示指南.md" />
    <None Include="docs\导航优化和进程退出修复总结.md" />
    <None Include="docs\导航视觉指示优化总结.md" />
    <None Include="docs\导航问题修复完成总结.md" />
    <None Include="docs\导航问题修复说明.md" />
    <None Include="docs\底部导航栏使用指南.md" />
    <None Include="docs\底部导航栏图标显示修复总结.md" />
    <None Include="docs\底部导航栏实现总结.md" />
    <None Include="docs\按钮功能测试指南.md" />
    <None Include="docs\最终修复验证.md" />
    <None Include="docs\视觉指示功能测试指南.md" />
    <None Include="docs\设备管理器页面集成总结.md" />
    <None Include="docs\项目说明.md" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\McLaser.Core\McLaser.Core.csproj">
      <Project>{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}</Project>
      <Name>McLaser.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\McLaser.Device\McLaser.Device.csproj">
      <Project>{f87277ce-d77d-47e0-b602-335625f0dba3}</Project>
      <Name>McLaser.Device</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>