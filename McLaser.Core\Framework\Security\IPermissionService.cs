using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace McLaser.Core.Framework.Security
{
    /// <summary>
    /// 权限服务接口
    /// 提供权限管理和授权功能
    /// </summary>
    public interface IPermissionService
    {
        #region 权限管理

        /// <summary>
        /// 创建权限
        /// </summary>
        /// <param name="permission">权限信息</param>
        /// <returns>创建结果</returns>
        Task<PermissionOperationResult> CreatePermissionAsync(Permission permission);

        /// <summary>
        /// 更新权限
        /// </summary>
        /// <param name="permission">权限信息</param>
        /// <returns>更新结果</returns>
        Task<PermissionOperationResult> UpdatePermissionAsync(Permission permission);

        /// <summary>
        /// 删除权限
        /// </summary>
        /// <param name="permissionId">权限ID</param>
        /// <returns>删除结果</returns>
        Task<PermissionOperationResult> DeletePermissionAsync(string permissionId);

        /// <summary>
        /// 根据ID获取权限
        /// </summary>
        /// <param name="permissionId">权限ID</param>
        /// <returns>权限信息</returns>
        Task<Permission?> GetPermissionByIdAsync(string permissionId);

        /// <summary>
        /// 根据名称获取权限
        /// </summary>
        /// <param name="permissionName">权限名称</param>
        /// <returns>权限信息</returns>
        Task<Permission?> GetPermissionByNameAsync(string permissionName);

        /// <summary>
        /// 获取所有权限
        /// </summary>
        /// <returns>权限列表</returns>
        Task<IEnumerable<Permission>> GetAllPermissionsAsync();

        /// <summary>
        /// 根据类别获取权限
        /// </summary>
        /// <param name="category">权限类别</param>
        /// <returns>权限列表</returns>
        Task<IEnumerable<Permission>> GetPermissionsByCategoryAsync(string category);

        /// <summary>
        /// 搜索权限
        /// </summary>
        /// <param name="searchTerm">搜索条件</param>
        /// <returns>权限列表</returns>
        Task<IEnumerable<Permission>> SearchPermissionsAsync(string searchTerm);

        #endregion

        #region 授权检查

        /// <summary>
        /// 检查用户是否拥有指定权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="permissionName">权限名称</param>
        /// <returns>是否拥有权限</returns>
        Task<bool> UserHasPermissionAsync(string userId, string permissionName);

        /// <summary>
        /// 检查用户是否拥有指定权限（按ID）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="permissionId">权限ID</param>
        /// <returns>是否拥有权限</returns>
        Task<bool> UserHasPermissionByIdAsync(string userId, string permissionId);

        /// <summary>
        /// 检查用户是否拥有任一权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="permissionNames">权限名称列表</param>
        /// <returns>是否拥有任一权限</returns>
        Task<bool> UserHasAnyPermissionAsync(string userId, IEnumerable<string> permissionNames);

        /// <summary>
        /// 检查用户是否拥有所有权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="permissionNames">权限名称列表</param>
        /// <returns>是否拥有所有权限</returns>
        Task<bool> UserHasAllPermissionsAsync(string userId, IEnumerable<string> permissionNames);

        /// <summary>
        /// 获取用户的所有权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>权限列表</returns>
        Task<IEnumerable<Permission>> GetUserPermissionsAsync(string userId);

        /// <summary>
        /// 获取用户的有效权限（包括通过角色继承的权限）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>权限列表</returns>
        Task<IEnumerable<Permission>> GetUserEffectivePermissionsAsync(string userId);

        #endregion

        #region 权限授予和撤销

        /// <summary>
        /// 直接授予用户权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="permissionId">权限ID</param>
        /// <returns>授予结果</returns>
        Task<PermissionOperationResult> GrantPermissionToUserAsync(string userId, string permissionId);

        /// <summary>
        /// 直接授予用户多个权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="permissionIds">权限ID列表</param>
        /// <returns>授予结果</returns>
        Task<PermissionOperationResult> GrantPermissionsToUserAsync(string userId, IEnumerable<string> permissionIds);

        /// <summary>
        /// 撤销用户权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="permissionId">权限ID</param>
        /// <returns>撤销结果</returns>
        Task<PermissionOperationResult> RevokePermissionFromUserAsync(string userId, string permissionId);

        /// <summary>
        /// 撤销用户的所有直接权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>撤销结果</returns>
        Task<PermissionOperationResult> RevokeAllPermissionsFromUserAsync(string userId);

        #endregion

        #region 权限层次结构

        /// <summary>
        /// 设置权限的父权限
        /// </summary>
        /// <param name="permissionId">权限ID</param>
        /// <param name="parentPermissionId">父权限ID</param>
        /// <returns>设置结果</returns>
        Task<PermissionOperationResult> SetParentPermissionAsync(string permissionId, string parentPermissionId);

        /// <summary>
        /// 移除权限的父权限
        /// </summary>
        /// <param name="permissionId">权限ID</param>
        /// <returns>移除结果</returns>
        Task<PermissionOperationResult> RemoveParentPermissionAsync(string permissionId);

        /// <summary>
        /// 获取子权限
        /// </summary>
        /// <param name="permissionId">权限ID</param>
        /// <returns>子权限列表</returns>
        Task<IEnumerable<Permission>> GetChildPermissionsAsync(string permissionId);

        /// <summary>
        /// 获取权限的所有后代权限
        /// </summary>
        /// <param name="permissionId">权限ID</param>
        /// <returns>后代权限列表</returns>
        Task<IEnumerable<Permission>> GetDescendantPermissionsAsync(string permissionId);

        #endregion

        #region 权限验证

        /// <summary>
        /// 验证权限名称是否有效
        /// </summary>
        /// <param name="permissionName">权限名称</param>
        /// <returns>验证结果</returns>
        bool ValidatePermissionName(string permissionName);

        /// <summary>
        /// 检查权限是否存在
        /// </summary>
        /// <param name="permissionName">权限名称</param>
        /// <returns>是否存在</returns>
        Task<bool> PermissionExistsAsync(string permissionName);

        /// <summary>
        /// 检查权限是否存在（按ID）
        /// </summary>
        /// <param name="permissionId">权限ID</param>
        /// <returns>是否存在</returns>
        Task<bool> PermissionExistsByIdAsync(string permissionId);

        #endregion

        #region 权限缓存

        /// <summary>
        /// 清除权限缓存
        /// </summary>
        void ClearPermissionCache();

        /// <summary>
        /// 清除用户权限缓存
        /// </summary>
        /// <param name="userId">用户ID</param>
        void ClearUserPermissionCache(string userId);

        /// <summary>
        /// 预加载用户权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>预加载任务</returns>
        Task PreloadUserPermissionsAsync(string userId);

        #endregion

        #region 事件

        /// <summary>
        /// 权限创建事件
        /// </summary>
        event EventHandler<PermissionCreatedEventArgs>? PermissionCreated;

        /// <summary>
        /// 权限更新事件
        /// </summary>
        event EventHandler<PermissionUpdatedEventArgs>? PermissionUpdated;

        /// <summary>
        /// 权限删除事件
        /// </summary>
        event EventHandler<PermissionDeletedEventArgs>? PermissionDeleted;

        /// <summary>
        /// 权限授予事件
        /// </summary>
        event EventHandler<PermissionGrantedEventArgs>? PermissionGranted;

        /// <summary>
        /// 权限撤销事件
        /// </summary>
        event EventHandler<PermissionRevokedEventArgs>? PermissionRevoked;

        /// <summary>
        /// 权限检查事件
        /// </summary>
        event EventHandler<PermissionCheckedEventArgs>? PermissionChecked;

        #endregion
    }

    #region 结果类

    /// <summary>
    /// 权限操作结果
    /// </summary>
    public class PermissionOperationResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public string? ErrorCode { get; set; }

        /// <summary>
        /// 附加数据
        /// </summary>
        public object? Data { get; set; }

        /// <summary>
        /// 创建成功结果
        /// </summary>
        public static PermissionOperationResult CreateSuccess(object? data = null)
        {
            return new PermissionOperationResult { Success = true, Data = data };
        }

        /// <summary>
        /// 创建失败结果
        /// </summary>
        public static PermissionOperationResult CreateFailure(string errorMessage, string? errorCode = null)
        {
            return new PermissionOperationResult 
            { 
                Success = false, 
                ErrorMessage = errorMessage, 
                ErrorCode = errorCode 
            };
        }
    }

    #endregion
}
