using System;
using System.Collections.Generic;
using System.Windows;

namespace McLaser.Core.Framework.Styles
{
    /// <summary>
    /// 样式服务接口
    /// 提供统一的WPF样式和主题管理功能
    /// </summary>
    public interface IStyleService
    {
        #region 样式管理

        /// <summary>
        /// 当前样式集合名称
        /// </summary>
        string CurrentStyleSet { get; }

        /// <summary>
        /// 可用样式集合列表
        /// </summary>
        IReadOnlyList<string> AvailableStyleSets { get; }

        /// <summary>
        /// 注册样式集合
        /// </summary>
        /// <param name="name">样式集合名称</param>
        /// <param name="resourceDictionary">样式资源字典</param>
        void RegisterStyleSet(string name, ResourceDictionary resourceDictionary);

        /// <summary>
        /// 注册样式集合（从URI加载）
        /// </summary>
        /// <param name="name">样式集合名称</param>
        /// <param name="resourceUri">资源URI</param>
        void RegisterStyleSet(string name, Uri resourceUri);

        /// <summary>
        /// 移除样式集合
        /// </summary>
        /// <param name="name">样式集合名称</param>
        /// <returns>是否成功移除</returns>
        bool RemoveStyleSet(string name);

        /// <summary>
        /// 应用样式集合
        /// </summary>
        /// <param name="name">样式集合名称</param>
        /// <returns>是否成功应用</returns>
        bool ApplyStyleSet(string name);

        #endregion

        #region 样式查询

        /// <summary>
        /// 获取样式资源
        /// </summary>
        /// <param name="key">样式键</param>
        /// <returns>样式资源</returns>
        Style? GetStyle(string key);

        /// <summary>
        /// 获取样式资源（泛型）
        /// </summary>
        /// <typeparam name="T">资源类型</typeparam>
        /// <param name="key">资源键</param>
        /// <returns>资源对象</returns>
        T? GetResource<T>(string key) where T : class;

        /// <summary>
        /// 检查样式是否存在
        /// </summary>
        /// <param name="key">样式键</param>
        /// <returns>是否存在</returns>
        bool HasStyle(string key);

        /// <summary>
        /// 获取所有样式键
        /// </summary>
        /// <returns>样式键列表</returns>
        IEnumerable<string> GetAllStyleKeys();

        #endregion

        #region 动态样式

        /// <summary>
        /// 动态设置样式属性
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="property">依赖属性</param>
        /// <param name="value">属性值</param>
        void SetStyleProperty(FrameworkElement element, DependencyProperty property, object value);

        /// <summary>
        /// 动态获取样式属性
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="property">依赖属性</param>
        /// <returns>属性值</returns>
        object? GetStyleProperty(FrameworkElement element, DependencyProperty property);

        /// <summary>
        /// 应用样式到元素
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="styleKey">样式键</param>
        void ApplyStyleToElement(FrameworkElement element, string styleKey);

        #endregion

        #region 响应式设计

        /// <summary>
        /// 当前DPI缩放比例
        /// </summary>
        double DpiScale { get; }

        /// <summary>
        /// 当前屏幕尺寸类别
        /// </summary>
        ScreenSizeCategory ScreenSizeCategory { get; }

        /// <summary>
        /// 根据DPI获取适应的样式
        /// </summary>
        /// <param name="baseStyleKey">基础样式键</param>
        /// <returns>适应的样式</returns>
        Style? GetDpiAdaptiveStyle(string baseStyleKey);

        /// <summary>
        /// 根据屏幕尺寸获取适应的样式
        /// </summary>
        /// <param name="baseStyleKey">基础样式键</param>
        /// <returns>适应的样式</returns>
        Style? GetScreenSizeAdaptiveStyle(string baseStyleKey);

        #endregion

        #region 样式缓存

        /// <summary>
        /// 清除样式缓存
        /// </summary>
        void ClearStyleCache();

        /// <summary>
        /// 预加载样式集合
        /// </summary>
        /// <param name="styleSetName">样式集合名称</param>
        void PreloadStyleSet(string styleSetName);

        /// <summary>
        /// 获取样式缓存统计信息
        /// </summary>
        /// <returns>缓存统计信息</returns>
        StyleCacheStatistics GetCacheStatistics();

        #endregion

        #region 事件

        /// <summary>
        /// 样式集合变更事件
        /// </summary>
        event EventHandler<StyleSetChangedEventArgs>? StyleSetChanged;

        /// <summary>
        /// 样式应用事件
        /// </summary>
        event EventHandler<StyleAppliedEventArgs>? StyleApplied;

        /// <summary>
        /// DPI变更事件
        /// </summary>
        event EventHandler<DpiChangedEventArgs>? DpiChanged;

        #endregion
    }

    #region 枚举和数据类

    /// <summary>
    /// 屏幕尺寸类别
    /// </summary>
    public enum ScreenSizeCategory
    {
        /// <summary>
        /// 小屏幕（手机、小平板）
        /// </summary>
        Small,

        /// <summary>
        /// 中等屏幕（平板、小笔记本）
        /// </summary>
        Medium,

        /// <summary>
        /// 大屏幕（桌面显示器）
        /// </summary>
        Large,

        /// <summary>
        /// 超大屏幕（4K显示器）
        /// </summary>
        ExtraLarge
    }

    /// <summary>
    /// 样式缓存统计信息
    /// </summary>
    public class StyleCacheStatistics
    {
        /// <summary>
        /// 缓存的样式数量
        /// </summary>
        public int CachedStylesCount { get; set; }

        /// <summary>
        /// 缓存命中次数
        /// </summary>
        public long CacheHits { get; set; }

        /// <summary>
        /// 缓存未命中次数
        /// </summary>
        public long CacheMisses { get; set; }

        /// <summary>
        /// 缓存命中率
        /// </summary>
        public double HitRatio => CacheHits + CacheMisses > 0 ? (double)CacheHits / (CacheHits + CacheMisses) : 0;

        /// <summary>
        /// 内存使用量（字节）
        /// </summary>
        public long MemoryUsage { get; set; }
    }

    #endregion

    #region 事件参数类

    /// <summary>
    /// 样式集合变更事件参数
    /// </summary>
    public class StyleSetChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 旧样式集合名称
        /// </summary>
        public string? OldStyleSet { get; }

        /// <summary>
        /// 新样式集合名称
        /// </summary>
        public string NewStyleSet { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public StyleSetChangedEventArgs(string? oldStyleSet, string newStyleSet)
        {
            OldStyleSet = oldStyleSet;
            NewStyleSet = newStyleSet;
        }
    }

    /// <summary>
    /// 样式应用事件参数
    /// </summary>
    public class StyleAppliedEventArgs : EventArgs
    {
        /// <summary>
        /// 目标元素
        /// </summary>
        public FrameworkElement Element { get; }

        /// <summary>
        /// 样式键
        /// </summary>
        public string StyleKey { get; }

        /// <summary>
        /// 应用的样式
        /// </summary>
        public Style Style { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public StyleAppliedEventArgs(FrameworkElement element, string styleKey, Style style)
        {
            Element = element;
            StyleKey = styleKey;
            Style = style;
        }
    }

    /// <summary>
    /// DPI变更事件参数
    /// </summary>
    public class DpiChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 旧DPI缩放比例
        /// </summary>
        public double OldDpiScale { get; }

        /// <summary>
        /// 新DPI缩放比例
        /// </summary>
        public double NewDpiScale { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public DpiChangedEventArgs(double oldDpiScale, double newDpiScale)
        {
            OldDpiScale = oldDpiScale;
            NewDpiScale = newDpiScale;
        }
    }

    #endregion
}
