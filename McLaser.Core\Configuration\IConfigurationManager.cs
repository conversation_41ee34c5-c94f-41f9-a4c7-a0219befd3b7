using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace McLaser.Core.Configuration
{
    /// <summary>
    /// 配置管理器接口
    /// 提供统一的配置读取、写入、监控和管理功能
    /// </summary>
    public interface IConfigurationManager
    {
        #region 基本配置操作

        /// <summary>
        /// 获取配置值
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        T GetValue<T>(string key, T defaultValue = default(T));

        /// <summary>
        /// 异步获取配置值
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        Task<T> GetValueAsync<T>(string key, T defaultValue = default(T));

        /// <summary>
        /// 设置配置值
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        void SetValue<T>(string key, T value);

        /// <summary>
        /// 异步设置配置值
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        /// <returns>异步任务</returns>
        Task SetValueAsync<T>(string key, T value);

        /// <summary>
        /// 检查配置键是否存在
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>是否存在</returns>
        bool ContainsKey(string key);

        /// <summary>
        /// 删除配置项
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>是否删除成功</returns>
        bool RemoveKey(string key);

        /// <summary>
        /// 获取所有配置键
        /// </summary>
        /// <returns>配置键集合</returns>
        IEnumerable<string> GetAllKeys();

        #endregion

        #region 配置节操作

        /// <summary>
        /// 获取配置节
        /// </summary>
        /// <typeparam name="T">配置节类型</typeparam>
        /// <param name="sectionName">配置节名称</param>
        /// <returns>配置节对象</returns>
        T GetSection<T>(string sectionName) where T : class, new();

        /// <summary>
        /// 设置配置节
        /// </summary>
        /// <typeparam name="T">配置节类型</typeparam>
        /// <param name="sectionName">配置节名称</param>
        /// <param name="section">配置节对象</param>
        void SetSection<T>(string sectionName, T section) where T : class;

        /// <summary>
        /// 获取配置节的原始数据
        /// </summary>
        /// <param name="sectionName">配置节名称</param>
        /// <returns>配置数据字典</returns>
        IDictionary<string, object> GetSectionData(string sectionName);

        #endregion

        #region 配置提供者管理

        /// <summary>
        /// 添加配置提供者
        /// </summary>
        /// <param name="provider">配置提供者</param>
        /// <param name="priority">优先级（数值越大优先级越高）</param>
        void AddProvider(IConfigurationProvider provider, int priority = 0);

        /// <summary>
        /// 移除配置提供者
        /// </summary>
        /// <param name="provider">配置提供者</param>
        void RemoveProvider(IConfigurationProvider provider);

        /// <summary>
        /// 获取所有配置提供者
        /// </summary>
        /// <returns>配置提供者集合</returns>
        IEnumerable<IConfigurationProvider> GetProviders();

        #endregion

        #region 配置监控和通知

        /// <summary>
        /// 配置变更事件
        /// </summary>
        event EventHandler<ConfigurationChangedEventArgs> ConfigurationChanged;

        /// <summary>
        /// 开始监控配置变更
        /// </summary>
        void StartWatching();

        /// <summary>
        /// 停止监控配置变更
        /// </summary>
        void StopWatching();

        /// <summary>
        /// 是否正在监控配置变更
        /// </summary>
        bool IsWatching { get; }

        #endregion

        #region 配置持久化

        /// <summary>
        /// 保存配置到持久化存储
        /// </summary>
        /// <returns>异步任务</returns>
        Task SaveAsync();

        /// <summary>
        /// 从持久化存储重新加载配置
        /// </summary>
        /// <returns>异步任务</returns>
        Task ReloadAsync();

        /// <summary>
        /// 重置配置为默认值
        /// </summary>
        void Reset();

        #endregion

        #region 配置验证

        /// <summary>
        /// 验证配置
        /// </summary>
        /// <returns>验证结果</returns>
        ConfigurationValidationResult Validate();

        /// <summary>
        /// 添加配置验证规则
        /// </summary>
        /// <param name="rule">验证规则</param>
        void AddValidationRule(IConfigurationValidationRule rule);

        /// <summary>
        /// 移除配置验证规则
        /// </summary>
        /// <param name="rule">验证规则</param>
        void RemoveValidationRule(IConfigurationValidationRule rule);

        #endregion

        #region 配置加密

        /// <summary>
        /// 设置加密的配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        void SetEncryptedValue(string key, string value);

        /// <summary>
        /// 获取加密的配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>解密后的配置值</returns>
        string GetEncryptedValue(string key, string defaultValue = null);

        #endregion

        #region 配置导入导出

        /// <summary>
        /// 导出配置到文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="format">导出格式</param>
        /// <returns>异步任务</returns>
        Task ExportAsync(string filePath, ConfigurationFormat format = ConfigurationFormat.Json);

        /// <summary>
        /// 从文件导入配置
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="format">导入格式</param>
        /// <param name="merge">是否合并现有配置</param>
        /// <returns>异步任务</returns>
        Task ImportAsync(string filePath, ConfigurationFormat format = ConfigurationFormat.Json, bool merge = true);

        #endregion

        #region 配置统计和诊断

        /// <summary>
        /// 获取配置统计信息
        /// </summary>
        /// <returns>配置统计信息</returns>
        ConfigurationStatistics GetStatistics();

        /// <summary>
        /// 获取配置诊断信息
        /// </summary>
        /// <returns>配置诊断信息</returns>
        ConfigurationDiagnostics GetDiagnostics();

        #endregion
    }

    /// <summary>
    /// 配置格式枚举
    /// </summary>
    public enum ConfigurationFormat
    {
        /// <summary>
        /// JSON格式
        /// </summary>
        Json,

        /// <summary>
        /// XML格式
        /// </summary>
        Xml,

        /// <summary>
        /// INI格式
        /// </summary>
        Ini,

        /// <summary>
        /// YAML格式
        /// </summary>
        Yaml
    }
}
