using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Threading;
using McLaser.Core.Plugins;

namespace McLaser.Plugins.Samples
{
    /// <summary>
    /// 示例状态栏插件
    /// 演示UI扩展插件功能
    /// </summary>
    [PluginMetadata(
        Id = "McLaser.Plugins.StatusBar",
        Name = "示例状态栏",
        Version = "1.0.0",
        Description = "提供系统状态显示的状态栏插件",
        Author = "McLaser Team",
        Category = "界面"
    )]
    public class StatusBarPlugin : PluginBase
    {
        #region 私有字段

        private StatusBar _statusBar;
        private TextBlock _statusText;
        private TextBlock _timeText;
        private TextBlock _memoryText;
        private ProgressBar _progressBar;
        private Timer _updateTimer;
        private readonly DispatcherTimer _uiTimer;

        #endregion

        #region 属性

        /// <summary>
        /// 状态栏UI元素
        /// </summary>
        public StatusBar StatusBarElement => _statusBar;

        /// <summary>
        /// 当前状态文本
        /// </summary>
        public string CurrentStatus { get; private set; } = "就绪";

        /// <summary>
        /// 更新间隔（毫秒）
        /// </summary>
        public int UpdateInterval { get; set; } = 1000;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化状态栏插件
        /// </summary>
        public StatusBarPlugin()
        {
            // 创建UI更新定时器
            _uiTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(1000)
            };
            _uiTimer.Tick += OnUiTimerTick;
        }

        #endregion

        #region 插件生命周期

        /// <summary>
        /// 初始化插件
        /// </summary>
        public override async Task InitializeAsync(IPluginContext context, CancellationToken cancellationToken = default)
        {
            await base.InitializeAsync(context, cancellationToken);
            
            OnStatusChanged(PluginStatus.Initializing);
            
            // 在UI线程中创建状态栏
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                CreateStatusBar();
            });
            
            OnStatusChanged(PluginStatus.Initialized);
            LogMessage("状态栏插件初始化完成", LogLevel.Info);
        }

        /// <summary>
        /// 启动插件
        /// </summary>
        public override async Task StartAsync(CancellationToken cancellationToken = default)
        {
            await base.StartAsync(cancellationToken);
            
            OnStatusChanged(PluginStatus.Starting);
            
            // 启动更新定时器
            _uiTimer.Start();
            
            OnStatusChanged(PluginStatus.Running);
            SetStatus("状态栏插件已启动");
            LogMessage("状态栏插件已启动", LogLevel.Info);
        }

        /// <summary>
        /// 停止插件
        /// </summary>
        public override async Task StopAsync(CancellationToken cancellationToken = default)
        {
            OnStatusChanged(PluginStatus.Stopping);
            
            // 停止定时器
            _uiTimer.Stop();
            
            SetStatus("状态栏插件已停止");
            
            await base.StopAsync(cancellationToken);
            OnStatusChanged(PluginStatus.Stopped);
            LogMessage("状态栏插件已停止", LogLevel.Info);
        }

        #endregion

        #region UI创建

        /// <summary>
        /// 创建状态栏
        /// </summary>
        private void CreateStatusBar()
        {
            try
            {
                _statusBar = new StatusBar
                {
                    Height = 25,
                    Background = System.Windows.Media.Brushes.LightGray
                };

                // 状态文本
                _statusText = new TextBlock
                {
                    Text = CurrentStatus,
                    Margin = new Thickness(5, 0, 5, 0),
                    VerticalAlignment = VerticalAlignment.Center
                };

                // 时间显示
                _timeText = new TextBlock
                {
                    Text = DateTime.Now.ToString("HH:mm:ss"),
                    Margin = new Thickness(5, 0, 5, 0),
                    VerticalAlignment = VerticalAlignment.Center,
                    MinWidth = 60
                };

                // 内存使用显示
                _memoryText = new TextBlock
                {
                    Text = "内存: 0 MB",
                    Margin = new Thickness(5, 0, 5, 0),
                    VerticalAlignment = VerticalAlignment.Center,
                    MinWidth = 80
                };

                // 进度条
                _progressBar = new ProgressBar
                {
                    Width = 100,
                    Height = 15,
                    Margin = new Thickness(5, 0, 5, 0),
                    VerticalAlignment = VerticalAlignment.Center,
                    Minimum = 0,
                    Maximum = 100,
                    Value = 0,
                    Visibility = Visibility.Collapsed
                };

                // 添加状态栏项
                _statusBar.Items.Add(new StatusBarItem { Content = _statusText });
                _statusBar.Items.Add(new Separator());
                _statusBar.Items.Add(new StatusBarItem { Content = _progressBar });
                _statusBar.Items.Add(new StatusBarItem { Content = _memoryText, HorizontalAlignment = HorizontalAlignment.Right });
                _statusBar.Items.Add(new StatusBarItem { Content = _timeText, HorizontalAlignment = HorizontalAlignment.Right });

                LogMessage("状态栏UI已创建", LogLevel.Debug);
            }
            catch (Exception ex)
            {
                LogMessage($"创建状态栏失败: {ex.Message}", LogLevel.Error);
                OnErrorOccurred($"创建状态栏失败: {ex.Message}", ex);
            }
        }

        #endregion

        #region 状态管理

        /// <summary>
        /// 设置状态文本
        /// </summary>
        /// <param name="status">状态文本</param>
        public void SetStatus(string status)
        {
            CurrentStatus = status;
            
            Application.Current.Dispatcher.BeginInvoke(new Action(() =>
            {
                if (_statusText != null)
                {
                    _statusText.Text = status;
                }
            }));
            
            LogMessage($"状态更新: {status}", LogLevel.Debug);
        }

        /// <summary>
        /// 显示进度
        /// </summary>
        /// <param name="value">进度值 (0-100)</param>
        /// <param name="visible">是否显示进度条</param>
        public void SetProgress(double value, bool visible = true)
        {
            Application.Current.Dispatcher.BeginInvoke(new Action(() =>
            {
                if (_progressBar != null)
                {
                    _progressBar.Value = Math.Max(0, Math.Min(100, value));
                    _progressBar.Visibility = visible ? Visibility.Visible : Visibility.Collapsed;
                }
            }));
        }

        /// <summary>
        /// 隐藏进度条
        /// </summary>
        public void HideProgress()
        {
            SetProgress(0, false);
        }

        /// <summary>
        /// UI定时器事件处理
        /// </summary>
        private void OnUiTimerTick(object sender, EventArgs e)
        {
            try
            {
                // 更新时间
                if (_timeText != null)
                {
                    _timeText.Text = DateTime.Now.ToString("HH:mm:ss");
                }

                // 更新内存使用
                if (_memoryText != null)
                {
                    var memoryUsage = GC.GetTotalMemory(false) / (1024 * 1024);
                    _memoryText.Text = $"内存: {memoryUsage} MB";
                }
            }
            catch (Exception ex)
            {
                LogMessage($"更新状态栏信息失败: {ex.Message}", LogLevel.Error);
            }
        }

        #endregion

        #region 统计信息

        /// <summary>
        /// 获取统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public Dictionary<string, object> GetStatistics()
        {
            var memoryUsage = GC.GetTotalMemory(false);
            
            return new Dictionary<string, object>
            {
                { "CurrentStatus", CurrentStatus },
                { "UpdateInterval", UpdateInterval },
                { "IsUICreated", _statusBar != null },
                { "IsTimerRunning", _uiTimer.IsEnabled },
                { "MemoryUsage", memoryUsage },
                { "MemoryUsageFormatted", $"{memoryUsage / (1024 * 1024)} MB" },
                { "Status", Status.ToString() },
                { "StartTime", StartTime },
                { "RunTime", Status == PluginStatus.Running ? DateTime.Now - StartTime : TimeSpan.Zero }
            };
        }

        #endregion

        #region 配置管理

        /// <summary>
        /// 获取默认配置
        /// </summary>
        public override Dictionary<string, object> GetDefaultConfiguration()
        {
            return new Dictionary<string, object>
            {
                { "UpdateInterval", 1000 },
                { "ShowTime", true },
                { "ShowMemory", true },
                { "ShowProgress", true },
                { "StatusBarHeight", 25 },
                { "BackgroundColor", "LightGray" }
            };
        }

        /// <summary>
        /// 验证配置
        /// </summary>
        public override bool ValidateConfiguration(Dictionary<string, object> configuration)
        {
            try
            {
                if (configuration.TryGetValue("UpdateInterval", out var interval))
                {
                    var intervalValue = Convert.ToInt32(interval);
                    if (intervalValue < 100 || intervalValue > 10000)
                    {
                        return false;
                    }
                }

                if (configuration.TryGetValue("StatusBarHeight", out var height))
                {
                    var heightValue = Convert.ToDouble(height);
                    if (heightValue < 20 || heightValue > 50)
                    {
                        return false;
                    }
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 记录日志消息
        /// </summary>
        private void LogMessage(string message, LogLevel level)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[StatusBarPlugin] [{level}] {message}");
            }
            catch
            {
                // 忽略日志错误
            }
        }

        #endregion

        #region 资源清理

        /// <summary>
        /// 释放资源
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                if (_uiTimer != null)
                    _uiTimer.Stop();
                if (_updateTimer != null)
                    _updateTimer.Dispose();
                
                // 清理UI资源
                Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                {
                    _statusBar = null;
                    _statusText = null;
                    _timeText = null;
                    _memoryText = null;
                    _progressBar = null;
                }));
                
                LogMessage("状态栏插件资源已释放", LogLevel.Info);
            }
            base.Dispose(disposing);
        }

        #endregion
    }
}
