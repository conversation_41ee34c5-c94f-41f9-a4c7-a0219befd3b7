using System;
using System.ComponentModel.Composition;
using System.IO;
using System.Text;

namespace McLaser.Core.Framework.Logging
{
    /// <summary>
    /// 默认日志记录器实现
    /// 支持文件和控制台输出
    /// </summary>
    [Export(typeof(ILogger))]
    public class DefaultLogger : ILogger
    {
        private readonly string _name;
        private readonly LogLevel _minLevel;
        private readonly string? _logFilePath;
        private readonly object _lockObject = new object();

        public DefaultLogger()
        {
                
        }

        /// <summary>
        /// 初始化日志记录器
        /// </summary>
        /// <param name="name">日志记录器名称</param>
        /// <param name="minLevel">最小日志级别</param>
        /// <param name="logFilePath">日志文件路径，为null时只输出到控制台</param>
        public DefaultLogger(string name, LogLevel minLevel = LogLevel.Info, string? logFilePath = null)
        {
            _name = name ?? throw new ArgumentNullException(nameof(name));
            _minLevel = minLevel;
            _logFilePath = logFilePath;

            // 确保日志目录存在
            if (!string.IsNullOrEmpty(_logFilePath))
            {
                var directory = Path.GetDirectoryName(_logFilePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
            }
        }

        /// <summary>
        /// 检查是否启用调试级别
        /// </summary>
        public bool IsDebugEnabled => _minLevel <= LogLevel.Debug;

        /// <summary>
        /// 检查是否启用信息级别
        /// </summary>
        public bool IsInfoEnabled => _minLevel <= LogLevel.Info;

        /// <summary>
        /// 检查是否启用警告级别
        /// </summary>
        public bool IsWarnEnabled => _minLevel <= LogLevel.Warn;

        /// <summary>
        /// 检查是否启用错误级别
        /// </summary>
        public bool IsErrorEnabled => _minLevel <= LogLevel.Error;

        /// <summary>
        /// 检查是否启用致命错误级别
        /// </summary>
        public bool IsFatalEnabled => _minLevel <= LogLevel.Fatal;

        /// <summary>
        /// 记录调试信息
        /// </summary>
        /// <param name="message">消息</param>
        public void Debug(string message)
        {
            if (IsDebugEnabled)
                WriteLog(LogLevel.Debug, message);
        }

        /// <summary>
        /// 记录调试信息
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="args">格式化参数</param>
        public void Debug(string message, params object[] args)
        {
            if (IsDebugEnabled)
                WriteLog(LogLevel.Debug, string.Format(message, args));
        }

        /// <summary>
        /// 记录信息
        /// </summary>
        /// <param name="message">消息</param>
        public void Info(string message)
        {
            if (IsInfoEnabled)
                WriteLog(LogLevel.Info, message);
        }

        /// <summary>
        /// 记录信息
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="args">格式化参数</param>
        public void Info(string message, params object[] args)
        {
            if (IsInfoEnabled)
                WriteLog(LogLevel.Info, string.Format(message, args));
        }

        /// <summary>
        /// 记录警告
        /// </summary>
        /// <param name="message">消息</param>
        public void Warn(string message)
        {
            if (IsWarnEnabled)
                WriteLog(LogLevel.Warn, message);
        }

        /// <summary>
        /// 记录警告
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="args">格式化参数</param>
        public void Warn(string message, params object[] args)
        {
            if (IsWarnEnabled)
                WriteLog(LogLevel.Warn, string.Format(message, args));
        }

        /// <summary>
        /// 记录错误
        /// </summary>
        /// <param name="message">消息</param>
        public void Error(string message)
        {
            if (IsErrorEnabled)
                WriteLog(LogLevel.Error, message);
        }

        /// <summary>
        /// 记录错误
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="args">格式化参数</param>
        public void Error(string message, params object[] args)
        {
            if (IsErrorEnabled)
                WriteLog(LogLevel.Error, string.Format(message, args));
        }

        /// <summary>
        /// 记录错误和异常
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="message">消息</param>
        public void Error(Exception exception, string message = "")
        {
            if (IsErrorEnabled)
            {
                var fullMessage = string.IsNullOrEmpty(message) 
                    ? exception.ToString() 
                    : $"{message}\n{exception}";
                WriteLog(LogLevel.Error, fullMessage);
            }
        }

        /// <summary>
        /// 记录致命错误
        /// </summary>
        /// <param name="message">消息</param>
        public void Fatal(string message)
        {
            if (IsFatalEnabled)
                WriteLog(LogLevel.Fatal, message);
        }

        /// <summary>
        /// 记录致命错误
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="args">格式化参数</param>
        public void Fatal(string message, params object[] args)
        {
            if (IsFatalEnabled)
                WriteLog(LogLevel.Fatal, string.Format(message, args));
        }

        /// <summary>
        /// 记录致命错误和异常
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="message">消息</param>
        public void Fatal(Exception exception, string message = "")
        {
            if (IsFatalEnabled)
            {
                var fullMessage = string.IsNullOrEmpty(message) 
                    ? exception.ToString() 
                    : $"{message}\n{exception}";
                WriteLog(LogLevel.Fatal, fullMessage);
            }
        }

        /// <summary>
        /// 写入日志
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">消息</param>
        private void WriteLog(LogLevel level, string message)
        {
            if (level < _minLevel)
                return;

            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var logEntry = $"[{timestamp}] [{level.ToString().ToUpper()}] [{_name}] {message}";

            lock (_lockObject)
            {
                // 输出到控制台
                Console.WriteLine(logEntry);

                // 输出到文件
                if (!string.IsNullOrEmpty(_logFilePath))
                {
                    try
                    {
                        File.AppendAllText(_logFilePath, logEntry + Environment.NewLine, Encoding.UTF8);
                    }
                    catch
                    {
                        // 忽略文件写入错误，避免日志记录本身导致异常
                    }
                }
            }
        }
    }

    /// <summary>
    /// 默认日志工厂实现
    /// </summary>
    public class DefaultLoggerFactory : ILoggerFactory
    {
        private readonly LogLevel _minLevel;
        private readonly string? _logDirectory;

        /// <summary>
        /// 初始化日志工厂
        /// </summary>
        /// <param name="minLevel">最小日志级别</param>
        /// <param name="logDirectory">日志目录，为null时只输出到控制台</param>
        public DefaultLoggerFactory(LogLevel minLevel = LogLevel.Info, string? logDirectory = null)
        {
            _minLevel = minLevel;
            _logDirectory = logDirectory;
        }

        /// <summary>
        /// 创建日志记录器
        /// </summary>
        /// <param name="name">日志记录器名称</param>
        /// <returns>日志记录器实例</returns>
        public ILogger CreateLogger(string name)
        {
            string? logFilePath = null;
            if (!string.IsNullOrEmpty(_logDirectory))
            {
                var fileName = $"{DateTime.Now:yyyy-MM-dd}.log";
                logFilePath = Path.Combine(_logDirectory, fileName);
            }

            return new DefaultLogger(name, _minLevel, logFilePath);
        }

        /// <summary>
        /// 创建日志记录器
        /// </summary>
        /// <typeparam name="T">类型</typeparam>
        /// <returns>日志记录器实例</returns>
        public ILogger CreateLogger<T>()
        {
            return CreateLogger(typeof(T).Name);
        }

        /// <summary>
        /// 创建日志记录器
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>日志记录器实例</returns>
        public ILogger CreateLogger(Type type)
        {
            return CreateLogger(type.Name);
        }
    }
}
