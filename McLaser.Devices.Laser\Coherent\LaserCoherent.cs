using System;
using System.ComponentModel;
using System.IO.Ports;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Xml.Serialization;
using McLaser.Device;

namespace McLaser.Devices.Laser.Coherent
{
    /// <summary>
    /// Coherent激光器驱动实现
    /// 支持Coherent CO2激光器的控制和监控功能
    /// </summary>
    [Category("激光器")]
    [DisplayName("Coherent激光器")]
    [Serializable]
    public class LaserCoherent : LaserBase
    {
        #region 私有字段

        /// <summary>
        /// 串口对象
        /// </summary>
        private SerialPort serialPort;

        /// <summary>
        /// 状态监控任务取消令牌
        /// </summary>
        private CancellationTokenSource statusCancellationToken;

        /// <summary>
        /// 线程锁对象
        /// </summary>
        private static readonly object objLock = new object();

        #endregion

        #region 属性

        /// <summary>
        /// 串口名称
        /// </summary>
        [Category("Coherent连接"), DisplayName("串口名称")]
        public string PortName { get; set; } = "COM1";

        /// <summary>
        /// 波特率
        /// </summary>
        [Category("Coherent连接"), DisplayName("波特率")]
        public int BaudRate { get; set; } = 9600;

        /// <summary>
        /// 数据位
        /// </summary>
        [Category("Coherent连接"), DisplayName("数据位")]
        public int DataBits { get; set; } = 8;

        /// <summary>
        /// 停止位
        /// </summary>
        [Category("Coherent连接"), DisplayName("停止位")]
        public StopBits StopBits { get; set; } = StopBits.One;

        /// <summary>
        /// 校验位
        /// </summary>
        [Category("Coherent连接"), DisplayName("校验位")]
        public Parity Parity { get; set; } = Parity.None;

        /// <summary>
        /// 读取超时时间(毫秒)
        /// </summary>
        [Category("Coherent连接"), DisplayName("读取超时(ms)")]
        public int ReadTimeout { get; set; } = 1000;

        /// <summary>
        /// 写入超时时间(毫秒)
        /// </summary>
        [Category("Coherent连接"), DisplayName("写入超时(ms)")]
        public int WriteTimeout { get; set; } = 1000;

        /// <summary>
        /// 激光器类型
        /// </summary>
        [Category("Coherent信息"), DisplayName("激光器类型")]
        public override LaserType LaserType => LaserType.CO2;

        /// <summary>
        /// 最大功率(W)
        /// </summary>
        [Category("Coherent信息"), DisplayName("最大功率(W)")]
        public override double MaxPower { get; set; } = 100;

        /// <summary>
        /// 最小功率(W)
        /// </summary>
        [Category("Coherent信息"), DisplayName("最小功率(W)")]
        public override double MinPower => 0;

        /// <summary>
        /// 最大频率(Hz)
        /// </summary>
        [Category("Coherent信息"), DisplayName("最大频率(Hz)")]
        public override double MaxFrequency => 50000;

        /// <summary>
        /// 最小频率(Hz)
        /// </summary>
        [Category("Coherent信息"), DisplayName("最小频率(Hz)")]
        public override double MinFrequency => 1;

        /// <summary>
        /// 激光器状态
        /// </summary>
        [Category("Coherent状态"), DisplayName("激光器状态")]
        public override LaserStatus LaserStatus
        {
            get
            {
                if (Status is StatusCoherent coherentStatus)
                    return coherentStatus.LaserStatus;
                return LaserStatus.Unknown;
            }
        }

        /// <summary>
        /// 设备状态
        /// </summary>
        [XmlIgnore, Browsable(false)]
        public override StatusDevice Status { get; set; } = new StatusCoherent();

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public LaserCoherent()
        {
            Name = "Coherent激光器";
            DeviceType = DeviceType.Laser;
        }

        #endregion

        #region 设备基本操作

        /// <summary>
        /// 打开设备连接
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool Open()
        {
            try
            {
                if (!IsEnabled) return false;
                if (Status.IsConnected) Close();

                // 创建串口连接
                serialPort = new SerialPort(PortName, BaudRate, Parity, DataBits, StopBits);
                serialPort.ReadTimeout = ReadTimeout;
                serialPort.WriteTimeout = WriteTimeout;
                serialPort.NewLine = "\r\n";

                serialPort.Open();
                Status.IsConnected = true;

                // 启动状态监控
                StartStatusMonitoring();

                // 初始化激光器
                if (!InitializeLaser())
                {
                    Close();
                    return false;
                }

                return IsOpen();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"打开Coherent激光器异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查设备是否已打开
        /// </summary>
        /// <returns>是否已打开</returns>
        public override bool IsOpen()
        {
            return Status.IsConnected && serialPort != null && serialPort.IsOpen;
        }

        /// <summary>
        /// 关闭设备连接
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool Close()
        {
            try
            {
                // 关闭激光器
                LaserOff();

                // 停止状态监控
                StopStatusMonitoring();

                // 关闭串口连接
                serialPort?.Close();
                serialPort?.Dispose();
                serialPort = null;

                Status.IsConnected = false;
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"关闭Coherent激光器异常：{ex.Message}");
                return false;
            }
        }

        #endregion

        #region 激光器控制

        /// <summary>
        /// 打开激光器
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool LaserOn()
        {
            try
            {
                if (!IsOpen()) return false;

                string command = "L1";
                if (SendCommand(command))
                {
                    IsLaserEnabled = true;
                    if (Status is StatusCoherent coherentStatus)
                    {
                        coherentStatus.IsLaserOn = true;
                        coherentStatus.LaserStatus = LaserStatus.LaserOn;
                    }
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Coherent激光器开启异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 关闭激光器
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool LaserOff()
        {
            try
            {
                if (!IsOpen()) return false;

                string command = "L0";
                if (SendCommand(command))
                {
                    IsLaserEnabled = false;
                    if (Status is StatusCoherent coherentStatus)
                    {
                        coherentStatus.IsLaserOn = false;
                        coherentStatus.LaserStatus = LaserStatus.LaserOff;
                    }
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Coherent激光器关闭异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置激光器功率
        /// </summary>
        /// <param name="power">功率值(0-100%)</param>
        /// <returns>是否成功</returns>
        public override bool SetPower(double power)
        {
            try
            {
                if (!IsOpen()) return false;

                power = Math.Max(0, Math.Min(100, power));
                string command = $"P{power:F2}";
                
                if (SendCommand(command))
                {
                    Power = power;
                    if (Status is StatusCoherent coherentStatus)
                    {
                        coherentStatus.CurrentPower = power;
                    }
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置Coherent激光器功率异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取激光器功率
        /// </summary>
        /// <param name="power">功率值</param>
        /// <returns>是否成功</returns>
        public override bool GetPower(ref double power)
        {
            try
            {
                if (!IsOpen()) return false;

                string command = "P?";
                string response = string.Empty;
                
                if (SendCommand(command, out response))
                {
                    if (double.TryParse(response.Trim(), out power))
                    {
                        Power = power;
                        if (Status is StatusCoherent coherentStatus)
                        {
                            coherentStatus.CurrentPower = power;
                        }
                        return true;
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取Coherent激光器功率异常：{ex.Message}");
                return false;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化激光器
        /// </summary>
        /// <returns>是否成功</returns>
        private bool InitializeLaser()
        {
            try
            {
                // 获取激光器状态
                if (!UpdateLaserStatus()) return false;

                // 设置默认参数
                SetPower(0);
                SetFrequency(1000);
                SetPulseWidth(100);

                if (Status is StatusCoherent coherentStatus)
                {
                    coherentStatus.LaserStatus = LaserStatus.Ready;
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化Coherent激光器异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 发送命令
        /// </summary>
        /// <param name="command">命令字符串</param>
        /// <returns>是否成功</returns>
        private bool SendCommand(string command)
        {
            string response;
            return SendCommand(command, out response);
        }

        /// <summary>
        /// 发送命令并获取响应
        /// </summary>
        /// <param name="command">命令字符串</param>
        /// <param name="response">响应字符串</param>
        /// <returns>是否成功</returns>
        private bool SendCommand(string command, out string response)
        {
            response = string.Empty;
            try
            {
                lock (objLock)
                {
                    if (serialPort == null || !serialPort.IsOpen)
                        return false;

                    // 清空缓冲区
                    serialPort.DiscardInBuffer();
                    serialPort.DiscardOutBuffer();

                    // 发送命令
                    serialPort.WriteLine(command);

                    // 读取响应
                    response = serialPort.ReadLine();

                    return true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Coherent激光器发送命令异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 启动状态监控
        /// </summary>
        private void StartStatusMonitoring()
        {
            statusCancellationToken = new CancellationTokenSource();
            Task.Run(() => StatusMonitoringLoop(statusCancellationToken.Token));
        }

        /// <summary>
        /// 停止状态监控
        /// </summary>
        private void StopStatusMonitoring()
        {
            statusCancellationToken?.Cancel();
        }

        /// <summary>
        /// 状态监控循环
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        private void StatusMonitoringLoop(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested && IsOpen())
            {
                try
                {
                    UpdateLaserStatus();
                    Thread.Sleep(1000);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Coherent激光器状态监控异常：{ex.Message}");
                    break;
                }
            }
        }

        /// <summary>
        /// 更新激光器状态
        /// </summary>
        /// <returns>是否成功</returns>
        private bool UpdateLaserStatus()
        {
            try
            {
                // 更新功率
                double power = 0;
                GetPower(ref power);

                // 更新频率
                double frequency = 0;
                GetFrequency(ref frequency);

                // 更新温度
                double temperature = 0;
                GetTemperature(ref temperature);

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新Coherent激光器状态异常：{ex.Message}");
                return false;
            }
        }

        #endregion

        #region 其他激光器方法实现

        /// <summary>
        /// 设置激光器频率
        /// </summary>
        /// <param name="frequency">频率值(Hz)</param>
        /// <returns>是否成功</returns>
        public override bool SetFrequency(double frequency)
        {
            try
            {
                if (!IsOpen()) return false;

                frequency = Math.Max(MinFrequency, Math.Min(MaxFrequency, frequency));
                string command = $"F{frequency:F0}";

                if (SendCommand(command))
                {
                    Frequency = frequency;
                    if (Status is StatusCoherent coherentStatus)
                    {
                        coherentStatus.CurrentFrequency = frequency;
                    }
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置Coherent激光器频率异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取激光器频率
        /// </summary>
        /// <param name="frequency">频率值</param>
        /// <returns>是否成功</returns>
        public override bool GetFrequency(ref double frequency)
        {
            try
            {
                if (!IsOpen()) return false;

                string command = "F?";
                string response = string.Empty;

                if (SendCommand(command, out response))
                {
                    if (double.TryParse(response.Trim(), out frequency))
                    {
                        Frequency = frequency;
                        if (Status is StatusCoherent coherentStatus)
                        {
                            coherentStatus.CurrentFrequency = frequency;
                        }
                        return true;
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取Coherent激光器频率异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置脉冲宽度
        /// </summary>
        /// <param name="pulseWidth">脉冲宽度(μs)</param>
        /// <returns>是否成功</returns>
        public override bool SetPulseWidth(double pulseWidth)
        {
            try
            {
                if (!IsOpen()) return false;

                pulseWidth = Math.Max(1, Math.Min(10000, pulseWidth));
                string command = $"W{pulseWidth:F2}";

                if (SendCommand(command))
                {
                    PulseWidth = pulseWidth;
                    if (Status is StatusCoherent coherentStatus)
                    {
                        coherentStatus.CurrentPulseWidth = pulseWidth;
                    }
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置Coherent激光器脉冲宽度异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取脉冲宽度
        /// </summary>
        /// <param name="pulseWidth">脉冲宽度</param>
        /// <returns>是否成功</returns>
        public override bool GetPulseWidth(ref double pulseWidth)
        {
            try
            {
                if (!IsOpen()) return false;

                string command = "W?";
                string response = string.Empty;

                if (SendCommand(command, out response))
                {
                    if (double.TryParse(response.Trim(), out pulseWidth))
                    {
                        PulseWidth = pulseWidth;
                        if (Status is StatusCoherent coherentStatus)
                        {
                            coherentStatus.CurrentPulseWidth = pulseWidth;
                        }
                        return true;
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取Coherent激光器脉冲宽度异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置激光器模式
        /// </summary>
        /// <param name="mode">激光器模式</param>
        /// <returns>是否成功</returns>
        public override bool SetLaserMode(LaserMode mode)
        {
            try
            {
                if (!IsOpen()) return false;

                string command = mode == LaserMode.Continuous ? "M0" : "M1";

                if (SendCommand(command))
                {
                    LaserMode = mode;
                    if (Status is StatusCoherent coherentStatus)
                    {
                        coherentStatus.CurrentMode = mode;
                    }
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置Coherent激光器模式异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取激光器模式
        /// </summary>
        /// <param name="mode">激光器模式</param>
        /// <returns>是否成功</returns>
        public override bool GetLaserMode(ref LaserMode mode)
        {
            try
            {
                if (!IsOpen()) return false;

                string command = "M?";
                string response = string.Empty;

                if (SendCommand(command, out response))
                {
                    mode = response.Trim() == "0" ? LaserMode.Continuous : LaserMode.Pulsed;
                    LaserMode = mode;
                    if (Status is StatusCoherent coherentStatus)
                    {
                        coherentStatus.CurrentMode = mode;
                    }
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取Coherent激光器模式异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查激光器是否准备就绪
        /// </summary>
        /// <returns>是否准备就绪</returns>
        public override bool IsReady()
        {
            try
            {
                if (!IsOpen()) return false;

                string command = "S?";
                string response = string.Empty;

                if (SendCommand(command, out response))
                {
                    return response.Trim().Contains("READY");
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查Coherent激光器就绪状态异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查激光器是否有错误
        /// </summary>
        /// <returns>是否有错误</returns>
        public override bool HasError()
        {
            try
            {
                if (!IsOpen()) return true;

                string command = "E?";
                string response = string.Empty;

                if (SendCommand(command, out response))
                {
                    return !response.Trim().Contains("NO_ERROR");
                }
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查Coherent激光器错误状态异常：{ex.Message}");
                return true;
            }
        }

        /// <summary>
        /// 获取激光器错误信息
        /// </summary>
        /// <returns>错误信息</returns>
        public override string GetErrorMessage()
        {
            try
            {
                if (!IsOpen()) return "设备未连接";

                string command = "E?";
                string response = string.Empty;

                if (SendCommand(command, out response))
                {
                    return response.Trim();
                }
                return "无法获取错误信息";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取Coherent激光器错误信息异常：{ex.Message}");
                return $"获取错误信息异常：{ex.Message}";
            }
        }

        /// <summary>
        /// 清除激光器错误
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool ClearError()
        {
            try
            {
                if (!IsOpen()) return false;

                string command = "EC";
                if (SendCommand(command))
                {
                    if (Status is StatusCoherent coherentStatus)
                    {
                        coherentStatus.ClearError();
                    }
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清除Coherent激光器错误异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 激光器自检
        /// </summary>
        /// <returns>自检结果</returns>
        public override bool SelfTest()
        {
            try
            {
                if (!IsOpen()) return false;

                string command = "ST";
                string response = string.Empty;

                if (SendCommand(command, out response))
                {
                    return response.Trim().Contains("PASS");
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Coherent激光器自检异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取激光器温度
        /// </summary>
        /// <param name="temperature">温度值(℃)</param>
        /// <returns>是否成功</returns>
        public override bool GetTemperature(ref double temperature)
        {
            try
            {
                if (!IsOpen()) return false;

                string command = "T?";
                string response = string.Empty;

                if (SendCommand(command, out response))
                {
                    if (double.TryParse(response.Trim(), out temperature))
                    {
                        if (Status is StatusCoherent coherentStatus)
                        {
                            coherentStatus.Temperature = temperature;
                        }
                        return true;
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取Coherent激光器温度异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取激光器工作时间
        /// </summary>
        /// <param name="workingHours">工作时间(小时)</param>
        /// <returns>是否成功</returns>
        public override bool GetWorkingHours(ref double workingHours)
        {
            try
            {
                if (!IsOpen()) return false;

                string command = "H?";
                string response = string.Empty;

                if (SendCommand(command, out response))
                {
                    if (double.TryParse(response.Trim(), out workingHours))
                    {
                        if (Status is StatusCoherent coherentStatus)
                        {
                            coherentStatus.WorkingHours = workingHours;
                        }
                        return true;
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取Coherent激光器工作时间异常：{ex.Message}");
                return false;
            }
        }

        #endregion
    }
}
