using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace McLaser.Core.Communication
{
    /// <summary>
    /// TCP通信通道实现
    /// 支持客户端和服务器模式的TCP通信
    /// </summary>
    public class TcpCommunicationChannel : ICommunicationChannel, IConfigurableCommunicationChannel, IHeartbeatCommunicationChannel, IReconnectableCommunicationChannel, IDisposable
    {
        #region 字段

        private TcpClient? _tcpClient;
        private TcpListener? _tcpListener;
        private NetworkStream? _networkStream;
        private readonly ConcurrentQueue<byte> _receiveBuffer = new ConcurrentQueue<byte>();
        private readonly object _lockObject = new object();
        private CancellationTokenSource? _cancellationTokenSource;
        private Task? _receiveTask;
        private Task? _heartbeatTask;
        private Task? _reconnectTask;
        private bool _disposed = false;
        private ConnectionState _connectionState = ConnectionState.Disconnected;
        private readonly CommunicationStatistics _statistics = new CommunicationStatistics();

        #endregion

        #region 属性

        /// <summary>
        /// 通道名称
        /// </summary>
        public string Name { get; set; } = "TCP通信通道";

        /// <summary>
        /// 通道类型
        /// </summary>
        public CommunicationChannelType ChannelType => CommunicationChannelType.Tcp;

        /// <summary>
        /// 连接状态
        /// </summary>
        public bool IsConnected => _connectionState == ConnectionState.Connected && _tcpClient?.Connected == true;

        /// <summary>
        /// 是否正在连接
        /// </summary>
        public bool IsConnecting => _connectionState == ConnectionState.Connecting;

        /// <summary>
        /// 连接配置
        /// </summary>
        public CommunicationConfig Configuration { get; set; } = new TcpConfig();

        /// <summary>
        /// TCP配置
        /// </summary>
        public TcpConfig TcpConfiguration => Configuration as TcpConfig ?? new TcpConfig();

        /// <summary>
        /// 心跳间隔
        /// </summary>
        public TimeSpan HeartbeatInterval { get; set; } = TimeSpan.FromSeconds(30);

        /// <summary>
        /// 心跳超时时间
        /// </summary>
        public TimeSpan HeartbeatTimeout { get; set; } = TimeSpan.FromSeconds(10);

        /// <summary>
        /// 是否启用心跳检测
        /// </summary>
        public bool HeartbeatEnabled { get; set; } = false;

        /// <summary>
        /// 心跳数据
        /// </summary>
        public byte[] HeartbeatData { get; set; } = Encoding.UTF8.GetBytes("PING");

        /// <summary>
        /// 是否启用自动重连
        /// </summary>
        public bool AutoReconnectEnabled { get; set; } = true;

        /// <summary>
        /// 重连间隔
        /// </summary>
        public TimeSpan ReconnectInterval { get; set; } = TimeSpan.FromSeconds(5);

        /// <summary>
        /// 最大重连次数
        /// </summary>
        public int MaxReconnectAttempts { get; set; } = 10;

        /// <summary>
        /// 当前重连次数
        /// </summary>
        public int CurrentReconnectAttempts { get; private set; } = 0;

        #endregion

        #region 事件

        /// <summary>
        /// 连接状态变更事件
        /// </summary>
        public event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;

        /// <summary>
        /// 数据接收事件
        /// </summary>
        public event EventHandler<DataReceivedEventArgs>? DataReceived;

        /// <summary>
        /// 错误发生事件
        /// </summary>
        public event EventHandler<CommunicationErrorEventArgs>? ErrorOccurred;

        /// <summary>
        /// 心跳状态变更事件
        /// </summary>
        public event EventHandler<HeartbeatStateChangedEventArgs>? HeartbeatStateChanged;

        /// <summary>
        /// 重连状态变更事件
        /// </summary>
        public event EventHandler<ReconnectStateChangedEventArgs>? ReconnectStateChanged;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化TCP通信通道
        /// </summary>
        public TcpCommunicationChannel()
        {
        }

        /// <summary>
        /// 初始化TCP通信通道
        /// </summary>
        /// <param name="config">TCP配置</param>
        public TcpCommunicationChannel(TcpConfig config)
        {
            Configuration = config ?? throw new ArgumentNullException(nameof(config));
        }

        #endregion

        #region 连接管理

        /// <summary>
        /// 异步连接
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>连接任务</returns>
        public async Task<bool> ConnectAsync(CancellationToken cancellationToken = default)
        {
            if (IsConnected)
                return true;

            if (IsConnecting)
                return false;

            try
            {
                SetConnectionState(ConnectionState.Connecting);
                _cancellationTokenSource = new CancellationTokenSource();

                var config = TcpConfiguration;
                
                if (config.IsServer)
                {
                    return await StartServerAsync(config, cancellationToken);
                }
                else
                {
                    return await ConnectClientAsync(config, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred(CommunicationErrorType.ConnectionError, $"连接失败: {ex.Message}", ex);
                SetConnectionState(ConnectionState.Error);
                return false;
            }
        }

        private async Task<bool> ConnectClientAsync(TcpConfig config, CancellationToken cancellationToken)
        {
            _tcpClient = new TcpClient();
            
            // 配置TCP客户端
            _tcpClient.ReceiveBufferSize = config.ReceiveBufferSize;
            _tcpClient.SendBufferSize = config.SendBufferSize;
            _tcpClient.NoDelay = config.NoDelay;

            // 连接到服务器
            await _tcpClient.ConnectAsync(config.Host, config.Port);
            
            if (!_tcpClient.Connected)
                return false;

            _networkStream = _tcpClient.GetStream();
            _networkStream.ReadTimeout = config.ReceiveTimeout;
            _networkStream.WriteTimeout = config.SendTimeout;

            SetConnectionState(ConnectionState.Connected);
            _statistics.ConnectedTime = DateTime.Now;
            CurrentReconnectAttempts = 0;

            // 启动接收任务
            StartReceiveTask();

            // 启动心跳任务
            if (HeartbeatEnabled)
            {
                StartHeartbeat();
            }

            return true;
        }

        private async Task<bool> StartServerAsync(TcpConfig config, CancellationToken cancellationToken)
        {
            _tcpListener = new TcpListener(IPAddress.Any, config.Port);
            _tcpListener.Start();

            // 等待客户端连接
            _tcpClient = await _tcpListener.AcceptTcpClientAsync();
            _networkStream = _tcpClient.GetStream();

            SetConnectionState(ConnectionState.Connected);
            _statistics.ConnectedTime = DateTime.Now;

            // 启动接收任务
            StartReceiveTask();

            // 启动心跳任务
            if (HeartbeatEnabled)
            {
                StartHeartbeat();
            }

            return true;
        }

        /// <summary>
        /// 异步断开连接
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>断开任务</returns>
        public async Task DisconnectAsync(CancellationToken cancellationToken = default)
        {
            if (!IsConnected && !IsConnecting)
                return;

            try
            {
                SetConnectionState(ConnectionState.Disconnecting);

                // 停止心跳
                StopHeartbeat();

                // 停止重连
                StopReconnect();

                // 取消所有任务
                _cancellationTokenSource?.Cancel();

                // 等待任务完成
                if (_receiveTask != null && !_receiveTask.IsCompleted)
                {
                    await _receiveTask.ConfigureAwait(false);
                }

                // 关闭网络流
                _networkStream?.Close();
                _networkStream?.Dispose();
                _networkStream = null;

                // 关闭TCP客户端
                _tcpClient?.Close();
                _tcpClient?.Dispose();
                _tcpClient = null;

                // 停止TCP监听器
                _tcpListener?.Stop();
                _tcpListener = null;

                SetConnectionState(ConnectionState.Disconnected);

                // 更新统计信息
                if (_statistics.ConnectedTime.HasValue)
                {
                    _statistics.TotalConnectedTime += DateTime.Now - _statistics.ConnectedTime.Value;
                    _statistics.ConnectedTime = null;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred(CommunicationErrorType.ConnectionError, $"断开连接失败: {ex.Message}", ex);
            }
        }

        #endregion

        #region 数据传输

        /// <summary>
        /// 异步发送数据
        /// </summary>
        /// <param name="data">要发送的数据</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>发送任务，返回实际发送的字节数</returns>
        public async Task<int> SendAsync(byte[] data, CancellationToken cancellationToken = default)
        {
            if (data == null)
                throw new ArgumentNullException(nameof(data));

            if (!IsConnected || _networkStream == null)
                throw new InvalidOperationException("连接未建立");

            try
            {
                await _networkStream.WriteAsync(data, 0, data.Length, cancellationToken);
                await _networkStream.FlushAsync(cancellationToken);

                // 更新统计信息
                _statistics.BytesSent += data.Length;
                _statistics.MessagesSent++;
                _statistics.LastActivity = DateTime.Now;

                return data.Length;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(CommunicationErrorType.SendError, $"发送数据失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 异步发送字符串数据
        /// </summary>
        /// <param name="message">要发送的消息</param>
        /// <param name="encoding">编码方式，默认UTF8</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>发送任务，返回实际发送的字节数</returns>
        public async Task<int> SendAsync(string message, Encoding? encoding = null, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(message))
                throw new ArgumentException("消息不能为空", nameof(message));

            encoding ??= Encoding.UTF8;
            var data = encoding.GetBytes(message);
            return await SendAsync(data, cancellationToken);
        }

        /// <summary>
        /// 异步接收数据
        /// </summary>
        /// <param name="buffer">接收缓冲区</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>接收任务，返回实际接收的字节数</returns>
        public async Task<int> ReceiveAsync(byte[] buffer, CancellationToken cancellationToken = default)
        {
            if (buffer == null)
                throw new ArgumentNullException(nameof(buffer));

            if (!IsConnected || _networkStream == null)
                throw new InvalidOperationException("连接未建立");

            try
            {
                var bytesRead = await _networkStream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);

                // 更新统计信息
                if (bytesRead > 0)
                {
                    _statistics.BytesReceived += bytesRead;
                    _statistics.MessagesReceived++;
                    _statistics.LastActivity = DateTime.Now;
                }

                return bytesRead;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(CommunicationErrorType.ReceiveError, $"接收数据失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 异步接收数据直到指定分隔符
        /// </summary>
        /// <param name="delimiter">分隔符</param>
        /// <param name="maxLength">最大长度</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>接收任务，返回接收到的数据</returns>
        public async Task<byte[]> ReceiveUntilAsync(byte[] delimiter, int maxLength = 4096, CancellationToken cancellationToken = default)
        {
            if (delimiter == null || delimiter.Length == 0)
                throw new ArgumentException("分隔符不能为空", nameof(delimiter));

            if (!IsConnected || _networkStream == null)
                throw new InvalidOperationException("连接未建立");

            var buffer = new byte[1];
            var result = new List<byte>();
            var delimiterIndex = 0;

            try
            {
                while (result.Count < maxLength && !cancellationToken.IsCancellationRequested)
                {
                    var bytesRead = await _networkStream.ReadAsync(buffer, 0, 1, cancellationToken);
                    if (bytesRead == 0)
                        break; // 连接已关闭

                    var receivedByte = buffer[0];
                    result.Add(receivedByte);

                    // 检查分隔符
                    if (receivedByte == delimiter[delimiterIndex])
                    {
                        delimiterIndex++;
                        if (delimiterIndex == delimiter.Length)
                        {
                            // 找到完整分隔符，移除分隔符并返回
                            for (int i = 0; i < delimiter.Length; i++)
                            {
                                result.RemoveAt(result.Count - 1);
                            }
                            break;
                        }
                    }
                    else
                    {
                        delimiterIndex = 0;
                    }
                }

                // 更新统计信息
                if (result.Count > 0)
                {
                    _statistics.BytesReceived += result.Count;
                    _statistics.MessagesReceived++;
                    _statistics.LastActivity = DateTime.Now;
                }

                return result.ToArray();
            }
            catch (Exception ex)
            {
                OnErrorOccurred(CommunicationErrorType.ReceiveError, $"接收数据失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 清空接收缓冲区
        /// </summary>
        public void ClearReceiveBuffer()
        {
            while (_receiveBuffer.TryDequeue(out _))
            {
                // 清空队列
            }
        }

        /// <summary>
        /// 清空发送缓冲区
        /// </summary>
        public void ClearSendBuffer()
        {
            // TCP没有发送缓冲区需要清空
        }

        #endregion

        #region 统计和测试

        /// <summary>
        /// 获取通道统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public CommunicationStatistics GetStatistics()
        {
            return _statistics;
        }

        /// <summary>
        /// 重置统计信息
        /// </summary>
        public void ResetStatistics()
        {
            _statistics.Reset();
        }

        /// <summary>
        /// 测试连接
        /// </summary>
        /// <param name="timeout">超时时间</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>测试结果</returns>
        public async Task<bool> TestConnectionAsync(TimeSpan timeout, CancellationToken cancellationToken = default)
        {
            if (!IsConnected)
                return false;

            try
            {
                using (var timeoutCts = new CancellationTokenSource(timeout))
                using (var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token))
                {
                    // 发送测试数据
                    var testData = Encoding.UTF8.GetBytes("TEST");
                    await SendAsync(testData, combinedCts.Token);
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region 配置管理

        /// <summary>
        /// 应用配置
        /// </summary>
        /// <param name="config">配置信息</param>
        /// <returns>是否成功应用配置</returns>
        public bool ApplyConfiguration(CommunicationConfig config)
        {
            if (config == null)
                return false;

            try
            {
                Configuration = config;
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证配置
        /// </summary>
        /// <param name="config">配置信息</param>
        /// <returns>验证结果</returns>
        public ConfigurationValidationResult ValidateConfiguration(CommunicationConfig config)
        {
            var result = new ConfigurationValidationResult { IsValid = true };

            if (config == null)
            {
                result.Errors.Add("配置不能为空");
                result.IsValid = false;
                return result;
            }

            if (config is TcpConfig tcpConfig)
            {
                // 验证主机地址
                if (!tcpConfig.IsServer && string.IsNullOrEmpty(tcpConfig.Host))
                {
                    result.Errors.Add("客户端模式下主机地址不能为空");
                    result.IsValid = false;
                }

                // 验证端口号
                if (tcpConfig.Port <= 0 || tcpConfig.Port > 65535)
                {
                    result.Errors.Add("端口号必须在1-65535范围内");
                    result.IsValid = false;
                }

                // 验证超时设置
                if (tcpConfig.ConnectionTimeout <= 0)
                {
                    result.Warnings.Add("连接超时时间应该大于0");
                }

                if (tcpConfig.ReceiveTimeout <= 0)
                {
                    result.Warnings.Add("接收超时时间应该大于0");
                }

                if (tcpConfig.SendTimeout <= 0)
                {
                    result.Warnings.Add("发送超时时间应该大于0");
                }
            }
            else
            {
                result.Errors.Add("配置类型不匹配，需要TcpConfig");
                result.IsValid = false;
            }

            return result;
        }

        /// <summary>
        /// 获取默认配置
        /// </summary>
        /// <returns>默认配置</returns>
        public CommunicationConfig GetDefaultConfiguration()
        {
            return new TcpConfig
            {
                Name = "默认TCP配置",
                Host = "localhost",
                Port = 8080,
                ConnectionTimeout = 5000,
                ReceiveTimeout = 3000,
                SendTimeout = 3000,
                ReceiveBufferSize = 4096,
                SendBufferSize = 4096,
                NoDelay = true,
                KeepAlive = true,
                IsServer = false
            };
        }

        /// <summary>
        /// 获取支持的配置选项
        /// </summary>
        /// <returns>配置选项列表</returns>
        public CommunicationConfigOption[] GetSupportedOptions()
        {
            return new[]
            {
                new CommunicationConfigOption
                {
                    Name = "Host",
                    OptionType = typeof(string),
                    DefaultValue = "localhost",
                    IsRequired = true,
                    Description = "服务器主机地址"
                },
                new CommunicationConfigOption
                {
                    Name = "Port",
                    OptionType = typeof(int),
                    DefaultValue = 8080,
                    IsRequired = true,
                    Description = "端口号",
                    MinValue = 1,
                    MaxValue = 65535
                },
                new CommunicationConfigOption
                {
                    Name = "IsServer",
                    OptionType = typeof(bool),
                    DefaultValue = false,
                    IsRequired = false,
                    Description = "是否作为服务器运行"
                },
                new CommunicationConfigOption
                {
                    Name = "ConnectionTimeout",
                    OptionType = typeof(int),
                    DefaultValue = 5000,
                    IsRequired = false,
                    Description = "连接超时时间（毫秒）",
                    MinValue = 1000
                }
            };
        }

        #endregion

        #region 心跳管理

        /// <summary>
        /// 启动心跳检测
        /// </summary>
        public void StartHeartbeat()
        {
            if (!HeartbeatEnabled || _heartbeatTask != null)
                return;

            _heartbeatTask = Task.Run(async () =>
            {
                while (HeartbeatEnabled && IsConnected && !_cancellationTokenSource?.Token.IsCancellationRequested == true)
                {
                    try
                    {
                        await Task.Delay(HeartbeatInterval, _cancellationTokenSource?.Token ?? CancellationToken.None);

                        if (IsConnected)
                        {
                            await SendHeartbeatAsync(_cancellationTokenSource?.Token ?? CancellationToken.None);
                            OnHeartbeatStateChanged(true, DateTime.Now, HeartbeatInterval);
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        break;
                    }
                    catch (Exception ex)
                    {
                        OnErrorOccurred(CommunicationErrorType.ConnectionError, $"心跳检测失败: {ex.Message}", ex);
                        OnHeartbeatStateChanged(false, DateTime.Now, HeartbeatInterval);

                        if (AutoReconnectEnabled)
                        {
                            _ = Task.Run(() => ReconnectAsync());
                        }
                        break;
                    }
                }
            });
        }

        /// <summary>
        /// 停止心跳检测
        /// </summary>
        public void StopHeartbeat()
        {
            _heartbeatTask = null;
        }

        /// <summary>
        /// 发送心跳
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>发送任务</returns>
        public async Task SendHeartbeatAsync(CancellationToken cancellationToken = default)
        {
            if (HeartbeatData != null && HeartbeatData.Length > 0)
            {
                await SendAsync(HeartbeatData, cancellationToken);
            }
        }

        #endregion

        #region 重连管理

        /// <summary>
        /// 手动重连
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>重连任务</returns>
        public async Task<bool> ReconnectAsync(CancellationToken cancellationToken = default)
        {
            if (!AutoReconnectEnabled || CurrentReconnectAttempts >= MaxReconnectAttempts)
                return false;

            try
            {
                CurrentReconnectAttempts++;
                OnReconnectStateChanged(true, CurrentReconnectAttempts, MaxReconnectAttempts, DateTime.Now + ReconnectInterval);

                // 先断开现有连接
                await DisconnectAsync(cancellationToken);

                // 等待重连间隔
                await Task.Delay(ReconnectInterval, cancellationToken);

                // 尝试重新连接
                var success = await ConnectAsync(cancellationToken);

                if (success)
                {
                    CurrentReconnectAttempts = 0;
                    OnReconnectStateChanged(false, 0, MaxReconnectAttempts);
                    _statistics.ReconnectCount++;
                }
                else
                {
                    OnReconnectStateChanged(false, CurrentReconnectAttempts, MaxReconnectAttempts);
                }

                return success;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(CommunicationErrorType.ConnectionError, $"重连失败: {ex.Message}", ex);
                OnReconnectStateChanged(false, CurrentReconnectAttempts, MaxReconnectAttempts);
                return false;
            }
        }

        /// <summary>
        /// 停止重连
        /// </summary>
        public void StopReconnect()
        {
            CurrentReconnectAttempts = MaxReconnectAttempts; // 阻止进一步重连
            _reconnectTask = null;
        }

        #endregion

        #region 私有方法

        private void StartReceiveTask()
        {
            _receiveTask = Task.Run(async () =>
            {
                var buffer = new byte[Configuration.ReceiveBufferSize];

                while (IsConnected && !_cancellationTokenSource?.Token.IsCancellationRequested == true)
                {
                    try
                    {
                        if (_networkStream != null && _networkStream.DataAvailable)
                        {
                            var bytesRead = await _networkStream.ReadAsync(buffer, 0, buffer.Length, _cancellationTokenSource?.Token ?? CancellationToken.None);

                            if (bytesRead > 0)
                            {
                                var data = new byte[bytesRead];
                                Array.Copy(buffer, 0, data, 0, bytesRead);

                                // 触发数据接收事件
                                OnDataReceived(data, bytesRead, _tcpClient?.Client.RemoteEndPoint);

                                // 更新统计信息
                                _statistics.BytesReceived += bytesRead;
                                _statistics.MessagesReceived++;
                                _statistics.LastActivity = DateTime.Now;
                            }
                            else
                            {
                                // 连接已关闭
                                break;
                            }
                        }
                        else
                        {
                            // 没有数据可读，短暂等待
                            await Task.Delay(10, _cancellationTokenSource?.Token ?? CancellationToken.None);
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        break;
                    }
                    catch (Exception ex)
                    {
                        OnErrorOccurred(CommunicationErrorType.ReceiveError, $"接收数据时发生错误: {ex.Message}", ex);

                        if (AutoReconnectEnabled)
                        {
                            _ = Task.Run(() => ReconnectAsync());
                        }
                        break;
                    }
                }
            });
        }

        private void SetConnectionState(ConnectionState newState)
        {
            var oldState = _connectionState;
            _connectionState = newState;

            if (oldState != newState)
            {
                OnConnectionStateChanged(oldState, newState);
            }
        }

        #endregion

        #region 事件触发

        private void OnConnectionStateChanged(ConnectionState oldState, ConnectionState newState)
        {
            ConnectionStateChanged?.Invoke(this, new ConnectionStateChangedEventArgs(oldState, newState));
        }

        private void OnDataReceived(byte[] data, int length, EndPoint? remoteEndPoint)
        {
            DataReceived?.Invoke(this, new DataReceivedEventArgs(data, length, remoteEndPoint));
        }

        private void OnErrorOccurred(CommunicationErrorType errorType, string errorMessage, Exception? exception = null, bool isRecoverable = false)
        {
            _statistics.ErrorCount++;
            ErrorOccurred?.Invoke(this, new CommunicationErrorEventArgs(errorType, errorMessage, exception, isRecoverable));
        }

        private void OnHeartbeatStateChanged(bool isAlive, DateTime lastHeartbeat, TimeSpan interval)
        {
            HeartbeatStateChanged?.Invoke(this, new HeartbeatStateChangedEventArgs(isAlive, lastHeartbeat, interval));
        }

        private void OnReconnectStateChanged(bool isReconnecting, int attemptCount, int maxAttempts, DateTime? nextAttemptTime = null)
        {
            ReconnectStateChanged?.Invoke(this, new ReconnectStateChangedEventArgs(isReconnecting, attemptCount, maxAttempts, nextAttemptTime));
        }

        #endregion

        #region IDisposable

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否正在释放</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                try
                {
                    DisconnectAsync().Wait(5000); // 等待最多5秒
                }
                catch
                {
                    // 忽略释放时的异常
                }

                _cancellationTokenSource?.Dispose();
                _disposed = true;
            }
        }

        /// <summary>
        /// 析构函数
        /// </summary>
        ~TcpCommunicationChannel()
        {
            Dispose(false);
        }

        #endregion
    }
}
