using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel.Composition;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Forms;
using McLaser.Core.Framework.Configuration;

namespace McLaser.Core.Framework.UI
{
    /// <summary>
    /// 窗口管理器实现
    /// 提供完整的窗口生命周期管理功能
    /// </summary>
    [Export(typeof(IWindowManager))]
    public class WindowManager : IWindowManager
    {
        private readonly Dictionary<string, Window> _windows;
        private readonly Dictionary<Window, string> _windowIds;
        private readonly Dictionary<string, WindowLayout> _savedLayouts;
        private readonly IConfigurationService _configurationService;
        private Window? _mainWindow;
        private int _windowCounter;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="configurationService">配置服务</param>
        public WindowManager()
        {
            //_configurationService = configurationService ?? throw new ArgumentNullException(nameof(configurationService));
            _windows = new Dictionary<string, Window>();
            _windowIds = new Dictionary<Window, string>();
            _savedLayouts = new Dictionary<string, WindowLayout>();
            _windowCounter = 0;

            LoadSavedLayouts();
        }

        /// <summary>
        /// 活动窗口列表
        /// </summary>
        public IReadOnlyList<Window> ActiveWindows => new ReadOnlyCollection<Window>(_windows.Values.ToList());

        /// <summary>
        /// 主窗口
        /// </summary>
        public Window? MainWindow
        {
            get => _mainWindow;
            set
            {
                if (_mainWindow != value)
                {
                    _mainWindow = value;
                    if (value != null && System.Windows.Application.Current != null)
                    {
                        System.Windows.Application.Current.MainWindow = value;
                    }
                }
            }
        }

        /// <summary>
        /// 设置主窗口
        /// </summary>
        /// <param name="window">窗口实例</param>
        public void SetMainWindow(Window window)
        {
            MainWindow = window;
        }

        /// <summary>
        /// 窗口打开事件
        /// </summary>
        public event EventHandler<WindowEventArgs>? WindowOpened;

        /// <summary>
        /// 窗口关闭事件
        /// </summary>
        public event EventHandler<WindowEventArgs>? WindowClosed;

        /// <summary>
        /// 窗口激活事件
        /// </summary>
        public event EventHandler<WindowEventArgs>? WindowActivated;

        /// <summary>
        /// 显示窗口
        /// </summary>
        /// <param name="window">窗口实例</param>
        /// <param name="owner">父窗口</param>
        /// <returns>窗口ID</returns>
        public string ShowWindow(Window window, Window? owner = null)
        {
            if (window == null)
                throw new ArgumentNullException(nameof(window));

            var windowId = GenerateWindowId();
            
            // 设置窗口属性
            if (owner != null)
                window.Owner = owner;

            // 注册窗口
            RegisterWindow(window, windowId);

            // 恢复窗口状态
            RestoreWindowState(window);

            // 确保窗口在屏幕范围内
            EnsureWindowOnScreen(window);

            // 显示窗口
            window.Show();

            // 触发事件
            WindowOpened?.Invoke(this, new WindowEventArgs(window, windowId));

            return windowId;
        }

        /// <summary>
        /// 显示模态对话框
        /// </summary>
        /// <param name="window">窗口实例</param>
        /// <param name="owner">父窗口</param>
        /// <returns>对话框结果</returns>
        public bool? ShowDialog(Window window, Window? owner = null)
        {
            if (window == null)
                throw new ArgumentNullException(nameof(window));

            var windowId = GenerateWindowId();

            // 设置窗口属性
            if (owner != null)
                window.Owner = owner;

            // 注册窗口
            RegisterWindow(window, windowId);

            // 恢复窗口状态
            RestoreWindowState(window);

            // 确保窗口在屏幕范围内
            EnsureWindowOnScreen(window);

            // 触发事件
            WindowOpened?.Invoke(this, new WindowEventArgs(window, windowId));

            // 显示模态对话框
            var result = window.ShowDialog();

            return result;
        }

        /// <summary>
        /// 异步显示模态对话框
        /// </summary>
        /// <param name="window">窗口实例</param>
        /// <param name="owner">父窗口</param>
        /// <returns>对话框结果</returns>
        public async Task<bool?> ShowDialogAsync(Window window, Window? owner = null)
        {
            return await Task.Run(() => ShowDialog(window, owner));
        }

        /// <summary>
        /// 关闭窗口
        /// </summary>
        /// <param name="windowId">窗口ID</param>
        /// <returns>是否成功关闭</returns>
        public bool CloseWindow(string windowId)
        {
            if (string.IsNullOrWhiteSpace(windowId) || !_windows.ContainsKey(windowId))
                return false;

            var window = _windows[windowId];
            return CloseWindow(window);
        }

        /// <summary>
        /// 关闭窗口
        /// </summary>
        /// <param name="window">窗口实例</param>
        /// <returns>是否成功关闭</returns>
        public bool CloseWindow(Window window)
        {
            if (window == null)
                return false;

            try
            {
                // 保存窗口状态
                SaveWindowState(window);

                // 关闭窗口
                window.Close();

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"关闭窗口失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 关闭所有窗口
        /// </summary>
        /// <param name="excludeMain">是否排除主窗口</param>
        public void CloseAllWindows(bool excludeMain = true)
        {
            var windowsToClose = _windows.Values.ToList();

            foreach (var window in windowsToClose)
            {
                if (excludeMain && window == _mainWindow)
                    continue;

                CloseWindow(window);
            }
        }

        /// <summary>
        /// 激活窗口
        /// </summary>
        /// <param name="windowId">窗口ID</param>
        /// <returns>是否成功激活</returns>
        public bool ActivateWindow(string windowId)
        {
            if (string.IsNullOrWhiteSpace(windowId) || !_windows.ContainsKey(windowId))
                return false;

            var window = _windows[windowId];
            return ActivateWindow(window);
        }

        /// <summary>
        /// 激活窗口
        /// </summary>
        /// <param name="window">窗口实例</param>
        /// <returns>是否成功激活</returns>
        public bool ActivateWindow(Window window)
        {
            if (window == null)
                return false;

            try
            {
                if (window.WindowState == WindowState.Minimized)
                    window.WindowState = WindowState.Normal;

                window.Activate();
                window.Focus();

                // 触发激活事件
                if (_windowIds.ContainsKey(window))
                {
                    var windowId = _windowIds[window];
                    WindowActivated?.Invoke(this, new WindowEventArgs(window, windowId));
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"激活窗口失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 查找窗口
        /// </summary>
        /// <param name="windowId">窗口ID</param>
        /// <returns>窗口实例</returns>
        public Window? FindWindow(string windowId)
        {
            if (string.IsNullOrWhiteSpace(windowId))
                return null;

            return _windows.ContainsKey(windowId) ? _windows[windowId] : null;
        }

        /// <summary>
        /// 查找窗口
        /// </summary>
        /// <typeparam name="T">窗口类型</typeparam>
        /// <returns>窗口实例</returns>
        public T? FindWindow<T>() where T : Window
        {
            return _windows.Values.OfType<T>().FirstOrDefault();
        }

        /// <summary>
        /// 保存窗口状态
        /// </summary>
        /// <param name="window">窗口实例</param>
        public void SaveWindowState(Window window)
        {
            if (window == null || !_windowIds.ContainsKey(window))
                return;

            try
            {
                var windowId = _windowIds[window];
                var layout = GetWindowLayout(window);
                
                _savedLayouts[windowId] = layout;
                
                // 保存到配置服务
                var configKey = $"WindowLayout.{window.GetType().Name}";
                _configurationService.SetValue(configKey, layout);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存窗口状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 恢复窗口状态
        /// </summary>
        /// <param name="window">窗口实例</param>
        public void RestoreWindowState(Window window)
        {
            if (window == null)
                return;

            try
            {
                var configKey = $"WindowLayout.{window.GetType().Name}";
                var layout = _configurationService.GetValue<WindowLayout>(configKey);
                
                if (layout != null)
                {
                    ApplyWindowLayout(window, layout);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"恢复窗口状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存所有窗口状态
        /// </summary>
        public void SaveAllWindowStates()
        {
            foreach (var window in _windows.Values)
            {
                SaveWindowState(window);
            }
        }

        /// <summary>
        /// 获取窗口布局信息
        /// </summary>
        /// <param name="window">窗口实例</param>
        /// <returns>布局信息</returns>
        public WindowLayout GetWindowLayout(Window window)
        {
            if (window == null)
                throw new ArgumentNullException(nameof(window));

            return new WindowLayout
            {
                Left = window.Left,
                Top = window.Top,
                Width = window.Width,
                Height = window.Height,
                WindowState = window.WindowState,
                Topmost = window.Topmost,
                ScreenIndex = GetScreenIndex(window),
                LastUpdated = DateTime.Now
            };
        }

        /// <summary>
        /// 应用窗口布局
        /// </summary>
        /// <param name="window">窗口实例</param>
        /// <param name="layout">布局信息</param>
        public void ApplyWindowLayout(Window window, WindowLayout layout)
        {
            if (window == null || layout == null)
                return;

            try
            {
                window.Left = layout.Left;
                window.Top = layout.Top;
                window.Width = layout.Width;
                window.Height = layout.Height;
                window.WindowState = layout.WindowState;
                window.Topmost = layout.Topmost;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用窗口布局失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查窗口是否在屏幕范围内
        /// </summary>
        /// <param name="window">窗口实例</param>
        /// <returns>是否在屏幕范围内</returns>
        public bool IsWindowOnScreen(Window window)
        {
            if (window == null)
                return false;

            var windowRect = new System.Drawing.Rectangle(
                (int)window.Left, (int)window.Top,
                (int)window.Width, (int)window.Height);

            return Screen.AllScreens.Any(screen => screen.Bounds.IntersectsWith(windowRect));
        }

        /// <summary>
        /// 将窗口移动到屏幕范围内
        /// </summary>
        /// <param name="window">窗口实例</param>
        public void EnsureWindowOnScreen(Window window)
        {
            if (window == null || IsWindowOnScreen(window))
                return;

            var primaryScreen = Screen.PrimaryScreen;
            if (primaryScreen != null)
            {
                var workingArea = primaryScreen.WorkingArea;
                
                // 调整窗口位置
                if (window.Left < workingArea.Left)
                    window.Left = workingArea.Left;
                
                if (window.Top < workingArea.Top)
                    window.Top = workingArea.Top;
                
                if (window.Left + window.Width > workingArea.Right)
                    window.Left = workingArea.Right - window.Width;
                
                if (window.Top + window.Height > workingArea.Bottom)
                    window.Top = workingArea.Bottom - window.Height;
            }
        }

        /// <summary>
        /// 获取最佳显示器
        /// </summary>
        /// <param name="window">窗口实例</param>
        /// <returns>显示器信息</returns>
        public ScreenInfo GetBestScreen(Window window)
        {
            if (window == null)
                return GetPrimaryScreenInfo();

            var windowRect = new System.Drawing.Rectangle(
                (int)window.Left, (int)window.Top,
                (int)window.Width, (int)window.Height);

            var bestScreen = Screen.FromRectangle(windowRect);
            return ConvertToScreenInfo(bestScreen);
        }

        /// <summary>
        /// 生成窗口ID
        /// </summary>
        /// <returns>窗口ID</returns>
        private string GenerateWindowId()
        {
            return $"Window_{++_windowCounter}_{DateTime.Now.Ticks}";
        }

        /// <summary>
        /// 注册窗口
        /// </summary>
        /// <param name="window">窗口实例</param>
        /// <param name="windowId">窗口ID</param>
        private void RegisterWindow(Window window, string windowId)
        {
            _windows[windowId] = window;
            _windowIds[window] = windowId;

            // 订阅窗口关闭事件
            window.Closed += (sender, e) => OnWindowClosed(window, windowId);
        }

        /// <summary>
        /// 窗口关闭事件处理
        /// </summary>
        /// <param name="window">窗口实例</param>
        /// <param name="windowId">窗口ID</param>
        private void OnWindowClosed(Window window, string windowId)
        {
            // 保存窗口状态
            SaveWindowState(window);

            // 移除窗口注册
            _windows.Remove(windowId);
            _windowIds.Remove(window);

            // 触发关闭事件
            WindowClosed?.Invoke(this, new WindowEventArgs(window, windowId));
        }

        /// <summary>
        /// 获取屏幕索引
        /// </summary>
        /// <param name="window">窗口实例</param>
        /// <returns>屏幕索引</returns>
        private int GetScreenIndex(Window window)
        {
            var screens = Screen.AllScreens;
            var windowRect = new System.Drawing.Rectangle(
                (int)window.Left, (int)window.Top,
                (int)window.Width, (int)window.Height);

            for (int i = 0; i < screens.Length; i++)
            {
                if (screens[i].Bounds.IntersectsWith(windowRect))
                    return i;
            }

            return 0; // 默认返回主屏幕
        }

        /// <summary>
        /// 获取主屏幕信息
        /// </summary>
        /// <returns>主屏幕信息</returns>
        private ScreenInfo GetPrimaryScreenInfo()
        {
            var primaryScreen = Screen.PrimaryScreen;
            return ConvertToScreenInfo(primaryScreen);
        }

        /// <summary>
        /// 转换为屏幕信息
        /// </summary>
        /// <param name="screen">屏幕对象</param>
        /// <returns>屏幕信息</returns>
        private ScreenInfo ConvertToScreenInfo(Screen screen)
        {
            return new ScreenInfo
            {
                Index = Array.IndexOf(Screen.AllScreens, screen),
                Name = screen.DeviceName,
                WorkingArea = new Rect(screen.WorkingArea.X, screen.WorkingArea.Y,
                                     screen.WorkingArea.Width, screen.WorkingArea.Height),
                Bounds = new Rect(screen.Bounds.X, screen.Bounds.Y,
                                screen.Bounds.Width, screen.Bounds.Height),
                IsPrimary = screen.Primary,
                DpiScale = 1.0 // 简化处理，实际应该获取真实DPI
            };
        }

        /// <summary>
        /// 加载保存的布局
        /// </summary>
        private void LoadSavedLayouts()
        {
            try
            {
                // 这里可以从配置服务加载保存的窗口布局
                // 实现细节根据具体需求调整
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载窗口布局失败: {ex.Message}");
            }
        }
    }
}
