<UserControl x:Class="McLaser.Core.ApplicationBase.NavigationButton"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d" 
             d:DesignHeight="80" d:DesignWidth="120">
    
    <UserControl.Resources>
        <!-- 导航按钮样式 -->
        <Style x:Key="NavigationButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" 
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" 
                                        Value="{DynamicResource {x:Static SystemColors.ControlLightBrushKey}}"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" 
                                        Value="{DynamicResource {x:Static SystemColors.ControlDarkBrushKey}}"/>
                            </Trigger>
                            <DataTrigger Binding="{Binding IsSelected}" Value="True">
                                <Setter TargetName="border" Property="Background" 
                                        Value="{DynamicResource {x:Static SystemColors.HighlightBrushKey}}"/>
                                <Setter Property="Foreground" 
                                        Value="{DynamicResource {x:Static SystemColors.HighlightTextBrushKey}}"/>
                            </DataTrigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 图标样式 -->
        <Style x:Key="IconTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Segoe UI Emoji"/>
            <Setter Property="FontSize" Value="20"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- 标题样式 -->
        <Style x:Key="TitleTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,2,0,0"/>
        </Style>

        <!-- 子页面标题样式 -->
        <Style x:Key="SubPageTitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="9"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,1,0,0"/>
            <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.GrayTextBrushKey}}"/>
            <Setter Property="FontStyle" Value="Italic"/>
        </Style>
    </UserControl.Resources>

    <Button Style="{StaticResource NavigationButtonStyle}"
            Command="{Binding Command}"
            CommandParameter="{Binding CommandParameter}"
            IsEnabled="{Binding IsEnabled}"
            ToolTip="{Binding ToolTip}"
            Width="80"
            Height="70">
        <StackPanel Orientation="Vertical">
            <!-- 图标 -->
            <TextBlock Text="{Binding Icon}"
                       Style="{StaticResource IconTextStyle}"/>

            <!-- 标题 -->
            <TextBlock Text="{Binding Title}"
                       Style="{StaticResource TitleTextStyle}"/>

            <!-- 当前子页面标题 -->
            <TextBlock Text="{Binding CurrentSubPageTitle}">
                <TextBlock.Style>
                    <Style TargetType="TextBlock" BasedOn="{StaticResource SubPageTitleStyle}">
                        <Setter Property="Visibility" Value="Collapsed"/>
                        <Style.Triggers>
                            <MultiDataTrigger>
                                <MultiDataTrigger.Conditions>
                                    <Condition Binding="{Binding IsCategory}" Value="True"/>
                                    <Condition Binding="{Binding IsSelected}" Value="True"/>
                                </MultiDataTrigger.Conditions>
                                <Setter Property="Visibility" Value="Visible"/>
                            </MultiDataTrigger>
                        </Style.Triggers>
                    </Style>
                </TextBlock.Style>
            </TextBlock>

            <!-- 分类指示器已移除，不再显示小三角形 -->
        </StackPanel>
    </Button>
</UserControl>
