<Window x:Class="TestPluginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="插件演示测试" Height="400" Width="600"
        WindowStartupLocation="CenterScreen">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 顶部按钮 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="10">
            <Button Content="测试按钮" Width="100" Height="30" Margin="5"/>
            <TextBlock Text="状态: 正常" VerticalAlignment="Center" Margin="10,0"/>
        </StackPanel>

        <!-- 中间内容 -->
        <TabControl Grid.Row="1" Margin="10">
            <TabItem Header="基本信息">
                <StackPanel Margin="10">
                    <TextBlock Text="这是一个简化的插件演示窗口测试" FontSize="14" Margin="0,0,0,10"/>
                    <TextBlock Text="用于验证XAML解析是否正常" FontSize="12"/>
                </StackPanel>
            </TabItem>
            <TabItem Header="测试数据">
                <DataGrid AutoGenerateColumns="True" Margin="10">
                    <!-- 空的DataGrid用于测试 -->
                </DataGrid>
            </TabItem>
        </TabControl>

        <!-- 底部状态栏 -->
        <StatusBar Grid.Row="2">
            <StatusBarItem>
                <TextBlock Text="就绪"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
