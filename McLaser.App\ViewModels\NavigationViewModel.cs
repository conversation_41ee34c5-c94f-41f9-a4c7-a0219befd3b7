#nullable enable
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows.Input;
using McLaser.App.Models;
using McLaser.App.Services;
using McLaser.Core.Common;
using McLaser.Core.Framework.Logging;
using AppNavigationService = McLaser.App.Services.INavigationService;
using AppPageChangedEventArgs = McLaser.App.Services.PageChangedEventArgs;

namespace McLaser.App.ViewModels
{
    /// <summary>
    /// 导航ViewModel
    /// 管理底部导航栏的显示和交互
    /// </summary>
    public class NavigationViewModel : ViewModelBase
    {
        private readonly AppNavigationService _navigationService;
        private readonly ILogger? _logger;
        private string _currentTheme = "Light";

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="navigationService">导航服务</param>
        /// <param name="logger">日志服务</param>
        public NavigationViewModel(AppNavigationService navigationService, ILogger? logger = null)
        {
            _navigationService = navigationService ?? throw new ArgumentNullException(nameof(navigationService));
            _logger = logger;

            InitializeCommands();
            InitializeNavigationItems();
            SubscribeToEvents();
        }

        #region 属性

        /// <summary>
        /// 导航项集合
        /// </summary>
        public ObservableCollection<NavigationItemViewModel> NavigationItems { get; } = new ObservableCollection<NavigationItemViewModel>();

        /// <summary>
        /// 可用主题列表
        /// </summary>
        public ObservableCollection<string> AvailableThemes { get; } = new ObservableCollection<string> { "Light", "Dark" };

        /// <summary>
        /// 当前主题
        /// </summary>
        public string CurrentTheme
        {
            get => _currentTheme;
            set => SetProperty(ref _currentTheme, value);
        }

        #endregion

        #region 命令

        /// <summary>
        /// 导航到页面命令
        /// </summary>
        public ICommand NavigateToPageCommand { get; private set; } = null!;

        /// <summary>
        /// 返回上一页命令
        /// </summary>
        public ICommand GoBackCommand { get; private set; } = null!;

        /// <summary>
        /// 前进到下一页命令
        /// </summary>
        public ICommand GoForwardCommand { get; private set; } = null!;

        /// <summary>
        /// 打开设置命令
        /// </summary>
        public ICommand OpenSettingsCommand { get; private set; } = null!;

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化命令
        /// </summary>
        private void InitializeCommands()
        {
            NavigateToPageCommand = new RelayCommand<PageInfo>(ExecuteNavigateToPage, CanExecuteNavigateToPage);
            GoBackCommand = new RelayCommand(ExecuteGoBack, CanExecuteGoBack);
            GoForwardCommand = new RelayCommand(ExecuteGoForward, CanExecuteGoForward);
            OpenSettingsCommand = new RelayCommand(ExecuteOpenSettings);
        }

        /// <summary>
        /// 初始化导航项
        /// </summary>
        private void InitializeNavigationItems()
        {
            // 从导航服务获取已注册的页面
            var registeredPages = _navigationService.RegisteredPages.ToDictionary(p => p.Id, p => p);

            // 创建导航项，使用已注册的页面信息
            var navigationItems = new[]
            {
                // 主页
                new NavigationItem
                {
                    Id = "home",
                    Title = "主页",
                    Icon = "🏠",
                    Category = NavigationCategory.Main,
                    Order = 1,
                    PageInfo = registeredPages.ContainsKey("home") ? registeredPages["home"] : null
                },

                // 设备管理分类
                new NavigationItem
                {
                    Id = "device",
                    Title = "设备",
                    Icon = "📱",
                    Category = NavigationCategory.Device,
                    IsCategory = true,
                    Order = 2,
                    SubPages = new System.Collections.Generic.List<PageInfo>
                    {
                        registeredPages.ContainsKey("device-manager") ? registeredPages["device-manager"] : CreateFallbackPageInfo("device-manager", "设备管理器", "管理所有设备", "⚙"),
                        registeredPages.ContainsKey("device-status") ? registeredPages["device-status"] : CreateFallbackPageInfo("device-status", "设备状态", "查看设备状态", "📊")
                    }
                },

                // 系统功能分类
                new NavigationItem
                {
                    Id = "system",
                    Title = "系统",
                    Icon = "🔧",
                    Category = NavigationCategory.System,
                    IsCategory = true,
                    Order = 3,
                    SubPages = new System.Collections.Generic.List<PageInfo>
                    {
                        registeredPages.ContainsKey("eventbus-demo") ? registeredPages["eventbus-demo"] : CreateFallbackPageInfo("eventbus-demo", "事件总线", "事件总线演示", "📡"),
                        registeredPages.ContainsKey("exception-demo") ? registeredPages["exception-demo"] : CreateFallbackPageInfo("exception-demo", "异常处理", "异常处理演示", "⚠"),
                        registeredPages.ContainsKey("plugin-demo") ? registeredPages["plugin-demo"] : CreateFallbackPageInfo("plugin-demo", "插件管理", "插件管理演示", "🔌")
                    }
                },

                // 工具
                new NavigationItem
                {
                    Id = "tools",
                    Title = "工具",
                    Icon = "🛠",
                    Category = NavigationCategory.Tools,
                    Order = 4,
                    PageInfo = registeredPages.ContainsKey("data-input") ? registeredPages["data-input"] : null
                },

                // 设置
                new NavigationItem
                {
                    Id = "settings",
                    Title = "设置",
                    Icon = "⚙",
                    Category = NavigationCategory.Settings,
                    Order = 5,
                    PageInfo = registeredPages.ContainsKey("settings") ? registeredPages["settings"] : null
                }
            };

            // 转换为ViewModel并添加到集合
            foreach (var item in navigationItems.OrderBy(x => x.Order))
            {
                var viewModel = new NavigationItemViewModel(item, this);
                NavigationItems.Add(viewModel);
            }

            _logger?.LogInfo($"已初始化 {NavigationItems.Count} 个导航项");
        }

        /// <summary>
        /// 创建备用页面信息
        /// </summary>
        private PageInfo CreateFallbackPageInfo(string id, string title, string description, string icon)
        {
            return new PageInfo
            {
                Id = id,
                Title = title,
                Description = description,
                Icon = icon,
                PageFactory = () => new System.Windows.Controls.TextBlock
                {
                    Text = $"页面 '{title}' 尚未实现",
                    HorizontalAlignment = System.Windows.HorizontalAlignment.Center,
                    VerticalAlignment = System.Windows.VerticalAlignment.Center,
                    FontSize = 16
                }
            };
        }

        /// <summary>
        /// 订阅事件
        /// </summary>
        private void SubscribeToEvents()
        {
            _navigationService.PageChanged += OnPageChanged;
        }

        /// <summary>
        /// 页面变更事件处理
        /// </summary>
        private void OnPageChanged(object? sender, AppPageChangedEventArgs e)
        {
            // 更新选中状态
            foreach (var item in NavigationItems)
            {
                item.UpdateSelection(e.NewPage?.Id);
            }

            _logger?.LogInfo($"页面已切换: {e.OldPage?.Title} -> {e.NewPage?.Title}");
        }

        /// <summary>
        /// 关闭所有弹出框
        /// </summary>
        private void CloseAllPopups()
        {
            foreach (var item in NavigationItems)
            {
                if (item.IsPopupOpen)
                {
                    item.IsPopupOpen = false;
                }
            }
        }

        #endregion

        #region 命令执行方法

        /// <summary>
        /// 执行导航到页面命令
        /// </summary>
        /// <param name="pageInfo">页面信息</param>
        private void ExecuteNavigateToPage(PageInfo? pageInfo)
        {
            if (pageInfo == null) return;

            try
            {
                var success = _navigationService.NavigateTo(pageInfo);
                if (success)
                {
                    // 导航成功后关闭所有弹出框
                    CloseAllPopups();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"导航失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 判断是否可以导航到页面
        /// </summary>
        /// <param name="pageInfo">页面信息</param>
        /// <returns>是否可以导航</returns>
        private bool CanExecuteNavigateToPage(PageInfo? pageInfo)
        {
            return pageInfo != null && pageInfo.Id != _navigationService.CurrentPage?.Id;
        }

        /// <summary>
        /// 执行返回上一页命令
        /// </summary>
        private void ExecuteGoBack()
        {
            try
            {
                var success = _navigationService.GoBack();
                if (success)
                {
                    // 导航成功后关闭所有弹出框
                    CloseAllPopups();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"返回失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 判断是否可以返回上一页
        /// </summary>
        /// <returns>是否可以返回</returns>
        private bool CanExecuteGoBack()
        {
            return _navigationService.NavigationHistory.Count > 1;
        }

        /// <summary>
        /// 执行前进到下一页命令
        /// </summary>
        private void ExecuteGoForward()
        {
            try
            {
                var success = _navigationService.GoForward();
                if (success)
                {
                    // 导航成功后关闭所有弹出框
                    CloseAllPopups();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"前进失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 判断是否可以前进到下一页
        /// </summary>
        /// <returns>是否可以前进</returns>
        private bool CanExecuteGoForward()
        {
            // 这里需要根据实际的前进逻辑来判断
            return false; // 暂时返回false
        }

        /// <summary>
        /// 执行打开设置命令
        /// </summary>
        private void ExecuteOpenSettings()
        {
            try
            {
                var settingsPage = NavigationItems
                    .FirstOrDefault(x => x.NavigationItem.Id == "settings")?.NavigationItem.PageInfo;

                if (settingsPage != null)
                {
                    var success = _navigationService.NavigateTo(settingsPage);
                    if (success)
                    {
                        // 导航成功后关闭所有弹出框
                        CloseAllPopups();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"打开设置失败: {ex.Message}");
            }
        }

        #endregion

        #region 清理

        /// <summary>
        /// 清理资源
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _navigationService.PageChanged -= OnPageChanged;
            }

            base.Dispose(disposing);
        }

        #endregion
    }
}
