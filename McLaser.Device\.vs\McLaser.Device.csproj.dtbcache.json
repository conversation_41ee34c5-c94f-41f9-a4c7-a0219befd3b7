{"RootPath": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\McLaser_V1\\McLaser.Device", "ProjectFileName": "McLaser.Device.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Base\\Camera\\BoolToInverseConverter.cs"}, {"SourceFile": "Base\\Camera\\CameraBase.cs"}, {"SourceFile": "Base\\Camera\\CameraEnums.cs"}, {"SourceFile": "Base\\Camera\\CameraInfo.cs"}, {"SourceFile": "Base\\Camera\\CameraView.xaml.cs"}, {"SourceFile": "Base\\Camera\\CameraViewModel.cs"}, {"SourceFile": "Base\\Camera\\ICamera.cs"}, {"SourceFile": "Base\\Camera\\ImageData.cs"}, {"SourceFile": "Base\\Camera\\StatusCamera.cs"}, {"SourceFile": "Base\\Camera\\TrigMode.cs"}, {"SourceFile": "Base\\Motion\\AxisBase.cs"}, {"SourceFile": "Base\\Motion\\CardBase.cs"}, {"SourceFile": "Base\\Motion\\CardConfigControlFactory.cs"}, {"SourceFile": "Base\\Motion\\CardConfigViewModel.cs"}, {"SourceFile": "Base\\Motion\\IAxis.cs"}, {"SourceFile": "Base\\Motion\\ICard.cs"}, {"SourceFile": "Base\\Motion\\ICardConfigControl.cs"}, {"SourceFile": "Base\\Motion\\IOBase.cs"}, {"SourceFile": "Base\\Motion\\LimitBase.cs"}, {"SourceFile": "Base\\Motion\\MotionEnums.cs"}, {"SourceFile": "Base\\Motion\\StatusAxis.cs"}, {"SourceFile": "Base\\Motion\\Views\\CardConfigControlBase.xaml.cs"}, {"SourceFile": "Base\\Motion\\Views\\CardConfigTestWindow.xaml.cs"}, {"SourceFile": "ConfigBase.cs"}, {"SourceFile": "Converters\\BoolToColorConverter.cs"}, {"SourceFile": "Converters\\BoolToRotateTransformConverter.cs"}, {"SourceFile": "Converters\\BoolToTextConverter.cs"}, {"SourceFile": "DeviceBase.cs"}, {"SourceFile": "DeviceManager\\DeviceCategoryGroup.cs"}, {"SourceFile": "DeviceManager\\DeviceFactory.cs"}, {"SourceFile": "DeviceManager\\DeviceItemAttribute.cs"}, {"SourceFile": "DeviceManager\\DeviceManager.cs"}, {"SourceFile": "DeviceManager\\DeviceTypeMetadata.cs"}, {"SourceFile": "DeviceManager\\IDeviceManager.cs"}, {"SourceFile": "DeviceManager\\ViewModels\\DeviceManagerViewModel.cs"}, {"SourceFile": "DeviceManager\\ViewModels\\DeviceTypeCategoryViewModel.cs"}, {"SourceFile": "DeviceManager\\ViewModels\\DeviceTypeItemViewModel.cs"}, {"SourceFile": "DeviceManager\\Views\\DeviceManagerControl.xaml.cs"}, {"SourceFile": "DeviceType.cs"}, {"SourceFile": "IDevice.cs"}, {"SourceFile": "IDeviceConfiguration.cs"}, {"SourceFile": "ILaser.cs"}, {"SourceFile": "ISensor.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "StatusBase.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.cs"}], "References": [{"Reference": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\bin\\halcondotnetxl.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\bin\\McLaser.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\bin\\McLaser.Core.dll"}, {"Reference": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\bin\\McLaser.Modules.Vision.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\bin\\McLaser.Modules.Vision.dll"}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\bin\\Microsoft.Xaml.Behaviors.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\bin\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\PresentationCore.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\PresentationFramework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.ComponentModel.Composition.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Xaml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\WindowsBase.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\bin\\McLaser.Device.dll", "OutputItemRelativePath": "McLaser.Device.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}