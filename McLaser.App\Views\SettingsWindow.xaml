<Window x:Class="McLaser.App.Views.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="设置" Height="500" Width="600"
        MinHeight="400" MinWidth="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">
    
    <Window.Resources>
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14" />
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="Margin" Value="0,15,0,10" />
            <Setter Property="Foreground" Value="{DynamicResource AccentBrush}" />
        </Style>

        <Style x:Key="SettingLabelStyle" TargetType="Label">
            <Setter Property="Width" Value="120" />
            <Setter Property="VerticalAlignment" Value="Center" />
            <Setter Property="Margin" Value="0,5" />
        </Style>

        <Style x:Key="SettingControlStyle" TargetType="FrameworkElement">
            <Setter Property="Margin" Value="10,5" />
            <Setter Property="VerticalAlignment" Value="Center" />
        </Style>
    </Window.Resources>
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        
        <!-- 设置内容 -->
        <ScrollViewer Grid.Row="0" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- 外观设置 -->
                <TextBlock Text="外观设置" Style="{StaticResource SectionHeaderStyle}" />
                
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    
                    <Label Grid.Row="0" Grid.Column="0" Content="主题：" Style="{StaticResource SettingLabelStyle}" />
                    <ComboBox Grid.Row="0" Grid.Column="1" 
                              ItemsSource="{Binding AvailableThemes}"
                              SelectedItem="{Binding SelectedTheme}"
                              Style="{StaticResource SettingControlStyle}" />
                    <Button Grid.Row="0" Grid.Column="2" Content="预览" 
                            Command="{Binding PreviewThemeCommand}" 
                            CommandParameter="{Binding SelectedTheme}"
                            Style="{StaticResource SettingControlStyle}" 
                            Padding="10,5" />
                </Grid>
                
                <!-- 行为设置 -->
                <TextBlock Text="行为设置" Style="{StaticResource SectionHeaderStyle}" />
                
                <StackPanel Margin="0,0,0,15">
                    <CheckBox Content="自动保存设置" 
                              IsChecked="{Binding AutoSaveSettings}"
                              Margin="0,5" />
                    <CheckBox Content="记住窗口状态" 
                              IsChecked="{Binding RememberWindowState}"
                              Margin="0,5" />
                </StackPanel>
                
                <!-- 系统设置 -->
                <TextBlock Text="系统设置" Style="{StaticResource SectionHeaderStyle}" />
                
                <StackPanel Margin="0,0,0,15">
                    <CheckBox Content="启用日志记录" 
                              IsChecked="{Binding EnableLogging}"
                              Margin="0,5" />
                    <CheckBox Content="启用缓存" 
                              IsChecked="{Binding EnableCaching}"
                              Margin="0,5" />
                    <CheckBox Content="启用性能监控" 
                              IsChecked="{Binding EnablePerformanceMonitoring}"
                              Margin="0,5" />
                </StackPanel>
                
                <!-- 日志设置 -->
                <TextBlock Text="日志设置" Style="{StaticResource SectionHeaderStyle}" />
                
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    
                    <Label Grid.Column="0" Content="日志级别：" Style="{StaticResource SettingLabelStyle}" />
                    <ComboBox Grid.Column="1" 
                              ItemsSource="{Binding AvailableLogLevels}"
                              SelectedItem="{Binding LogLevel}"
                              Style="{StaticResource SettingControlStyle}" />
                </Grid>
                
                <!-- 设置说明 -->
                <TextBlock Text="设置说明" Style="{StaticResource SectionHeaderStyle}" />
                
                <TextBlock TextWrapping="Wrap" Margin="0,0,0,15" Foreground="{DynamicResource DisabledForegroundBrush}">
                    <Run Text="• 主题设置：选择应用程序的外观主题，支持浅色和深色主题。" /><LineBreak />
                    <Run Text="• 自动保存设置：启用后，设置更改将自动保存。" /><LineBreak />
                    <Run Text="• 记住窗口状态：启用后，应用程序将记住窗口的位置和大小。" /><LineBreak />
                    <Run Text="• 日志记录：启用后，应用程序将记录操作日志。" /><LineBreak />
                    <Run Text="• 缓存：启用后，应用程序将使用缓存提高性能。" /><LineBreak />
                    <Run Text="• 性能监控：启用后，应用程序将监控性能指标。" />
                </TextBlock>
            </StackPanel>
        </ScrollViewer>
        
        <!-- 按钮区域 -->
        <Border Grid.Row="1" BorderThickness="0,1,0,0" BorderBrush="{DynamicResource BorderBrush}" 
                Padding="0,15,0,0" Margin="0,15,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="应用" Command="{Binding ApplySettingsCommand}" 
                        Margin="0,0,10,0" Padding="15,8" MinWidth="80" />
                <Button Content="保存" Command="{Binding SaveSettingsCommand}" 
                        Margin="0,0,10,0" Padding="15,8" MinWidth="80" />
                <Button Content="重置" Command="{Binding ResetSettingsCommand}" 
                        Margin="0,0,10,0" Padding="15,8" MinWidth="80" />
                <Button Content="关闭" Command="{Binding CloseCommand}" 
                        CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                        Padding="15,8" MinWidth="80" IsCancel="True" />
            </StackPanel>
        </Border>
    </Grid>
</Window>
