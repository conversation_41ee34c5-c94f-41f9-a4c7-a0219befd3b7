using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Threading;
using System.Threading.Tasks;
using System.Xml.Serialization;
using McLaser.Device;
using MvCameraControl;
using HalconDotNet;
using IDevice = MvCameraControl.IDevice;


namespace McLaser.Devices.Camera
{

    [DeviceItem("工业相机", "海康相机", "兼容网口/USB3系列", "📷")]
    public class CameraHIK : CameraBase
    {
        #region 私有字段
        
        /// <summary>
        /// 海康相机设备对象
        /// </summary>
        private IDevice device = null;
        
        /// <summary>
        /// 图像帧输出对象
        /// </summary>
        private IFrameOut frame = null;
        
        /// <summary>
        /// 设备层类型枚举
        /// </summary>
        private readonly DeviceTLayerType enumTLayerType = DeviceTLayerType.MvGigEDevice | 
            DeviceTLayerType.MvUsbDevice | DeviceTLayerType.MvGenTLGigEDevice | 
            DeviceTLayerType.MvGenTLCXPDevice | DeviceTLayerType.MvGenTLCameraLinkDevice | 
            DeviceTLayerType.MvGenTLXoFDevice;
        
        #endregion

        #region 属性

        /// <summary>
        /// 设备状态
        /// </summary>
        [XmlIgnore, Browsable(false)]
        public override StatusDevice Status { get; set; } = new StatusHIK();

        /// <summary>
        /// 图像格式
        /// </summary>
        [Category("海康相机"), DisplayName("图像格式")]
        public string PixelFormat { get; set; } = "Mono8";

        /// <summary>
        /// 是否正在采集
        /// </summary>
        [XmlIgnore, Browsable(false)]
        public bool IsGrabbing { get; private set; } = false;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public CameraHIK()
        {
            Name = "海康威视相机";
            DeviceType = DevicesType.Camera;
        }

        #endregion

        #region 设备基本操作

        /// <summary>
        /// 搜索相机设备
        /// </summary>
        /// <returns>相机信息列表</returns>
        public override List<CameraInfo> SearchCameras()
        {
            List<CameraInfo> cameraInfos = new List<CameraInfo>();
            try
            {
                // 枚举设备
                int result = DeviceEnumerator.EnumDevices(enumTLayerType, out List<IDeviceInfo> deviceInfos);
                if (result != MvError.MV_OK)
                {
                    return cameraInfos;
                }

                // 添加设备信息到列表
                for (int i = 0; i < deviceInfos.Count; i++)
                {
                    IDeviceInfo deviceInfo = deviceInfos[i];
                    string displayName = !string.IsNullOrEmpty(deviceInfo.UserDefinedName) 
                        ? $"{deviceInfo.UserDefinedName} {deviceInfo.SerialNumber}"
                        : deviceInfo.SerialNumber;
                    
                    cameraInfos.Add(new CameraInfo() 
                    { 
                        CamName = displayName, 
                        SerialNO = deviceInfo.SerialNumber, 
                        ExtInfo = deviceInfo 
                    });
                }
            }
            catch (Exception ex)
            {
                // 记录异常信息
                System.Diagnostics.Debug.WriteLine($"搜索海康相机异常：{ex.Message}");
            }
            return cameraInfos;
        }

        /// <summary>
        /// 打开相机设备
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool Open()
        {
            try
            {
                if (!IsEnabled) return false;
                if (Status.IsConnected) Close();

                var curDevice = (IDeviceInfo)CameraInfo.ExtInfo;
                try
                {
                    device = MvCameraControl.DeviceFactory.CreateDevice(curDevice);
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show("创建海康相机设备失败!" + ex.Message);
                    return false;
                }

                // 打开设备
                int result = device.Open();
                if (result != MvError.MV_OK)
                {
                    device.Dispose();
                    device = null;
                    return false;
                }

                // 注册回调函数
                device.StreamGrabber.FrameGrabedEvent += ImageCallbackFunc;
                
                // 设置采集模式为连续模式
                device.Parameters.SetEnumValueByString("AcquisitionMode", "Continuous");

                // 开启抓图
                result = device.StreamGrabber.StartGrabbing();
                if (result != MvError.MV_OK)
                {
                    return false;
                }

                Status.IsConnected = true;
                
                // 启动采集
                if (!Start()) return false;
                
                // 获取图像尺寸
                int width = 0, height = 0;
                if (!GetWidth(ref width)) return false;
                if (!GetHeight(ref height)) return false;

                return IsOpen();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"打开海康相机异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查设备是否已打开
        /// </summary>
        /// <returns>是否已打开</returns>
        public override bool IsOpen()
        {
            return Status.IsConnected;
        }

        /// <summary>
        /// 关闭相机设备
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool Close()
        {
            try
            {
                Stop();
                
                // 注销回调
                if (device != null)
                {
                    device.StreamGrabber.FrameGrabedEvent -= ImageCallbackFunc;
                    device.Close();
                    device.Dispose();
                    device = null;
                }

                Status.IsConnected = false;
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"关闭海康相机异常：{ex.Message}");
                return false;
            }
        }

        #endregion

        #region 采集控制

        /// <summary>
        /// 开始采集
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool Start()
        {
            try
            {
                if (!IsOpen()) return false;
                
                int result = device.StreamGrabber.StartGrabbing();
                if (result != MvError.MV_OK) return false;
                
                (Status as StatusCamera).IsStart = true;
                (Status as StatusCamera).IsGrapOnce = false;
                (Status as StatusCamera).IsGrapContinue = false;
                
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"启动海康相机采集异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查是否正在采集
        /// </summary>
        /// <returns>是否正在采集</returns>
        public override bool IsStart()
        {
            return (Status as StatusCamera).IsStart;
        }

        /// <summary>
        /// 停止采集
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool Stop()
        {
            try
            {
                if (device != null)
                {
                    device.StreamGrabber.StopGrabbing();
                }
                
                (Status as StatusCamera).IsStart = false;
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"停止海康相机采集异常：{ex.Message}");
                return false;
            }
        }

        #endregion

        #region 图像采集

        /// <summary>
        /// 采集单帧图像
        /// </summary>
        /// <param name="source">输出图像数据</param>
        /// <returns>是否成功</returns>
        public override bool GrabImageData(out ImageData source)
        {
            source = new ImageData();
            try
            {
                if (!IsOpen()) return false;
                if (!IsStart()) return false;
                if (device == null) return false;

                // 请求修改FrameOut
                Interlocked.Exchange(ref isRequestUpdate, 1);

                // 软触发
                if (TrigMode == TrigMode.软触发)
                {
                    device.Parameters.SetCommandValue("TriggerSoftware");
                }

                // 等待帧获取完成
                updateImageEvent.WaitOne(1000);

                // 拷贝帧数据
                CopyFrameData(ref source);
                (Status as StatusCamera).IsGrapOnce = true;
                
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"海康相机采集图像异常：{ex.Message}");
                return false;
            }
        }

        #endregion

        #region 参数设置

        /// <summary>
        /// 获取曝光时间
        /// </summary>
        /// <param name="value">曝光时间值</param>
        /// <returns>是否成功</returns>
        public override bool GetExposure(ref double value)
        {
            try
            {
                if (!IsOpen()) return false;
                var result = device.Parameters.GetFloatValue("ExposureTime", out var v);
                value = (double)v.CurValue;
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取海康相机曝光时间异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置曝光时间
        /// </summary>
        /// <param name="value">曝光时间值</param>
        /// <returns>是否成功</returns>
        public override bool SetExposure(double value)
        {
            try
            {
                if (!IsOpen()) return false;
                device.Parameters.SetEnumValue("ExposureAuto", 0);
                int result = device.Parameters.SetFloatValue("ExposureTime", (float)value);
                if (result == MvError.MV_OK)
                {
                    Exposure = value;
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置海康相机曝光时间异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取增益值
        /// </summary>
        /// <param name="value">增益值</param>
        /// <returns>是否成功</returns>
        public override bool GetGain(ref double value)
        {
            try
            {
                if (!IsOpen()) return false;
                var result = device.Parameters.GetFloatValue("Gain", out var v);
                value = (double)v.CurValue;
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取海康相机增益异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置增益值
        /// </summary>
        /// <param name="value">增益值</param>
        /// <returns>是否成功</returns>
        public override bool SetGain(double value)
        {
            try
            {
                if (!IsOpen()) return false;
                device.Parameters.SetEnumValue("GainAuto", 0);
                int result = device.Parameters.SetFloatValue("Gain", (float)value);
                if (result == MvError.MV_OK)
                {
                    Gain = value;
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置海康相机增益异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取图像宽度
        /// </summary>
        /// <param name="width">图像宽度</param>
        /// <returns>是否成功</returns>
        public override bool GetWidth(ref int width)
        {
            try
            {
                if (!IsOpen()) return false;
                var result = device.Parameters.GetIntValue("Width", out var v);
                width = (int)v.CurValue;
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取海康相机图像宽度异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取图像高度
        /// </summary>
        /// <param name="height">图像高度</param>
        /// <returns>是否成功</returns>
        public override bool GetHeight(ref int height)
        {
            try
            {
                if (!IsOpen()) return false;
                var result = device.Parameters.GetIntValue("Height", out var v);
                height = (int)v.CurValue;
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取海康相机图像高度异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置触发模式
        /// </summary>
        /// <param name="mode">触发模式</param>
        /// <returns>是否成功</returns>
        public bool SetTriggerMode(TrigMode mode)
        {
            try
            {
                if (!IsOpen()) return false;

                switch (mode)
                {
                    case TrigMode.上升沿:
                        {
                            int nRet = device.Parameters.SetEnumValue("TriggerMode", 1);
                            nRet += device.Parameters.SetEnumValue("TriggerSource", 0);
                            nRet += device.Parameters.SetEnumValue("TriggerActivation", 0);
                            if (nRet != MvError.MV_OK)
                            {
                                System.Diagnostics.Debug.WriteLine("设置海康相机上升沿触发失败!");
                                return false;
                            }
                        }
                        break;
                    case TrigMode.下降沿:
                        {
                            int nRet = device.Parameters.SetEnumValue("TriggerMode", 1);
                            nRet += device.Parameters.SetEnumValue("TriggerSource", 0);
                            nRet += device.Parameters.SetEnumValue("TriggerActivation", 1);
                            if (nRet != MvError.MV_OK)
                            {
                                System.Diagnostics.Debug.WriteLine("设置海康相机下降沿触发失败!");
                                return false;
                            }
                        }
                        break;
                    case TrigMode.软触发:
                        {
                            int nRet = device.Parameters.SetEnumValue("TriggerMode", 1);
                            nRet += device.Parameters.SetEnumValue("TriggerSource", 7);
                            if (nRet != MvError.MV_OK)
                            {
                                System.Diagnostics.Debug.WriteLine("设置海康相机软触发失败!");
                                return false;
                            }
                        }
                        break;
                    case TrigMode.连续采集:
                        {
                            int nRet = device.Parameters.SetEnumValue("TriggerMode", 0);
                            if (nRet != MvError.MV_OK)
                            {
                                System.Diagnostics.Debug.WriteLine("设置海康相机连续采集失败!");
                                return false;
                            }
                        }
                        break;
                }

                TrigMode = mode;
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置海康相机触发模式异常：{ex.Message}");
                return false;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 图像回调函数
        /// </summary>
        /// <param name="sender">发送者</param>
        /// <param name="e">事件参数</param>
        private void ImageCallbackFunc(object sender, FrameGrabbedEventArgs e)
        {
            try
            {
                // 获取帧
                if (Interlocked.Read(ref isRequestUpdate) == 1)
                {
                    frame = e.FrameOut;
                    updateImageEvent.Set();
                    Interlocked.Exchange(ref isRequestUpdate, 0);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"海康相机获取帧异常：{ex.Message}");
                updateImageEvent.Set();
            }
        }

        /// <summary>
        /// 拷贝帧数据
        /// </summary>
        /// <param name="source">目标图像数据</param>
        private void CopyFrameData(ref ImageData imageData)
        {
            if (frame.Image != null)
            {
                imageData.PixelDataPtr = frame.Image.PixelDataPtr;
                imageData.Width = frame.Image.Width;
                imageData.Height = frame.Image.Height;
                imageData.ImageSize = frame.Image.ImageSize;
            }
        }

        #endregion
    }
}
