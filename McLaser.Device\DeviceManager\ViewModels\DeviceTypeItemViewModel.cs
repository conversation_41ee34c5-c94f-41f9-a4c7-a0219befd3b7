#nullable enable
using McLaser.Core.Common;

namespace McLaser.Device.ViewModels
{
    /// <summary>
    /// 设备类型项视图模型
    /// </summary>
    public partial class DeviceTypeItemViewModel : ObservableObject
    {
        /// <summary>
        /// 设备类型元数据
        /// </summary>
        public DeviceTypeMetadata Metadata { get; }

        /// <summary>
        /// 类型名称
        /// </summary>
        public string TypeName => Metadata.TypeName;

        /// <summary>
        /// 显示名称
        /// </summary>
        public string DisplayName => Metadata.DisplayName;

        /// <summary>
        /// 图标
        /// </summary>
        public string Icon => string.IsNullOrEmpty(Metadata.Icon) ? "🔧" : Metadata.Icon;

        /// <summary>
        /// 描述
        /// </summary>
        public string Description => Metadata.Description;

        /// <summary>
        /// 制造商
        /// </summary>
        public string Manufacturer => Metadata.Manufacturer;

        /// <summary>
        /// 是否支持自动发现
        /// </summary>
        public bool SupportsAutoDiscovery => Metadata.SupportsAutoDiscovery;

        /// <summary>
        /// 是否需要驱动程序
        /// </summary>
        public bool RequiresDriver => Metadata.RequiresDriver;

        /// <summary>
        /// 支持的连接类型
        /// </summary>
        public string SupportedConnections => string.Join(", ", Metadata.SupportedConnections);

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="metadata">设备类型元数据</param>
        public DeviceTypeItemViewModel(DeviceTypeMetadata metadata)
        {
            Metadata = metadata;
        }

        /// <summary>
        /// 转换为字符串
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"{DisplayName} ({TypeName})";
        }
    }
}
