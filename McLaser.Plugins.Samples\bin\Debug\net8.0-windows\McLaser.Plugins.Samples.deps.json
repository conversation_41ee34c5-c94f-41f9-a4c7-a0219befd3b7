{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"McLaser.Plugins.Samples/1.0.0": {"dependencies": {"McLaser.Core": "1.0.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"McLaser.Plugins.Samples.dll": {}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "McLaser.Core/1.0.0": {"runtime": {"McLaser.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"McLaser.Plugins.Samples/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "McLaser.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}