using System;
using System.ComponentModel;
using McLaser.Core.Common;
using McLaser.Device;
using Newtonsoft.Json;

namespace McLaser.Devices
{

    [Serializable]
    public class AxisBase : ObservableObject, IAxis
    {
        #region 基本属性

        [Category("基本"), DisplayName("轴名称")]
        public string Name { get; set; } = "Axis1";

        [Category("基本"), DisplayName("轴ID")]
        public int ID { get; set; } = 1;

        [Browsable(false)]
        [JsonIgnore]
        public ICard Card { get; set; }

        [Category("基本"), DisplayName("是否启用")]
        public bool IsUse { get; set; } = true;

        #endregion


        #region 运动参数
        
        [Category("运动参数"), DisplayName("脉冲当量")]
        public double PluseScale { get; set; } = 1;


        [Category("运动参数"), DisplayName("跟随误差")]
        public double FollowError { get; set; } = 0.003;


        [Category("运动参数"), DisplayName("软正限位")]
        public double PosLimitMax { get; set; } = 10000;


        [Category("运动参数"), DisplayName("软负限位")]
        public double PosLimitMin { get; set; } = -10;


        [Category("PMAC轴运动"), DisplayName("移动超时(ms)")]
        public int MoveTimeout { get; set; } = 30000;


        [Category("PMAC轴运动"), DisplayName("最大速度(mm/s)")]
        public double VelMax { get; set; } = 1000;


        [Category("PMAC轴运动"), DisplayName("最大加速度(mm/s²)")]
        public double AccMax { get; set; } = 10000;


        [Category("PMAC轴运动"), DisplayName("最大加加速度(mm/s³)")]
        public double JerkMax { get; set; } = 100000;

        #endregion


        #region 实际参数
        /// <summary>
        /// 实际位置
        /// </summary>
        [Category("实际参数"), DisplayName("当前位置")]
        public double CurPos { get; set; } = 0;

        /// <summary>
        /// 实际速度
        /// </summary>
        [Category("实际参数"), DisplayName("当前速度")]
        public double CurVel { get; set; } = 0;


        #endregion


        #region 状态属性
        [Browsable(false)]
        public bool IsEnable { get; set; } = false;

        [Browsable(false)]
        public bool IsHome { get; set; } = false;

        [Browsable(false)]
        public bool IsHoming { get; set; } = false;

        [Browsable(false)]
        public bool IsPlusLimit { get; set; } = false;

        [Browsable(false)]
        public bool IsMinusLimit { get; set; } = false;

        [Browsable(false)]
        public bool IsOrigin { get; set; } = false;

        [Browsable(false)]
        public bool IsError { get; set; } = false;

        [Browsable(false)]
        public bool IsMoving { get; set; } = false;

        [Browsable(false)]
        public bool IsInPos { get; set; } = false;
        #endregion


        #region 回零参数
    
        [Category("回零参数"), DisplayName("回零速度")]
        public double VelHome { get; set; } = 20;

        [Category("回零参数"), DisplayName("回零超时(ms)")]
        public double TimeoutHome { get; set; } = 10000;

        [Category("回零参数"), DisplayName("回零模式")]
        public HomeMode HomeMode {  get; set; }  = HomeMode.负极限_原点;

        [Category("回零参数"), DisplayName("回零偏移")]
        public double HomeOffset { get; set; } = 0;

        #endregion


        #region 构造函数
        /// <summary>
        /// 构造函数
        /// </summary>
        public AxisBase()
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="name">轴名称</param>
        /// <param name="id">轴ID</param>
        public AxisBase(string name, int id)
        {
            Name = name;
            ID = id;
        }
        #endregion


        #region 虚方法实现
        /// <summary>
        /// 移动到指定位置
        /// </summary>
        /// <param name="pos">目标位置</param>
        /// <returns>操作是否成功</returns>
        public virtual bool Move(double pos)
        {
            return true;
        }

        /// <summary>
        /// 停止运动
        /// </summary>
        /// <returns>操作是否成功</returns>
        public virtual bool Stop()
        {
            return true;
        }

        /// <summary>
        /// 设置轴参数
        /// </summary>
        /// <returns>操作是否成功</returns>
        public virtual bool SetParam()
        {
            return true;
        }

        /// <summary>
        /// 获取轴参数
        /// </summary>
        /// <returns>操作是否成功</returns>
        public virtual bool GetParam()
        {
            return true;
        }
        #endregion


    }
}
