using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using McLaser.Core.Plugins;
using McLaser.Core.Data;
using Newtonsoft.Json;

namespace McLaser.Plugins.DataProcessing
{
    /// <summary>
    /// 生产数据分析插件
    /// 提供激光加工生产数据的统计分析和报表生成功能
    /// </summary>
    [PluginMetadata(
        Id = "McLaser.Plugins.ProductionDataAnalyzer",
        Name = "生产数据分析器",
        Version = "1.3.0",
        Description = "提供生产数据统计分析、趋势分析、质量报告生成等功能，支持多种数据源和报表格式",
        Author = "McLaser Data Team",
        Category = "数据处理",
        SupportedPlatforms = new[] { "Windows" },
        MinFrameworkVersion = "4.7.2"
    )]
    public class ProductionDataAnalyzerPlugin : IPlugin, IDataProcessor
    {
        #region 私有字段

        private IPluginContext _context;
        private readonly Dictionary<string, IDataAnalyzer> _analyzers;
        private readonly Dictionary<string, IReportGenerator> _reportGenerators;
        private DataAnalysisConfiguration _configuration;

        #endregion

        #region 构造函数

        public ProductionDataAnalyzerPlugin()
        {
            _analyzers = new Dictionary<string, IDataAnalyzer>
            {
                ["Production"] = new ProductionAnalyzer(),
                ["Quality"] = new QualityAnalyzer(),
                ["Efficiency"] = new EfficiencyAnalyzer(),
                ["Trend"] = new TrendAnalyzer()
            };

            _reportGenerators = new Dictionary<string, IReportGenerator>
            {
                ["Excel"] = new ExcelReportGenerator(),
                ["PDF"] = new PDFReportGenerator(),
                ["HTML"] = new HTMLReportGenerator(),
                ["JSON"] = new JSONReportGenerator()
            };

            _configuration = new DataAnalysisConfiguration();
        }

        #endregion

        #region IPlugin 实现

        public string Id => "McLaser.Plugins.ProductionDataAnalyzer";
        public string Name => "生产数据分析器";
        public Version Version => new Version(1, 3, 0);
        public string Description => "生产数据统计分析和报表生成";
        public PluginStatus Status { get; private set; } = PluginStatus.Stopped;

        public event EventHandler<PluginStatusChangedEventArgs> StatusChanged;
        public event EventHandler<PluginErrorEventArgs> ErrorOccurred;

        public async Task InitializeAsync(IPluginContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            
            try
            {
                // 加载配置
                await LoadConfigurationAsync();
                
                // 初始化分析器
                foreach (var analyzer in _analyzers.Values)
                {
                    await analyzer.InitializeAsync(_configuration);
                }
                
                // 初始化报表生成器
                foreach (var generator in _reportGenerators.Values)
                {
                    await generator.InitializeAsync(_configuration);
                }
                
                Status = PluginStatus.Initialized;
                OnStatusChanged(PluginStatus.Stopped, PluginStatus.Initialized);
            }
            catch (Exception ex)
            {
                Status = PluginStatus.Error;
                OnErrorOccurred($"生产数据分析器初始化失败: {ex.Message}", ex);
                throw;
            }
        }

        public async Task StartAsync()
        {
            try
            {
                if (Status != PluginStatus.Initialized && Status != PluginStatus.Stopped)
                    throw new InvalidOperationException($"插件状态错误: {Status}");

                Status = PluginStatus.Running;
                OnStatusChanged(PluginStatus.Initialized, PluginStatus.Running);
                
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                Status = PluginStatus.Error;
                OnErrorOccurred($"生产数据分析器启动失败: {ex.Message}", ex);
                throw;
            }
        }

        public async Task StopAsync()
        {
            try
            {
                Status = PluginStatus.Stopped;
                OnStatusChanged(PluginStatus.Running, PluginStatus.Stopped);
                
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"生产数据分析器停止失败: {ex.Message}", ex);
            }
        }

        public async Task ShutdownAsync()
        {
            try
            {
                if (Status == PluginStatus.Running)
                {
                    await StopAsync();
                }

                // 清理资源
                foreach (var analyzer in _analyzers.Values)
                {
                    analyzer.Dispose();
                }
                
                foreach (var generator in _reportGenerators.Values)
                {
                    generator.Dispose();
                }
                
                Status = PluginStatus.Shutdown;
                OnStatusChanged(Status, PluginStatus.Shutdown);
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"生产数据分析器关闭失败: {ex.Message}", ex);
            }
        }

        #endregion

        #region IDataProcessor 实现

        public string ProcessorName => "ProductionDataAnalyzer";
        public string ProcessorVersion => "1.3.0";
        public DataProcessorType ProcessorType => DataProcessorType.Analyzer;
        public bool SupportsRealTime => true;

        public async Task<DataProcessingResult> ProcessDataAsync(DataSet inputData, Dictionary<string, object> parameters = null)
        {
            try
            {
                if (Status != PluginStatus.Running)
                    throw new InvalidOperationException("数据处理器未运行");

                if (inputData == null || inputData.Tables.Count == 0)
                    throw new ArgumentException("输入数据为空");

                var processingParams = ParseParameters(parameters);
                var startTime = DateTime.Now;

                // 执行数据分析
                var analysisResults = new Dictionary<string, object>();
                
                foreach (var analyzerType in processingParams.AnalyzerTypes)
                {
                    if (_analyzers.TryGetValue(analyzerType, out var analyzer))
                    {
                        var result = await analyzer.AnalyzeAsync(inputData, processingParams);
                        analysisResults[analyzerType] = result;
                    }
                }

                // 生成报表
                var reports = new Dictionary<string, byte[]>();
                if (processingParams.GenerateReports)
                {
                    foreach (var reportFormat in processingParams.ReportFormats)
                    {
                        if (_reportGenerators.TryGetValue(reportFormat, out var generator))
                        {
                            var reportData = await generator.GenerateReportAsync(analysisResults, processingParams);
                            reports[reportFormat] = reportData;
                        }
                    }
                }

                var processingTime = DateTime.Now - startTime;

                return new DataProcessingResult
                {
                    Success = true,
                    ProcessedData = CreateResultDataSet(analysisResults),
                    ProcessingTime = processingTime,
                    ProcessorName = ProcessorName,
                    Parameters = parameters,
                    Metadata = new Dictionary<string, object>
                    {
                        ["AnalysisResults"] = analysisResults,
                        ["Reports"] = reports,
                        ["RecordCount"] = inputData.Tables.Cast<DataTable>().Sum(t => t.Rows.Count),
                        ["AnalyzerTypes"] = processingParams.AnalyzerTypes,
                        ["ReportFormats"] = processingParams.ReportFormats
                    }
                };
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"数据处理失败: {ex.Message}", ex);
                return new DataProcessingResult
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    ProcessingTime = TimeSpan.Zero
                };
            }
        }

        public async Task<bool> ValidateDataAsync(DataSet inputData)
        {
            try
            {
                if (inputData == null || inputData.Tables.Count == 0)
                    return false;

                // 验证数据结构
                foreach (DataTable table in inputData.Tables)
                {
                    if (table.Rows.Count == 0)
                        continue;

                    // 检查必需的列
                    var requiredColumns = GetRequiredColumns(table.TableName);
                    foreach (var column in requiredColumns)
                    {
                        if (!table.Columns.Contains(column))
                            return false;
                    }
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        public Dictionary<string, object> GetDefaultParameters()
        {
            return new Dictionary<string, object>
            {
                ["AnalyzerTypes"] = new[] { "Production", "Quality" },
                ["ReportFormats"] = new[] { "Excel", "PDF" },
                ["GenerateReports"] = true,
                ["DateRange"] = new { Start = DateTime.Today.AddDays(-30), End = DateTime.Today },
                ["GroupBy"] = "Day",
                ["IncludeCharts"] = true,
                ["IncludeTrends"] = true
            };
        }

        public List<DataProcessorParameter> GetParameterDefinitions()
        {
            return new List<DataProcessorParameter>
            {
                new DataProcessorParameter
                {
                    Name = "AnalyzerTypes",
                    DisplayName = "分析类型",
                    Description = "选择要执行的分析类型",
                    Type = typeof(string[]),
                    DefaultValue = new[] { "Production", "Quality" },
                    AllowedValues = new object[] { "Production", "Quality", "Efficiency", "Trend" }
                },
                new DataProcessorParameter
                {
                    Name = "ReportFormats",
                    DisplayName = "报表格式",
                    Description = "选择生成的报表格式",
                    Type = typeof(string[]),
                    DefaultValue = new[] { "Excel", "PDF" },
                    AllowedValues = new object[] { "Excel", "PDF", "HTML", "JSON" }
                },
                new DataProcessorParameter
                {
                    Name = "GenerateReports",
                    DisplayName = "生成报表",
                    Description = "是否生成分析报表",
                    Type = typeof(bool),
                    DefaultValue = true
                },
                new DataProcessorParameter
                {
                    Name = "GroupBy",
                    DisplayName = "分组方式",
                    Description = "数据分组的时间单位",
                    Type = typeof(string),
                    DefaultValue = "Day",
                    AllowedValues = new object[] { "Hour", "Day", "Week", "Month" }
                }
            };
        }

        #endregion

        #region 私有方法

        private async Task LoadConfigurationAsync()
        {
            var configPath = Path.Combine(_context.ConfigurationDirectory, "DataAnalysis.json");
            if (File.Exists(configPath))
            {
                var json = File.ReadAllText(configPath);
                _configuration = JsonConvert.DeserializeObject<DataAnalysisConfiguration>(json);
            }
            
            await Task.CompletedTask;
        }

        private DataAnalysisParameters ParseParameters(Dictionary<string, object> parameters)
        {
            var result = new DataAnalysisParameters();
            
            if (parameters != null)
            {
                if (parameters.TryGetValue("AnalyzerTypes", out var analyzerTypes))
                    result.AnalyzerTypes = ((object[])analyzerTypes).Cast<string>().ToArray();
                
                if (parameters.TryGetValue("ReportFormats", out var reportFormats))
                    result.ReportFormats = ((object[])reportFormats).Cast<string>().ToArray();
                
                if (parameters.TryGetValue("GenerateReports", out var generateReports))
                    result.GenerateReports = Convert.ToBoolean(generateReports);
                
                if (parameters.TryGetValue("GroupBy", out var groupBy))
                    result.GroupBy = groupBy.ToString();
            }
            
            return result;
        }

        private string[] GetRequiredColumns(string tableName)
        {
            return tableName.ToLower() switch
            {
                "production" => new[] { "Timestamp", "ProductId", "Quantity", "Status" },
                "quality" => new[] { "Timestamp", "ProductId", "TestResult", "DefectType" },
                "efficiency" => new[] { "Timestamp", "MachineId", "UtilizationRate", "Throughput" },
                _ => new[] { "Timestamp", "Id" }
            };
        }

        private DataSet CreateResultDataSet(Dictionary<string, object> analysisResults)
        {
            var resultDataSet = new DataSet("AnalysisResults");
            
            foreach (var kvp in analysisResults)
            {
                var table = new DataTable(kvp.Key);
                
                // 根据分析结果类型创建相应的表结构
                switch (kvp.Key)
                {
                    case "Production":
                        CreateProductionResultTable(table, kvp.Value);
                        break;
                    case "Quality":
                        CreateQualityResultTable(table, kvp.Value);
                        break;
                    case "Efficiency":
                        CreateEfficiencyResultTable(table, kvp.Value);
                        break;
                    case "Trend":
                        CreateTrendResultTable(table, kvp.Value);
                        break;
                }
                
                resultDataSet.Tables.Add(table);
            }
            
            return resultDataSet;
        }

        private void CreateProductionResultTable(DataTable table, object analysisResult)
        {
            table.Columns.Add("Period", typeof(string));
            table.Columns.Add("TotalQuantity", typeof(int));
            table.Columns.Add("SuccessRate", typeof(double));
            table.Columns.Add("AverageTime", typeof(double));
            
            // 填充数据（这里是示例数据）
            var row = table.NewRow();
            row["Period"] = "Today";
            row["TotalQuantity"] = 1000;
            row["SuccessRate"] = 98.5;
            row["AverageTime"] = 45.2;
            table.Rows.Add(row);
        }

        private void CreateQualityResultTable(DataTable table, object analysisResult)
        {
            table.Columns.Add("DefectType", typeof(string));
            table.Columns.Add("Count", typeof(int));
            table.Columns.Add("Percentage", typeof(double));
            
            // 填充示例数据
            table.Rows.Add("表面缺陷", 15, 1.5);
            table.Rows.Add("尺寸偏差", 8, 0.8);
            table.Rows.Add("其他", 2, 0.2);
        }

        private void CreateEfficiencyResultTable(DataTable table, object analysisResult)
        {
            table.Columns.Add("MachineId", typeof(string));
            table.Columns.Add("UtilizationRate", typeof(double));
            table.Columns.Add("Throughput", typeof(double));
            table.Columns.Add("DowntimeHours", typeof(double));
            
            // 填充示例数据
            table.Rows.Add("Laser001", 85.5, 120.5, 2.5);
            table.Rows.Add("Laser002", 92.1, 135.2, 1.2);
        }

        private void CreateTrendResultTable(DataTable table, object analysisResult)
        {
            table.Columns.Add("Date", typeof(DateTime));
            table.Columns.Add("Metric", typeof(string));
            table.Columns.Add("Value", typeof(double));
            table.Columns.Add("Trend", typeof(string));
            
            // 填充示例数据
            table.Rows.Add(DateTime.Today, "Production", 1000, "Increasing");
            table.Rows.Add(DateTime.Today, "Quality", 98.5, "Stable");
        }

        private void OnStatusChanged(PluginStatus oldStatus, PluginStatus newStatus)
        {
            StatusChanged?.Invoke(this, new PluginStatusChangedEventArgs(oldStatus, newStatus));
        }

        private void OnErrorOccurred(string message, Exception exception = null)
        {
            ErrorOccurred?.Invoke(this, new PluginErrorEventArgs(this, message, exception));
        }

        #endregion
    }

    #region 辅助类

    public class DataAnalysisConfiguration
    {
        public string DatabaseConnectionString { get; set; }
        public string ReportOutputPath { get; set; }
        public int MaxRecordsPerAnalysis { get; set; } = 100000;
        public bool EnableCaching { get; set; } = true;
        public TimeSpan CacheExpiration { get; set; } = TimeSpan.FromHours(1);
    }

    public class DataAnalysisParameters
    {
        public string[] AnalyzerTypes { get; set; } = { "Production" };
        public string[] ReportFormats { get; set; } = { "Excel" };
        public bool GenerateReports { get; set; } = true;
        public string GroupBy { get; set; } = "Day";
        public DateTime StartDate { get; set; } = DateTime.Today.AddDays(-30);
        public DateTime EndDate { get; set; } = DateTime.Today;
        public bool IncludeCharts { get; set; } = true;
        public bool IncludeTrends { get; set; } = true;
    }

    #endregion
}
