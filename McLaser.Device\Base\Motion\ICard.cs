using System.Collections.Generic;
using McLaser.Device;

namespace McLaser.Devices
{
    /// <summary>
    /// 控制卡接口
    /// 定义运动控制卡必须实现的功能
    /// </summary>
    public interface ICard : IDevice
    {
        /// <summary>
        /// 使能轴
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <param name="isEnable">是否使能</param>
        /// <returns>操作是否成功</returns>
        bool Enable(int index, bool isEnable);

        /// <summary>
        /// 检查轴是否使能
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <param name="bIsEnable">输出使能状态</param>
        /// <returns>操作是否成功</returns>
        bool IsEnable(int index, ref bool bIsEnable);

        /// <summary>
        /// 轴回零
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <param name="vel">回零速度</param>
        /// <param name="timeout">超时时间(毫秒)</param>
        /// <returns>操作是否成功</returns>
        bool Home(int index, double vel = 100, int timeout = 10000);

        /// <summary>
        /// 检查轴是否已回零
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <param name="bIsHome">输出回零状态</param>
        /// <returns>操作是否成功</returns>
        bool IsHome(int index, ref bool bIsHome);

        /// <summary>
        /// 轴运动
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <param name="pos">目标位置</param>
        /// <param name="isAbs">是否绝对运动</param>
        /// <param name="timeout">超时时间(毫秒)</param>
        /// <returns>操作是否成功</returns>
        bool Move(int index, double pos, bool isAbs = true, int timeout = 5000);

        /// <summary>
        /// 检查轴是否正在运动
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <returns>是否正在运动</returns>
        bool IsMove(int index);

        /// <summary>
        /// 多轴同时运动
        /// </summary>
        /// <param name="listIndex">轴索引列表</param>
        /// <param name="listPos">目标位置列表</param>
        /// <returns>操作是否成功</returns>
        bool MoveMult(List<int> listIndex, List<double> listPos);

        /// <summary>
        /// Jog运动
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <param name="isPlus">是否正向</param>
        /// <returns>操作是否成功</returns>
        bool Jog(int index, bool isPlus);

        /// <summary>
        /// 停止轴运动
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <returns>操作是否成功</returns>
        bool Stop(int index);

        /// <summary>
        /// 检查轴是否到位
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <param name="pos">目标位置</param>
        /// <returns>是否到位</returns>
        bool IsPos(int index, double pos);

        /// <summary>
        /// 获取轴当前位置
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <param name="pos">输出当前位置</param>
        /// <returns>操作是否成功</returns>
        bool GetPos(int index, ref double pos);

        /// <summary>
        /// 获取轴状态
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <param name="status">输出轴状态</param>
        /// <returns>操作是否成功</returns>
        bool GetStatus(int index, ref AxisStatus status);

        /// <summary>
        /// 设置轴零点
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <returns>操作是否成功</returns>
        bool SetZero(int index);

        /// <summary>
        /// 清除轴报警
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <returns>操作是否成功</returns>
        bool Clear(int index);

        /// <summary>
        /// 设置轴限位
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <returns>操作是否成功</returns>
        bool SetLimit(int index);

        /// <summary>
        /// 获取数字输入
        /// </summary>
        /// <param name="index">输入索引</param>
        /// <param name="isValue">输出输入值</param>
        /// <returns>操作是否成功</returns>
        bool GetDi(int index, ref bool isValue);

        /// <summary>
        /// 获取数字输出
        /// </summary>
        /// <param name="index">输出索引</param>
        /// <param name="isValue">输出输出值</param>
        /// <returns>操作是否成功</returns>
        bool GetDo(int index, ref bool isValue);

        /// <summary>
        /// 设置数字输出
        /// </summary>
        /// <param name="index">输出索引</param>
        /// <param name="isValue">要设置的值</param>
        /// <returns>操作是否成功</returns>
        bool SetDo(int index, bool isValue);

        /// <summary>
        /// 激光开启
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <returns>操作是否成功</returns>
        bool LaserOn(int index);

        /// <summary>
        /// 激光关闭
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <returns>操作是否成功</returns>
        bool LaserOff(int index);

        /// <summary>
        /// 检查激光是否开启
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <returns>激光是否开启</returns>
        bool IsLaserOn(int index);
    }
}
