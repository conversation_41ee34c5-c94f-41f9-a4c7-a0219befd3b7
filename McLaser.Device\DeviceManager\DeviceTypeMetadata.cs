#nullable enable
using System;
using System.Collections.Generic;

namespace McLaser.Device
{
    /// <summary>
    /// 设备类型元数据
    /// 包含设备类型的详细信息和配置
    /// </summary>
    public class DeviceTypeMetadata
    {
        /// <summary>
        /// 设备类型名称
        /// </summary>
        public string TypeName { get; set; } = string.Empty;

        /// <summary>
        /// 显示名称
        /// </summary>
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// 设备类别
        /// </summary>
        public DeviceCategory Category { get; set; } = DeviceCategory.None;

        /// <summary>
        /// 设备类型（用于DeviceBase）
        /// </summary>
        public DevicesType DeviceType { get; set; } = DevicesType.Unknown;

        /// <summary>
        /// 描述信息
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 制造商
        /// </summary>
        public string Manufacturer { get; set; } = string.Empty;

        /// <summary>
        /// 版本信息
        /// </summary>
        public string Version { get; set; } = string.Empty;

        /// <summary>
        /// 图标路径或资源键
        /// </summary>
        public string Icon { get; set; } = string.Empty;

        /// <summary>
        /// 是否支持自动发现
        /// </summary>
        public bool SupportsAutoDiscovery { get; set; } = false;

        /// <summary>
        /// 是否需要驱动程序
        /// </summary>
        public bool RequiresDriver { get; set; } = false;

        /// <summary>
        /// 支持的连接类型
        /// </summary>
        public List<string> SupportedConnections { get; set; } = new List<string>();

        /// <summary>
        /// 默认配置参数
        /// </summary>
        public Dictionary<string, object> DefaultConfiguration { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 配置参数定义
        /// </summary>
        public List<DeviceParameterDefinition> ParameterDefinitions { get; set; } = new List<DeviceParameterDefinition>();

        /// <summary>
        /// 实际的.NET类型
        /// </summary>
        public Type? ActualType { get; set; }

        /// <summary>
        /// 程序集名称
        /// </summary>
        public string AssemblyName { get; set; } = string.Empty;

        /// <summary>
        /// 是否为内置类型
        /// </summary>
        public bool IsBuiltIn { get; set; } = false;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 构造函数
        /// </summary>
        public DeviceTypeMetadata()
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="typeName">类型名称</param>
        /// <param name="displayName">显示名称</param>
        /// <param name="category">设备类别</param>
        public DeviceTypeMetadata(string typeName, string displayName, DeviceCategory category)
        {
            TypeName = typeName;
            DisplayName = displayName;
            Category = category;
        }

        /// <summary>
        /// 从类型创建元数据
        /// </summary>
        /// <param name="type">设备类型</param>
        /// <returns>设备类型元数据</returns>
        public static DeviceTypeMetadata FromType(Type type)
        {
            var metadata = new DeviceTypeMetadata
            {
                TypeName = type.Name,
                DisplayName = GetDisplayNameFromType(type),
                ActualType = type,
                AssemblyName = type.Assembly.GetName().Name ?? string.Empty,
                IsBuiltIn = IsBuiltInType(type)
            };

            // 尝试从类型推断设备类别
            metadata.Category = InferCategoryFromType(type);
            metadata.DeviceType = InferDeviceTypeFromCategory(metadata.Category);

            // 设置默认描述
            metadata.Description = $"{metadata.DisplayName} - 来自程序集 {metadata.AssemblyName}";

            // 设置默认连接类型
            SetDefaultConnections(metadata);

            return metadata;
        }

        /// <summary>
        /// 从类型名称获取显示名称
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>显示名称</returns>
        private static string GetDisplayNameFromType(Type type)
        {
            var name = type.Name;
            
            // 移除常见的后缀
            if (name.EndsWith("Device"))
                name = name.Substring(0, name.Length - 6);
            if (name.EndsWith("Driver"))
                name = name.Substring(0, name.Length - 6);

            // 添加空格分隔大写字母
            var result = string.Empty;
            for (int i = 0; i < name.Length; i++)
            {
                if (i > 0 && char.IsUpper(name[i]))
                    result += " ";
                result += name[i];
            }

            return result;
        }

        /// <summary>
        /// 从类型推断设备类别
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>设备类别</returns>
        private static DeviceCategory InferCategoryFromType(Type type)
        {
            var name = type.Name.ToLower();

            if (name.Contains("camera") || name.Contains("cam"))
                return DeviceCategory.Camera;
            if (name.Contains("motion") || name.Contains("card") || name.Contains("axis"))
                return DeviceCategory.MotionController;
            if (name.Contains("laser"))
                return DeviceCategory.Laser;
            if (name.Contains("sensor"))
                return DeviceCategory.Sensor;
            if (name.Contains("barcode") || name.Contains("scanner"))
                return DeviceCategory.BarcodeReader;
            if (name.Contains("network") || name.Contains("ethernet") || name.Contains("tcp"))
                return DeviceCategory.NetworkDevice;
            if (name.Contains("serial") || name.Contains("com") || name.Contains("rs232") || name.Contains("rs485"))
                return DeviceCategory.SerialDevice;

            return DeviceCategory.None;
        }

        /// <summary>
        /// 从设备类别推断设备类型
        /// </summary>
        /// <param name="category">设备类别</param>
        /// <returns>设备类型</returns>
        private static DevicesType InferDeviceTypeFromCategory(DeviceCategory category)
        {
            return category switch
            {
                DeviceCategory.Camera => DevicesType.Camera,
                DeviceCategory.MotionController => DevicesType.MotionCard,
                DeviceCategory.Laser => DevicesType.Laser,
                DeviceCategory.Sensor => DevicesType.Sensor,
                DeviceCategory.BarcodeReader => DevicesType.BarcodeReader,
                DeviceCategory.NetworkDevice => DevicesType.NetworkDevice,
                DeviceCategory.SerialDevice => DevicesType.SerialDevice,
                _ => DevicesType.Unknown
            };
        }

        /// <summary>
        /// 判断是否为内置类型
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>是否为内置类型</returns>
        private static bool IsBuiltInType(Type type)
        {
            var assemblyName = type.Assembly.GetName().Name;
            return assemblyName != null && assemblyName.StartsWith("McLaser.Device");
        }

        /// <summary>
        /// 设置默认连接类型
        /// </summary>
        /// <param name="metadata">元数据</param>
        private static void SetDefaultConnections(DeviceTypeMetadata metadata)
        {
            switch (metadata.Category)
            {
                case DeviceCategory.Camera:
                    metadata.SupportedConnections.AddRange(new[] { "USB", "GigE", "Camera Link" });
                    break;
                case DeviceCategory.MotionController:
                    metadata.SupportedConnections.AddRange(new[] { "PCI", "PCIe", "Ethernet", "USB" });
                    break;
                case DeviceCategory.Laser:
                    metadata.SupportedConnections.AddRange(new[] { "Serial", "Ethernet", "USB" });
                    break;
                case DeviceCategory.Sensor:
                    metadata.SupportedConnections.AddRange(new[] { "Analog", "Digital", "Serial", "I2C" });
                    break;
                case DeviceCategory.NetworkDevice:
                    metadata.SupportedConnections.AddRange(new[] { "Ethernet", "WiFi" });
                    break;
                case DeviceCategory.SerialDevice:
                    metadata.SupportedConnections.AddRange(new[] { "RS232", "RS485", "USB" });
                    break;
                default:
                    metadata.SupportedConnections.Add("Unknown");
                    break;
            }
        }

        /// <summary>
        /// 转换为字符串
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"{DisplayName} ({TypeName}) - {Category}";
        }
    }

    /// <summary>
    /// 设备参数定义
    /// </summary>
    public class DeviceParameterDefinition
    {
        /// <summary>
        /// 参数名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 显示名称
        /// </summary>
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// 参数类型
        /// </summary>
        public Type ParameterType { get; set; } = typeof(string);

        /// <summary>
        /// 默认值
        /// </summary>
        public object? DefaultValue { get; set; }

        /// <summary>
        /// 是否必需
        /// </summary>
        public bool IsRequired { get; set; } = false;

        /// <summary>
        /// 描述信息
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 可选值列表（用于枚举类型）
        /// </summary>
        public List<object> PossibleValues { get; set; } = new List<object>();

        /// <summary>
        /// 最小值（用于数值类型）
        /// </summary>
        public object? MinValue { get; set; }

        /// <summary>
        /// 最大值（用于数值类型）
        /// </summary>
        public object? MaxValue { get; set; }
    }
}
