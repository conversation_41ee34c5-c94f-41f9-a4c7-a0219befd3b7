using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace McLaser.Core.Framework.Security
{
    /// <summary>
    /// 用户服务接口
    /// 提供用户管理和认证功能
    /// </summary>
    public interface IUserService
    {
        #region 用户管理

        /// <summary>
        /// 当前登录用户
        /// </summary>
        User? CurrentUser { get; }

        /// <summary>
        /// 是否已登录
        /// </summary>
        bool IsAuthenticated { get; }

        /// <summary>
        /// 创建用户
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <param name="password">密码</param>
        /// <returns>创建结果</returns>
        Task<UserOperationResult> CreateUserAsync(User user, string password);

        /// <summary>
        /// 更新用户信息
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <returns>更新结果</returns>
        Task<UserOperationResult> UpdateUserAsync(User user);

        /// <summary>
        /// 删除用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>删除结果</returns>
        Task<UserOperationResult> DeleteUserAsync(string userId);

        /// <summary>
        /// 根据ID获取用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户信息</returns>
        Task<User?> GetUserByIdAsync(string userId);

        /// <summary>
        /// 根据用户名获取用户
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>用户信息</returns>
        Task<User?> GetUserByUsernameAsync(string username);

        /// <summary>
        /// 获取所有用户
        /// </summary>
        /// <returns>用户列表</returns>
        Task<IEnumerable<User>> GetAllUsersAsync();

        /// <summary>
        /// 搜索用户
        /// </summary>
        /// <param name="searchTerm">搜索条件</param>
        /// <returns>用户列表</returns>
        Task<IEnumerable<User>> SearchUsersAsync(string searchTerm);

        #endregion

        #region 认证功能

        /// <summary>
        /// 用户登录
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        /// <returns>登录结果</returns>
        Task<AuthenticationResult> LoginAsync(string username, string password);

        /// <summary>
        /// 用户登出
        /// </summary>
        /// <returns>登出结果</returns>
        Task<bool> LogoutAsync();

        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="oldPassword">旧密码</param>
        /// <param name="newPassword">新密码</param>
        /// <returns>修改结果</returns>
        Task<UserOperationResult> ChangePasswordAsync(string userId, string oldPassword, string newPassword);

        /// <summary>
        /// 重置密码
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="newPassword">新密码</param>
        /// <returns>重置结果</returns>
        Task<UserOperationResult> ResetPasswordAsync(string userId, string newPassword);

        /// <summary>
        /// 验证密码
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="password">密码</param>
        /// <returns>验证结果</returns>
        Task<bool> ValidatePasswordAsync(string userId, string password);

        #endregion

        #region 会话管理

        /// <summary>
        /// 获取当前会话信息
        /// </summary>
        /// <returns>会话信息</returns>
        UserSession? GetCurrentSession();

        /// <summary>
        /// 刷新会话
        /// </summary>
        /// <returns>刷新结果</returns>
        Task<bool> RefreshSessionAsync();

        /// <summary>
        /// 检查会话是否有效
        /// </summary>
        /// <returns>是否有效</returns>
        bool IsSessionValid();

        /// <summary>
        /// 获取会话剩余时间
        /// </summary>
        /// <returns>剩余时间</returns>
        TimeSpan GetSessionRemainingTime();

        #endregion

        #region 用户状态

        /// <summary>
        /// 启用用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>操作结果</returns>
        Task<UserOperationResult> EnableUserAsync(string userId);

        /// <summary>
        /// 禁用用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>操作结果</returns>
        Task<UserOperationResult> DisableUserAsync(string userId);

        /// <summary>
        /// 锁定用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="lockoutEnd">锁定结束时间</param>
        /// <returns>操作结果</returns>
        Task<UserOperationResult> LockUserAsync(string userId, DateTime? lockoutEnd = null);

        /// <summary>
        /// 解锁用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>操作结果</returns>
        Task<UserOperationResult> UnlockUserAsync(string userId);

        #endregion

        #region 事件

        /// <summary>
        /// 用户登录事件
        /// </summary>
        event EventHandler<UserLoginEventArgs>? UserLoggedIn;

        /// <summary>
        /// 用户登出事件
        /// </summary>
        event EventHandler<UserLogoutEventArgs>? UserLoggedOut;

        /// <summary>
        /// 用户创建事件
        /// </summary>
        event EventHandler<UserCreatedEventArgs>? UserCreated;

        /// <summary>
        /// 用户更新事件
        /// </summary>
        event EventHandler<UserUpdatedEventArgs>? UserUpdated;

        /// <summary>
        /// 用户删除事件
        /// </summary>
        event EventHandler<UserDeletedEventArgs>? UserDeleted;

        /// <summary>
        /// 密码变更事件
        /// </summary>
        event EventHandler<PasswordChangedEventArgs>? PasswordChanged;

        #endregion
    }

    #region 结果类

    /// <summary>
    /// 用户操作结果
    /// </summary>
    public class UserOperationResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public string? ErrorCode { get; set; }

        /// <summary>
        /// 附加数据
        /// </summary>
        public object? Data { get; set; }

        /// <summary>
        /// 创建成功结果
        /// </summary>
        public static UserOperationResult CreateSuccess(object? data = null)
        {
            return new UserOperationResult { Success = true, Data = data };
        }

        /// <summary>
        /// 创建失败结果
        /// </summary>
        public static UserOperationResult CreateFailure(string errorMessage, string? errorCode = null)
        {
            return new UserOperationResult 
            { 
                Success = false, 
                ErrorMessage = errorMessage, 
                ErrorCode = errorCode 
            };
        }
    }

    /// <summary>
    /// 认证结果
    /// </summary>
    public class AuthenticationResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 用户信息
        /// </summary>
        public User? User { get; set; }

        /// <summary>
        /// 会话信息
        /// </summary>
        public UserSession? Session { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public string? ErrorCode { get; set; }

        /// <summary>
        /// 创建成功结果
        /// </summary>
        public static AuthenticationResult CreateSuccess(User user, UserSession session)
        {
            return new AuthenticationResult 
            { 
                Success = true, 
                User = user, 
                Session = session 
            };
        }

        /// <summary>
        /// 创建失败结果
        /// </summary>
        public static AuthenticationResult CreateFailure(string errorMessage, string? errorCode = null)
        {
            return new AuthenticationResult 
            { 
                Success = false, 
                ErrorMessage = errorMessage, 
                ErrorCode = errorCode 
            };
        }
    }

    #endregion
}
