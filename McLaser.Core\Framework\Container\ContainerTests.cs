using System;

namespace McLaser.Core.Framework.Container
{
    /// <summary>
    /// 容器功能测试类
    /// 用于验证统一DI容器的基本功能
    /// </summary>
    public static class ContainerTests
    {
        /// <summary>
        /// 运行基本容器测试
        /// </summary>
        /// <returns>测试结果</returns>
        public static ContainerTestResult RunBasicTests()
        {
            var result = new ContainerTestResult();

            try
            {
                // 测试1: 默认容器创建
                result.AddTest("默认容器创建", () =>
                {
                    ContainerManager.UseDefaultContainer();
                    var container = ContainerManager.Current;
                    return container != null;
                });

                // 测试2: 单例注册和解析
                result.AddTest("单例注册和解析", () =>
                {
                    var container = ContainerManager.Current;
                    var testService = new TestService();
                    container.RegisterSingleton<ITestService>(testService);
                    
                    var resolved = container.Resolve<ITestService>();
                    return resolved == testService;
                });

                // 测试3: 瞬态注册和解析
                result.AddTest("瞬态注册和解析", () =>
                {
                    var container = ContainerManager.Current;
                    container.RegisterTransient<ITestService, TestService>();
                    
                    var resolved1 = container.Resolve<ITestService>();
                    var resolved2 = container.Resolve<ITestService>();
                    return resolved1 != null && resolved2 != null && resolved1 != resolved2;
                });

                // 测试4: 工厂注册和解析
                result.AddTest("工厂注册和解析", () =>
                {
                    var container = ContainerManager.Current;
                    container.RegisterFactory<ITestService>(c => new TestService { Name = "Factory" });
                    
                    var resolved = container.Resolve<ITestService>();
                    return resolved != null && ((TestService)resolved).Name == "Factory";
                });

                // 测试5: 带键服务注册和解析
                result.AddTest("带键服务注册和解析", () =>
                {
                    var container = ContainerManager.Current;
                    var keyedService = new TestService { Name = "Keyed" };
                    container.RegisterKeyed<ITestService>("test-key", keyedService);
                    
                    var resolved = container.ResolveKeyed<ITestService>("test-key");
                    return resolved == keyedService;
                });

                // 测试6: 服务存在性检查
                result.AddTest("服务存在性检查", () =>
                {
                    var container = ContainerManager.Current;
                    return container.IsRegistered<ITestService>() && 
                           container.IsKeyedRegistered<ITestService>("test-key");
                });

                // 测试7: 容器统计信息
                result.AddTest("容器统计信息", () =>
                {
                    var stats = ContainerManager.GetStatistics();
                    return stats.TotalRegistrations > 0 && stats.TotalResolutions > 0;
                });

                // 测试8: 容器验证
                result.AddTest("容器验证", () =>
                {
                    var validationResult = ContainerManager.ValidateConfiguration();
                    return validationResult != null;
                });

                result.IsSuccess = result.FailedTests.Count == 0;
            }
            catch (Exception ex)
            {
                result.AddError($"测试执行异常: {ex.Message}");
                result.IsSuccess = false;
            }

            return result;
        }

        /// <summary>
        /// 运行MEF容器测试
        /// </summary>
        /// <returns>测试结果</returns>
        public static ContainerTestResult RunMefTests()
        {
            var result = new ContainerTestResult();

            try
            {
                // 测试MEF容器创建
                result.AddTest("MEF容器创建", () =>
                {
                    ContainerManager.UseMefContainer();
                    var container = ContainerManager.Current;
                    return container != null && ContainerManager.CurrentType == ContainerType.Mef;
                });

                // 测试MEF容器基本功能
                result.AddTest("MEF容器基本功能", () =>
                {
                    var container = ContainerManager.Current;
                    var testService = new TestService();
                    container.RegisterSingleton<ITestService>(testService);
                    
                    var resolved = container.Resolve<ITestService>();
                    return resolved == testService;
                });

                result.IsSuccess = result.FailedTests.Count == 0;
            }
            catch (Exception ex)
            {
                result.AddError($"MEF测试执行异常: {ex.Message}");
                result.IsSuccess = false;
            }

            return result;
        }

        /// <summary>
        /// 运行性能测试
        /// </summary>
        /// <returns>测试结果</returns>
        public static ContainerTestResult RunPerformanceTests()
        {
            var result = new ContainerTestResult();

            try
            {
                var container = ContainerManager.Current;
                container.RegisterTransient<ITestService, TestService>();

                // 性能测试: 解析1000次服务
                var startTime = DateTime.Now;
                for (int i = 0; i < 1000; i++)
                {
                    var service = container.Resolve<ITestService>();
                }
                var elapsed = DateTime.Now - startTime;

                result.AddTest("性能测试(1000次解析)", () => elapsed.TotalMilliseconds < 1000);
                result.AddInfo($"1000次解析耗时: {elapsed.TotalMilliseconds:F2}ms");

                result.IsSuccess = result.FailedTests.Count == 0;
            }
            catch (Exception ex)
            {
                result.AddError($"性能测试执行异常: {ex.Message}");
                result.IsSuccess = false;
            }

            return result;
        }
    }

    /// <summary>
    /// 测试服务接口
    /// </summary>
    public interface ITestService
    {
        string Name { get; set; }
    }

    /// <summary>
    /// 测试服务实现
    /// </summary>
    public class TestService : ITestService
    {
        public string Name { get; set; } = "Default";
    }

    /// <summary>
    /// 容器测试结果
    /// </summary>
    public class ContainerTestResult
    {
        public bool IsSuccess { get; set; }
        public System.Collections.Generic.List<string> PassedTests { get; } = new();
        public System.Collections.Generic.List<string> FailedTests { get; } = new();
        public System.Collections.Generic.List<string> Errors { get; } = new();
        public System.Collections.Generic.List<string> Info { get; } = new();

        public void AddTest(string testName, Func<bool> test)
        {
            try
            {
                if (test())
                {
                    PassedTests.Add(testName);
                }
                else
                {
                    FailedTests.Add(testName);
                }
            }
            catch (Exception ex)
            {
                FailedTests.Add($"{testName}: {ex.Message}");
            }
        }

        public void AddError(string error)
        {
            Errors.Add(error);
        }

        public void AddInfo(string info)
        {
            Info.Add(info);
        }

        public string GetSummary()
        {
            return $"测试结果: {(IsSuccess ? "成功" : "失败")}, " +
                   $"通过: {PassedTests.Count}, 失败: {FailedTests.Count}, 错误: {Errors.Count}";
        }
    }
}
