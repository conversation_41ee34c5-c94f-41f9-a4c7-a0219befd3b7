using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using McLaser.Core.Common;
using McLaser.Core.Framework.Logging;
using McLaser.Core.Plugins;
using McLaser.App.Core;
using McLaser.App.Models;
using LogLevel = McLaser.App.Models.LogLevel;

namespace McLaser.App.ViewModels
{
    /// <summary>
    /// 插件管理演示视图模型
    /// 展示插件系统的完整功能，包括插件加载、卸载、配置、监控等
    /// </summary>
    public partial class PluginDemoViewModel : ObservableObject, IDisposable
    {
        #region 私有字段

        private readonly IPluginManager _pluginManager;
        private readonly ILogger _logger;
        private string _statusMessage = "插件管理器已就绪";
        private bool _isInitialized = false;
        private bool _isLoading = false;
        private PluginDisplayInfo? _selectedPlugin;
        private string _pluginDirectory = "Plugins";
        private string _logMessages = "";

        #endregion

        #region 属性

        /// <summary>
        /// 可用插件列表
        /// </summary>
        public ObservableCollection<PluginDisplayInfo> AvailablePlugins { get; } = new();

        /// <summary>
        /// 已加载插件列表
        /// </summary>
        public ObservableCollection<PluginDisplayInfo> LoadedPlugins { get; } = new();

        /// <summary>
        /// 插件统计信息
        /// </summary>
        public ObservableCollection<StatisticItem> PluginStatistics { get; } = new();

        /// <summary>
        /// 插件日志消息
        /// </summary>
        public ObservableCollection<LogMessage> PluginLogs { get; } = new();

        /// <summary>
        /// 状态消息
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set => Set(ref _statusMessage, value);
        }

        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized
        {
            get => _isInitialized;
            set => Set(ref _isInitialized, value);
        }

        /// <summary>
        /// 是否正在加载
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => Set(ref _isLoading, value);
        }

        /// <summary>
        /// 选中的插件
        /// </summary>
        public PluginDisplayInfo? SelectedPlugin
        {
            get => _selectedPlugin;
            set => Set(ref _selectedPlugin, value);
        }

        /// <summary>
        /// 插件目录
        /// </summary>
        public string PluginDirectory
        {
            get => _pluginDirectory;
            set => Set(ref _pluginDirectory, value);
        }

        /// <summary>
        /// 日志消息文本
        /// </summary>
        public string LogMessages
        {
            get => _logMessages;
            set => Set(ref _logMessages, value);
        }

        #endregion

        #region 命令

        /// <summary>
        /// 初始化插件管理器命令
        /// </summary>
        public ICommand InitializePluginManagerCommand { get; private set; }

        /// <summary>
        /// 扫描插件命令
        /// </summary>
        public ICommand ScanPluginsCommand { get; private set; }

        /// <summary>
        /// 加载插件命令
        /// </summary>
        public ICommand LoadPluginCommand { get; private set; }

        /// <summary>
        /// 卸载插件命令
        /// </summary>
        public ICommand UnloadPluginCommand { get; private set; }

        /// <summary>
        /// 启动插件命令
        /// </summary>
        public ICommand StartPluginCommand { get; private set; }

        /// <summary>
        /// 停止插件命令
        /// </summary>
        public ICommand StopPluginCommand { get; private set; }

        /// <summary>
        /// 刷新统计信息命令
        /// </summary>
        public ICommand RefreshStatisticsCommand { get; private set; }

        /// <summary>
        /// 清空日志命令
        /// </summary>
        public ICommand ClearLogsCommand { get; private set; }

        /// <summary>
        /// 创建示例插件命令
        /// </summary>
        public ICommand CreateSamplePluginsCommand { get; private set; }

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化插件管理演示视图模型
        /// </summary>
        public PluginDemoViewModel()
        {
            // 创建插件管理器实例
            _pluginManager = new PluginManager();
            _logger = new ConsoleLogger("PluginDemo");

            // 初始化命令
            InitializeCommands();

            // 订阅插件管理器事件
            SubscribeToPluginManagerEvents();

            // 添加初始日志
            AddLogMessage("插件管理演示已启动", LogLevel.Info);
        }

        #endregion

        #region 命令实现

        /// <summary>
        /// 初始化命令
        /// </summary>
        private void InitializeCommands()
        {
            InitializePluginManagerCommand = new RelayCommand(async () => await InitializePluginManagerAsync());
            ScanPluginsCommand = new RelayCommand(async () => await ScanPluginsAsync());
            LoadPluginCommand = new RelayCommand(async () => await LoadSelectedPluginAsync(), () => SelectedPlugin != null && !SelectedPlugin.IsLoaded);
            UnloadPluginCommand = new RelayCommand(async () => await UnloadSelectedPluginAsync(), () => SelectedPlugin != null && SelectedPlugin.IsLoaded);
            StartPluginCommand = new RelayCommand(async () => await StartSelectedPluginAsync(), () => SelectedPlugin != null && SelectedPlugin.IsLoaded && SelectedPlugin.Status != PluginStatus.Running);
            StopPluginCommand = new RelayCommand(async () => await StopSelectedPluginAsync(), () => SelectedPlugin != null && SelectedPlugin.Status == PluginStatus.Running);
            RefreshStatisticsCommand = new RelayCommand(RefreshStatistics);
            ClearLogsCommand = new RelayCommand(ClearLogs);
            CreateSamplePluginsCommand = new RelayCommand(async () => await CreateSamplePluginsAsync());
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化插件管理器
        /// </summary>
        private async Task InitializePluginManagerAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在初始化插件管理器...";
                AddLogMessage("开始初始化插件管理器", LogLevel.Info);

                // 确保插件目录存在
                if (!Directory.Exists(PluginDirectory))
                {
                    Directory.CreateDirectory(PluginDirectory);
                    AddLogMessage($"创建插件目录: {PluginDirectory}", LogLevel.Info);
                }

                // 初始化插件管理器
                await _pluginManager.InitializeAsync(this);

                IsInitialized = true;
                StatusMessage = "插件管理器初始化完成";
                AddLogMessage("插件管理器初始化成功", LogLevel.Info);

                // 自动扫描插件
                await ScanPluginsAsync();
            }
            catch (Exception ex)
            {
                StatusMessage = $"初始化失败: {ex.Message}";
                AddLogMessage($"插件管理器初始化失败: {ex.Message}", LogLevel.Error);
                _logger.Error(ex, "初始化插件管理器失败");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 扫描插件
        /// </summary>
        private async Task ScanPluginsAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在扫描插件...";
                AddLogMessage("开始扫描插件", LogLevel.Info);

                // 清空现有列表
                AvailablePlugins.Clear();

                // 扫描插件目录
                var plugins = await _pluginManager.ScanPluginsAsync(PluginDirectory);

                // 添加到可用插件列表
                foreach (var plugin in plugins)
                {
                    var displayInfo = new PluginDisplayInfo
                    {
                        Id = plugin.Metadata.Id,
                        Name = plugin.Metadata.Name,
                        Version = plugin.Metadata.Version?.ToString() ?? "1.0.0",
                        Description = plugin.Metadata.Description,
                        Author = plugin.Metadata.Author,
                        Category = plugin.Metadata.Category ?? "General",
                        FilePath = plugin.FilePath,
                        IsLoaded = _pluginManager.IsPluginLoaded(plugin.Metadata.Id),
                        Status = _pluginManager.IsPluginLoaded(plugin.Metadata.Id) ?
                                _pluginManager.GetPlugin(plugin.Metadata.Id)?.Status ?? PluginStatus.Stopped :
                                PluginStatus.NotLoaded
                    };
                    AvailablePlugins.Add(displayInfo);
                }

                StatusMessage = $"扫描完成，发现 {plugins.Count} 个插件";
                AddLogMessage($"扫描完成，发现 {plugins.Count} 个插件", LogLevel.Info);

                // 刷新统计信息
                RefreshStatistics();
            }
            catch (Exception ex)
            {
                StatusMessage = $"扫描失败: {ex.Message}";
                AddLogMessage($"扫描插件失败: {ex.Message}", LogLevel.Error);
                _logger.Error(ex, "扫描插件失败");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 加载选中的插件
        /// </summary>
        private async Task LoadSelectedPluginAsync()
        {
            if (SelectedPlugin == null) return;

            try
            {
                IsLoading = true;
                StatusMessage = $"正在加载插件: {SelectedPlugin.Name}";
                AddLogMessage($"开始加载插件: {SelectedPlugin.Name}", LogLevel.Info);

                var plugin = await _pluginManager.LoadPluginAsync(SelectedPlugin.FilePath);
                if (plugin != null)
                {
                    SelectedPlugin.IsLoaded = true;
                    SelectedPlugin.Status = plugin.Status;

                    // 添加到已加载列表
                    if (!LoadedPlugins.Any(p => p.Id == SelectedPlugin.Id))
                    {
                        LoadedPlugins.Add(SelectedPlugin);
                    }

                    StatusMessage = $"插件加载成功: {SelectedPlugin.Name}";
                    AddLogMessage($"插件加载成功: {SelectedPlugin.Name}", LogLevel.Info);
                }
                else
                {
                    StatusMessage = $"插件加载失败: {SelectedPlugin.Name}";
                    AddLogMessage($"插件加载失败: {SelectedPlugin.Name}", LogLevel.Error);
                }

                RefreshStatistics();
            }
            catch (Exception ex)
            {
                StatusMessage = $"加载插件失败: {ex.Message}";
                AddLogMessage($"加载插件失败: {ex.Message}", LogLevel.Error);
                _logger.Error(ex, $"加载插件失败: {SelectedPlugin.Name}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 卸载选中的插件
        /// </summary>
        private async Task UnloadSelectedPluginAsync()
        {
            if (SelectedPlugin == null) return;

            try
            {
                IsLoading = true;
                StatusMessage = $"正在卸载插件: {SelectedPlugin.Name}";
                AddLogMessage($"开始卸载插件: {SelectedPlugin.Name}", LogLevel.Info);

                var success = await _pluginManager.UnloadPluginAsync(SelectedPlugin.Id);
                if (success)
                {
                    SelectedPlugin.IsLoaded = false;
                    SelectedPlugin.Status = PluginStatus.NotLoaded;

                    // 从已加载列表移除
                    var loadedPlugin = LoadedPlugins.FirstOrDefault(p => p.Id == SelectedPlugin.Id);
                    if (loadedPlugin != null)
                    {
                        LoadedPlugins.Remove(loadedPlugin);
                    }

                    StatusMessage = $"插件卸载成功: {SelectedPlugin.Name}";
                    AddLogMessage($"插件卸载成功: {SelectedPlugin.Name}", LogLevel.Info);
                }
                else
                {
                    StatusMessage = $"插件卸载失败: {SelectedPlugin.Name}";
                    AddLogMessage($"插件卸载失败: {SelectedPlugin.Name}", LogLevel.Error);
                }

                RefreshStatistics();
            }
            catch (Exception ex)
            {
                StatusMessage = $"卸载插件失败: {ex.Message}";
                AddLogMessage($"卸载插件失败: {ex.Message}", LogLevel.Error);
                _logger.Error(ex, $"卸载插件失败: {SelectedPlugin.Name}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 启动选中的插件
        /// </summary>
        private async Task StartSelectedPluginAsync()
        {
            if (SelectedPlugin == null) return;

            try
            {
                IsLoading = true;
                StatusMessage = $"正在启动插件: {SelectedPlugin.Name}";
                AddLogMessage($"开始启动插件: {SelectedPlugin.Name}", LogLevel.Info);

                var plugin = _pluginManager.GetPlugin(SelectedPlugin.Id);
                if (plugin != null)
                {
                    await plugin.StartAsync();
                    SelectedPlugin.Status = plugin.Status;
                    StatusMessage = $"插件启动成功: {SelectedPlugin.Name}";
                    AddLogMessage($"插件启动成功: {SelectedPlugin.Name}", LogLevel.Info);
                }
                else
                {
                    StatusMessage = $"找不到插件: {SelectedPlugin.Name}";
                    AddLogMessage($"找不到插件: {SelectedPlugin.Name}", LogLevel.Error);
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"启动插件失败: {ex.Message}";
                AddLogMessage($"启动插件失败: {ex.Message}", LogLevel.Error);
                _logger.Error(ex, $"启动插件失败: {SelectedPlugin.Name}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 停止选中的插件
        /// </summary>
        private async Task StopSelectedPluginAsync()
        {
            if (SelectedPlugin == null) return;

            try
            {
                IsLoading = true;
                StatusMessage = $"正在停止插件: {SelectedPlugin.Name}";
                AddLogMessage($"开始停止插件: {SelectedPlugin.Name}", LogLevel.Info);

                var plugin = _pluginManager.GetPlugin(SelectedPlugin.Id);
                if (plugin != null)
                {
                    await plugin.StopAsync();
                    SelectedPlugin.Status = plugin.Status;
                    StatusMessage = $"插件停止成功: {SelectedPlugin.Name}";
                    AddLogMessage($"插件停止成功: {SelectedPlugin.Name}", LogLevel.Info);
                }
                else
                {
                    StatusMessage = $"找不到插件: {SelectedPlugin.Name}";
                    AddLogMessage($"找不到插件: {SelectedPlugin.Name}", LogLevel.Error);
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"停止插件失败: {ex.Message}";
                AddLogMessage($"停止插件失败: {ex.Message}", LogLevel.Error);
                _logger.Error(ex, $"停止插件失败: {SelectedPlugin.Name}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 刷新统计信息
        /// </summary>
        private void RefreshStatistics()
        {
            try
            {
                PluginStatistics.Clear();

                var stats = _pluginManager.GetStatistics();

                PluginStatistics.Add(new StatisticItem { Name = "总插件数", Value = stats.TotalPlugins.ToString() });
                PluginStatistics.Add(new StatisticItem { Name = "已加载插件数", Value = stats.LoadedPlugins.ToString() });
                PluginStatistics.Add(new StatisticItem { Name = "运行中插件数", Value = LoadedPlugins.Count(p => p.Status == PluginStatus.Running).ToString() });
                PluginStatistics.Add(new StatisticItem { Name = "总加载次数", Value = stats.TotalLoads.ToString() });
                PluginStatistics.Add(new StatisticItem { Name = "总卸载次数", Value = stats.TotalUnloads.ToString() });
                PluginStatistics.Add(new StatisticItem { Name = "平均加载时间", Value = $"{stats.AverageLoadTime:F2} ms" });
                PluginStatistics.Add(new StatisticItem { Name = "启动时间", Value = stats.StartTime.ToString("yyyy-MM-dd HH:mm:ss") });
                PluginStatistics.Add(new StatisticItem { Name = "运行时间", Value = $"{(DateTime.Now - stats.StartTime).TotalMinutes:F1} 分钟" });

                AddLogMessage("统计信息已刷新", LogLevel.Debug);
            }
            catch (Exception ex)
            {
                AddLogMessage($"刷新统计信息失败: {ex.Message}", LogLevel.Error);
                _logger.Error(ex, "刷新统计信息失败");
            }
        }

        /// <summary>
        /// 清空日志
        /// </summary>
        private void ClearLogs()
        {
            PluginLogs.Clear();
            LogMessages = "";
            AddLogMessage("日志已清空", LogLevel.Info);
        }

        /// <summary>
        /// 创建示例插件
        /// </summary>
        private async Task CreateSamplePluginsAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在检查示例插件...";
                AddLogMessage("开始检查示例插件", LogLevel.Info);

                // 确保插件目录存在
                if (!Directory.Exists(PluginDirectory))
                {
                    Directory.CreateDirectory(PluginDirectory);
                }

                // 检查示例插件文件是否存在
                var samplePluginPath = Path.Combine(PluginDirectory, "McLaser.Plugins.Samples.dll");
                if (File.Exists(samplePluginPath))
                {
                    AddLogMessage("发现示例插件: McLaser.Plugins.Samples.dll", LogLevel.Info);
                    StatusMessage = "示例插件已存在";
                }
                else
                {
                    AddLogMessage("未找到示例插件文件，请先编译 McLaser.Plugins.Samples 项目", LogLevel.Warn);
                    StatusMessage = "示例插件文件不存在，请先编译项目";
                }

                // 重新扫描插件
                await ScanPluginsAsync();
            }
            catch (Exception ex)
            {
                StatusMessage = $"检查示例插件失败: {ex.Message}";
                AddLogMessage($"检查示例插件失败: {ex.Message}", LogLevel.Error);
                _logger.Error(ex, "检查示例插件失败");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 订阅插件管理器事件
        /// </summary>
        private void SubscribeToPluginManagerEvents()
        {
            _pluginManager.PluginLoaded += OnPluginLoaded;
            _pluginManager.PluginUnloaded += OnPluginUnloaded;
            _pluginManager.PluginStarted += OnPluginStarted;
            _pluginManager.PluginStopped += OnPluginStopped;
            _pluginManager.PluginError += OnPluginError;
        }

        /// <summary>
        /// 插件加载事件处理
        /// </summary>
        private void OnPluginLoaded(object? sender, PluginLoadedEventArgs e)
        {
            AddLogMessage($"插件已加载: {e.Plugin.Name} (耗时: {e.LoadTime}ms)", LogLevel.Info);
            RefreshStatistics();
        }

        /// <summary>
        /// 插件卸载事件处理
        /// </summary>
        private void OnPluginUnloaded(object? sender, PluginUnloadedEventArgs e)
        {
            AddLogMessage($"插件已卸载: {e.PluginId}", LogLevel.Info);
            RefreshStatistics();
        }

        /// <summary>
        /// 插件启动事件处理
        /// </summary>
        private void OnPluginStarted(object? sender, PluginStartedEventArgs e)
        {
            AddLogMessage($"插件已启动: {e.Plugin.Name}", LogLevel.Info);

            // 更新UI中的插件状态
            var plugin = AvailablePlugins.FirstOrDefault(p => p.Id == e.Plugin.Id);
            if (plugin != null)
            {
                plugin.Status = PluginStatus.Running;
            }
        }

        /// <summary>
        /// 插件停止事件处理
        /// </summary>
        private void OnPluginStopped(object? sender, PluginStoppedEventArgs e)
        {
            AddLogMessage($"插件已停止: {e.Plugin.Name}", LogLevel.Info);

            // 更新UI中的插件状态
            var plugin = AvailablePlugins.FirstOrDefault(p => p.Id == e.Plugin.Id);
            if (plugin != null)
            {
                plugin.Status = PluginStatus.Stopped;
            }
        }

        /// <summary>
        /// 插件错误事件处理
        /// </summary>
        private void OnPluginError(object? sender, PluginErrorEventArgs e)
        {
            AddLogMessage($"插件错误: {e.Plugin?.Id ?? "Unknown"} - {e.ErrorMessage}", LogLevel.Error);
        }

        /// <summary>
        /// 添加日志消息
        /// </summary>
        private void AddLogMessage(string message, LogLevel level)
        {
            var logMessage = new LogMessage
            {
                Timestamp = DateTime.Now,
                Level = level,
                Message = message,
                Source = "PluginDemo"
            };

            PluginLogs.Add(logMessage);

            // 更新日志文本
            LogMessages += $"[{logMessage.Timestamp:HH:mm:ss}] [{level}] {message}\n";

            // 限制日志数量
            if (PluginLogs.Count > 1000)
            {
                PluginLogs.RemoveAt(0);
            }

            // 记录到控制台
            _logger.Info($"[PluginDemo] {message}");
        }

        #endregion

        #region IDisposable 实现

        private bool _disposed = false;

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否正在释放</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    try
                    {
                        // 取消订阅事件
                        if (_pluginManager != null)
                        {
                            _pluginManager.PluginLoaded -= OnPluginLoaded;
                            _pluginManager.PluginUnloaded -= OnPluginUnloaded;
                            _pluginManager.PluginStarted -= OnPluginStarted;
                            _pluginManager.PluginStopped -= OnPluginStopped;
                            _pluginManager.PluginError -= OnPluginError;
                        }

                        // 释放插件管理器
                        _pluginManager?.Dispose();

                        AddLogMessage("插件管理演示已关闭", LogLevel.Info);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"释放资源时发生错误: {ex.Message}");
                    }
                }
                _disposed = true;
            }
        }

        #endregion
    }
}
