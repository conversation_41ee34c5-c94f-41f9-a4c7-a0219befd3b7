using System;

namespace McLaser.Core.Framework.Security
{
    #region 用户事件参数

    /// <summary>
    /// 用户登录事件参数
    /// </summary>
    public class UserLoginEventArgs : EventArgs
    {
        /// <summary>
        /// 用户信息
        /// </summary>
        public User User { get; }

        /// <summary>
        /// 会话信息
        /// </summary>
        public UserSession Session { get; }

        /// <summary>
        /// 登录时间
        /// </summary>
        public DateTime LoginTime { get; }

        /// <summary>
        /// 客户端IP地址
        /// </summary>
        public string? ClientIpAddress { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public UserLoginEventArgs(User user, UserSession session, DateTime loginTime, string? clientIpAddress = null)
        {
            User = user;
            Session = session;
            LoginTime = loginTime;
            ClientIpAddress = clientIpAddress;
        }
    }

    /// <summary>
    /// 用户登出事件参数
    /// </summary>
    public class UserLogoutEventArgs : EventArgs
    {
        /// <summary>
        /// 用户信息
        /// </summary>
        public User User { get; }

        /// <summary>
        /// 会话信息
        /// </summary>
        public UserSession Session { get; }

        /// <summary>
        /// 登出时间
        /// </summary>
        public DateTime LogoutTime { get; }

        /// <summary>
        /// 登出原因
        /// </summary>
        public string? Reason { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public UserLogoutEventArgs(User user, UserSession session, DateTime logoutTime, string? reason = null)
        {
            User = user;
            Session = session;
            LogoutTime = logoutTime;
            Reason = reason;
        }
    }

    /// <summary>
    /// 用户创建事件参数
    /// </summary>
    public class UserCreatedEventArgs : EventArgs
    {
        /// <summary>
        /// 创建的用户
        /// </summary>
        public User User { get; }

        /// <summary>
        /// 创建者用户ID
        /// </summary>
        public string? CreatedBy { get; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public UserCreatedEventArgs(User user, string? createdBy = null, DateTime? createdAt = null)
        {
            User = user;
            CreatedBy = createdBy;
            CreatedAt = createdAt ?? DateTime.UtcNow;
        }
    }

    /// <summary>
    /// 用户更新事件参数
    /// </summary>
    public class UserUpdatedEventArgs : EventArgs
    {
        /// <summary>
        /// 更新的用户
        /// </summary>
        public User User { get; }

        /// <summary>
        /// 更新者用户ID
        /// </summary>
        public string? UpdatedBy { get; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; }

        /// <summary>
        /// 更新的字段
        /// </summary>
        public string[]? UpdatedFields { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public UserUpdatedEventArgs(User user, string? updatedBy = null, DateTime? updatedAt = null, string[]? updatedFields = null)
        {
            User = user;
            UpdatedBy = updatedBy;
            UpdatedAt = updatedAt ?? DateTime.UtcNow;
            UpdatedFields = updatedFields;
        }
    }

    /// <summary>
    /// 用户删除事件参数
    /// </summary>
    public class UserDeletedEventArgs : EventArgs
    {
        /// <summary>
        /// 删除的用户ID
        /// </summary>
        public string UserId { get; }

        /// <summary>
        /// 删除的用户名
        /// </summary>
        public string Username { get; }

        /// <summary>
        /// 删除者用户ID
        /// </summary>
        public string? DeletedBy { get; }

        /// <summary>
        /// 删除时间
        /// </summary>
        public DateTime DeletedAt { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public UserDeletedEventArgs(string userId, string username, string? deletedBy = null, DateTime? deletedAt = null)
        {
            UserId = userId;
            Username = username;
            DeletedBy = deletedBy;
            DeletedAt = deletedAt ?? DateTime.UtcNow;
        }
    }

    /// <summary>
    /// 密码变更事件参数
    /// </summary>
    public class PasswordChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; }

        /// <summary>
        /// 变更者用户ID
        /// </summary>
        public string? ChangedBy { get; }

        /// <summary>
        /// 变更时间
        /// </summary>
        public DateTime ChangedAt { get; }

        /// <summary>
        /// 是否为重置密码
        /// </summary>
        public bool IsReset { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public PasswordChangedEventArgs(string userId, string? changedBy = null, DateTime? changedAt = null, bool isReset = false)
        {
            UserId = userId;
            ChangedBy = changedBy;
            ChangedAt = changedAt ?? DateTime.UtcNow;
            IsReset = isReset;
        }
    }

    #endregion

    #region 角色事件参数

    /// <summary>
    /// 角色创建事件参数
    /// </summary>
    public class RoleCreatedEventArgs : EventArgs
    {
        /// <summary>
        /// 创建的角色
        /// </summary>
        public Role Role { get; }

        /// <summary>
        /// 创建者用户ID
        /// </summary>
        public string? CreatedBy { get; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public RoleCreatedEventArgs(Role role, string? createdBy = null, DateTime? createdAt = null)
        {
            Role = role;
            CreatedBy = createdBy;
            CreatedAt = createdAt ?? DateTime.UtcNow;
        }
    }

    /// <summary>
    /// 角色更新事件参数
    /// </summary>
    public class RoleUpdatedEventArgs : EventArgs
    {
        /// <summary>
        /// 更新的角色
        /// </summary>
        public Role Role { get; }

        /// <summary>
        /// 更新者用户ID
        /// </summary>
        public string? UpdatedBy { get; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public RoleUpdatedEventArgs(Role role, string? updatedBy = null, DateTime? updatedAt = null)
        {
            Role = role;
            UpdatedBy = updatedBy;
            UpdatedAt = updatedAt ?? DateTime.UtcNow;
        }
    }

    /// <summary>
    /// 角色删除事件参数
    /// </summary>
    public class RoleDeletedEventArgs : EventArgs
    {
        /// <summary>
        /// 删除的角色ID
        /// </summary>
        public string RoleId { get; }

        /// <summary>
        /// 删除的角色名称
        /// </summary>
        public string RoleName { get; }

        /// <summary>
        /// 删除者用户ID
        /// </summary>
        public string? DeletedBy { get; }

        /// <summary>
        /// 删除时间
        /// </summary>
        public DateTime DeletedAt { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public RoleDeletedEventArgs(string roleId, string roleName, string? deletedBy = null, DateTime? deletedAt = null)
        {
            RoleId = roleId;
            RoleName = roleName;
            DeletedBy = deletedBy;
            DeletedAt = deletedAt ?? DateTime.UtcNow;
        }
    }

    /// <summary>
    /// 角色分配事件参数
    /// </summary>
    public class RoleAssignedEventArgs : EventArgs
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; }

        /// <summary>
        /// 角色ID
        /// </summary>
        public string RoleId { get; }

        /// <summary>
        /// 分配者用户ID
        /// </summary>
        public string? AssignedBy { get; }

        /// <summary>
        /// 分配时间
        /// </summary>
        public DateTime AssignedAt { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public RoleAssignedEventArgs(string userId, string roleId, string? assignedBy = null, DateTime? assignedAt = null)
        {
            UserId = userId;
            RoleId = roleId;
            AssignedBy = assignedBy;
            AssignedAt = assignedAt ?? DateTime.UtcNow;
        }
    }

    /// <summary>
    /// 角色移除事件参数
    /// </summary>
    public class RoleRemovedEventArgs : EventArgs
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; }

        /// <summary>
        /// 角色ID
        /// </summary>
        public string RoleId { get; }

        /// <summary>
        /// 移除者用户ID
        /// </summary>
        public string? RemovedBy { get; }

        /// <summary>
        /// 移除时间
        /// </summary>
        public DateTime RemovedAt { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public RoleRemovedEventArgs(string userId, string roleId, string? removedBy = null, DateTime? removedAt = null)
        {
            UserId = userId;
            RoleId = roleId;
            RemovedBy = removedBy;
            RemovedAt = removedAt ?? DateTime.UtcNow;
        }
    }

    #endregion

    #region 权限事件参数

    /// <summary>
    /// 权限创建事件参数
    /// </summary>
    public class PermissionCreatedEventArgs : EventArgs
    {
        /// <summary>
        /// 创建的权限
        /// </summary>
        public Permission Permission { get; }

        /// <summary>
        /// 创建者用户ID
        /// </summary>
        public string? CreatedBy { get; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public PermissionCreatedEventArgs(Permission permission, string? createdBy = null, DateTime? createdAt = null)
        {
            Permission = permission;
            CreatedBy = createdBy;
            CreatedAt = createdAt ?? DateTime.UtcNow;
        }
    }

    /// <summary>
    /// 权限更新事件参数
    /// </summary>
    public class PermissionUpdatedEventArgs : EventArgs
    {
        /// <summary>
        /// 更新的权限
        /// </summary>
        public Permission Permission { get; }

        /// <summary>
        /// 更新者用户ID
        /// </summary>
        public string? UpdatedBy { get; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public PermissionUpdatedEventArgs(Permission permission, string? updatedBy = null, DateTime? updatedAt = null)
        {
            Permission = permission;
            UpdatedBy = updatedBy;
            UpdatedAt = updatedAt ?? DateTime.UtcNow;
        }
    }

    /// <summary>
    /// 权限删除事件参数
    /// </summary>
    public class PermissionDeletedEventArgs : EventArgs
    {
        /// <summary>
        /// 删除的权限ID
        /// </summary>
        public string PermissionId { get; }

        /// <summary>
        /// 删除的权限名称
        /// </summary>
        public string PermissionName { get; }

        /// <summary>
        /// 删除者用户ID
        /// </summary>
        public string? DeletedBy { get; }

        /// <summary>
        /// 删除时间
        /// </summary>
        public DateTime DeletedAt { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public PermissionDeletedEventArgs(string permissionId, string permissionName, string? deletedBy = null, DateTime? deletedAt = null)
        {
            PermissionId = permissionId;
            PermissionName = permissionName;
            DeletedBy = deletedBy;
            DeletedAt = deletedAt ?? DateTime.UtcNow;
        }
    }

    /// <summary>
    /// 权限授予事件参数
    /// </summary>
    public class PermissionGrantedEventArgs : EventArgs
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; }

        /// <summary>
        /// 权限ID
        /// </summary>
        public string PermissionId { get; }

        /// <summary>
        /// 授予者用户ID
        /// </summary>
        public string? GrantedBy { get; }

        /// <summary>
        /// 授予时间
        /// </summary>
        public DateTime GrantedAt { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public PermissionGrantedEventArgs(string userId, string permissionId, string? grantedBy = null, DateTime? grantedAt = null)
        {
            UserId = userId;
            PermissionId = permissionId;
            GrantedBy = grantedBy;
            GrantedAt = grantedAt ?? DateTime.UtcNow;
        }
    }

    /// <summary>
    /// 权限撤销事件参数
    /// </summary>
    public class PermissionRevokedEventArgs : EventArgs
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; }

        /// <summary>
        /// 权限ID
        /// </summary>
        public string PermissionId { get; }

        /// <summary>
        /// 撤销者用户ID
        /// </summary>
        public string? RevokedBy { get; }

        /// <summary>
        /// 撤销时间
        /// </summary>
        public DateTime RevokedAt { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public PermissionRevokedEventArgs(string userId, string permissionId, string? revokedBy = null, DateTime? revokedAt = null)
        {
            UserId = userId;
            PermissionId = permissionId;
            RevokedBy = revokedBy;
            RevokedAt = revokedAt ?? DateTime.UtcNow;
        }
    }

    /// <summary>
    /// 权限检查事件参数
    /// </summary>
    public class PermissionCheckedEventArgs : EventArgs
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; }

        /// <summary>
        /// 权限名称
        /// </summary>
        public string PermissionName { get; }

        /// <summary>
        /// 检查结果
        /// </summary>
        public bool HasPermission { get; }

        /// <summary>
        /// 检查时间
        /// </summary>
        public DateTime CheckedAt { get; }

        /// <summary>
        /// 检查来源
        /// </summary>
        public string? Source { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public PermissionCheckedEventArgs(string userId, string permissionName, bool hasPermission, DateTime? checkedAt = null, string? source = null)
        {
            UserId = userId;
            PermissionName = permissionName;
            HasPermission = hasPermission;
            CheckedAt = checkedAt ?? DateTime.UtcNow;
            Source = source;
        }
    }

    #endregion
}
