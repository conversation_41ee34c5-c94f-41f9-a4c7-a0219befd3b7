using McLaser.Core.Common;
using McLaser.Device;
using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Forms;
using System.Windows.Threading;
using Button = System.Windows.Controls.Button;
using DataGrid = System.Windows.Controls.DataGrid;
using UserControl = System.Windows.Controls.UserControl;

namespace McLaser.Devices
{
    /// <summary>
    /// CardView.xaml 的交互逻辑
    /// 控制卡配置界面，提供轴管理、IO管理和参数配置功能
    /// </summary>
    public partial class CardView : UserControl, ICardConfigControl
    {
        protected DispatcherTimer _updateTimer;
       
        protected CardViewModel _viewModel;
        private CardBase _card;

        //private PropertyGrid _propertyGrid;

        /// <summary>
        /// 构造函数
        /// </summary>
        public CardView(CardBase card)
        {
            InitializeComponent();
            DataContext = new CardViewModel(card);
            _card = card;
            propertyGrid.Child = new PropertyGrid();
            // 初始化计时器
            _updateTimer = new DispatcherTimer();
            _updateTimer.Interval = TimeSpan.FromMilliseconds(500);
            _updateTimer.Tick += UpdateTimer_Tick;
        }
        
      
        
        /// <summary>
        /// 轴项目点击事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void AxisItem_MouseLeftButtonDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (sender is ContentPresenter contentPresenter)
            {
                // 获取数据上下文（轴状态对象）
                var axisStatus = contentPresenter.DataContext as AxisStatusInfo;

                // 如果找到对应的轴状态，则设置为选中项
                if (axisStatus != null)
                {
                    //_viewModel.SelectedAxisStatus = axisStatus;
                    //_viewModel.SelectedAxis = axisStatus.Axis;

                    // 更新属性网格
                    if (propertyGrid.Child is PropertyGrid propGrid)
                    {
                        propGrid.SelectedObject = axisStatus.Axis;
                    }
                }
            }
        }
        
        /// <summary>
        /// 计时器Tick事件处理
        /// </summary>
        private void UpdateTimer_Tick(object sender, EventArgs e)
        {
            if (_viewModel != null)
            {
                UpdateAllAxisStatus();
            }
        }
        
        /// <summary>
        /// 设置关联的控制卡
        /// </summary>
        /// <param name="card">控制卡</param>
        public virtual void SetCard(CardBase card)
        {
           
        }
        
   

        /// <summary>
        /// 获取配置界面的UI控件
        /// </summary>
        /// <returns>配置界面控件</returns>
        public virtual UserControl GetControl()
        {
            return this;
        }

        /// <summary>
        /// 获取配置界面的卡类型
        /// </summary>
        /// <returns>支持的控制卡类型</returns>
        public virtual CardType GetSupportCardType()
        {
            return CardType.Virtual;  // 基类支持虚拟卡类型
        }

        /// <summary>
        /// 更新所有轴的状态
        /// </summary>
        public virtual void UpdateAllAxisStatus()
        {
            if (_viewModel != null)
            {
                _viewModel.UpdateAllAxisStatus();
            }
        }

        /// <summary>
        /// 创建视图模型
        /// 可由子类重写以提供特定控制卡的视图模型
        /// </summary>
        /// <param name="card">控制卡</param>
        /// <returns>视图模型</returns>
        public virtual CardViewModel CreateViewModel(CardBase card)
        {
            return new CardViewModel(card);
        }

        /// <summary>
        /// 添加自定义Tab页
        /// </summary>
        /// <param name="header">页头</param>
        /// <param name="content">页内容</param>
        public void AddCustomTab(string header, UIElement content)
        {
            TabItem tabItem = new TabItem();
            tabItem.Header = header;
            tabItem.Content = content;
            tabItem.Style = (Style)FindResource("TabItemStyle");
            
            // 插入到最后一个Tab前
            int insertIndex = MainTabControl.Items.Count - 1;
            if (insertIndex < 0)
                insertIndex = 0;
                
            MainTabControl.Items.Insert(insertIndex, tabItem);
        }

        /// <summary>
        /// 替换已有的Tab页
        /// </summary>
        /// <param name="index">页索引</param>
        /// <param name="header">新页头</param>
        /// <param name="content">新页内容</param>
        public void ReplaceTab(int index, string header, UIElement content)
        {
            if (index < 0 || index >= MainTabControl.Items.Count - 1)
                return;
                
            TabItem tabItem = MainTabControl.Items[index] as TabItem;
            if (tabItem != null)
            {
                tabItem.Header = header;
                tabItem.Content = content;
            }
        }
        
        /// <summary>
        /// 设置轴管理页面的扩展内容
        /// </summary>
        /// <param name="content">扩展内容</param>
        public void SetAxisExtensionContent(UIElement content)
        {
            AxisExtensionContent.Content = content;
        }
        
        /// <summary>
        /// 控件卸载时清理资源
        /// </summary>
        protected void OnUnloaded(RoutedEventArgs e)
        {
            //base.OnUnloaded(e);
            
            // 停止计时器
            if (_updateTimer != null)
            {
                _updateTimer.Stop();
                _updateTimer.Tick -= UpdateTimer_Tick;
            }
        }

        private void btnTrue_Click(object sender, RoutedEventArgs e)
        {
            DataGrid datagrid = ((Button)sender).CommandParameter as DataGrid;
            var selectedRow = datagrid.SelectedItem;
            var selectedIndex = datagrid.SelectedIndex;
            Button button = sender as Button;
            UnitStatus io = button.DataContext as UnitStatus;
            _card.SetDo(selectedIndex, true);
        }

        private void btnFalse_Click(object sender, RoutedEventArgs e)
        {
            DataGrid datagrid = ((Button)sender).CommandParameter as DataGrid;
            var selectedRow = datagrid.SelectedItem;
            var selectedIndex = datagrid.SelectedIndex;
            Button button = sender as Button;
            UnitStatus io = button.DataContext as UnitStatus;
            _card.SetDo(selectedIndex, false);
        }
    }
} 