using System;
using System.Collections.Concurrent;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace McLaser.Core.Communication
{
    /// <summary>
    /// UDP通信通道实现
    /// 支持UDP数据报通信
    /// </summary>
    public class UdpCommunicationChannel : ICommunicationChannel, IConfigurableCommunicationChannel, IDisposable
    {
        #region 字段

        private UdpClient? _udpClient;
        private IPEndPoint? _remoteEndPoint;
        private readonly ConcurrentQueue<byte> _receiveBuffer = new ConcurrentQueue<byte>();
        private readonly object _lockObject = new object();
        private CancellationTokenSource? _cancellationTokenSource;
        private Task? _receiveTask;
        private bool _disposed = false;
        private ConnectionState _connectionState = ConnectionState.Disconnected;
        private readonly CommunicationStatistics _statistics = new CommunicationStatistics();

        #endregion

        #region 属性

        /// <summary>
        /// 通道名称
        /// </summary>
        public string Name { get; set; } = "UDP通信通道";

        /// <summary>
        /// 通道类型
        /// </summary>
        public CommunicationChannelType ChannelType => CommunicationChannelType.Udp;

        /// <summary>
        /// 连接状态
        /// </summary>
        public bool IsConnected => _connectionState == ConnectionState.Connected && _udpClient != null;

        /// <summary>
        /// 是否正在连接
        /// </summary>
        public bool IsConnecting => _connectionState == ConnectionState.Connecting;

        /// <summary>
        /// 连接配置
        /// </summary>
        public CommunicationConfig Configuration { get; set; } = new UdpConfig();

        /// <summary>
        /// UDP配置
        /// </summary>
        public UdpConfig UdpConfiguration => Configuration as UdpConfig ?? new UdpConfig();

        #endregion

        #region 事件

        /// <summary>
        /// 连接状态变更事件
        /// </summary>
        public event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;

        /// <summary>
        /// 数据接收事件
        /// </summary>
        public event EventHandler<DataReceivedEventArgs>? DataReceived;

        /// <summary>
        /// 错误发生事件
        /// </summary>
        public event EventHandler<CommunicationErrorEventArgs>? ErrorOccurred;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化UDP通信通道
        /// </summary>
        public UdpCommunicationChannel()
        {
        }

        /// <summary>
        /// 初始化UDP通信通道
        /// </summary>
        /// <param name="config">UDP配置</param>
        public UdpCommunicationChannel(UdpConfig config)
        {
            Configuration = config ?? throw new ArgumentNullException(nameof(config));
        }

        #endregion

        #region 连接管理

        /// <summary>
        /// 异步连接
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>连接任务</returns>
        public async Task<bool> ConnectAsync(CancellationToken cancellationToken = default)
        {
            if (IsConnected)
                return true;

            if (IsConnecting)
                return false;

            try
            {
                SetConnectionState(ConnectionState.Connecting);
                _cancellationTokenSource = new CancellationTokenSource();

                var config = UdpConfiguration;

                // 创建UDP客户端
                if (config.LocalPort > 0)
                {
                    _udpClient = new UdpClient(config.LocalPort);
                }
                else
                {
                    _udpClient = new UdpClient();
                }

                // 配置UDP客户端
                if (config.EnableBroadcast)
                {
                    _udpClient.EnableBroadcast = true;
                }

                // 设置远程端点
                if (!string.IsNullOrEmpty(config.RemoteHost) && config.RemotePort > 0)
                {
                    _remoteEndPoint = new IPEndPoint(IPAddress.Parse(config.RemoteHost), config.RemotePort);
                    _udpClient.Connect(_remoteEndPoint);
                }

                // 配置多播
                if (config.EnableMulticast && !string.IsNullOrEmpty(config.MulticastAddress))
                {
                    var multicastAddress = IPAddress.Parse(config.MulticastAddress);
                    _udpClient.JoinMulticastGroup(multicastAddress);
                }

                SetConnectionState(ConnectionState.Connected);
                _statistics.ConnectedTime = DateTime.Now;

                // 启动接收任务
                StartReceiveTask();

                return true;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(CommunicationErrorType.ConnectionError, $"连接失败: {ex.Message}", ex);
                SetConnectionState(ConnectionState.Error);
                return false;
            }
        }

        /// <summary>
        /// 异步断开连接
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>断开任务</returns>
        public async Task DisconnectAsync(CancellationToken cancellationToken = default)
        {
            if (!IsConnected && !IsConnecting)
                return;

            try
            {
                SetConnectionState(ConnectionState.Disconnecting);

                // 取消所有任务
                _cancellationTokenSource?.Cancel();

                // 等待任务完成
                if (_receiveTask != null && !_receiveTask.IsCompleted)
                {
                    await _receiveTask.ConfigureAwait(false);
                }

                // 关闭UDP客户端
                _udpClient?.Close();
                _udpClient?.Dispose();
                _udpClient = null;
                _remoteEndPoint = null;

                SetConnectionState(ConnectionState.Disconnected);

                // 更新统计信息
                if (_statistics.ConnectedTime.HasValue)
                {
                    _statistics.TotalConnectedTime += DateTime.Now - _statistics.ConnectedTime.Value;
                    _statistics.ConnectedTime = null;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred(CommunicationErrorType.ConnectionError, $"断开连接失败: {ex.Message}", ex);
            }
        }

        #endregion

        #region 数据传输

        /// <summary>
        /// 异步发送数据
        /// </summary>
        /// <param name="data">要发送的数据</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>发送任务，返回实际发送的字节数</returns>
        public async Task<int> SendAsync(byte[] data, CancellationToken cancellationToken = default)
        {
            if (data == null)
                throw new ArgumentNullException(nameof(data));

            if (!IsConnected || _udpClient == null)
                throw new InvalidOperationException("连接未建立");

            try
            {
                int bytesSent;
                
                if (_remoteEndPoint != null)
                {
                    // 发送到指定的远程端点
                    bytesSent = await _udpClient.SendAsync(data, data.Length, _remoteEndPoint);
                }
                else
                {
                    // 发送到已连接的端点
                    bytesSent = await _udpClient.SendAsync(data, data.Length);
                }

                // 更新统计信息
                _statistics.BytesSent += bytesSent;
                _statistics.MessagesSent++;
                _statistics.LastActivity = DateTime.Now;

                return bytesSent;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(CommunicationErrorType.SendError, $"发送数据失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 异步发送数据到指定端点
        /// </summary>
        /// <param name="data">要发送的数据</param>
        /// <param name="endPoint">目标端点</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>发送任务，返回实际发送的字节数</returns>
        public async Task<int> SendAsync(byte[] data, IPEndPoint endPoint, CancellationToken cancellationToken = default)
        {
            if (data == null)
                throw new ArgumentNullException(nameof(data));

            if (endPoint == null)
                throw new ArgumentNullException(nameof(endPoint));

            if (!IsConnected || _udpClient == null)
                throw new InvalidOperationException("连接未建立");

            try
            {
                var bytesSent = await _udpClient.SendAsync(data, data.Length, endPoint);

                // 更新统计信息
                _statistics.BytesSent += bytesSent;
                _statistics.MessagesSent++;
                _statistics.LastActivity = DateTime.Now;

                return bytesSent;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(CommunicationErrorType.SendError, $"发送数据失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 异步发送字符串数据
        /// </summary>
        /// <param name="message">要发送的消息</param>
        /// <param name="encoding">编码方式，默认UTF8</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>发送任务，返回实际发送的字节数</returns>
        public async Task<int> SendAsync(string message, Encoding? encoding = null, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(message))
                throw new ArgumentException("消息不能为空", nameof(message));

            encoding ??= Encoding.UTF8;
            var data = encoding.GetBytes(message);
            return await SendAsync(data, cancellationToken);
        }

        /// <summary>
        /// 异步接收数据
        /// </summary>
        /// <param name="buffer">接收缓冲区</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>接收任务，返回实际接收的字节数</returns>
        public async Task<int> ReceiveAsync(byte[] buffer, CancellationToken cancellationToken = default)
        {
            if (buffer == null)
                throw new ArgumentNullException(nameof(buffer));

            if (!IsConnected || _udpClient == null)
                throw new InvalidOperationException("连接未建立");

            try
            {
                var result = await _udpClient.ReceiveAsync();
                var bytesToCopy = Math.Min(buffer.Length, result.Buffer.Length);
                Array.Copy(result.Buffer, 0, buffer, 0, bytesToCopy);

                // 更新统计信息
                _statistics.BytesReceived += bytesToCopy;
                _statistics.MessagesReceived++;
                _statistics.LastActivity = DateTime.Now;

                return bytesToCopy;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(CommunicationErrorType.ReceiveError, $"接收数据失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 异步接收数据直到指定分隔符
        /// </summary>
        /// <param name="delimiter">分隔符</param>
        /// <param name="maxLength">最大长度</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>接收任务，返回接收到的数据</returns>
        public async Task<byte[]> ReceiveUntilAsync(byte[] delimiter, int maxLength = 4096, CancellationToken cancellationToken = default)
        {
            // UDP是数据报协议，不支持流式接收
            throw new NotSupportedException("UDP协议不支持流式接收");
        }

        /// <summary>
        /// 清空接收缓冲区
        /// </summary>
        public void ClearReceiveBuffer()
        {
            while (_receiveBuffer.TryDequeue(out _))
            {
                // 清空队列
            }
        }

        /// <summary>
        /// 清空发送缓冲区
        /// </summary>
        public void ClearSendBuffer()
        {
            // UDP没有发送缓冲区需要清空
        }

        #endregion

        #region 统计和测试

        /// <summary>
        /// 获取通道统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public CommunicationStatistics GetStatistics()
        {
            return _statistics;
        }

        /// <summary>
        /// 重置统计信息
        /// </summary>
        public void ResetStatistics()
        {
            _statistics.Reset();
        }

        /// <summary>
        /// 测试连接
        /// </summary>
        /// <param name="timeout">超时时间</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>测试结果</returns>
        public async Task<bool> TestConnectionAsync(TimeSpan timeout, CancellationToken cancellationToken = default)
        {
            if (!IsConnected)
                return false;

            try
            {
                using (var timeoutCts = new CancellationTokenSource(timeout))
                using (var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token))
                {
                    // 发送测试数据
                    var testData = Encoding.UTF8.GetBytes("TEST");
                    await SendAsync(testData, combinedCts.Token);
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region 配置管理

        /// <summary>
        /// 应用配置
        /// </summary>
        /// <param name="config">配置信息</param>
        /// <returns>是否成功应用配置</returns>
        public bool ApplyConfiguration(CommunicationConfig config)
        {
            if (config == null)
                return false;

            try
            {
                Configuration = config;
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证配置
        /// </summary>
        /// <param name="config">配置信息</param>
        /// <returns>验证结果</returns>
        public ConfigurationValidationResult ValidateConfiguration(CommunicationConfig config)
        {
            var result = new ConfigurationValidationResult { IsValid = true };

            if (config == null)
            {
                result.Errors.Add("配置不能为空");
                result.IsValid = false;
                return result;
            }

            if (config is UdpConfig udpConfig)
            {
                // 验证本地端口
                if (udpConfig.LocalPort < 0 || udpConfig.LocalPort > 65535)
                {
                    result.Errors.Add("本地端口号必须在0-65535范围内");
                    result.IsValid = false;
                }

                // 验证远程端口
                if (udpConfig.RemotePort < 0 || udpConfig.RemotePort > 65535)
                {
                    result.Errors.Add("远程端口号必须在0-65535范围内");
                    result.IsValid = false;
                }

                // 验证远程主机地址
                if (!string.IsNullOrEmpty(udpConfig.RemoteHost))
                {
                    if (!IPAddress.TryParse(udpConfig.RemoteHost, out _))
                    {
                        result.Warnings.Add("远程主机地址格式可能不正确");
                    }
                }

                // 验证多播地址
                if (udpConfig.EnableMulticast)
                {
                    if (string.IsNullOrEmpty(udpConfig.MulticastAddress))
                    {
                        result.Errors.Add("启用多播时必须指定多播地址");
                        result.IsValid = false;
                    }
                    else if (IPAddress.TryParse(udpConfig.MulticastAddress, out var multicastAddr))
                    {
                        if (!IsMulticastAddress(multicastAddr))
                        {
                            result.Errors.Add("多播地址不在有效的多播地址范围内");
                            result.IsValid = false;
                        }
                    }
                    else
                    {
                        result.Errors.Add("多播地址格式不正确");
                        result.IsValid = false;
                    }
                }
            }
            else
            {
                result.Errors.Add("配置类型不匹配，需要UdpConfig");
                result.IsValid = false;
            }

            return result;
        }

        /// <summary>
        /// 获取默认配置
        /// </summary>
        /// <returns>默认配置</returns>
        public CommunicationConfig GetDefaultConfiguration()
        {
            return new UdpConfig
            {
                Name = "默认UDP配置",
                LocalPort = 0,
                RemoteHost = "",
                RemotePort = 0,
                EnableBroadcast = false,
                EnableMulticast = false,
                MulticastAddress = "",
                ReceiveBufferSize = 4096,
                SendBufferSize = 4096
            };
        }

        /// <summary>
        /// 获取支持的配置选项
        /// </summary>
        /// <returns>配置选项列表</returns>
        public CommunicationConfigOption[] GetSupportedOptions()
        {
            return new[]
            {
                new CommunicationConfigOption
                {
                    Name = "LocalPort",
                    OptionType = typeof(int),
                    DefaultValue = 0,
                    IsRequired = false,
                    Description = "本地端口号（0表示自动分配）",
                    MinValue = 0,
                    MaxValue = 65535
                },
                new CommunicationConfigOption
                {
                    Name = "RemoteHost",
                    OptionType = typeof(string),
                    DefaultValue = "",
                    IsRequired = false,
                    Description = "远程主机地址"
                },
                new CommunicationConfigOption
                {
                    Name = "RemotePort",
                    OptionType = typeof(int),
                    DefaultValue = 0,
                    IsRequired = false,
                    Description = "远程端口号",
                    MinValue = 0,
                    MaxValue = 65535
                },
                new CommunicationConfigOption
                {
                    Name = "EnableBroadcast",
                    OptionType = typeof(bool),
                    DefaultValue = false,
                    IsRequired = false,
                    Description = "是否启用广播"
                },
                new CommunicationConfigOption
                {
                    Name = "EnableMulticast",
                    OptionType = typeof(bool),
                    DefaultValue = false,
                    IsRequired = false,
                    Description = "是否启用多播"
                },
                new CommunicationConfigOption
                {
                    Name = "MulticastAddress",
                    OptionType = typeof(string),
                    DefaultValue = "",
                    IsRequired = false,
                    Description = "多播地址"
                }
            };
        }

        #endregion

        #region 私有方法

        private void StartReceiveTask()
        {
            _receiveTask = Task.Run(async () =>
            {
                while (IsConnected && !_cancellationTokenSource?.Token.IsCancellationRequested == true)
                {
                    try
                    {
                        if (_udpClient != null)
                        {
                            var result = await _udpClient.ReceiveAsync();

                            // 触发数据接收事件
                            OnDataReceived(result.Buffer, result.Buffer.Length, result.RemoteEndPoint);

                            // 更新统计信息
                            _statistics.BytesReceived += result.Buffer.Length;
                            _statistics.MessagesReceived++;
                            _statistics.LastActivity = DateTime.Now;
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        break;
                    }
                    catch (ObjectDisposedException)
                    {
                        break;
                    }
                    catch (Exception ex)
                    {
                        OnErrorOccurred(CommunicationErrorType.ReceiveError, $"接收数据时发生错误: {ex.Message}", ex);
                        break;
                    }
                }
            });
        }

        private void SetConnectionState(ConnectionState newState)
        {
            var oldState = _connectionState;
            _connectionState = newState;

            if (oldState != newState)
            {
                OnConnectionStateChanged(oldState, newState);
            }
        }

        private static bool IsMulticastAddress(IPAddress address)
        {
            if (address.AddressFamily == AddressFamily.InterNetwork)
            {
                // IPv4多播地址范围：********* 到 ***************
                var bytes = address.GetAddressBytes();
                return bytes[0] >= 224 && bytes[0] <= 239;
            }
            else if (address.AddressFamily == AddressFamily.InterNetworkV6)
            {
                // IPv6多播地址以FF开头
                var bytes = address.GetAddressBytes();
                return bytes[0] == 0xFF;
            }

            return false;
        }

        #endregion

        #region 事件触发

        private void OnConnectionStateChanged(ConnectionState oldState, ConnectionState newState)
        {
            ConnectionStateChanged?.Invoke(this, new ConnectionStateChangedEventArgs(oldState, newState));
        }

        private void OnDataReceived(byte[] data, int length, EndPoint? remoteEndPoint)
        {
            DataReceived?.Invoke(this, new DataReceivedEventArgs(data, length, remoteEndPoint));
        }

        private void OnErrorOccurred(CommunicationErrorType errorType, string errorMessage, Exception? exception = null, bool isRecoverable = false)
        {
            _statistics.ErrorCount++;
            ErrorOccurred?.Invoke(this, new CommunicationErrorEventArgs(errorType, errorMessage, exception, isRecoverable));
        }

        #endregion

        #region IDisposable

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否正在释放</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                try
                {
                    DisconnectAsync().Wait(5000); // 等待最多5秒
                }
                catch
                {
                    // 忽略释放时的异常
                }

                _cancellationTokenSource?.Dispose();
                _disposed = true;
            }
        }

        /// <summary>
        /// 析构函数
        /// </summary>
        ~UdpCommunicationChannel()
        {
            Dispose(false);
        }

        #endregion
    }
}
