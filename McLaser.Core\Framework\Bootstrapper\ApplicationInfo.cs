using System;

namespace McLaser.Core.Framework
{
    /// <summary>
    /// 应用程序信息
    /// 包含应用程序的基本元数据
    /// </summary>
    public class ApplicationInfo
    {
        /// <summary>
        /// 应用程序唯一标识符
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 应用程序显示名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 应用程序版本
        /// </summary>
        public Version Version { get; set; } = new Version(1, 0, 0, 0);

        /// <summary>
        /// 应用程序描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 应用程序公司/组织
        /// </summary>
        public string Company { get; set; } = string.Empty;

        /// <summary>
        /// 应用程序版权信息
        /// </summary>
        public string Copyright { get; set; } = string.Empty;

        /// <summary>
        /// 应用程序启动时间
        /// </summary>
        public DateTime StartTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 应用程序运行时间
        /// </summary>
        public TimeSpan RunningTime => DateTime.Now - StartTime;

        /// <summary>
        /// 应用程序配置目录
        /// </summary>
        public string ConfigDirectory { get; set; } = string.Empty;

        /// <summary>
        /// 应用程序数据目录
        /// </summary>
        public string DataDirectory { get; set; } = string.Empty;

        /// <summary>
        /// 应用程序日志目录
        /// </summary>
        public string LogDirectory { get; set; } = string.Empty;

        /// <summary>
        /// 应用程序临时目录
        /// </summary>
        public string TempDirectory { get; set; } = string.Empty;

        /// <summary>
        /// 是否为调试模式
        /// </summary>
        public bool IsDebugMode { get; set; }

        /// <summary>
        /// 应用程序文化信息
        /// </summary>
        public string Culture { get; set; } = "zh-CN";

        /// <summary>
        /// 应用程序主题
        /// </summary>
        public string Theme { get; set; } = "Default";

        /// <summary>
        /// 获取版本字符串
        /// </summary>
        /// <returns>版本字符串</returns>
        public string GetVersionString()
        {
            return Version.ToString();
        }

        /// <summary>
        /// 获取完整的应用程序标题
        /// </summary>
        /// <returns>应用程序标题</returns>
        public string GetFullTitle()
        {
            return $"{Name} v{GetVersionString()}";
        }

        /// <summary>
        /// 获取应用程序信息摘要
        /// </summary>
        /// <returns>信息摘要</returns>
        public string GetSummary()
        {
            return $"{GetFullTitle()} - {Description}";
        }

        /// <summary>
        /// 创建默认的应用程序信息
        /// </summary>
        /// <param name="id">应用程序ID</param>
        /// <param name="name">应用程序名称</param>
        /// <param name="version">应用程序版本</param>
        /// <returns>应用程序信息实例</returns>
        public static ApplicationInfo CreateDefault(string id, string name, Version? version = null)
        {
            var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            
            return new ApplicationInfo
            {
                Id = id,
                Name = name,
                Version = version ?? new Version(1, 0, 0, 0),
                ConfigDirectory = System.IO.Path.Combine(baseDirectory, "Config"),
                DataDirectory = System.IO.Path.Combine(baseDirectory, "Data"),
                LogDirectory = System.IO.Path.Combine(baseDirectory, "Logs"),
                TempDirectory = System.IO.Path.Combine(baseDirectory, "Temp"),
                IsDebugMode = System.Diagnostics.Debugger.IsAttached,
                StartTime = DateTime.Now
            };
        }

        /// <summary>
        /// 确保目录存在
        /// </summary>
        public void EnsureDirectoriesExist()
        {
            var directories = new[]
            {
                ConfigDirectory,
                DataDirectory,
                LogDirectory,
                TempDirectory
            };

            foreach (var directory in directories)
            {
                if (!string.IsNullOrEmpty(directory) && !System.IO.Directory.Exists(directory))
                {
                    try
                    {
                        System.IO.Directory.CreateDirectory(directory);
                    }
                    catch
                    {
                        // 忽略目录创建错误
                    }
                }
            }
        }

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return GetFullTitle();
        }
    }
}
