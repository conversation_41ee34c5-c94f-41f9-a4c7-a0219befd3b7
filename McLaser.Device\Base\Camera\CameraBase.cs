using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Threading;
using System.Windows.Controls;
using Newtonsoft.Json;
using McLaser.Device;

namespace McLaser.Devices
{
    /// <summary>
    /// 相机基类
    /// 为所有相机设备提供基础功能实现
    /// </summary>
    [Serializable]
    public partial class CameraBase : DeviceBase, ICamera
    {
        #region 字段
        /// <summary>
        /// 请求更新标志（0--不修改, 1--修改）
        /// </summary>
        protected long isRequestUpdate = 0;

        /// <summary>
        /// 图像更新事件
        /// </summary>
        protected AutoResetEvent updateImageEvent = new AutoResetEvent(false);

        /// <summary>
        /// 线程锁对象
        /// </summary>
        protected static readonly object objLock = new object();
        #endregion

        #region 属性
        /// <summary>
        /// 相机类型
        /// </summary>
        [Category("相机"), DisplayName("相机类型")]
        public CameraType Type { get; set; } = CameraType.HIK;

        /// <summary>
        /// 触发模式
        /// </summary>
        private TrigMode _trigMode = TrigMode.软触发;
        [Category("相机"), DisplayName("触发模式")]
        public TrigMode TrigMode
        {
            get { return _trigMode; }
            set { Set(ref _trigMode, value); }
        }

        private int _timeoutOnce = 1000;
        [Category("相机"), DisplayName("单次采集超时")]
        public int TimeoutOnce
        {
            get { return _timeoutOnce; }
            set { Set(ref _timeoutOnce, value); }
        }

        /// <summary>
        /// 触发模式选项（用于界面绑定）
        /// </summary>
        [JsonIgnore]
        public Array TrigModes { get; set; } = Enum.GetValues(typeof(TrigMode));

        /// <summary>
        /// 曝光时间
        /// </summary>
        [Category("曝光增益"), DisplayName("曝光时间")]
        public double Exposure { get; set; } = 1000;

        /// <summary>
        /// 增益值
        /// </summary>
        [Category("曝光增益"), DisplayName("增益")]
        public double Gain { get; set; } = 1;

        /// <summary>
        /// 像素宽度
        /// </summary>
        [Category("相机"), DisplayName("像素宽度")]
        public int PixelX { get; set; } = 2048;

        /// <summary>
        /// 像素高度
        /// </summary>
        [Category("相机"), DisplayName("像素高度")]
        public int PixelY { get; set; } = 2048;

        /// <summary>
        /// 是否彩图转灰度图
        /// </summary>
        [Category("相机"), DisplayName("是否彩图转灰度图")]
        public bool IsRGBToGray { get; set; } = false;

        /// <summary>
        /// 是否旋转图像
        /// </summary>
        [Category("旋转"), DisplayName("是否旋转图像")]
        public bool IsRotate { get; set; } = false;

        /// <summary>
        /// 旋转角度
        /// </summary>
        [Category("旋转"), DisplayName("旋转角度")]
        public RotateAngle Angle { get; set; } = RotateAngle.零;

        /// <summary>
        /// 图像宽度（实际采集）
        /// </summary>
        public int Width { get; set; } = 0;

        /// <summary>
        /// 图像高度（实际采集）
        /// </summary>
        public int Height { get; set; } = 0;

        /// <summary>
        /// 相机信息
        /// </summary>
        public CameraInfo CameraInfo { get; set; } = new CameraInfo();

        /// <summary>
        /// 设备状态
        /// </summary>
        [JsonIgnore, Browsable(false)]
        public override StatusDevice Status { get; set; } = new StatusCamera();

        /// <summary>
        /// 设备名称
        /// </summary>
        public new string Name { get; set; } = "Camera";

        /// <summary>
        /// 设备类别
        /// </summary>
        public override DeviceCategory Category => DeviceCategory.Camera;
        #endregion

        #region 虚方法实现
        /// <summary>
        /// 搜索可用相机
        /// </summary>
        /// <returns>相机信息列表</returns>
        public virtual List<CameraInfo> SearchCameras()
        {
            return new List<CameraInfo>();
        }

        /// <summary>
        /// 开始采集
        /// </summary>
        /// <returns>操作是否成功</returns>
        public virtual bool Start()
        {
            return false;
        }

        /// <summary>
        /// 检查是否正在采集
        /// </summary>
        /// <returns>是否正在采集</returns>
        public virtual bool IsStart()
        {
            return false;
        }

        /// <summary>
        /// 停止采集
        /// </summary>
        /// <returns>操作是否成功</returns>
        public virtual bool Stop()
        {
            return false;
        }

        /// <summary>
        /// 单次采集图像
        /// </summary>
        /// <param name="source">输出的图像数据</param>
        /// <returns>采集是否成功</returns>
        public virtual bool GrabImageData(out ImageData source)
        {
            source = new ImageData();
            return false;
        }

        /// <summary>
        /// 开始连续采集
        /// </summary>
        /// <returns>操作是否成功</returns>
        public virtual bool GrabContinue()
        {
            return false;
        }

        /// <summary>
        /// 获取当前曝光时间
        /// </summary>
        /// <param name="value">曝光时间值</param>
        /// <returns>操作是否成功</returns>
        public virtual bool GetExposure(ref double value)
        {
            value = Exposure;
            return true;
        }

        /// <summary>
        /// 设置曝光时间
        /// </summary>
        /// <param name="value">曝光时间值</param>
        /// <returns>操作是否成功</returns>
        public virtual bool SetExposure(double value)
        {
            Exposure = value;
            return true;
        }

        /// <summary>
        /// 获取当前增益值
        /// </summary>
        /// <param name="value">增益值</param>
        /// <returns>操作是否成功</returns>
        public virtual bool GetGain(ref double value)
        {
            value = Gain;
            return true;
        }

        /// <summary>
        /// 设置增益值
        /// </summary>
        /// <param name="value">增益值</param>
        /// <returns>操作是否成功</returns>
        public virtual bool SetGain(double value)
        {
            Gain = value;
            return true;
        }

        /// <summary>
        /// 获取图像宽度
        /// </summary>
        /// <param name="value">宽度值</param>
        /// <returns>操作是否成功</returns>
        public virtual bool GetWidth(ref int value)
        {
            value = Width;
            return true;
        }

        /// <summary>
        /// 设置图像宽度
        /// </summary>
        /// <param name="value">宽度值</param>
        /// <returns>操作是否成功</returns>
        public virtual bool SetWidth(int value)
        {
            Width = value;
            return true;
        }

        /// <summary>
        /// 获取图像高度
        /// </summary>
        /// <param name="value">高度值</param>
        /// <returns>操作是否成功</returns>
        public virtual bool GetHeight(ref int value)
        {
            value = Height;
            return true;
        }

        /// <summary>
        /// 设置图像高度
        /// </summary>
        /// <param name="value">高度值</param>
        /// <returns>操作是否成功</returns>
        public virtual bool SetHeight(int value)
        {
            Height = value;
            return true;
        }

        /// <summary>
        /// 获取X方向偏移
        /// </summary>
        /// <param name="value">偏移值</param>
        /// <returns>操作是否成功</returns>
        public virtual bool GetOffsetX(ref int value)
        {
            value = 0;
            return false;
        }

        /// <summary>
        /// 设置X方向偏移
        /// </summary>
        /// <param name="value">偏移值</param>
        /// <returns>操作是否成功</returns>
        public virtual bool SetOffsetX(int value)
        {
            return false;
        }

        /// <summary>
        /// 获取Y方向偏移
        /// </summary>
        /// <param name="value">偏移值</param>
        /// <returns>操作是否成功</returns>
        public virtual bool GetOffsetY(ref int value)
        {
            value = 0;
            return false;
        }

        /// <summary>
        /// 设置Y方向偏移
        /// </summary>
        /// <param name="value">偏移值</param>
        /// <returns>操作是否成功</returns>
        public virtual bool SetOffsetY(int value)
        {
            return false;
        }

        /// <summary>
        /// 获取触发模式
        /// </summary>
        /// <param name="isTrigger">是否为触发模式</param>
        /// <returns>操作是否成功</returns>
        public virtual bool GetTriggerMode(ref bool isTrigger)
        {
            isTrigger = TrigMode != TrigMode.软触发;
            return true;
        }

        /// <summary>
        /// 设置触发模式
        /// </summary>
        /// <param name="mode">触发模式</param>
        /// <returns>操作是否成功</returns>
        public virtual bool SetTriggerMode(TrigMode mode)
        {
            TrigMode = mode;
            return true;
        }


        CameraView cameraSetView;
        /// <summary>
        /// 获取设备配置界面
        /// </summary>
        /// <returns>配置界面控件</returns>
        public override UserControl GetConfigurationView()
        {
            if (cameraSetView == null)
                cameraSetView = new CameraView(this);
            return cameraSetView;
        }
        #endregion
    }
}
