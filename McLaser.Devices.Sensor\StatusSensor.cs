using System;
using System.ComponentModel;
using McLaser.Device;

namespace McLaser.Devices.Sensor
{
    /// <summary>
    /// 传感器状态基类
    /// 提供传感器设备状态的基本实现和通用功能
    /// </summary>
    [Serializable]
    public abstract class StatusSensor : StatusBase
    {
        #region 私有字段

        private bool _isConnected = false;
        private SensorStatus _sensorStatus = SensorStatus.Offline;
        private double _currentValue = 0;
        private double _minValue = 0;
        private double _maxValue = 0;
        private double _averageValue = 0;
        private bool _isCalibrated = false;
        private DateTime _lastCalibrationTime = DateTime.MinValue;
        private DateTime _lastUpdateTime = DateTime.MinValue;
        private string _errorMessage = string.Empty;
        private bool _isSampling = false;
        private double _samplingRate = 0;
        private int _sampleCount = 0;

        #endregion

        #region 属性

        /// <summary>
        /// 设备是否已连接
        /// </summary>
        [Category("传感器状态"), DisplayName("连接状态")]
        public override bool IsConnected
        {
            get => _isConnected;
            set
            {
                if (_isConnected != value)
                {
                    _isConnected = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// 传感器状态
        /// </summary>
        [Category("传感器状态"), DisplayName("传感器状态")]
        public SensorStatus SensorStatus
        {
            get => _sensorStatus;
            set
            {
                if (_sensorStatus != value)
                {
                    _sensorStatus = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// 当前测量值
        /// </summary>
        [Category("传感器数据"), DisplayName("当前测量值")]
        public double CurrentValue
        {
            get => _currentValue;
            set
            {
                if (Math.Abs(_currentValue - value) > 0.001)
                {
                    _currentValue = value;
                    OnPropertyChanged();
                    UpdateStatistics(value);
                }
            }
        }

        /// <summary>
        /// 最小值
        /// </summary>
        [Category("传感器数据"), DisplayName("最小值")]
        public double MinValue
        {
            get => _minValue;
            set
            {
                if (Math.Abs(_minValue - value) > 0.001)
                {
                    _minValue = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 最大值
        /// </summary>
        [Category("传感器数据"), DisplayName("最大值")]
        public double MaxValue
        {
            get => _maxValue;
            set
            {
                if (Math.Abs(_maxValue - value) > 0.001)
                {
                    _maxValue = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 平均值
        /// </summary>
        [Category("传感器数据"), DisplayName("平均值")]
        public double AverageValue
        {
            get => _averageValue;
            set
            {
                if (Math.Abs(_averageValue - value) > 0.001)
                {
                    _averageValue = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 是否已校准
        /// </summary>
        [Category("传感器状态"), DisplayName("校准状态")]
        public bool IsCalibrated
        {
            get => _isCalibrated;
            set
            {
                if (_isCalibrated != value)
                {
                    _isCalibrated = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// 最后校准时间
        /// </summary>
        [Category("传感器状态"), DisplayName("最后校准时间")]
        public DateTime LastCalibrationTime
        {
            get => _lastCalibrationTime;
            set
            {
                if (_lastCalibrationTime != value)
                {
                    _lastCalibrationTime = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        [Category("传感器状态"), DisplayName("最后更新时间")]
        public DateTime LastUpdateTime
        {
            get => _lastUpdateTime;
            set
            {
                if (_lastUpdateTime != value)
                {
                    _lastUpdateTime = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 错误信息
        /// </summary>
        [Category("传感器状态"), DisplayName("错误信息")]
        public string ErrorMessage
        {
            get => _errorMessage;
            set
            {
                if (_errorMessage != value)
                {
                    _errorMessage = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// 是否正在采样
        /// </summary>
        [Category("传感器状态"), DisplayName("采样状态")]
        public bool IsSampling
        {
            get => _isSampling;
            set
            {
                if (_isSampling != value)
                {
                    _isSampling = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// 采样频率(Hz)
        /// </summary>
        [Category("传感器状态"), DisplayName("采样频率(Hz)")]
        public double SamplingRate
        {
            get => _samplingRate;
            set
            {
                if (Math.Abs(_samplingRate - value) > 0.001)
                {
                    _samplingRate = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 采样计数
        /// </summary>
        [Category("传感器状态"), DisplayName("采样计数")]
        public int SampleCount
        {
            get => _sampleCount;
            set
            {
                if (_sampleCount != value)
                {
                    _sampleCount = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 状态文本描述
        /// </summary>
        [Category("传感器状态"), DisplayName("状态描述")]
        public override string StatusText
        {
            get
            {
                if (!string.IsNullOrEmpty(_errorMessage))
                    return $"错误: {_errorMessage}";

                if (!_isConnected)
                    return "设备未连接";

                if (!_isCalibrated)
                    return "设备未校准";

                if (_isSampling)
                    return $"正在采样 - 值: {_currentValue:F3} - 频率: {_samplingRate:F1}Hz";

                return $"就绪 - 当前值: {_currentValue:F3}";
            }
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        protected StatusSensor()
        {
            Reset();
        }

        #endregion

        #region 方法

        /// <summary>
        /// 重置状态
        /// </summary>
        public override void Reset()
        {
            IsConnected = false;
            SensorStatus = SensorStatus.Offline;
            CurrentValue = 0;
            MinValue = 0;
            MaxValue = 0;
            AverageValue = 0;
            IsCalibrated = false;
            LastCalibrationTime = DateTime.MinValue;
            LastUpdateTime = DateTime.MinValue;
            ErrorMessage = string.Empty;
            IsSampling = false;
            SamplingRate = 0;
            SampleCount = 0;
        }

        /// <summary>
        /// 设置错误信息
        /// </summary>
        /// <param name="error">错误信息</param>
        public void SetError(string error)
        {
            ErrorMessage = error;
            SensorStatus = SensorStatus.Error;
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 清除错误信息
        /// </summary>
        public void ClearError()
        {
            ErrorMessage = string.Empty;
            if (SensorStatus == SensorStatus.Error)
            {
                SensorStatus = IsConnected ? SensorStatus.Ready : SensorStatus.Offline;
            }
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 更新测量值
        /// </summary>
        /// <param name="value">测量值</param>
        public void UpdateValue(double value)
        {
            CurrentValue = value;
            LastUpdateTime = DateTime.Now;
            SampleCount++;
        }

        /// <summary>
        /// 更新采样状态
        /// </summary>
        /// <param name="isSampling">是否正在采样</param>
        /// <param name="samplingRate">采样频率</param>
        public void UpdateSamplingStatus(bool isSampling, double samplingRate)
        {
            IsSampling = isSampling;
            SamplingRate = samplingRate;
            SensorStatus = isSampling ? SensorStatus.Sampling : SensorStatus.Ready;
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 更新校准状态
        /// </summary>
        /// <param name="isCalibrated">是否已校准</param>
        public void UpdateCalibrationStatus(bool isCalibrated)
        {
            IsCalibrated = isCalibrated;
            if (isCalibrated)
            {
                LastCalibrationTime = DateTime.Now;
            }
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 重置统计数据
        /// </summary>
        public void ResetStatistics()
        {
            MinValue = CurrentValue;
            MaxValue = CurrentValue;
            AverageValue = CurrentValue;
            SampleCount = 0;
        }

        /// <summary>
        /// 获取状态摘要
        /// </summary>
        /// <returns>状态摘要字符串</returns>
        public override string GetStatusSummary()
        {
            return $"传感器 - {StatusText} | 范围: [{MinValue:F3}, {MaxValue:F3}] | 平均: {AverageValue:F3}";
        }

        /// <summary>
        /// 检查是否需要更新状态
        /// </summary>
        /// <param name="intervalSeconds">更新间隔(秒)</param>
        /// <returns>是否需要更新</returns>
        public bool ShouldUpdate(double intervalSeconds = 1.0)
        {
            if (LastUpdateTime == DateTime.MinValue)
                return true;

            return (DateTime.Now - LastUpdateTime).TotalSeconds >= intervalSeconds;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 更新统计数据
        /// </summary>
        /// <param name="value">新的测量值</param>
        private void UpdateStatistics(double value)
        {
            if (SampleCount == 0)
            {
                MinValue = value;
                MaxValue = value;
                AverageValue = value;
            }
            else
            {
                if (value < MinValue) MinValue = value;
                if (value > MaxValue) MaxValue = value;
                
                // 计算移动平均值
                AverageValue = (AverageValue * SampleCount + value) / (SampleCount + 1);
            }
        }

        #endregion
    }
}
