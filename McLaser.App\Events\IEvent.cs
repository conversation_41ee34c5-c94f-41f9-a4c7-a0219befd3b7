using System;

namespace McLaser.App.Events
{
    /// <summary>
    /// 事件基接口
    /// 用于标识事件类型
    /// </summary>
    public interface IEvent
    {
        /// <summary>
        /// 事件标识
        /// </summary>
        string Id { get; }

        /// <summary>
        /// 事件时间戳
        /// </summary>
        DateTime Timestamp { get; }

        /// <summary>
        /// 事件来源
        /// </summary>
        string Source { get; }

        /// <summary>
        /// 事件优先级
        /// </summary>
        EventPriority Priority { get; }
    }

    /// <summary>
    /// 事件优先级枚举
    /// </summary>
    public enum EventPriority
    {
        /// <summary>
        /// 低优先级
        /// </summary>
        Low = 0,

        /// <summary>
        /// 普通优先级
        /// </summary>
        Normal = 1,

        /// <summary>
        /// 高优先级
        /// </summary>
        High = 2,

        /// <summary>
        /// 紧急优先级
        /// </summary>
        Critical = 3
    }
}
