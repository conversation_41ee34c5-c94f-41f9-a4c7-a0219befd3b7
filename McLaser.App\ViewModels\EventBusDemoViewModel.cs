using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using McLaser.Core.Common;
using McLaser.Core.EventBus;
using McLaser.Core.Framework.Logging;
using McLaser.App.Events;

namespace McLaser.App.ViewModels
{
    /// <summary>
    /// EventBus演示ViewModel
    /// 展示事件总线的发布/订阅机制和各种功能特性
    /// </summary>
    public class EventBusDemoViewModel : ViewModelBase
    {
        #region 私有字段

        private readonly IEventBus _eventBus;
        private readonly ILogger _logger;

        private string _eventBusStatus = "未初始化";
        private string _statusMessage = "就绪";
        private int _subscriberCount = 0;
        private int _publishedEventCount = 0;
        private int _processingEventCount = 0;
        private int _exceptionCount = 0;

        // 设备事件相关
        private string _deviceId = "Device001";
        private string _deviceName = "激光器设备";
        private string _selectedDeviceStatus = "在线";

        // 系统通知相关
        private string _selectedNotificationType = "信息";
        private string _notificationTitle = "系统通知";
        private string _notificationMessage = "这是一个测试通知消息";

        // 用户操作相关
        private string _userName = "Admin";
        private string _selectedActionType = "登录";
        private string _actionDescription = "用户登录系统";

        // 事件处理选项
        private bool _enableAsyncProcessing = true;
        private bool _enableEventFiltering = false;
        private bool _enableEventInterception = false;
        private bool _enableEventPersistence = false;
        private int _handlingTimeoutSeconds = 30;

        // 统计信息
        private int _totalEventsPublished = 0;
        private int _totalEventsHandled = 0;
        private double _averageHandlingTime = 0;
        private int _activeSubscribers = 0;
        private double _eventsPerSecond = 0;
        private DateTime _lastEventTime = DateTime.MinValue;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public EventBusDemoViewModel()
        {
            _logger = new McLaser.App.Core.ConsoleLogger("EventBusDemo");
            _eventBus = new EventBus();

            InitializeCommands();
            InitializeData();
            InitializeEventBus();
        }

        /// <summary>
        /// 构造函数（依赖注入）
        /// </summary>
        /// <param name="eventBus">事件总线</param>
        /// <param name="logger">日志服务</param>
        public EventBusDemoViewModel(IEventBus eventBus, ILogger logger)
        {
            _eventBus = eventBus ?? throw new ArgumentNullException(nameof(eventBus));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            InitializeCommands();
            InitializeData();
            InitializeEventBus();
        }

        #endregion

        #region 属性

        /// <summary>
        /// EventBus状态
        /// </summary>
        public string EventBusStatus
        {
            get => _eventBusStatus;
            set => SetProperty(ref _eventBusStatus, value);
        }

        /// <summary>
        /// 状态消息
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        /// <summary>
        /// 订阅者数量
        /// </summary>
        public int SubscriberCount
        {
            get => _subscriberCount;
            set => SetProperty(ref _subscriberCount, value);
        }

        /// <summary>
        /// 已发布事件数量
        /// </summary>
        public int PublishedEventCount
        {
            get => _publishedEventCount;
            set => SetProperty(ref _publishedEventCount, value);
        }

        /// <summary>
        /// 处理中事件数量
        /// </summary>
        public int ProcessingEventCount
        {
            get => _processingEventCount;
            set => SetProperty(ref _processingEventCount, value);
        }

        /// <summary>
        /// 异常次数
        /// </summary>
        public int ExceptionCount
        {
            get => _exceptionCount;
            set => SetProperty(ref _exceptionCount, value);
        }

        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceId
        {
            get => _deviceId;
            set => SetProperty(ref _deviceId, value);
        }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string DeviceName
        {
            get => _deviceName;
            set => SetProperty(ref _deviceName, value);
        }

        /// <summary>
        /// 选中的设备状态
        /// </summary>
        public string SelectedDeviceStatus
        {
            get => _selectedDeviceStatus;
            set => SetProperty(ref _selectedDeviceStatus, value);
        }

        /// <summary>
        /// 设备状态列表
        /// </summary>
        public ObservableCollection<string> DeviceStatusList { get; } = new ObservableCollection<string>
        {
            "在线", "离线", "故障", "维护", "待机"
        };

        /// <summary>
        /// 选中的通知类型
        /// </summary>
        public string SelectedNotificationType
        {
            get => _selectedNotificationType;
            set => SetProperty(ref _selectedNotificationType, value);
        }

        /// <summary>
        /// 通知类型列表
        /// </summary>
        public ObservableCollection<string> NotificationTypeList { get; } = new ObservableCollection<string>
        {
            "信息", "警告", "错误", "成功"
        };

        /// <summary>
        /// 通知标题
        /// </summary>
        public string NotificationTitle
        {
            get => _notificationTitle;
            set => SetProperty(ref _notificationTitle, value);
        }

        /// <summary>
        /// 通知消息
        /// </summary>
        public string NotificationMessage
        {
            get => _notificationMessage;
            set => SetProperty(ref _notificationMessage, value);
        }

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName
        {
            get => _userName;
            set => SetProperty(ref _userName, value);
        }

        /// <summary>
        /// 选中的操作类型
        /// </summary>
        public string SelectedActionType
        {
            get => _selectedActionType;
            set => SetProperty(ref _selectedActionType, value);
        }

        /// <summary>
        /// 操作类型列表
        /// </summary>
        public ObservableCollection<string> ActionTypeList { get; } = new ObservableCollection<string>
        {
            "登录", "登出", "创建", "编辑", "删除", "查看", "导出", "导入"
        };

        /// <summary>
        /// 操作描述
        /// </summary>
        public string ActionDescription
        {
            get => _actionDescription;
            set => SetProperty(ref _actionDescription, value);
        }

        /// <summary>
        /// 启用异步处理
        /// </summary>
        public bool EnableAsyncProcessing
        {
            get => _enableAsyncProcessing;
            set => SetProperty(ref _enableAsyncProcessing, value);
        }

        /// <summary>
        /// 启用事件过滤
        /// </summary>
        public bool EnableEventFiltering
        {
            get => _enableEventFiltering;
            set => SetProperty(ref _enableEventFiltering, value);
        }

        /// <summary>
        /// 启用事件拦截
        /// </summary>
        public bool EnableEventInterception
        {
            get => _enableEventInterception;
            set => SetProperty(ref _enableEventInterception, value);
        }

        /// <summary>
        /// 启用事件持久化
        /// </summary>
        public bool EnableEventPersistence
        {
            get => _enableEventPersistence;
            set => SetProperty(ref _enableEventPersistence, value);
        }

        /// <summary>
        /// 处理超时秒数
        /// </summary>
        public int HandlingTimeoutSeconds
        {
            get => _handlingTimeoutSeconds;
            set => SetProperty(ref _handlingTimeoutSeconds, value);
        }

        /// <summary>
        /// 总发布事件数
        /// </summary>
        public int TotalEventsPublished
        {
            get => _totalEventsPublished;
            set => SetProperty(ref _totalEventsPublished, value);
        }

        /// <summary>
        /// 总处理事件数
        /// </summary>
        public int TotalEventsHandled
        {
            get => _totalEventsHandled;
            set => SetProperty(ref _totalEventsHandled, value);
        }

        /// <summary>
        /// 平均处理时间
        /// </summary>
        public double AverageHandlingTime
        {
            get => _averageHandlingTime;
            set => SetProperty(ref _averageHandlingTime, value);
        }

        /// <summary>
        /// 活跃订阅者数
        /// </summary>
        public int ActiveSubscribers
        {
            get => _activeSubscribers;
            set => SetProperty(ref _activeSubscribers, value);
        }

        /// <summary>
        /// 每秒事件数
        /// </summary>
        public double EventsPerSecond
        {
            get => _eventsPerSecond;
            set => SetProperty(ref _eventsPerSecond, value);
        }

        /// <summary>
        /// 最后事件时间
        /// </summary>
        public DateTime LastEventTime
        {
            get => _lastEventTime;
            set => SetProperty(ref _lastEventTime, value);
        }

        /// <summary>
        /// 事件日志
        /// </summary>
        public ObservableCollection<EventLogEntry> EventLogs { get; } = new ObservableCollection<EventLogEntry>();

        /// <summary>
        /// 活跃订阅者列表
        /// </summary>
        public ObservableCollection<SubscriberInfo> ActiveSubscribersList { get; } = new ObservableCollection<SubscriberInfo>();

        #endregion

        #region 命令

        /// <summary>
        /// 初始化EventBus命令
        /// </summary>
        public ICommand InitializeEventBusCommand { get; private set; } = null!;

        /// <summary>
        /// 发布设备事件命令
        /// </summary>
        public ICommand PublishDeviceEventCommand { get; private set; } = null!;

        /// <summary>
        /// 发布系统通知命令
        /// </summary>
        public ICommand PublishSystemNotificationCommand { get; private set; } = null!;

        /// <summary>
        /// 发布用户操作事件命令
        /// </summary>
        public ICommand PublishUserActionEventCommand { get; private set; } = null!;

        /// <summary>
        /// 清空日志命令
        /// </summary>
        public ICommand ClearLogCommand { get; private set; } = null!;

        /// <summary>
        /// 导出日志命令
        /// </summary>
        public ICommand ExportLogCommand { get; private set; } = null!;

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化命令
        /// </summary>
        private void InitializeCommands()
        {
            InitializeEventBusCommand = new RelayCommand(async () => await InitializeEventBusAsync());
            PublishDeviceEventCommand = new RelayCommand(async () => await PublishDeviceEventAsync());
            PublishSystemNotificationCommand = new RelayCommand(async () => await PublishSystemNotificationAsync());
            PublishUserActionEventCommand = new RelayCommand(async () => await PublishUserActionEventAsync());
            ClearLogCommand = new RelayCommand(ClearLog);
            ExportLogCommand = new RelayCommand(async () => await ExportLogAsync());
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            // 添加示例日志
            AddEventLog("系统", "EventBus演示系统启动");

            // 初始化统计信息
            UpdateStatistics();
        }

        /// <summary>
        /// 初始化EventBus
        /// </summary>
        private void InitializeEventBus()
        {
            try
            {
                // 订阅各种事件类型
                SubscribeToEvents();

                EventBusStatus = "已初始化";
                StatusMessage = "EventBus初始化完成";
                AddEventLog("系统", "EventBus初始化成功");

                UpdateSubscriberCount();
            }
            catch (Exception ex)
            {
                _logger.Error($"EventBus初始化失败: {ex.Message}", ex);
                EventBusStatus = "初始化失败";
                StatusMessage = $"初始化失败: {ex.Message}";
                ExceptionCount++;
            }
        }

        /// <summary>
        /// 订阅事件
        /// </summary>
        private void SubscribeToEvents()
        {
            // 订阅设备状态变更事件
            var deviceHandler = new DelegateEventHandler<DeviceStatusChangedEvent>(async evt => OnDeviceStatusChanged(evt), "设备状态监听器");
            _eventBus.Subscribe<DeviceStatusChangedEvent>(deviceHandler);
            AddSubscriber("设备状态监听器", "DeviceStatusChangedEvent", 0);

            // 订阅系统通知事件
            var notificationHandler = new DelegateEventHandler<SystemNotificationEvent>(async evt => OnSystemNotification(evt), "系统通知监听器");
            _eventBus.Subscribe<SystemNotificationEvent>(notificationHandler);
            AddSubscriber("系统通知监听器", "SystemNotificationEvent", 0);

            // 订阅用户操作事件
            var userActionHandler = new DelegateEventHandler<UserActionEvent>(async evt => OnUserAction(evt), "用户操作监听器");
            _eventBus.Subscribe<UserActionEvent>(userActionHandler);
            AddSubscriber("用户操作监听器", "UserActionEvent", 0);

            // 订阅异常事件
            var exceptionHandler = new DelegateEventHandler<ExceptionEvent>(async evt => OnException(evt), "异常监听器");
            _eventBus.Subscribe<ExceptionEvent>(exceptionHandler);
            AddSubscriber("异常监听器", "ExceptionEvent", 0);
        }

        /// <summary>
        /// 异步初始化EventBus
        /// </summary>
        private async Task InitializeEventBusAsync()
        {
            try
            {
                StatusMessage = "正在重新初始化EventBus...";

                await Task.Run(() =>
                {
                    // 清理现有订阅
                    ClearSubscribers();

                    // 重新初始化
                    InitializeEventBus();
                });

                StatusMessage = "EventBus重新初始化完成";
                AddEventLog("系统", "EventBus重新初始化成功");
            }
            catch (Exception ex)
            {
                _logger.Error($"EventBus重新初始化失败: {ex.Message}", ex);
                StatusMessage = $"重新初始化失败: {ex.Message}";
                ExceptionCount++;
            }
        }

        /// <summary>
        /// 发布设备事件
        /// </summary>
        private async Task PublishDeviceEventAsync()
        {
            try
            {
                var deviceEvent = new DeviceStatusChangedEvent
                {
                    DeviceId = DeviceId,
                    DeviceName = DeviceName,
                    OldStatus = "未知",
                    NewStatus = SelectedDeviceStatus,
                    Timestamp = DateTime.Now,
                    Message = $"设备 {DeviceName} 状态变更为 {SelectedDeviceStatus}"
                };

                if (EnableAsyncProcessing)
                {
                    await _eventBus.PublishAsync(deviceEvent);
                }
                else
                {
                    _eventBus.Publish(deviceEvent);
                }

                PublishedEventCount++;
                TotalEventsPublished++;
                LastEventTime = DateTime.Now;

                AddEventLog("设备事件", $"发布设备状态变更事件: {DeviceName} -> {SelectedDeviceStatus}");
                StatusMessage = "设备事件发布成功";

                UpdateStatistics();
            }
            catch (Exception ex)
            {
                _logger.Error($"发布设备事件失败: {ex.Message}", ex);
                StatusMessage = $"发布设备事件失败: {ex.Message}";
                ExceptionCount++;
            }
        }

        /// <summary>
        /// 发布系统通知事件
        /// </summary>
        private async Task PublishSystemNotificationAsync()
        {
            try
            {
                var notificationEvent = new SystemNotificationEvent
                {
                    Type = SelectedNotificationType,
                    Title = NotificationTitle,
                    Message = NotificationMessage,
                    Timestamp = DateTime.Now,
                    Source = "EventBusDemo"
                };

                if (EnableAsyncProcessing)
                {
                    await _eventBus.PublishAsync(notificationEvent);
                }
                else
                {
                    _eventBus.Publish(notificationEvent);
                }

                PublishedEventCount++;
                TotalEventsPublished++;
                LastEventTime = DateTime.Now;

                AddEventLog("系统通知", $"发布系统通知: {NotificationTitle}");
                StatusMessage = "系统通知发布成功";

                UpdateStatistics();
            }
            catch (Exception ex)
            {
                _logger.Error($"发布系统通知失败: {ex.Message}", ex);
                StatusMessage = $"发布系统通知失败: {ex.Message}";
                ExceptionCount++;
            }
        }

        /// <summary>
        /// 发布用户操作事件
        /// </summary>
        private async Task PublishUserActionEventAsync()
        {
            try
            {
                var userActionEvent = new UserActionEvent
                {
                    UserName = UserName,
                    ActionType = SelectedActionType,
                    Description = ActionDescription,
                    Timestamp = DateTime.Now,
                    IPAddress = "127.0.0.1",
                    SessionId = Guid.NewGuid().ToString()
                };

                if (EnableAsyncProcessing)
                {
                    await _eventBus.PublishAsync(userActionEvent);
                }
                else
                {
                    _eventBus.Publish(userActionEvent);
                }

                PublishedEventCount++;
                TotalEventsPublished++;
                LastEventTime = DateTime.Now;

                AddEventLog("用户操作", $"发布用户操作事件: {UserName} - {SelectedActionType}");
                StatusMessage = "用户操作事件发布成功";

                UpdateStatistics();
            }
            catch (Exception ex)
            {
                _logger.Error($"发布用户操作事件失败: {ex.Message}", ex);
                StatusMessage = $"发布用户操作事件失败: {ex.Message}";
                ExceptionCount++;
            }
        }

        /// <summary>
        /// 清空日志
        /// </summary>
        private void ClearLog()
        {
            EventLogs.Clear();
            AddEventLog("系统", "事件日志已清空");
            StatusMessage = "日志已清空";
        }

        /// <summary>
        /// 导出日志
        /// </summary>
        private async Task ExportLogAsync()
        {
            try
            {
                StatusMessage = "正在导出日志...";

                await Task.Run(() =>
                {
                    var fileName = $"EventBusLog_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                    var filePath = System.IO.Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), fileName);

                    var logContent = string.Join(Environment.NewLine,
                        EventLogs.Select(log => $"{log.Timestamp:yyyy-MM-dd HH:mm:ss} [{log.EventType}] {log.Message}"));

                    System.IO.File.WriteAllText(filePath, logContent);

                    StatusMessage = $"日志已导出到: {filePath}";
                });

                AddEventLog("系统", "事件日志导出完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"导出日志失败: {ex.Message}", ex);
                StatusMessage = $"导出日志失败: {ex.Message}";
                ExceptionCount++;
            }
        }

        /// <summary>
        /// 添加事件日志
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="message">消息</param>
        private void AddEventLog(string eventType, string message)
        {
            var logEntry = new EventLogEntry
            {
                Timestamp = DateTime.Now,
                EventType = eventType,
                Message = message
            };

            // 在UI线程中添加日志
            System.Windows.Application.Current?.Dispatcher.Invoke(() =>
            {
                EventLogs.Insert(0, logEntry);

                // 限制日志数量，保留最新的1000条
                while (EventLogs.Count > 1000)
                {
                    EventLogs.RemoveAt(EventLogs.Count - 1);
                }
            });
        }

        /// <summary>
        /// 添加订阅者
        /// </summary>
        /// <param name="name">订阅者名称</param>
        /// <param name="eventType">事件类型</param>
        /// <param name="handledCount">处理次数</param>
        private void AddSubscriber(string name, string eventType, int handledCount)
        {
            var subscriber = new SubscriberInfo
            {
                Name = name,
                EventType = eventType,
                HandledCount = handledCount
            };

            System.Windows.Application.Current?.Dispatcher.Invoke(() =>
            {
                ActiveSubscribersList.Add(subscriber);
            });
        }

        /// <summary>
        /// 清理订阅者
        /// </summary>
        private void ClearSubscribers()
        {
            System.Windows.Application.Current?.Dispatcher.Invoke(() =>
            {
                ActiveSubscribersList.Clear();
            });
        }

        /// <summary>
        /// 更新订阅者数量
        /// </summary>
        private void UpdateSubscriberCount()
        {
            SubscriberCount = ActiveSubscribersList.Count;
            ActiveSubscribers = ActiveSubscribersList.Count;
        }

        /// <summary>
        /// 更新统计信息
        /// </summary>
        private void UpdateStatistics()
        {
            // 计算每秒事件数
            var timeSpan = DateTime.Now - LastEventTime;
            if (timeSpan.TotalSeconds > 0)
            {
                EventsPerSecond = TotalEventsPublished / timeSpan.TotalSeconds;
            }

            // 更新其他统计信息
            UpdateSubscriberCount();
        }
        #endregion

        #region 事件处理方法

        /// <summary>
        /// 处理设备状态变更事件
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnDeviceStatusChanged(DeviceStatusChangedEvent eventData)
        {
            try
            {
                ProcessingEventCount++;

                var message = $"设备 {eventData.DeviceName} 状态从 {eventData.OldStatus} 变更为 {eventData.NewStatus}";
                AddEventLog("设备状态", message);

                // 更新订阅者处理次数
                UpdateSubscriberHandledCount("设备状态监听器");

                TotalEventsHandled++;
                ProcessingEventCount--;

                _logger.Info($"处理设备状态变更事件: {message}");
            }
            catch (Exception ex)
            {
                ProcessingEventCount--;
                ExceptionCount++;
                _logger.Error($"处理设备状态变更事件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 处理系统通知事件
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnSystemNotification(SystemNotificationEvent eventData)
        {
            try
            {
                ProcessingEventCount++;

                var message = $"[{eventData.Type}] {eventData.Title}: {eventData.Message}";
                AddEventLog("系统通知", message);

                // 更新订阅者处理次数
                UpdateSubscriberHandledCount("系统通知监听器");

                TotalEventsHandled++;
                ProcessingEventCount--;

                _logger.Info($"处理系统通知事件: {message}");
            }
            catch (Exception ex)
            {
                ProcessingEventCount--;
                ExceptionCount++;
                _logger.Error($"处理系统通知事件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 处理用户操作事件
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnUserAction(UserActionEvent eventData)
        {
            try
            {
                ProcessingEventCount++;

                var message = $"用户 {eventData.UserName} 执行操作: {eventData.ActionType} - {eventData.Description}";
                AddEventLog("用户操作", message);

                // 更新订阅者处理次数
                UpdateSubscriberHandledCount("用户操作监听器");

                TotalEventsHandled++;
                ProcessingEventCount--;

                _logger.Info($"处理用户操作事件: {message}");
            }
            catch (Exception ex)
            {
                ProcessingEventCount--;
                ExceptionCount++;
                _logger.Error($"处理用户操作事件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 处理异常事件
        /// </summary>
        /// <param name="eventData">事件数据</param>
        private void OnException(ExceptionEvent eventData)
        {
            try
            {
                ProcessingEventCount++;

                var message = $"异常: {eventData.Message} (来源: {eventData.Source})";
                AddEventLog("异常", message);

                // 更新订阅者处理次数
                UpdateSubscriberHandledCount("异常监听器");

                TotalEventsHandled++;
                ProcessingEventCount--;
                ExceptionCount++;

                _logger.Error($"处理异常事件: {message}");
            }
            catch (Exception ex)
            {
                ProcessingEventCount--;
                ExceptionCount++;
                _logger.Error($"处理异常事件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 更新订阅者处理次数
        /// </summary>
        /// <param name="subscriberName">订阅者名称</param>
        private void UpdateSubscriberHandledCount(string subscriberName)
        {
            System.Windows.Application.Current?.Dispatcher.Invoke(() =>
            {
                var subscriber = ActiveSubscribersList.FirstOrDefault(s => s.Name == subscriberName);
                if (subscriber != null)
                {
                    subscriber.HandledCount++;
                }
            });
        }

        #endregion

        #region IDisposable实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                // 注意：实际的Unsubscribe需要传递订阅时返回的ID或处理器实例
                // 这里简化处理，实际应用中应该保存订阅ID

                AddEventLog("系统", "EventBus演示系统关闭");
                _logger.Info("EventBus演示系统已关闭");
            }
            catch (Exception ex)
            {
                _logger.Error($"释放EventBus资源失败: {ex.Message}", ex);
            }
        }

        #endregion
    }

    #region 辅助类

    /// <summary>
    /// 事件日志条目
    /// </summary>
    public class EventLogEntry
    {
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 事件类型
        /// </summary>
        public string EventType { get; set; } = string.Empty;

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// 订阅者信息
    /// </summary>
    public class SubscriberInfo : ViewModelBase
    {
        private int _handledCount;

        /// <summary>
        /// 订阅者名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 事件类型
        /// </summary>
        public string EventType { get; set; } = string.Empty;

        /// <summary>
        /// 处理次数
        /// </summary>
        public int HandledCount
        {
            get => _handledCount;
            set => SetProperty(ref _handledCount, value);
        }
    }

    #endregion
}
