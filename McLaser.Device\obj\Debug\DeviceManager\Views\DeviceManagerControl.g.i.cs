﻿#pragma checksum "..\..\..\..\DeviceManager\Views\DeviceManagerControl.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "35DF58963527155C372C670F5EF471638316037D2CECD68D60C2C49A8FFE1C8C"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using McLaser.Device;
using McLaser.Device.Converters;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace McLaser.Device.Views {
    
    
    /// <summary>
    /// DeviceManagerControl
    /// </summary>
    public partial class DeviceManagerControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 205 "..\..\..\..\DeviceManager\Views\DeviceManagerControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ColumnDefinition DeviceTypeColumn;
        
        #line default
        #line hidden
        
        
        #line 213 "..\..\..\..\DeviceManager\Views\DeviceManagerControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Expander DeviceTypeExpander;
        
        #line default
        #line hidden
        
        
        #line 489 "..\..\..\..\DeviceManager\Views\DeviceManagerControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView DevicesListView;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/McLaser.Device;component/devicemanager/views/devicemanagercontrol.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\DeviceManager\Views\DeviceManagerControl.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 10 "..\..\..\..\DeviceManager\Views\DeviceManagerControl.xaml"
            ((McLaser.Device.Views.DeviceManagerControl)(target)).Loaded += new System.Windows.RoutedEventHandler(this.DeviceManagerControl_Loaded);
            
            #line default
            #line hidden
            
            #line 11 "..\..\..\..\DeviceManager\Views\DeviceManagerControl.xaml"
            ((McLaser.Device.Views.DeviceManagerControl)(target)).Unloaded += new System.Windows.RoutedEventHandler(this.DeviceManagerControl_Unloaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.DeviceTypeColumn = ((System.Windows.Controls.ColumnDefinition)(target));
            return;
            case 3:
            this.DeviceTypeExpander = ((System.Windows.Controls.Expander)(target));
            
            #line 219 "..\..\..\..\DeviceManager\Views\DeviceManagerControl.xaml"
            this.DeviceTypeExpander.Expanded += new System.Windows.RoutedEventHandler(this.DeviceTypeExpander_Expanded);
            
            #line default
            #line hidden
            
            #line 220 "..\..\..\..\DeviceManager\Views\DeviceManagerControl.xaml"
            this.DeviceTypeExpander.Collapsed += new System.Windows.RoutedEventHandler(this.DeviceTypeExpander_Collapsed);
            
            #line default
            #line hidden
            return;
            case 5:
            this.DevicesListView = ((System.Windows.Controls.ListView)(target));
            
            #line 494 "..\..\..\..\DeviceManager\Views\DeviceManagerControl.xaml"
            this.DevicesListView.Drop += new System.Windows.DragEventHandler(this.DeviceGroup_Drop);
            
            #line default
            #line hidden
            
            #line 495 "..\..\..\..\DeviceManager\Views\DeviceManagerControl.xaml"
            this.DevicesListView.DragOver += new System.Windows.DragEventHandler(this.DeviceGroup_DragOver);
            
            #line default
            #line hidden
            
            #line 496 "..\..\..\..\DeviceManager\Views\DeviceManagerControl.xaml"
            this.DevicesListView.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DevicesListView_SelectionChanged);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 4:
            
            #line 375 "..\..\..\..\DeviceManager\Views\DeviceManagerControl.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.DeviceType_MouseLeftButtonDown);
            
            #line default
            #line hidden
            
            #line 376 "..\..\..\..\DeviceManager\Views\DeviceManagerControl.xaml"
            ((System.Windows.Controls.Border)(target)).MouseMove += new System.Windows.Input.MouseEventHandler(this.DeviceType_MouseMove);
            
            #line default
            #line hidden
            break;
            case 6:
            
            #line 538 "..\..\..\..\DeviceManager\Views\DeviceManagerControl.xaml"
            ((System.Windows.Controls.Grid)(target)).MouseRightButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.DeviceItem_RightClick);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

