using System;
using System.ComponentModel;
using McLaser.Device;

namespace McLaser.Devices.Laser.Coherent
{
    /// <summary>
    /// Coherent激光器状态类
    /// 用于记录和监控Coherent激光器的运行状态信息
    /// </summary>
    [Serializable]
    public class StatusCoherent : StatusLaser
    {
        #region 私有字段

        private string _modelNumber = "未知";
        private string _serialNumber = "未知";
        private double _tubeVoltage = 0;
        private double _tubeCurrent = 0;
        private double _gasTemperature = 0;
        private double _waterTemperature = 0;
        private double _outputPower = 0;
        private bool _isWaterFlowOK = false;
        private bool _isGasPressureOK = false;
        private bool _isDoorClosed = false;
        private string _faultCode = string.Empty;
        private int _tubeHours = 0;

        #endregion

        #region 属性

        /// <summary>
        /// 型号
        /// </summary>
        [Category("Coherent信息"), DisplayName("型号")]
        public string ModelNumber
        {
            get => _modelNumber;
            set
            {
                if (_modelNumber != value)
                {
                    _modelNumber = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 序列号
        /// </summary>
        [Category("Coherent信息"), DisplayName("序列号")]
        public string SerialNumber
        {
            get => _serialNumber;
            set
            {
                if (_serialNumber != value)
                {
                    _serialNumber = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 激光管电压(V)
        /// </summary>
        [Category("Coherent电气"), DisplayName("激光管电压(V)")]
        public double TubeVoltage
        {
            get => _tubeVoltage;
            set
            {
                if (Math.Abs(_tubeVoltage - value) > 0.1)
                {
                    _tubeVoltage = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 激光管电流(mA)
        /// </summary>
        [Category("Coherent电气"), DisplayName("激光管电流(mA)")]
        public double TubeCurrent
        {
            get => _tubeCurrent;
            set
            {
                if (Math.Abs(_tubeCurrent - value) > 0.1)
                {
                    _tubeCurrent = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 气体温度(℃)
        /// </summary>
        [Category("Coherent温度"), DisplayName("气体温度(℃)")]
        public double GasTemperature
        {
            get => _gasTemperature;
            set
            {
                if (Math.Abs(_gasTemperature - value) > 0.1)
                {
                    _gasTemperature = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 水温(℃)
        /// </summary>
        [Category("Coherent温度"), DisplayName("水温(℃)")]
        public double WaterTemperature
        {
            get => _waterTemperature;
            set
            {
                if (Math.Abs(_waterTemperature - value) > 0.1)
                {
                    _waterTemperature = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 输出功率(W)
        /// </summary>
        [Category("Coherent功率"), DisplayName("输出功率(W)")]
        public double OutputPower
        {
            get => _outputPower;
            set
            {
                if (Math.Abs(_outputPower - value) > 0.001)
                {
                    _outputPower = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 水流状态
        /// </summary>
        [Category("Coherent状态"), DisplayName("水流状态")]
        public bool IsWaterFlowOK
        {
            get => _isWaterFlowOK;
            set
            {
                if (_isWaterFlowOK != value)
                {
                    _isWaterFlowOK = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// 气压状态
        /// </summary>
        [Category("Coherent状态"), DisplayName("气压状态")]
        public bool IsGasPressureOK
        {
            get => _isGasPressureOK;
            set
            {
                if (_isGasPressureOK != value)
                {
                    _isGasPressureOK = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// 门关闭状态
        /// </summary>
        [Category("Coherent状态"), DisplayName("门关闭状态")]
        public bool IsDoorClosed
        {
            get => _isDoorClosed;
            set
            {
                if (_isDoorClosed != value)
                {
                    _isDoorClosed = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// 故障代码
        /// </summary>
        [Category("Coherent状态"), DisplayName("故障代码")]
        public string FaultCode
        {
            get => _faultCode;
            set
            {
                if (_faultCode != value)
                {
                    _faultCode = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// 激光管使用时间(小时)
        /// </summary>
        [Category("Coherent状态"), DisplayName("激光管使用时间(小时)")]
        public int TubeHours
        {
            get => _tubeHours;
            set
            {
                if (_tubeHours != value)
                {
                    _tubeHours = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 状态文本描述
        /// </summary>
        [Category("Coherent状态"), DisplayName("状态描述")]
        public override string StatusText
        {
            get
            {
                if (!string.IsNullOrEmpty(_faultCode) && _faultCode != "NO_FAULT")
                    return $"故障: {_faultCode}";

                if (!string.IsNullOrEmpty(ErrorMessage))
                    return $"错误: {ErrorMessage}";

                if (!IsConnected)
                    return "设备未连接";

                if (!_isWaterFlowOK)
                    return "水流异常";

                if (!_isGasPressureOK)
                    return "气压异常";

                if (!_isDoorClosed)
                    return "门未关闭";

                if (IsLaserOn)
                    return $"激光开启 - 功率: {CurrentPower:F1}% ({_outputPower:F1}W)";

                return $"激光关闭 - 状态: {LaserStatus}";
            }
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public StatusCoherent()
        {
            Reset();
        }

        #endregion

        #region 方法

        /// <summary>
        /// 重置状态
        /// </summary>
        public override void Reset()
        {
            base.Reset();
            
            ModelNumber = "未知";
            SerialNumber = "未知";
            TubeVoltage = 0;
            TubeCurrent = 0;
            GasTemperature = 0;
            WaterTemperature = 0;
            OutputPower = 0;
            IsWaterFlowOK = false;
            IsGasPressureOK = false;
            IsDoorClosed = false;
            FaultCode = string.Empty;
            TubeHours = 0;
        }

        /// <summary>
        /// 更新Coherent特有状态
        /// </summary>
        /// <param name="tubeVoltage">激光管电压</param>
        /// <param name="tubeCurrent">激光管电流</param>
        /// <param name="gasTemp">气体温度</param>
        /// <param name="waterTemp">水温</param>
        /// <param name="outputPower">输出功率</param>
        public void UpdateCoherentStatus(double tubeVoltage, double tubeCurrent, double gasTemp, double waterTemp, double outputPower)
        {
            TubeVoltage = tubeVoltage;
            TubeCurrent = tubeCurrent;
            GasTemperature = gasTemp;
            WaterTemperature = waterTemp;
            OutputPower = outputPower;
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 更新安全状态
        /// </summary>
        /// <param name="isWaterFlowOK">水流状态</param>
        /// <param name="isGasPressureOK">气压状态</param>
        /// <param name="isDoorClosed">门关闭状态</param>
        public void UpdateSafetyStatus(bool isWaterFlowOK, bool isGasPressureOK, bool isDoorClosed)
        {
            IsWaterFlowOK = isWaterFlowOK;
            IsGasPressureOK = isGasPressureOK;
            IsDoorClosed = isDoorClosed;
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 设置故障代码
        /// </summary>
        /// <param name="faultCode">故障代码</param>
        public void SetFault(string faultCode)
        {
            FaultCode = faultCode;
            if (!string.IsNullOrEmpty(faultCode) && faultCode != "NO_FAULT")
            {
                LaserStatus = LaserStatus.Error;
            }
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 清除故障
        /// </summary>
        public void ClearFault()
        {
            FaultCode = string.Empty;
            if (LaserStatus == LaserStatus.Error)
            {
                LaserStatus = IsConnected ? LaserStatus.Ready : LaserStatus.Offline;
            }
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 获取状态摘要
        /// </summary>
        /// <returns>状态摘要字符串</returns>
        public override string GetStatusSummary()
        {
            return $"Coherent激光器 - {StatusText} | 气体温度: {GasTemperature:F1}℃ | 水温: {WaterTemperature:F1}℃ | 输出功率: {OutputPower:F1}W";
        }

        /// <summary>
        /// 检查是否有安全问题
        /// </summary>
        /// <returns>是否有安全问题</returns>
        public bool HasSafetyIssue()
        {
            return !IsWaterFlowOK || !IsGasPressureOK || !IsDoorClosed || (!string.IsNullOrEmpty(FaultCode) && FaultCode != "NO_FAULT");
        }

        /// <summary>
        /// 获取安全状态描述
        /// </summary>
        /// <returns>安全状态描述</returns>
        public string GetSafetyStatusDescription()
        {
            var issues = new System.Collections.Generic.List<string>();
            
            if (!IsWaterFlowOK)
                issues.Add("水流异常");
            
            if (!IsGasPressureOK)
                issues.Add("气压异常");
            
            if (!IsDoorClosed)
                issues.Add("门未关闭");
            
            if (!string.IsNullOrEmpty(FaultCode) && FaultCode != "NO_FAULT")
                issues.Add($"故障: {FaultCode}");
            
            return issues.Count > 0 ? string.Join(", ", issues) : "安全状态正常";
        }

        /// <summary>
        /// 更新激光管信息
        /// </summary>
        /// <param name="tubeHours">激光管使用时间</param>
        public void UpdateTubeInfo(int tubeHours)
        {
            TubeHours = tubeHours;
            LastUpdateTime = DateTime.Now;
        }

        #endregion
    }
}
