using System.Windows.Controls;

namespace McLaser.Device
{
    /// <summary>
    /// 设备配置界面接口
    /// 定义设备配置UI的标准接口
    /// </summary>
    public interface IDeviceConfigurationUI
    {
        /// <summary>
        /// 获取配置界面
        /// </summary>
        /// <returns>配置界面控件</returns>
        UserControl GetConfigurationUI();

        /// <summary>
        /// 更新配置参数
        /// </summary>
        void UpdateConfiguration();
    }
}
