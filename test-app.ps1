# McLaser.App 测试脚本
Write-Host "开始测试 McLaser.App..." -ForegroundColor Green

# 检查项目文件
if (Test-Path "McLaser.App\McLaser.App.csproj") {
    Write-Host "✓ 项目文件存在" -ForegroundColor Green
} else {
    Write-Host "✗ 项目文件不存在" -ForegroundColor Red
    exit 1
}

# 编译项目
Write-Host "编译项目..." -ForegroundColor Yellow
$buildResult = dotnet build McLaser.App --verbosity minimal 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ 编译成功" -ForegroundColor Green
} else {
    Write-Host "✗ 编译失败:" -ForegroundColor Red
    Write-Host $buildResult -ForegroundColor Red
    exit 1
}

# 尝试运行应用程序（5秒后自动停止）
Write-Host "启动应用程序..." -ForegroundColor Yellow
$process = Start-Process -FilePath "dotnet" -ArgumentList "run --project McLaser.App" -PassThru -WindowStyle Hidden

# 等待5秒
Start-Sleep -Seconds 5

# 检查进程是否还在运行
if (!$process.HasExited) {
    Write-Host "✓ 应用程序成功启动" -ForegroundColor Green
    $process.Kill()
    Write-Host "应用程序已停止" -ForegroundColor Yellow
} else {
    Write-Host "✗ 应用程序启动失败，退出代码: $($process.ExitCode)" -ForegroundColor Red
}

Write-Host "测试完成" -ForegroundColor Green
