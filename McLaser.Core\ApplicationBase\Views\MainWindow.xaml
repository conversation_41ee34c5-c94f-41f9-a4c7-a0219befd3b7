<Window x:Class="McLaser.Core.ApplicationBase.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:McLaser.Core.ApplicationBase"
        Title="McLaser示例应用程序"
        Height="600" Width="900"
        MinHeight="500" MinWidth="700"
        WindowStartupLocation="CenterScreen">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- 主内容区域 - 页面容器 -->
        <ContentPresenter Grid.Row="0"
                          Content="{Binding CurrentPageContent}"
                          Margin="10"/>

        <!-- 底部导航栏 -->
        <local:BottomNavigationBar Grid.Row="1"
                                      DataContext="{Binding NavigationViewModel}"/>

        <!-- 状态栏 -->
        <!--<StatusBar Grid.Row="2">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}" />
            </StatusBarItem>
            <Separator />
            <StatusBarItem>
                <TextBlock Text="{Binding CurrentTheme, StringFormat='主题: {0}'}" />
            </StatusBarItem>
            <Separator />
            <StatusBarItem>
                <TextBlock Text="{Binding CurrentPageTitle, StringFormat='当前页面: {0}'}" />
            </StatusBarItem>
        </StatusBar>-->
    </Grid>
</Window>
