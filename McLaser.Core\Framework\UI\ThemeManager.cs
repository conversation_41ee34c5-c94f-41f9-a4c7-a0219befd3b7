using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel.Composition;
using System.IO;
using System.Linq;
using System.Windows;
using McLaser.Core.Framework.Configuration;

namespace McLaser.Core.Framework.UI
{
    /// <summary>
    /// 主题管理器
    /// 实现主题服务接口，提供完整的主题管理功能
    /// </summary>
    [Export(typeof(IThemeService))]
    public class ThemeManager : IThemeService
    {
        private readonly Dictionary<string, ThemeInfo> _themes;
        private readonly IConfigurationService _configurationService;
        private string _currentTheme;
        private const string DefaultThemeName = "Default";
        private const string ThemeConfigKey = "UI.CurrentTheme";

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="configurationService">配置服务</param>
        public ThemeManager()
        {
           // _configurationService = configurationService ?? throw new ArgumentNullException(nameof(configurationService));
            _themes = new Dictionary<string, ThemeInfo>();
            _currentTheme = DefaultThemeName;

            InitializeDefaultThemes();
        }

        /// <summary>
        /// 当前主题名称
        /// </summary>
        public string CurrentTheme => _currentTheme;

        /// <summary>
        /// 可用主题列表
        /// </summary>
        public IReadOnlyList<string> AvailableThemes => new ReadOnlyCollection<string>(_themes.Keys.ToList());

        /// <summary>
        /// 主题变更事件
        /// </summary>
        public event EventHandler<ThemeChangedEventArgs>? ThemeChanged;

        /// <summary>
        /// 应用主题
        /// </summary>
        /// <param name="themeName">主题名称</param>
        /// <returns>是否成功应用</returns>
        public bool ApplyTheme(string themeName)
        {
            if (string.IsNullOrWhiteSpace(themeName))
                return false;

            if (!_themes.ContainsKey(themeName))
                return false;

            try
            {
                var oldTheme = _currentTheme;
                var themeInfo = _themes[themeName];

                // 清除当前主题资源
                ClearCurrentThemeResources();

                // 应用新主题资源
                ApplyThemeResources(themeInfo.Resources);

                _currentTheme = themeName;

                // 触发主题变更事件
                ThemeChanged?.Invoke(this, new ThemeChangedEventArgs(oldTheme, themeName));

                return true;
            }
            catch (Exception ex)
            {
                // 记录错误并回滚
                System.Diagnostics.Debug.WriteLine($"应用主题失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取主题资源
        /// </summary>
        /// <param name="themeName">主题名称</param>
        /// <returns>主题资源字典</returns>
        public ResourceDictionary? GetThemeResources(string themeName)
        {
            if (string.IsNullOrWhiteSpace(themeName) || !_themes.ContainsKey(themeName))
                return null;

            return _themes[themeName].Resources;
        }

        /// <summary>
        /// 注册主题
        /// </summary>
        /// <param name="themeName">主题名称</param>
        /// <param name="resourceUri">资源URI</param>
        public void RegisterTheme(string themeName, Uri resourceUri)
        {
            if (string.IsNullOrWhiteSpace(themeName))
                throw new ArgumentException("主题名称不能为空", nameof(themeName));

            if (resourceUri == null)
                throw new ArgumentNullException(nameof(resourceUri));

            try
            {
                var resources = new ResourceDictionary { Source = resourceUri };
                RegisterTheme(themeName, resources);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"注册主题失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 注册主题
        /// </summary>
        /// <param name="themeName">主题名称</param>
        /// <param name="resources">资源字典</param>
        public void RegisterTheme(string themeName, ResourceDictionary resources)
        {
            if (string.IsNullOrWhiteSpace(themeName))
                throw new ArgumentException("主题名称不能为空", nameof(themeName));

            if (resources == null)
                throw new ArgumentNullException(nameof(resources));

            var themeInfo = new ThemeInfo
            {
                Name = themeName,
                Resources = resources,
                Preview = CreateThemePreview(themeName, resources)
            };

            _themes[themeName] = themeInfo;
        }

        /// <summary>
        /// 移除主题
        /// </summary>
        /// <param name="themeName">主题名称</param>
        /// <returns>是否成功移除</returns>
        public bool RemoveTheme(string themeName)
        {
            if (string.IsNullOrWhiteSpace(themeName) || themeName == DefaultThemeName)
                return false;

            if (!_themes.ContainsKey(themeName))
                return false;

            // 如果正在使用要删除的主题，切换到默认主题
            if (_currentTheme == themeName)
            {
                ApplyTheme(DefaultThemeName);
            }

            return _themes.Remove(themeName);
        }

        /// <summary>
        /// 保存当前主题设置
        /// </summary>
        public void SaveCurrentTheme()
        {
            try
            {
                _configurationService.SetValue(ThemeConfigKey, _currentTheme);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存主题设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载保存的主题设置
        /// </summary>
        public void LoadSavedTheme()
        {
            try
            {
                var savedTheme = _configurationService.GetValue<string>(ThemeConfigKey);
                if (!string.IsNullOrWhiteSpace(savedTheme) && _themes.ContainsKey(savedTheme))
                {
                    ApplyTheme(savedTheme);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载主题设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 重置为默认主题
        /// </summary>
        public void ResetToDefault()
        {
            ApplyTheme(DefaultThemeName);
        }

        /// <summary>
        /// 检查主题是否存在
        /// </summary>
        /// <param name="themeName">主题名称</param>
        /// <returns>是否存在</returns>
        public bool ThemeExists(string themeName)
        {
            return !string.IsNullOrWhiteSpace(themeName) && _themes.ContainsKey(themeName);
        }

        /// <summary>
        /// 获取主题预览
        /// </summary>
        /// <param name="themeName">主题名称</param>
        /// <returns>预览信息</returns>
        public ThemePreview? GetThemePreview(string themeName)
        {
            if (string.IsNullOrWhiteSpace(themeName) || !_themes.ContainsKey(themeName))
                return null;

            return _themes[themeName].Preview;
        }

        /// <summary>
        /// 初始化默认主题
        /// </summary>
        private void InitializeDefaultThemes()
        {
            // 注册默认主题
            var defaultResources = new ResourceDictionary();
            RegisterTheme(DefaultThemeName, defaultResources);

            // 可以在这里添加更多内置主题
            CreateLightTheme();
            CreateDarkTheme();
        }

        /// <summary>
        /// 创建浅色主题
        /// </summary>
        private void CreateLightTheme()
        {
            var lightResources = new ResourceDictionary();
            
            // 添加浅色主题的基本颜色
            lightResources["PrimaryColor"] = System.Windows.Media.Colors.DodgerBlue;
            lightResources["SecondaryColor"] = System.Windows.Media.Colors.LightGray;
            lightResources["BackgroundColor"] = System.Windows.Media.Colors.White;
            lightResources["ForegroundColor"] = System.Windows.Media.Colors.Black;
            
            RegisterTheme("Light", lightResources);
        }

        /// <summary>
        /// 创建深色主题
        /// </summary>
        private void CreateDarkTheme()
        {
            var darkResources = new ResourceDictionary();
            
            // 添加深色主题的基本颜色
            darkResources["PrimaryColor"] = System.Windows.Media.Colors.CornflowerBlue;
            darkResources["SecondaryColor"] = System.Windows.Media.Colors.DarkGray;
            darkResources["BackgroundColor"] = System.Windows.Media.Colors.DarkSlateGray;
            darkResources["ForegroundColor"] = System.Windows.Media.Colors.White;
            
            RegisterTheme("Dark", darkResources);
        }

        /// <summary>
        /// 清除当前主题资源
        /// </summary>
        private void ClearCurrentThemeResources()
        {
            if (Application.Current?.Resources != null)
            {
                // 移除主题相关的资源字典
                var themeResources = Application.Current.Resources.MergedDictionaries
                    .Where(rd => rd.Contains("ThemeMarker"))
                    .ToList();

                foreach (var resource in themeResources)
                {
                    Application.Current.Resources.MergedDictionaries.Remove(resource);
                }
            }
        }

        /// <summary>
        /// 应用主题资源
        /// </summary>
        /// <param name="resources">资源字典</param>
        private void ApplyThemeResources(ResourceDictionary resources)
        {
            if (Application.Current?.Resources != null && resources != null)
            {
                // 添加主题标记
                resources["ThemeMarker"] = true;
                
                // 将主题资源添加到应用程序资源
                Application.Current.Resources.MergedDictionaries.Add(resources);
            }
        }

        /// <summary>
        /// 创建主题预览信息
        /// </summary>
        /// <param name="themeName">主题名称</param>
        /// <param name="resources">资源字典</param>
        /// <returns>主题预览</returns>
        private ThemePreview CreateThemePreview(string themeName, ResourceDictionary resources)
        {
            var preview = new ThemePreview
            {
                Name = themeName,
                DisplayName = themeName,
                Description = $"{themeName} 主题",
                Author = "McLaser Framework",
                Version = "1.0.0"
            };

            // 尝试从资源中提取颜色信息
            if (resources.Contains("PrimaryColor"))
                preview.PrimaryColor = resources["PrimaryColor"]?.ToString() ?? string.Empty;
            
            if (resources.Contains("SecondaryColor"))
                preview.SecondaryColor = resources["SecondaryColor"]?.ToString() ?? string.Empty;
            
            if (resources.Contains("BackgroundColor"))
                preview.BackgroundColor = resources["BackgroundColor"]?.ToString() ?? string.Empty;
            
            if (resources.Contains("ForegroundColor"))
                preview.ForegroundColor = resources["ForegroundColor"]?.ToString() ?? string.Empty;

            // 判断是否为深色主题
            preview.IsDarkTheme = themeName.IndexOf("Dark", StringComparison.OrdinalIgnoreCase) >= 0;

            return preview;
        }

        /// <summary>
        /// 主题信息内部类
        /// </summary>
        private class ThemeInfo
        {
            public string Name { get; set; } = string.Empty;
            public ResourceDictionary Resources { get; set; } = new ResourceDictionary();
            public ThemePreview Preview { get; set; } = new ThemePreview();
        }
    }
}
