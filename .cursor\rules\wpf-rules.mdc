---
description: 
globs: 
alwaysApply: true
---
1. Architecture & Patterns
prompt
- Strict MVVM separation: Zero code-behind in views. All logic in ViewModels
- Implement INotifyPropertyChanged using either Fody Weaver or lightweight base classes
- Use ObservableCollections for all dynamic data bindings
2. Command Handling
prompt
- Apply ICommand via Relay<PERSON>ommand/DelegateCommand pattern
- Validate CanExecute before execution with real-time UI feedback
- Prevent concurrent command execution during async operations
3. Async Operations
prompt
- Use async/await for ALL device communication (serial/network/OPC)
- Configure await false for non-UI thread operations
- Always marshal UI updates to main thread via Dispatcher.InvokeAsync
4. Resource Management
prompt
- Implement IDisposable for device proxies/connections
- Use 'using' blocks for transient hardware resources
- Cancel pending async operations on view unload
5. Error Resilience
prompt
- Wrap device interactions in try-catch with hardware-specific exceptions
- Implement retry policies for transient device errors
- Use configurable timeout thresholds for device responses
6. Thread Safety
prompt
- Apply double-check locking for device connection singletons
- Use ConcurrentCollections for multi-threaded data buffers
- Utilize ReaderWriterLock<PERSON>lim for shared calibration data
7. UI Responsiveness
prompt
- Maintain <30ms UI thread blocking time
- Virtualize data grids with >1000 elements
- Implement loading indicators for >200ms operations
8. Logging & Diagnostics
prompt
- Structured logging (Serilog/NLog) with machine-readable timestamps
- Critical errors must include device state snapshots
- Separate log streams for user actions and device I/O
9. Testing & Validation
prompt
- Mock device interfaces for ViewModel unit tests
- Validate measurement data with range/sanity checks
- Implement design-time data support
10. Industrial Standards
prompt
- ISO 8601 timestamps for all data records
- Support for high-DPI factory monitors
- Localizable resource files for all UI text
Usage Example:
When requesting code generation, preface prompts with:

prompt
// INDUSTRIAL-WPF-RULES: 
// [Brief feature description]
// Constraints: [Specific requirements]
Example:

prompt
// INDUSTRIAL-WPF-RULES: 
// Create PLC data monitor view

// Constraints: 10Hz update rate, OPC UA source, must show connection latency