using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace McLaser.Core.ExceptionHandling
{
    /// <summary>
    /// 异常策略管理器
    /// 负责异常处理策略的管理和应用
    /// </summary>
    public class ExceptionPolicyManager
    {
        #region 字段

        private readonly Dictionary<string, ExceptionHandlingPolicy> _policies = new Dictionary<string, ExceptionHandlingPolicy>();
        private ExceptionHandlingPolicy _currentPolicy;

        #endregion

        #region 属性

        /// <summary>
        /// 当前策略
        /// </summary>
        public ExceptionHandlingPolicy CurrentPolicy => _currentPolicy;

        /// <summary>
        /// 已注册的策略列表
        /// </summary>
        public IReadOnlyDictionary<string, ExceptionHandlingPolicy> RegisteredPolicies => _policies;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化异常策略管理器
        /// </summary>
        public ExceptionPolicyManager()
        {
            _currentPolicy = CreateDefaultPolicy();
            _policies["Default"] = _currentPolicy;
        }

        #endregion

        #region 策略管理

        /// <summary>
        /// 注册策略
        /// </summary>
        /// <param name="name">策略名称</param>
        /// <param name="policy">策略</param>
        public void RegisterPolicy(string name, ExceptionHandlingPolicy policy)
        {
            if (string.IsNullOrEmpty(name))
                throw new ArgumentException("策略名称不能为空", nameof(name));

            if (policy == null)
                throw new ArgumentNullException(nameof(policy));

            _policies[name] = policy;
        }

        /// <summary>
        /// 移除策略
        /// </summary>
        /// <param name="name">策略名称</param>
        /// <returns>是否成功移除</returns>
        public bool RemovePolicy(string name)
        {
            if (string.IsNullOrEmpty(name) || name == "Default")
                return false;

            return _policies.Remove(name);
        }

        /// <summary>
        /// 获取策略
        /// </summary>
        /// <param name="name">策略名称</param>
        /// <returns>策略</returns>
        public ExceptionHandlingPolicy? GetPolicy(string name)
        {
            return _policies.TryGetValue(name, out var policy) ? policy : null;
        }

        /// <summary>
        /// 设置当前策略
        /// </summary>
        /// <param name="name">策略名称</param>
        /// <returns>是否成功设置</returns>
        public bool SetCurrentPolicy(string name)
        {
            if (_policies.TryGetValue(name, out var policy))
            {
                _currentPolicy = policy;
                return true;
            }
            return false;
        }

        /// <summary>
        /// 设置当前策略
        /// </summary>
        /// <param name="policy">策略</param>
        public void SetCurrentPolicy(ExceptionHandlingPolicy policy)
        {
            _currentPolicy = policy ?? throw new ArgumentNullException(nameof(policy));
        }

        #endregion

        #region 策略应用

        /// <summary>
        /// 应用策略到异常处理器
        /// </summary>
        /// <param name="handler">异常处理器</param>
        /// <param name="policyName">策略名称</param>
        public void ApplyPolicy(IGlobalExceptionHandler handler, string? policyName = null)
        {
            if (handler == null)
                throw new ArgumentNullException(nameof(handler));

            var policy = string.IsNullOrEmpty(policyName) ? _currentPolicy : GetPolicy(policyName);
            if (policy == null)
                throw new ArgumentException($"策略不存在: {policyName}");

            handler.Policy = policy;
        }

        /// <summary>
        /// 根据策略创建异常处理器
        /// </summary>
        /// <param name="policyName">策略名称</param>
        /// <returns>异常处理器</returns>
        public IGlobalExceptionHandler CreateHandler(string? policyName = null)
        {
            var policy = string.IsNullOrEmpty(policyName) ? _currentPolicy : GetPolicy(policyName);
            if (policy == null)
                throw new ArgumentException($"策略不存在: {policyName}");

            return new GlobalExceptionHandler(policy);
        }

        #endregion

        #region 策略配置

        /// <summary>
        /// 从文件加载策略
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>加载任务</returns>
        public async Task<ExceptionHandlingPolicy> LoadPolicyFromFileAsync(string filePath, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));

            if (!File.Exists(filePath))
                throw new FileNotFoundException($"策略文件不存在: {filePath}");

            try
            {
                var json = File.ReadAllText(filePath);
                var policy = JsonConvert.DeserializeObject<ExceptionHandlingPolicy>(json);
                
                if (policy == null)
                    throw new InvalidOperationException("无法反序列化策略文件");

                return policy;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"加载策略文件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 保存策略到文件
        /// </summary>
        /// <param name="policy">策略</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>保存任务</returns>
        public async Task SavePolicyToFileAsync(ExceptionHandlingPolicy policy, string filePath, CancellationToken cancellationToken = default)
        {
            if (policy == null)
                throw new ArgumentNullException(nameof(policy));

            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));

            try
            {
                var json = JsonConvert.SerializeObject(policy, Formatting.Indented);
                File.WriteAllText(filePath, json);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"保存策略文件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 导入策略
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="name">策略名称</param>
        /// <param name="setAsCurrent">是否设置为当前策略</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>导入任务</returns>
        public async Task ImportPolicyAsync(string filePath, string name, bool setAsCurrent = false, CancellationToken cancellationToken = default)
        {
            var policy = await LoadPolicyFromFileAsync(filePath, cancellationToken);
            policy.Name = name;
            
            RegisterPolicy(name, policy);
            
            if (setAsCurrent)
            {
                SetCurrentPolicy(policy);
            }
        }

        /// <summary>
        /// 导出策略
        /// </summary>
        /// <param name="name">策略名称</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>导出任务</returns>
        public async Task ExportPolicyAsync(string name, string filePath, CancellationToken cancellationToken = default)
        {
            var policy = GetPolicy(name);
            if (policy == null)
                throw new ArgumentException($"策略不存在: {name}");

            await SavePolicyToFileAsync(policy, filePath, cancellationToken);
        }

        #endregion

        #region 预定义策略

        /// <summary>
        /// 创建默认策略
        /// </summary>
        /// <returns>默认策略</returns>
        public static ExceptionHandlingPolicy CreateDefaultPolicy()
        {
            var policy = new ExceptionHandlingPolicy
            {
                Name = "Default",
                EnableGlobalHandling = true,
                LogAllExceptions = true,
                EnableRecovery = true,
                EnableNotification = false,
                DefaultRetryCount = 3,
                RetryInterval = 1000,
                MaxHandlingTime = 30000
            };

            // 添加默认规则
            policy.Rules.AddRange(new[]
            {
                new ExceptionHandlingRule
                {
                    Name = "ArgumentException",
                    ExceptionType = typeof(ArgumentException).FullName!,
                    Action = ExceptionHandlingAction.Log,
                    ShouldLog = true,
                    ShouldRethrow = false,
                    UserMessageTemplate = "参数错误: {Message}",
                    ErrorCode = "ARG_ERROR"
                },
                new ExceptionHandlingRule
                {
                    Name = "InvalidOperationException",
                    ExceptionType = typeof(InvalidOperationException).FullName!,
                    Action = ExceptionHandlingAction.Log,
                    ShouldLog = true,
                    ShouldRethrow = false,
                    UserMessageTemplate = "操作无效: {Message}",
                    ErrorCode = "OP_ERROR"
                },
                new ExceptionHandlingRule
                {
                    Name = "FileNotFoundException",
                    ExceptionType = typeof(FileNotFoundException).FullName!,
                    Action = ExceptionHandlingAction.Log,
                    ShouldLog = true,
                    ShouldRethrow = false,
                    UserMessageTemplate = "文件未找到",
                    ErrorCode = "FILE_NOT_FOUND"
                },
                new ExceptionHandlingRule
                {
                    Name = "UnauthorizedAccessException",
                    ExceptionType = typeof(UnauthorizedAccessException).FullName!,
                    Action = ExceptionHandlingAction.LogAndRethrow,
                    ShouldLog = true,
                    ShouldRethrow = true,
                    ShouldNotify = true,
                    UserMessageTemplate = "访问被拒绝",
                    ErrorCode = "ACCESS_DENIED"
                },
                new ExceptionHandlingRule
                {
                    Name = "OutOfMemoryException",
                    ExceptionType = typeof(OutOfMemoryException).FullName!,
                    Action = ExceptionHandlingAction.Terminate,
                    ShouldLog = true,
                    ShouldNotify = true,
                    UserMessageTemplate = "内存不足，应用程序将终止",
                    ErrorCode = "OUT_OF_MEMORY"
                }
            });

            // 设置关键异常类型
            policy.CriticalExceptionTypes.Add(typeof(OutOfMemoryException).FullName!);
            policy.CriticalExceptionTypes.Add(typeof(StackOverflowException).FullName!);
            policy.CriticalExceptionTypes.Add(typeof(AccessViolationException).FullName!);

            return policy;
        }

        /// <summary>
        /// 创建开发环境策略
        /// </summary>
        /// <returns>开发环境策略</returns>
        public static ExceptionHandlingPolicy CreateDevelopmentPolicy()
        {
            var policy = CreateDefaultPolicy();
            policy.Name = "Development";
            policy.LogAllExceptions = true;
            policy.EnableNotification = false;
            policy.DefaultRetryCount = 1;

            // 开发环境下记录更多详细信息
            foreach (var rule in policy.Rules)
            {
                rule.ShouldLog = true;
                rule.UserMessageTemplate = "{Message}\n{StackTrace}";
            }

            return policy;
        }

        /// <summary>
        /// 创建生产环境策略
        /// </summary>
        /// <returns>生产环境策略</returns>
        public static ExceptionHandlingPolicy CreateProductionPolicy()
        {
            var policy = CreateDefaultPolicy();
            policy.Name = "Production";
            policy.LogAllExceptions = true;
            policy.EnableNotification = true;
            policy.DefaultRetryCount = 3;

            // 生产环境下提供用户友好的错误消息
            foreach (var rule in policy.Rules)
            {
                rule.ShouldNotify = rule.ExceptionType.Contains("OutOfMemory") || 
                                   rule.ExceptionType.Contains("UnauthorizedAccess");
            }

            // 忽略一些常见的非关键异常
            policy.IgnoredExceptionTypes.Add(typeof(OperationCanceledException).FullName!);
            policy.IgnoredExceptionTypes.Add(typeof(TaskCanceledException).FullName!);

            return policy;
        }

        /// <summary>
        /// 创建测试环境策略
        /// </summary>
        /// <returns>测试环境策略</returns>
        public static ExceptionHandlingPolicy CreateTestPolicy()
        {
            var policy = CreateDefaultPolicy();
            policy.Name = "Test";
            policy.LogAllExceptions = false;
            policy.EnableNotification = false;
            policy.EnableRecovery = false;
            policy.DefaultRetryCount = 0;

            // 测试环境下快速失败
            foreach (var rule in policy.Rules)
            {
                rule.ShouldRethrow = true;
                rule.RetryCount = 0;
            }

            return policy;
        }

        #endregion

        #region 策略验证

        /// <summary>
        /// 验证策略
        /// </summary>
        /// <param name="policy">策略</param>
        /// <returns>验证结果</returns>
        public static PolicyValidationResult ValidatePolicy(ExceptionHandlingPolicy policy)
        {
            var result = new PolicyValidationResult { IsValid = true };

            if (policy == null)
            {
                result.AddError("策略不能为空");
                return result;
            }

            if (string.IsNullOrEmpty(policy.Name))
            {
                result.AddError("策略名称不能为空");
            }

            if (policy.DefaultRetryCount < 0)
            {
                result.AddError("默认重试次数不能为负数");
            }

            if (policy.RetryInterval < 0)
            {
                result.AddError("重试间隔不能为负数");
            }

            if (policy.MaxHandlingTime <= 0)
            {
                result.AddError("最大处理时间必须大于0");
            }

            // 验证规则
            foreach (var rule in policy.Rules)
            {
                if (string.IsNullOrEmpty(rule.Name))
                {
                    result.AddWarning("规则名称为空");
                }

                if (string.IsNullOrEmpty(rule.ExceptionType))
                {
                    result.AddError("规则异常类型不能为空");
                }

                if (rule.RetryCount < 0)
                {
                    result.AddError($"规则 {rule.Name} 的重试次数不能为负数");
                }

                if (rule.RetryInterval < 0)
                {
                    result.AddError($"规则 {rule.Name} 的重试间隔不能为负数");
                }
            }

            return result;
        }

        #endregion
    }

    /// <summary>
    /// 策略验证结果
    /// </summary>
    public class PolicyValidationResult
    {
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid { get; set; } = true;

        /// <summary>
        /// 错误列表
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// 警告列表
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();

        /// <summary>
        /// 添加错误
        /// </summary>
        /// <param name="error">错误消息</param>
        public void AddError(string error)
        {
            Errors.Add(error);
            IsValid = false;
        }

        /// <summary>
        /// 添加警告
        /// </summary>
        /// <param name="warning">警告消息</param>
        public void AddWarning(string warning)
        {
            Warnings.Add(warning);
        }
    }
}
