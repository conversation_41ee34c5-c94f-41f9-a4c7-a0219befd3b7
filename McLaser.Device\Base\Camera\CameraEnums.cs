using System;
using System.ComponentModel;

namespace McLaser.Devices
{
    /// <summary>
    /// 相机类型枚举
    /// </summary>
    [Serializable]
    public enum CameraType
    {
        /// <summary>
        /// 海康相机
        /// </summary>
        HIK,
        
        /// <summary>
        /// 大恒相机
        /// </summary>
        Daheng,
        
        /// <summary>
        /// 巴斯勒相机
        /// </summary>
        <PERSON><PERSON>,
    }



    /// <summary>
    /// 图像旋转角度枚举
    /// </summary>
    [Serializable]
    public enum RotateAngle
    {
        /// <summary>
        /// 0°
        /// </summary>
        零 = 0,
        
        /// <summary>
        /// 90°
        /// </summary>
        九十 = 90,
        
        /// <summary>
        /// 180°
        /// </summary>
        一百八 = 180,
        
        /// <summary>
        /// 270°
        /// </summary>
        二百七 = 270,
    }

    /// <summary>
    /// 图像旋转角度枚举（用于图像数据）
    /// </summary>
    [Serializable]
    public enum RotateImageAngle
    {
        /// <summary>
        /// 不旋转
        /// </summary>
        None = 0,
        
        /// <summary>
        /// 顺时针90度
        /// </summary>
        Clockwise90 = 90,
        
        /// <summary>
        /// 180度
        /// </summary>
        Rotate180 = 180,
        
        /// <summary>
        /// 逆时针90度
        /// </summary>
        CounterClockwise90 = 270,
    }

    /// <summary>
    /// 镜像类型枚举
    /// </summary>
    [Serializable]
    public enum MirrorImageType
    {
        /// <summary>
        /// 不镜像
        /// </summary>
        None = 0,
        
        /// <summary>
        /// 水平镜像
        /// </summary>
        Horizontal,
        
        /// <summary>
        /// 垂直镜像
        /// </summary>
        Vertical,
        
        /// <summary>
        /// 水平垂直镜像
        /// </summary>
        Both,
    }
}
