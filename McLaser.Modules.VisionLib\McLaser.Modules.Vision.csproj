<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{5F8A19FD-8E20-4C7D-916E-477D81850B3D}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>McLaser.Modules.Vision</RootNamespace>
    <AssemblyName>McLaser.Modules.Vision</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>5</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="halcondotnetxl, Version=22.5.0.0, Culture=neutral, PublicKeyToken=4973bed59ddbf2b8, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\bin\halcondotnetxl.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Windows.Presentation" />
    <Reference Include="System.Xaml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="WindowsBase" />
    <Reference Include="WindowsFormsIntegration" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Base\HRoi.cs" />
    <Compile Include="Base\HText.cs" />
    <Compile Include="Base\ROI.cs" />
    <Compile Include="Base\ROICoordinates.cs" />
    <Compile Include="Base\RoiCursor.cs" />
    <Compile Include="Base\ROILine.cs" />
    <Compile Include="Base\RoiType.cs" />
    <Compile Include="Base\Circle.cs" />
    <Compile Include="Base\EnumType.cs" />
    <Compile Include="Base\Line.cs" />
    <Compile Include="Base\Rectangle.cs" />
    <Compile Include="Base\Rectangle2.cs" />
    <Compile Include="Base\VisionInfo.cs" />
    <Compile Include="Fit.cs" />
    <Compile Include="Gen.cs" />
    <Compile Include="RenderControl\IDrawable.cs" />
    <Compile Include="RenderControl\IRenderable.cs" />
    <Compile Include="RenderControl\IRenderView.cs" />
    <Compile Include="RenderControl\IRenderViewGroupEx.cs" />
    <Compile Include="RenderControl\IRenderViewManager.cs" />
    <Compile Include="RenderControl\RenderView.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="RenderControl\RenderView.Designer.cs">
      <DependentUpon>RenderView.cs</DependentUpon>
    </Compile>
    <Compile Include="RenderControl\RenderViewManager.cs" />
    <Compile Include="RenderControl\RenderViewWpf.xaml.cs">
      <DependentUpon>RenderViewWpf.xaml</DependentUpon>
    </Compile>
    <Compile Include="RenderControl\VisionView.xaml.cs">
      <DependentUpon>VisionView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Base\VisionException.cs" />
    <Compile Include="VisionLib.cs" />
    <Compile Include="Base\MV_ERR.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Base\VisionParam.cs" />
    <Compile Include="Base\VisionResult.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="RenderControl\RenderView.resx">
      <DependentUpon>RenderView.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <Page Include="RenderControl\RenderViewWpf.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="RenderControl\VisionView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\McLaser.Core\McLaser.Core.csproj">
      <Project>{B67B75F4-B65B-4FB4-9E9B-F86D2AA29B38}</Project>
      <Name>McLaser.Core</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>