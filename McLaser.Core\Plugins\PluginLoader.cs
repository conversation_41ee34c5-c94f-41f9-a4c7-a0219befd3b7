using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;

namespace McLaser.Core.Plugins
{
    /// <summary>
    /// 插件加载器
    /// 负责插件的动态加载和卸载
    /// </summary>
    public class PluginLoader : IDisposable
    {
        #region 字段

        private readonly Dictionary<string, PluginLoadContext> _loadContexts = new Dictionary<string, PluginLoadContext>();
        private readonly object _lockObject = new object();
        private bool _disposed = false;

        #endregion

        #region 属性

        /// <summary>
        /// 是否启用插件隔离
        /// </summary>
        public bool EnableIsolation { get; set; } = true;

        /// <summary>
        /// 已加载的插件上下文
        /// </summary>
        public IReadOnlyDictionary<string, PluginLoadContext> LoadContexts => _loadContexts;

        #endregion

        #region 事件

        /// <summary>
        /// 插件加载事件
        /// </summary>
        public event EventHandler<PluginLoadEventArgs>? PluginLoaded;

        /// <summary>
        /// 插件卸载事件
        /// </summary>
        public event EventHandler<PluginUnloadEventArgs>? PluginUnloaded;

        /// <summary>
        /// 加载错误事件
        /// </summary>
        public event EventHandler<PluginLoadErrorEventArgs>? LoadError;

        #endregion

        #region 加载方法

        /// <summary>
        /// 加载插件程序集
        /// </summary>
        /// <param name="pluginPath">插件路径</param>
        /// <param name="pluginId">插件ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>加载任务</returns>
        public async Task<Assembly?> LoadPluginAssemblyAsync(string pluginPath, string pluginId, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(pluginPath))
                throw new ArgumentException("插件路径不能为空", nameof(pluginPath));

            if (string.IsNullOrEmpty(pluginId))
                throw new ArgumentException("插件ID不能为空", nameof(pluginId));

            if (!File.Exists(pluginPath))
            {
                OnLoadError(pluginId, $"插件文件不存在: {pluginPath}");
                return null;
            }

            lock (_lockObject)
            {
                if (_loadContexts.ContainsKey(pluginId))
                {
                    OnLoadError(pluginId, $"插件已加载: {pluginId}");
                    return _loadContexts[pluginId].Assembly;
                }
            }

            try
            {
                var assembly = await Task.Run(() =>
                {
                    if (EnableIsolation)
                    {
                        return LoadWithIsolation(pluginPath, pluginId);
                    }
                    else
                    {
                        return LoadWithoutIsolation(pluginPath, pluginId);
                    }
                }, cancellationToken);

                if (assembly != null)
                {
                    OnPluginLoaded(pluginId, pluginPath, assembly);
                }

                return assembly;
            }
            catch (Exception ex)
            {
                OnLoadError(pluginId, $"加载插件失败: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 卸载插件程序集
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>卸载任务</returns>
        public async Task<bool> UnloadPluginAssemblyAsync(string pluginId, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(pluginId))
                throw new ArgumentException("插件ID不能为空", nameof(pluginId));

            PluginLoadContext? context;
            lock (_lockObject)
            {
                if (!_loadContexts.TryGetValue(pluginId, out context))
                {
                    OnLoadError(pluginId, $"插件未加载: {pluginId}");
                    return false;
                }

                _loadContexts.Remove(pluginId);
            }

            try
            {
                await Task.Run(() =>
                {
                    if (context.IsIsolated && context.LoadContext != null)
                    {
                        // .NET Framework 中无法卸载程序集
                        // 这里只是清理引用
                    }
                }, cancellationToken);

                OnPluginUnloaded(pluginId, context.PluginPath);
                return true;
            }
            catch (Exception ex)
            {
                OnLoadError(pluginId, $"卸载插件失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取插件类型
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <param name="typeName">类型名称</param>
        /// <returns>插件类型</returns>
        public Type? GetPluginType(string pluginId, string typeName)
        {
            lock (_lockObject)
            {
                if (!_loadContexts.TryGetValue(pluginId, out var context))
                    return null;

                return context.Assembly?.GetType(typeName);
            }
        }

        /// <summary>
        /// 获取插件中的所有类型
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <returns>类型列表</returns>
        public Type[] GetPluginTypes(string pluginId)
        {
            lock (_lockObject)
            {
                if (!_loadContexts.TryGetValue(pluginId, out var context))
                    return Array.Empty<Type>();

                try
                {
                    return context.Assembly?.GetTypes() ?? Array.Empty<Type>();
                }
                catch (ReflectionTypeLoadException ex)
                {
                    // 返回成功加载的类型
                    return ex.Types.Where(t => t != null).ToArray()!;
                }
                catch
                {
                    return Array.Empty<Type>();
                }
            }
        }

        /// <summary>
        /// 查找实现指定接口的类型
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <param name="interfaceType">接口类型</param>
        /// <returns>实现类型列表</returns>
        public Type[] FindImplementingTypes(string pluginId, Type interfaceType)
        {
            var types = GetPluginTypes(pluginId);
            return types.Where(t => t.IsClass && !t.IsAbstract && interfaceType.IsAssignableFrom(t)).ToArray();
        }

        /// <summary>
        /// 检查插件是否已加载
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <returns>是否已加载</returns>
        public bool IsPluginLoaded(string pluginId)
        {
            lock (_lockObject)
            {
                return _loadContexts.ContainsKey(pluginId);
            }
        }

        /// <summary>
        /// 获取已加载的插件列表
        /// </summary>
        /// <returns>插件ID列表</returns>
        public string[] GetLoadedPlugins()
        {
            lock (_lockObject)
            {
                return _loadContexts.Keys.ToArray();
            }
        }

        #endregion

        #region 私有方法

        private Assembly LoadWithIsolation(string pluginPath, string pluginId)
        {
            // 在 .NET Framework 中，使用 AppDomain 来实现隔离加载
            // 但由于复杂性，这里简化为普通加载，并标记为隔离
            var assembly = Assembly.LoadFrom(pluginPath);

            var context = new PluginLoadContext
            {
                PluginId = pluginId,
                PluginPath = pluginPath,
                Assembly = assembly,
                LoadContext = null, // .NET Framework 不支持 AssemblyLoadContext
                IsIsolated = true,
                LoadTime = DateTime.Now
            };

            lock (_lockObject)
            {
                _loadContexts[pluginId] = context;
            }

            return assembly;
        }

        private Assembly LoadWithoutIsolation(string pluginPath, string pluginId)
        {
            var assembly = Assembly.LoadFrom(pluginPath);

            var context = new PluginLoadContext
            {
                PluginId = pluginId,
                PluginPath = pluginPath,
                Assembly = assembly,
                LoadContext = null,
                IsIsolated = false,
                LoadTime = DateTime.Now
            };

            lock (_lockObject)
            {
                _loadContexts[pluginId] = context;
            }

            return assembly;
        }

        #endregion

        #region 事件触发

        private void OnPluginLoaded(string pluginId, string pluginPath, Assembly assembly)
        {
            PluginLoaded?.Invoke(this, new PluginLoadEventArgs(pluginId, pluginPath, assembly));
        }

        private void OnPluginUnloaded(string pluginId, string pluginPath)
        {
            PluginUnloaded?.Invoke(this, new PluginUnloadEventArgs(pluginId, pluginPath));
        }

        private void OnLoadError(string pluginId, string errorMessage, Exception? exception = null)
        {
            LoadError?.Invoke(this, new PluginLoadErrorEventArgs(pluginId, errorMessage, exception));
        }

        #endregion

        #region IDisposable

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否正在释放</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                lock (_lockObject)
                {
                    foreach (var context in _loadContexts.Values)
                    {
                        try
                        {
                            // .NET Framework 中无法卸载程序集
                            // 这里只是清理引用
                            if (context.IsIsolated)
                            {
                                // 在 .NET Framework 中，程序集一旦加载就无法卸载
                                // 只能清理引用
                            }
                        }
                        catch
                        {
                            // 忽略清理时的异常
                        }
                    }

                    _loadContexts.Clear();
                }

                _disposed = true;
            }
        }

        /// <summary>
        /// 析构函数
        /// </summary>
        ~PluginLoader()
        {
            Dispose(false);
        }

        #endregion
    }

    /// <summary>
    /// 插件加载上下文
    /// </summary>
    public class PluginLoadContext
    {
        /// <summary>
        /// 插件ID
        /// </summary>
        public string PluginId { get; set; } = string.Empty;

        /// <summary>
        /// 插件路径
        /// </summary>
        public string PluginPath { get; set; } = string.Empty;

        /// <summary>
        /// 程序集
        /// </summary>
        public Assembly? Assembly { get; set; }

        /// <summary>
        /// 程序集加载上下文（.NET Framework 中不支持）
        /// </summary>
        public object? LoadContext { get; set; }

        /// <summary>
        /// 是否隔离加载
        /// </summary>
        public bool IsIsolated { get; set; }

        /// <summary>
        /// 加载时间
        /// </summary>
        public DateTime LoadTime { get; set; }

        /// <summary>
        /// 依赖程序集列表
        /// </summary>
        public List<Assembly> Dependencies { get; set; } = new List<Assembly>();
    }

    #region 事件参数类

    /// <summary>
    /// 插件加载事件参数
    /// </summary>
    public class PluginLoadEventArgs : EventArgs
    {
        /// <summary>
        /// 插件ID
        /// </summary>
        public string PluginId { get; }

        /// <summary>
        /// 插件路径
        /// </summary>
        public string PluginPath { get; }

        /// <summary>
        /// 程序集
        /// </summary>
        public Assembly Assembly { get; }

        /// <summary>
        /// 加载时间
        /// </summary>
        public DateTime LoadTime { get; }

        public PluginLoadEventArgs(string pluginId, string pluginPath, Assembly assembly)
        {
            PluginId = pluginId;
            PluginPath = pluginPath;
            Assembly = assembly;
            LoadTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 插件卸载事件参数
    /// </summary>
    public class PluginUnloadEventArgs : EventArgs
    {
        /// <summary>
        /// 插件ID
        /// </summary>
        public string PluginId { get; }

        /// <summary>
        /// 插件路径
        /// </summary>
        public string PluginPath { get; }

        /// <summary>
        /// 卸载时间
        /// </summary>
        public DateTime UnloadTime { get; }

        public PluginUnloadEventArgs(string pluginId, string pluginPath)
        {
            PluginId = pluginId;
            PluginPath = pluginPath;
            UnloadTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 插件加载错误事件参数
    /// </summary>
    public class PluginLoadErrorEventArgs : EventArgs
    {
        /// <summary>
        /// 插件ID
        /// </summary>
        public string PluginId { get; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; }

        /// <summary>
        /// 异常对象
        /// </summary>
        public Exception? Exception { get; }

        /// <summary>
        /// 错误时间
        /// </summary>
        public DateTime ErrorTime { get; }

        public PluginLoadErrorEventArgs(string pluginId, string errorMessage, Exception? exception = null)
        {
            PluginId = pluginId;
            ErrorMessage = errorMessage;
            Exception = exception;
            ErrorTime = DateTime.Now;
        }
    }

    #endregion
}
