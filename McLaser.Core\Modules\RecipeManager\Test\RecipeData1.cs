﻿using McLaser.Core.Container;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace McLaser.Core.Modules.RecipeManager.Test
{
    public class RecipeData1 : RecipeInstance<RecipeData1>
    {
        public bool IsAdd { get; set; }
        public string Name { get; set; }

    }

    public class RecipeData3 : RecipeInstance<RecipeData3>
    {
        public string Destripe { get; set; }
        public bool IsEnable { get; set; }
        public string Name { get; set; }

    }

    public class Test
    {
        public static void Create()
        {
            var recipeManager = IoC.Get<IRecipeManager>();
            recipeManager.Create(out var recipe);
            recipeManager.Current = recipe;
        }

        public static void Save()
        {
            var recipeManager = IoC.Get<IRecipeManager>();
            recipeManager.Save();
            RecipeData1.Instance.Name = "test";
        }

        public static void Load()
        {
            var recipeManager = IoC.Get<IRecipeManager>();
            recipeManager.Load("");
        }
    }
}
