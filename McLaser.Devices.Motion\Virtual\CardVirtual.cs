﻿using McLaser.Device;
using ODT.PowerPmacComLib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using static System.Net.WebRequestMethods;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.StartPanel;

namespace McLaser.Devices.Motion.Virtual
{
    [DeviceItem("运动控制卡", "虚拟卡", "兼容CK3M/CK5M系列", "🎛️")]
    public class CardVirtual : CardBase
    {

        private double[] arrPos = new double[32];


        #region 基类重写

        public override bool Open()
        {
            try
            {
                if (!IsEnabled) return false;
                if (Status.IsConnected) Close();

                Status.IsConnected = true;

                // 使能所有轴
                for (int i = 0; i < ListAxis.Count; i++)
                {
                    Enable(i, true);
                }

                // 启动状态监控任务
                StartStatusMonitoring();

                return IsOpen();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"打开虚拟控制卡异常：{ex.Message}");
                return false;
            }
        }

       

        public override bool IsOpen()
        {
            return Status.IsConnected;
        }

       
        public override bool Close()
        {
            try
            {
                // 停止状态监控
                StopStatusMonitoring();

                Status.IsConnected = false;
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"关闭PMAC控制卡异常：{ex.Message}");
                return false;
            }
        }

        public override bool Enable(int index, bool isEnable)
        {
            try
            {
                if (!CheckAxis(index)) return false;

                AxisVirtual axis = ListAxis[index] as AxisVirtual;
                axis.IsEnable = true;
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public override bool Home(int index, double vel = 100, int timeout = 10000)
        {
            try
            {
                if (!CheckAxis(index)) return false;
                AxisVirtual axis = ListAxis[index] as AxisVirtual;
                if (!Stop(index)) return false;
                Thread.Sleep(1000);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public override bool Move(int index, double pos, bool isAbs = true, int timeout = 5000)
        {
            try
            {
                if (!CheckAxis(index)) return false;
                AxisVirtual axis = ListAxis[index] as AxisVirtual;
                if (!Stop(index)) return false;
                Thread.Sleep(1000);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public override bool Jog(int index, bool isPlus)
        {
            try
            {
                if (!CheckAxis(index)) return false;
                AxisVirtual axis = ListAxis[index] as AxisVirtual;
                if (!Stop(index)) return false;
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public override bool Stop(int index)
        {
            try
            {
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public override bool GetPos(int index, ref double pos)
        {
            try
            {
                if (!CheckAxis(index)) return false;

                AxisPmac axis = ListAxis[index] as AxisPmac;
                axis.CurPos = arrPos[index];
                pos = arrPos[index];
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取PMAC轴位置异常：{ex.Message}");
                return false;
            }
        }

        public override bool IsHome(int index, ref bool bIsHome)
        {
            try
            {
                if (!CheckAxis(index)) return false;
                AxisVirtual axis = ListAxis[index] as AxisVirtual;
                return axis.IsHome;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查PMAC轴回零状态异常：{ex.Message}");
                return false;
            }
        }

        protected override void AddAxis()
        {
            try
            {
                AxisVirtual axisPmac = new AxisVirtual();
                axisPmac.ID = ListAxis.Count;
                axisPmac.Name = $"Axis{axisPmac.ID}";
                axisPmac.Card = this;
                ListAxis.Add(axisPmac);
            }
            catch (Exception ex)
            {

            }
        }

        protected override bool CheckAxis(int index)
        {
            return index >= 0 && index < ListAxis.Count;
        }

        public override bool WaitForInPos(int index, double targetPos, int timeout)
        {
            try
            {
                var startTime = DateTime.Now;
                AxisVirtual axis = ListAxis[index] as AxisVirtual;

                while ((DateTime.Now - startTime).TotalMilliseconds < timeout)
                {
                    double currentPos = 0;
                    if (GetPos(index, ref currentPos))
                    {
                        if (Math.Abs(currentPos - targetPos) <= axis.FollowError)
                        {
                            return true;
                        }
                    }
                    Thread.Sleep(10);
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"等待PMAC轴到位异常：{ex.Message}");
                return false;
            }
        }

        #endregion


        private void StartStatusMonitoring()
        {
        }

        void StopStatusMonitoring()
        {
        }
    }
}
