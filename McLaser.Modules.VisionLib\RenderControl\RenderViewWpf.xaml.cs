﻿using HalconDotNet;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Forms;
using UserControl = System.Windows.Controls.UserControl;

namespace McLaser.Modules.Vision
{
 
    public partial class RenderViewWpf : UserControl, IRenderView
    {
        public event EventHandler RealTimeChanged;
        public event System.Windows.Forms.MouseEventHandler OnRenderViewMouseDown;
        public event System.Windows.Forms.MouseEventHandler OnRenderViewMouseUp;
        public RenderView View;
        public HWindow HalconWindow => View?.HalconWindow;
        public RenderViewWpf()
        {
            InitializeComponent();
            if (View == null)
            {

                View = new RenderView();
                View.Owner = this;
                View.Dock = DockStyle.Fill;
                WindowsFormsHostElement.Child = View;
            }
        }

        private void UserControl_Loaded(object sender, RoutedEventArgs e)
        {

        }

        public void AddHObject(HRoi hObj, string name)
        {
            View.AddHObject(hObj, name);
        }

        public void AddHROIs(Dictionary<string, HRoi> rois, bool isCopyMode = true)
        {
            View.AddHROIs(rois, isCopyMode);
        }

        public void ClearAllROI()
        {
            View.ClearAllROI();
        }

        public void ClearHWindow()
        {
            View.ClearHWindow();
        }

        public void ClearWindow()
        {
            View?.ClearWindow();
        }

        public void DispImage(HImage hImage)
        {
            View.DispImage(hImage);
        }

        public void DispObj(HObject hObject)
        {
            View.DispObj(hObject);
        }

        public void DispROI(ROI roi)
        {
            View.DispROI(roi);
        }

        public void DrawMask(HRegion region)
        {
            View.DrawMask(region);
        }

        public void DumpWindow(string filename)
        {
            View.DumpWindow(filename);
        }

        public HRegion EndDrawMask()
        {
            return View.EndDrawMask();
        }

        public HImage EndRealTimeShow()
        {
            return View.EndRealTimeShow();
        }

        public void HideROI(ROI roi)
        {
            View.HideROI(roi);
        }

        public void Repaint(HImage dispImage = null, ConcurrentDictionary<string, HRoi> hObjects = null)
        {
            View.Repaint(dispImage, hObjects);
        }

        public bool SetFullImagePart()
        {
            return View.SetFullImagePart();
        }

        public void SetMaskShape(int shape, int size)
        {
            View.SetMaskShape(shape, size);
        }

        public void SetViewParam(string param, bool param_value)
        {
            View.SetViewParam(param, param_value);
        }

        public void StartRealTimeShow()
        {
            View.StartRealTimeShow();
        }

        public void RegisterView(IRenderView renderView)
        {
            throw new NotImplementedException();
        }
    }
}
