# McLaser.App 底部导航栏使用指南

## 启动应用程序

1. 编译并运行McLaser.App项目
2. 应用程序将显示新的底部导航栏界面
3. 默认会加载主页内容

## 界面布局

### 主要区域
- **顶部内容区域**：显示当前选中页面的内容
- **底部导航栏**：包含导航按钮和工具按钮
- **底部状态栏**：显示状态信息、当前主题和页面信息

### 底部导航栏组成
- **左侧导航按钮区域**：主要功能导航按钮
- **右侧工具区域**：前进/后退、主题切换、设置等工具

## 导航功能使用

### 1. 基本页面导航
- **点击导航按钮**：直接切换到对应页面
- **主页按钮**：返回应用程序主页
- **工具按钮**：打开数据输入工具
- **设置按钮**：打开应用程序设置

### 2. 分类按钮使用
- **设备按钮**：点击后弹出上拉框，显示设备相关页面选项
  - 设备管理器：管理所有设备
  - 设备状态：查看设备状态
- **系统按钮**：点击后弹出上拉框，显示系统功能演示选项
  - 事件总线演示：展示事件总线功能
  - 异常处理演示：展示异常处理机制
  - 插件管理演示：展示插件系统功能

### 3. 导航历史操作
- **后退按钮 (◀)**：返回上一个访问的页面
- **前进按钮 (▶)**：前进到下一个页面（如果有的话）
- 按钮状态会根据导航历史自动启用/禁用

### 4. 工具功能
- **主题下拉框**：快速切换Light/Dark主题
- **设置按钮 (⚙)**：打开设置页面

## 页面功能介绍

### 主页 (🏠)
- **框架特性展示**：显示McLaser.Core框架的主要功能
- **快速操作按钮**：提供常用功能的快速访问
- **主题切换**：在主页也可以进行主题切换
- **系统信息**：显示当前页面、导航历史和系统状态

### 设备管理器 (📱 > ⚙)
- 设备管理和配置功能
- 设备状态监控
- 设备操作控制

### 系统演示页面 (🔧)
- **事件总线演示 (📡)**：展示事件发布和订阅机制
- **异常处理演示 (⚠)**：展示异常捕获和处理流程
- **插件管理演示 (🔌)**：展示插件加载、卸载和管理

### 数据输入工具 (🛠)
- 数据输入和验证功能
- 表单处理演示

### 设置页面 (⚙)
- 应用程序配置选项
- 主题和界面设置
- 系统参数配置

## 交互提示

### 视觉反馈
- **悬停效果**：鼠标悬停在按钮上时会有高亮效果
- **选中状态**：当前页面对应的导航按钮会保持选中状态
- **分类指示器**：分类按钮下方有小三角形 (▼) 指示器

### 弹出框操作
- **点击分类按钮**：弹出子页面选择框
- **点击子页面选项**：直接导航到对应页面
- **点击其他区域**：自动关闭弹出框

### 状态栏信息
- **左侧**：显示当前操作状态和时间戳
- **中间**：显示当前主题信息
- **右侧**：显示当前页面名称

## 快捷操作

### 主页快速操作
在主页中提供了以下快速操作按钮：
- **设备管理**：直接跳转到设备管理器
- **系统演示**：直接跳转到事件总线演示
- **数据输入**：直接跳转到数据输入工具

### 主题切换
可以通过以下方式切换主题：
1. 主页的主题切换按钮
2. 底部导航栏的主题下拉框
3. 设置页面的主题选项

## 注意事项

### 页面缓存
- 大部分页面采用单例模式，切换时会保持页面状态
- 首次访问页面时可能需要稍等加载时间

### 导航历史
- 系统会自动记录导航历史，支持前进/后退
- 历史记录有数量限制（最多50条）

### 错误处理
- 如果页面加载失败，会显示错误信息
- 可以通过刷新或重新导航来恢复

## 故障排除

### 页面无法加载
1. 检查是否有编译错误
2. 查看状态栏的错误信息
3. 尝试重新启动应用程序

### 导航按钮无响应
1. 检查是否有异常信息在状态栏显示
2. 尝试点击其他导航按钮
3. 使用主页的快速操作按钮

### 主题切换失败
1. 检查主题资源文件是否存在
2. 尝试重新启动应用程序
3. 在设置页面重置主题配置

## 扩展功能

### 添加自定义页面
开发者可以通过修改`MainViewModel.RegisterPages()`方法来添加新的页面和导航项。

### 自定义导航按钮
可以通过修改`NavigationViewModel.InitializeNavigationItems()`方法来自定义导航按钮的图标、文字和行为。

### 主题定制
可以通过修改主题资源文件来定制导航栏的外观和样式。
