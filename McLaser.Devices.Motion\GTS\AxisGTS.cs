using System;
using System.ComponentModel;
using System.Xml.Serialization;
using McLaser.Device;

namespace McLaser.Devices.Motion.GTS
{
    /// <summary>
    /// 固高GTS轴类
    /// 表示固高GTS运动控制器中的单个轴，包含轴的配置参数和状态信息
    /// </summary>
    [Serializable]
    public class AxisGTS : AxisBase
    {
        #region 私有字段

        private int _id = 1;
        private double _pluseScale = 1000.0;
        private double _followError = 0.003;
        private double _stepValue = 1.0;
        private double _posLimitMax = 10000;
        private double _posLimitMin = -10000;
        private double _velMax = 100;
        private double _velJog = 50;
        private double _accMax = 1000;
        private double _decMax = 1000;
        private int _moveTimeout = 5000;
        private bool _isVirtual = false;

        #endregion

        #region 属性

        /// <summary>
        /// 轴ID号
        /// </summary>
        [Category("GTS轴基本"), DisplayName("轴ID")]
        public override int ID
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 脉冲当量(脉冲/毫米)
        /// </summary>
        [Category("GTS轴基本"), DisplayName("脉冲当量(脉冲/mm)")]
        public override double PluseScale
        {
            get => _pluseScale;
            set
            {
                if (Math.Abs(_pluseScale - value) > 0.001)
                {
                    _pluseScale = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 跟随误差(毫米)
        /// </summary>
        [Category("GTS轴基本"), DisplayName("跟随误差(mm)")]
        public override double FollowError
        {
            get => _followError;
            set
            {
                if (Math.Abs(_followError - value) > 0.001)
                {
                    _followError = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 步进值(毫米)
        /// </summary>
        [Category("GTS轴基本"), DisplayName("步进值(mm)")]
        public override double StepValue
        {
            get => _stepValue;
            set
            {
                if (Math.Abs(_stepValue - value) > 0.001)
                {
                    _stepValue = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 正向软限位(毫米)
        /// </summary>
        [Category("GTS轴限位"), DisplayName("正向软限位(mm)")]
        public override double PosLimitMax
        {
            get => _posLimitMax;
            set
            {
                if (Math.Abs(_posLimitMax - value) > 0.001)
                {
                    _posLimitMax = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 负向软限位(毫米)
        /// </summary>
        [Category("GTS轴限位"), DisplayName("负向软限位(mm)")]
        public override double PosLimitMin
        {
            get => _posLimitMin;
            set
            {
                if (Math.Abs(_posLimitMin - value) > 0.001)
                {
                    _posLimitMin = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 最大速度(毫米/秒)
        /// </summary>
        [Category("GTS轴运动"), DisplayName("最大速度(mm/s)")]
        public double VelMax
        {
            get => _velMax;
            set
            {
                if (Math.Abs(_velMax - value) > 0.001)
                {
                    _velMax = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 点动速度(毫米/秒)
        /// </summary>
        [Category("GTS轴运动"), DisplayName("点动速度(mm/s)")]
        public double VelJog
        {
            get => _velJog;
            set
            {
                if (Math.Abs(_velJog - value) > 0.001)
                {
                    _velJog = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 最大加速度(毫米/秒²)
        /// </summary>
        [Category("GTS轴运动"), DisplayName("最大加速度(mm/s²)")]
        public double AccMax
        {
            get => _accMax;
            set
            {
                if (Math.Abs(_accMax - value) > 0.001)
                {
                    _accMax = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 最大减速度(毫米/秒²)
        /// </summary>
        [Category("GTS轴运动"), DisplayName("最大减速度(mm/s²)")]
        public double DecMax
        {
            get => _decMax;
            set
            {
                if (Math.Abs(_decMax - value) > 0.001)
                {
                    _decMax = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 移动超时时间(毫秒)
        /// </summary>
        [Category("GTS轴运动"), DisplayName("移动超时(ms)")]
        public int MoveTimeout
        {
            get => _moveTimeout;
            set
            {
                if (_moveTimeout != value)
                {
                    _moveTimeout = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 是否虚拟轴
        /// </summary>
        [Category("GTS轴高级"), DisplayName("是否虚拟轴")]
        public bool IsVirtual
        {
            get => _isVirtual;
            set
            {
                if (_isVirtual != value)
                {
                    _isVirtual = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 限位配置
        /// </summary>
        [Category("GTS轴限位"), DisplayName("限位配置")]
        public override LimitBase Limit { get; set; } = new LimitGTS();

        /// <summary>
        /// 轴状态
        /// </summary>
        [XmlIgnore, Browsable(false)]
        public override StatusBase Status { get; set; } = new StatusAxisGTS();

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public AxisGTS()
        {
            Name = "GTS轴";
            
            // 初始化默认值
            InitializeDefaults();
        }

        /// <summary>
        /// 带参数的构造函数
        /// </summary>
        /// <param name="id">轴ID</param>
        /// <param name="name">轴名称</param>
        public AxisGTS(int id, string name) : this()
        {
            ID = id;
            Name = name;
        }

        #endregion

        #region 方法

        /// <summary>
        /// 初始化默认值
        /// </summary>
        private void InitializeDefaults()
        {
            // 设置默认的运动参数
            VelMax = 100.0;        // 最大速度 100mm/s
            VelJog = 50.0;         // 点动速度 50mm/s
            AccMax = 1000.0;       // 最大加速度 1000mm/s²
            DecMax = 1000.0;       // 最大减速度 1000mm/s²
            
            // 设置默认的限位
            PosLimitMax = 10000.0; // 正向限位 10000mm
            PosLimitMin = -10000.0; // 负向限位 -10000mm
            
            // 设置默认的精度参数
            FollowError = 0.003;   // 跟随误差 0.003mm
            PluseScale = 1000.0;   // 脉冲当量 1000脉冲/mm
            
            // 设置默认的超时时间
            MoveTimeout = 5000;    // 移动超时 5秒
        }

        /// <summary>
        /// 验证轴参数
        /// </summary>
        /// <returns>验证结果</returns>
        public bool ValidateParameters()
        {
            try
            {
                // 检查基本参数
                if (ID <= 0 || ID > 8)
                {
                    System.Diagnostics.Debug.WriteLine($"轴ID {ID} 超出范围 (1-8)");
                    return false;
                }

                if (PluseScale <= 0)
                {
                    System.Diagnostics.Debug.WriteLine($"轴 {Name} 脉冲当量必须大于0");
                    return false;
                }

                if (FollowError <= 0)
                {
                    System.Diagnostics.Debug.WriteLine($"轴 {Name} 跟随误差必须大于0");
                    return false;
                }

                // 检查限位参数
                if (PosLimitMax <= PosLimitMin)
                {
                    System.Diagnostics.Debug.WriteLine($"轴 {Name} 正向限位必须大于负向限位");
                    return false;
                }

                // 检查运动参数
                if (VelMax <= 0)
                {
                    System.Diagnostics.Debug.WriteLine($"轴 {Name} 最大速度必须大于0");
                    return false;
                }

                if (AccMax <= 0)
                {
                    System.Diagnostics.Debug.WriteLine($"轴 {Name} 最大加速度必须大于0");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"验证轴参数异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取轴配置摘要
        /// </summary>
        /// <returns>配置摘要字符串</returns>
        public string GetConfigurationSummary()
        {
            return $"GTS轴 {Name} (ID:{ID}) - 脉冲当量:{PluseScale:F1} - 限位:[{PosLimitMin:F1}, {PosLimitMax:F1}] - 最大速度:{VelMax:F1}mm/s";
        }

        /// <summary>
        /// 重置轴状态
        /// </summary>
        public void ResetStatus()
        {
            if (Status is StatusAxisGTS gtsStatus)
            {
                gtsStatus.Reset();
            }
        }

        #endregion
    }
}
