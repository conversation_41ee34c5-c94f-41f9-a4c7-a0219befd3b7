using System;
using System.ComponentModel;
using McLaser.Device;

namespace McLaser.Devices
{
    /// <summary>
    /// IO基类
    /// 定义数字输入输出的基本功能
    /// </summary>
    [Serializable]
    public class IOBase : ConfigBaseEx
    {
        /// <summary>
        /// IO位号
        /// </summary>
        [Category("IO"), DisplayName("位号")]
        public virtual int Bit { get; set; } = 0;

        /// <summary>
        /// 是否取反
        /// </summary>
        [Category("IO"), DisplayName("是否取反")]
        public virtual bool IsReverse { get; set; } = false;

        /// <summary>
        /// IO值
        /// </summary>
        [Category("IO"), DisplayName("IO值")]
        public virtual IOValue IOValue { get; set; } = IOValue.NotValue;

        /// <summary>
        /// 布尔值
        /// </summary>
        [Category("IO"), DisplayName("布尔值")]
        public virtual bool Value { get; set; } = false;

        /// <summary>
        /// 状态对象
        /// </summary>
        [Browsable(false)]
        public virtual StatusBase Status { get; set; }

        /// <summary>
        /// 所属控制卡
        /// </summary>
        [Browsable(false)]
        public virtual ICard Card { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public IOBase()
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="bit">IO位号</param>
        /// <param name="isReverse">是否取反</param>
        public IOBase(int bit, bool isReverse = false)
        {
            Bit = bit;
            IsReverse = isReverse;
        }

        /// <summary>
        /// 设置IO值
        /// </summary>
        /// <param name="value">要设置的值</param>
        /// <returns>操作是否成功</returns>
        public virtual bool SetValue(IOValue value)
        {
            IOValue = value;
            Value = (value == IOValue.True);
            
            // 如果有关联的控制卡，调用控制卡的设置方法
            if (Card != null)
            {
                bool actualValue = IsReverse ? !Value : Value;
                return Card.SetDo(Bit, actualValue);
            }
            
            return true;
        }

        /// <summary>
        /// 设置IO值（布尔类型）
        /// </summary>
        /// <param name="value">要设置的布尔值</param>
        /// <returns>操作是否成功</returns>
        public virtual bool SetValue(bool value)
        {
            return SetValue(value ? IOValue.True : IOValue.False);
        }

        /// <summary>
        /// 获取IO值
        /// </summary>
        /// <param name="value">输出的IO值</param>
        /// <returns>操作是否成功</returns>
        public virtual bool GetValue(out IOValue value)
        {
            value = IOValue.NotValue;
            
            // 如果有关联的控制卡，从控制卡读取值
            if (Card != null)
            {
                bool actualValue = false;
                if (Card.GetDi(Bit, ref actualValue))
                {
                    // 根据是否取反来确定最终值
                    bool finalValue = IsReverse ? !actualValue : actualValue;
                    value = finalValue ? IOValue.True : IOValue.False;
                    Value = finalValue;
                    IOValue = value;
                    return true;
                }
            }
            else
            {
                // 如果没有关联控制卡，返回当前存储的值
                value = IOValue;
                return true;
            }
            
            return false;
        }

        /// <summary>
        /// 获取IO值（布尔类型）
        /// </summary>
        /// <param name="value">输出的布尔值</param>
        /// <returns>操作是否成功</returns>
        public virtual bool GetValue(out bool value)
        {
            IOValue ioValue;
            bool result = GetValue(out ioValue);
            value = (ioValue == IOValue.True);
            return result;
        }

        /// <summary>
        /// 切换IO值
        /// </summary>
        /// <returns>操作是否成功</returns>
        public virtual bool Toggle()
        {
            return SetValue(!Value);
        }

        /// <summary>
        /// 重写ToString方法
        /// </summary>
        /// <returns>IO信息字符串</returns>
        public override string ToString()
        {
            return $"IO{Bit}: {IOValue} (Value: {Value}, Reverse: {IsReverse})";
        }
    }
}
