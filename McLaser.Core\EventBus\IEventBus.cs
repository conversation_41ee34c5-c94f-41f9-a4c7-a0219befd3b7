using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace McLaser.Core.EventBus
{
    /// <summary>
    /// 事件总线接口
    /// 提供松耦合的事件发布/订阅机制
    /// </summary>
    public interface IEventBus
    {
        #region 事件发布

        /// <summary>
        /// 发布事件
        /// </summary>
        /// <typeparam name="TEvent">事件类型</typeparam>
        /// <param name="eventData">事件数据</param>
        void Publish<TEvent>(TEvent eventData) where TEvent : class;

        /// <summary>
        /// 异步发布事件
        /// </summary>
        /// <typeparam name="TEvent">事件类型</typeparam>
        /// <param name="eventData">事件数据</param>
        /// <returns>异步任务</returns>
        Task PublishAsync<TEvent>(TEvent eventData) where TEvent : class;

        /// <summary>
        /// 发布事件（通过类型）
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="eventData">事件数据</param>
        void Publish(Type eventType, object eventData);

        /// <summary>
        /// 异步发布事件（通过类型）
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="eventData">事件数据</param>
        /// <returns>异步任务</returns>
        Task PublishAsync(Type eventType, object eventData);

        #endregion

        #region 事件订阅

        /// <summary>
        /// 订阅事件
        /// </summary>
        /// <typeparam name="TEvent">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅标识</returns>
        string Subscribe<TEvent>(IEventHandler<TEvent> handler) where TEvent : class;

        /// <summary>
        /// 订阅事件（使用委托）
        /// </summary>
        /// <typeparam name="TEvent">事件类型</typeparam>
        /// <param name="handler">事件处理委托</param>
        /// <returns>订阅标识</returns>
        string Subscribe<TEvent>(Action<TEvent> handler) where TEvent : class;

        /// <summary>
        /// 订阅事件（使用异步委托）
        /// </summary>
        /// <typeparam name="TEvent">事件类型</typeparam>
        /// <param name="handler">异步事件处理委托</param>
        /// <returns>订阅标识</returns>
        string Subscribe<TEvent>(Func<TEvent, Task> handler) where TEvent : class;

        /// <summary>
        /// 订阅事件（通过类型）
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅标识</returns>
        string Subscribe(Type eventType, IEventHandler handler);

        #endregion

        #region 事件取消订阅

        /// <summary>
        /// 取消订阅事件
        /// </summary>
        /// <param name="subscriptionId">订阅标识</param>
        /// <returns>是否取消成功</returns>
        bool Unsubscribe(string subscriptionId);

        /// <summary>
        /// 取消订阅事件
        /// </summary>
        /// <typeparam name="TEvent">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        /// <returns>是否取消成功</returns>
        bool Unsubscribe<TEvent>(IEventHandler<TEvent> handler) where TEvent : class;

        /// <summary>
        /// 取消所有订阅
        /// </summary>
        /// <typeparam name="TEvent">事件类型</typeparam>
        void UnsubscribeAll<TEvent>() where TEvent : class;

        /// <summary>
        /// 取消所有订阅
        /// </summary>
        /// <param name="eventType">事件类型</param>
        void UnsubscribeAll(Type eventType);

        /// <summary>
        /// 清空所有订阅
        /// </summary>
        void Clear();

        #endregion

        #region 事件过滤

        /// <summary>
        /// 添加事件过滤器
        /// </summary>
        /// <param name="filter">事件过滤器</param>
        void AddFilter(IEventFilter filter);

        /// <summary>
        /// 移除事件过滤器
        /// </summary>
        /// <param name="filter">事件过滤器</param>
        void RemoveFilter(IEventFilter filter);

        /// <summary>
        /// 清空事件过滤器
        /// </summary>
        void ClearFilters();

        #endregion

        #region 事件拦截

        /// <summary>
        /// 添加事件拦截器
        /// </summary>
        /// <param name="interceptor">事件拦截器</param>
        void AddInterceptor(IEventInterceptor interceptor);

        /// <summary>
        /// 移除事件拦截器
        /// </summary>
        /// <param name="interceptor">事件拦截器</param>
        void RemoveInterceptor(IEventInterceptor interceptor);

        /// <summary>
        /// 清空事件拦截器
        /// </summary>
        void ClearInterceptors();

        #endregion

        #region 事件监控

        /// <summary>
        /// 事件发布前事件
        /// </summary>
        event EventHandler<EventPublishingEventArgs> EventPublishing;

        /// <summary>
        /// 事件发布后事件
        /// </summary>
        event EventHandler<EventPublishedEventArgs> EventPublished;

        /// <summary>
        /// 事件处理异常事件
        /// </summary>
        event EventHandler<EventHandlingExceptionEventArgs> EventHandlingException;

        /// <summary>
        /// 获取事件总线统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        EventBusStatistics GetStatistics();

        /// <summary>
        /// 重置统计信息
        /// </summary>
        void ResetStatistics();

        #endregion

        #region 事件持久化

        /// <summary>
        /// 启用事件持久化
        /// </summary>
        /// <param name="store">事件存储</param>
        void EnablePersistence(IEventStore store);

        /// <summary>
        /// 禁用事件持久化
        /// </summary>
        void DisablePersistence();

        /// <summary>
        /// 重放事件
        /// </summary>
        /// <param name="fromTimestamp">开始时间</param>
        /// <param name="toTimestamp">结束时间</param>
        /// <returns>异步任务</returns>
        Task ReplayEventsAsync(DateTime fromTimestamp, DateTime? toTimestamp = null);

        #endregion

        #region 配置和管理

        /// <summary>
        /// 获取所有订阅信息
        /// </summary>
        /// <returns>订阅信息集合</returns>
        IEnumerable<EventSubscriptionInfo> GetSubscriptions();

        /// <summary>
        /// 获取指定事件类型的订阅信息
        /// </summary>
        /// <typeparam name="TEvent">事件类型</typeparam>
        /// <returns>订阅信息集合</returns>
        IEnumerable<EventSubscriptionInfo> GetSubscriptions<TEvent>() where TEvent : class;

        /// <summary>
        /// 获取指定事件类型的订阅信息
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <returns>订阅信息集合</returns>
        IEnumerable<EventSubscriptionInfo> GetSubscriptions(Type eventType);

        /// <summary>
        /// 设置事件处理超时时间
        /// </summary>
        /// <param name="timeout">超时时间</param>
        void SetHandlingTimeout(TimeSpan timeout);

        /// <summary>
        /// 设置最大并发处理数
        /// </summary>
        /// <param name="maxConcurrency">最大并发数</param>
        void SetMaxConcurrency(int maxConcurrency);

        /// <summary>
        /// 启用/禁用异步处理
        /// </summary>
        /// <param name="enabled">是否启用</param>
        void SetAsyncProcessing(bool enabled);

        #endregion
    }

    /// <summary>
    /// 事件处理器接口
    /// </summary>
    /// <typeparam name="TEvent">事件类型</typeparam>
    public interface IEventHandler<in TEvent> : IEventHandler where TEvent : class
    {
        /// <summary>
        /// 处理事件
        /// </summary>
        /// <param name="eventData">事件数据</param>
        /// <returns>异步任务</returns>
        Task HandleAsync(TEvent eventData);
    }

    /// <summary>
    /// 事件处理器基接口
    /// </summary>
    public interface IEventHandler
    {
        /// <summary>
        /// 处理器名称
        /// </summary>
        string Name { get; }

        /// <summary>
        /// 处理器优先级（数值越大优先级越高）
        /// </summary>
        int Priority { get; }

        /// <summary>
        /// 是否启用
        /// </summary>
        bool IsEnabled { get; }
    }

    /// <summary>
    /// 事件过滤器接口
    /// </summary>
    public interface IEventFilter
    {
        /// <summary>
        /// 过滤器名称
        /// </summary>
        string Name { get; }

        /// <summary>
        /// 检查事件是否应该被处理
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="eventData">事件数据</param>
        /// <returns>是否应该处理</returns>
        bool ShouldHandle(Type eventType, object eventData);
    }

    /// <summary>
    /// 事件拦截器接口
    /// </summary>
    public interface IEventInterceptor
    {
        /// <summary>
        /// 拦截器名称
        /// </summary>
        string Name { get; }

        /// <summary>
        /// 拦截器优先级（数值越大优先级越高）
        /// </summary>
        int Priority { get; }

        /// <summary>
        /// 事件发布前拦截
        /// </summary>
        /// <param name="context">拦截上下文</param>
        /// <returns>异步任务</returns>
        Task BeforePublishAsync(EventInterceptorContext context);

        /// <summary>
        /// 事件发布后拦截
        /// </summary>
        /// <param name="context">拦截上下文</param>
        /// <returns>异步任务</returns>
        Task AfterPublishAsync(EventInterceptorContext context);

        /// <summary>
        /// 事件处理异常拦截
        /// </summary>
        /// <param name="context">拦截上下文</param>
        /// <param name="exception">异常对象</param>
        /// <returns>异步任务</returns>
        Task OnExceptionAsync(EventInterceptorContext context, Exception exception);
    }

    /// <summary>
    /// 事件存储接口
    /// </summary>
    public interface IEventStore
    {
        /// <summary>
        /// 保存事件
        /// </summary>
        /// <param name="eventData">事件数据</param>
        /// <returns>异步任务</returns>
        Task SaveEventAsync(StoredEvent eventData);

        /// <summary>
        /// 获取事件
        /// </summary>
        /// <param name="fromTimestamp">开始时间</param>
        /// <param name="toTimestamp">结束时间</param>
        /// <returns>事件集合</returns>
        Task<IEnumerable<StoredEvent>> GetEventsAsync(DateTime fromTimestamp, DateTime? toTimestamp = null);

        /// <summary>
        /// 删除事件
        /// </summary>
        /// <param name="beforeTimestamp">删除此时间之前的事件</param>
        /// <returns>删除的事件数量</returns>
        Task<int> DeleteEventsAsync(DateTime beforeTimestamp);
    }
}
