using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace McLaser.Core.Framework.Performance
{
    /// <summary>
    /// 性能计数器接口
    /// 提供性能指标收集和监控功能
    /// </summary>
    public interface IPerformanceCounter
    {
        /// <summary>
        /// 性能指标更新事件
        /// </summary>
        event EventHandler<PerformanceMetricEventArgs>? MetricUpdated;

        /// <summary>
        /// 性能阈值超出事件
        /// </summary>
        event EventHandler<PerformanceThresholdEventArgs>? ThresholdExceeded;

        /// <summary>
        /// 是否启用监控
        /// </summary>
        bool IsEnabled { get; set; }

        /// <summary>
        /// 监控间隔
        /// </summary>
        TimeSpan MonitoringInterval { get; set; }

        // 计数器操作
        /// <summary>
        /// 增加计数器
        /// </summary>
        /// <param name="counterName">计数器名称</param>
        /// <param name="increment">增量</param>
        void IncrementCounter(string counterName, long increment = 1);

        /// <summary>
        /// 减少计数器
        /// </summary>
        /// <param name="counterName">计数器名称</param>
        /// <param name="decrement">减量</param>
        void DecrementCounter(string counterName, long decrement = 1);

        /// <summary>
        /// 设置计数器值
        /// </summary>
        /// <param name="counterName">计数器名称</param>
        /// <param name="value">值</param>
        void SetCounter(string counterName, long value);

        /// <summary>
        /// 获取计数器值
        /// </summary>
        /// <param name="counterName">计数器名称</param>
        /// <returns>计数器值</returns>
        long GetCounter(string counterName);

        /// <summary>
        /// 重置计数器
        /// </summary>
        /// <param name="counterName">计数器名称</param>
        void ResetCounter(string counterName);

        // 计时器操作
        /// <summary>
        /// 开始计时
        /// </summary>
        /// <param name="timerName">计时器名称</param>
        /// <returns>计时器令牌</returns>
        IDisposable StartTimer(string timerName);

        /// <summary>
        /// 记录执行时间
        /// </summary>
        /// <param name="timerName">计时器名称</param>
        /// <param name="duration">持续时间</param>
        void RecordTime(string timerName, TimeSpan duration);

        /// <summary>
        /// 获取平均执行时间
        /// </summary>
        /// <param name="timerName">计时器名称</param>
        /// <returns>平均执行时间</returns>
        TimeSpan GetAverageTime(string timerName);

        /// <summary>
        /// 获取最小执行时间
        /// </summary>
        /// <param name="timerName">计时器名称</param>
        /// <returns>最小执行时间</returns>
        TimeSpan GetMinTime(string timerName);

        /// <summary>
        /// 获取最大执行时间
        /// </summary>
        /// <param name="timerName">计时器名称</param>
        /// <returns>最大执行时间</returns>
        TimeSpan GetMaxTime(string timerName);

        // 采样器操作
        /// <summary>
        /// 记录采样值
        /// </summary>
        /// <param name="samplerName">采样器名称</param>
        /// <param name="value">采样值</param>
        void RecordSample(string samplerName, double value);

        /// <summary>
        /// 获取平均采样值
        /// </summary>
        /// <param name="samplerName">采样器名称</param>
        /// <returns>平均采样值</returns>
        double GetAverageSample(string samplerName);

        /// <summary>
        /// 获取最小采样值
        /// </summary>
        /// <param name="samplerName">采样器名称</param>
        /// <returns>最小采样值</returns>
        double GetMinSample(string samplerName);

        /// <summary>
        /// 获取最大采样值
        /// </summary>
        /// <param name="samplerName">采样器名称</param>
        /// <returns>最大采样值</returns>
        double GetMaxSample(string samplerName);

        // 系统指标
        /// <summary>
        /// 获取CPU使用率
        /// </summary>
        /// <returns>CPU使用率（百分比）</returns>
        double GetCpuUsage();

        /// <summary>
        /// 获取内存使用量
        /// </summary>
        /// <returns>内存使用量（字节）</returns>
        long GetMemoryUsage();

        /// <summary>
        /// 获取可用内存
        /// </summary>
        /// <returns>可用内存（字节）</returns>
        long GetAvailableMemory();

        /// <summary>
        /// 获取GC信息
        /// </summary>
        /// <returns>GC信息</returns>
        GCInfo GetGCInfo();

        /// <summary>
        /// 获取线程数量
        /// </summary>
        /// <returns>线程数量</returns>
        int GetThreadCount();

        /// <summary>
        /// 获取句柄数量
        /// </summary>
        /// <returns>句柄数量</returns>
        int GetHandleCount();

        // 报告和导出
        /// <summary>
        /// 获取所有性能指标
        /// </summary>
        /// <returns>性能指标字典</returns>
        IDictionary<string, PerformanceMetric> GetAllMetrics();

        /// <summary>
        /// 获取性能报告
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>性能报告</returns>
        PerformanceReport GetReport(DateTime? startTime = null, DateTime? endTime = null);

        /// <summary>
        /// 导出性能数据
        /// </summary>
        /// <param name="format">导出格式</param>
        /// <param name="filePath">文件路径</param>
        /// <returns>导出任务</returns>
        Task ExportDataAsync(PerformanceDataFormat format, string filePath);

        // 配置和管理
        /// <summary>
        /// 设置性能阈值
        /// </summary>
        /// <param name="metricName">指标名称</param>
        /// <param name="threshold">阈值</param>
        /// <param name="comparison">比较类型</param>
        void SetThreshold(string metricName, double threshold, ThresholdComparison comparison);

        /// <summary>
        /// 移除性能阈值
        /// </summary>
        /// <param name="metricName">指标名称</param>
        void RemoveThreshold(string metricName);

        /// <summary>
        /// 开始监控
        /// </summary>
        void StartMonitoring();

        /// <summary>
        /// 停止监控
        /// </summary>
        void StopMonitoring();

        /// <summary>
        /// 清除所有数据
        /// </summary>
        void Clear();

        /// <summary>
        /// 重置所有指标
        /// </summary>
        void Reset();
    }

    /// <summary>
    /// 内存分析器接口
    /// </summary>
    public interface IMemoryProfiler
    {
        /// <summary>
        /// 内存泄漏检测事件
        /// </summary>
        event EventHandler<MemoryLeakEventArgs>? MemoryLeakDetected;

        /// <summary>
        /// 大对象堆分析事件
        /// </summary>
        event EventHandler<LargeObjectHeapEventArgs>? LargeObjectHeapAnalyzed;

        /// <summary>
        /// 开始内存分析
        /// </summary>
        void StartProfiling();

        /// <summary>
        /// 停止内存分析
        /// </summary>
        void StopProfiling();

        /// <summary>
        /// 创建内存快照
        /// </summary>
        /// <param name="name">快照名称</param>
        /// <returns>内存快照</returns>
        MemorySnapshot CreateSnapshot(string name);

        /// <summary>
        /// 比较内存快照
        /// </summary>
        /// <param name="snapshot1">快照1</param>
        /// <param name="snapshot2">快照2</param>
        /// <returns>比较结果</returns>
        MemorySnapshotComparison CompareSnapshots(MemorySnapshot snapshot1, MemorySnapshot snapshot2);

        /// <summary>
        /// 检测内存泄漏
        /// </summary>
        /// <returns>内存泄漏信息</returns>
        IEnumerable<MemoryLeakInfo> DetectMemoryLeaks();

        /// <summary>
        /// 分析大对象堆
        /// </summary>
        /// <returns>大对象堆分析结果</returns>
        LargeObjectHeapAnalysis AnalyzeLargeObjectHeap();

        /// <summary>
        /// 获取对象分配统计
        /// </summary>
        /// <returns>对象分配统计</returns>
        ObjectAllocationStatistics GetAllocationStatistics();

        /// <summary>
        /// 强制垃圾回收
        /// </summary>
        /// <param name="generation">GC代数</param>
        void ForceGarbageCollection(int generation = -1);
    }

    /// <summary>
    /// 性能指标
    /// </summary>
    public class PerformanceMetric
    {
        /// <summary>
        /// 指标名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 指标类型
        /// </summary>
        public PerformanceMetricType Type { get; set; }

        /// <summary>
        /// 当前值
        /// </summary>
        public double CurrentValue { get; set; }

        /// <summary>
        /// 最小值
        /// </summary>
        public double MinValue { get; set; }

        /// <summary>
        /// 最大值
        /// </summary>
        public double MaxValue { get; set; }

        /// <summary>
        /// 平均值
        /// </summary>
        public double AverageValue { get; set; }

        /// <summary>
        /// 采样次数
        /// </summary>
        public long SampleCount { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdated { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; } = string.Empty;

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// 性能指标类型
    /// </summary>
    public enum PerformanceMetricType
    {
        /// <summary>
        /// 计数器
        /// </summary>
        Counter,

        /// <summary>
        /// 计时器
        /// </summary>
        Timer,

        /// <summary>
        /// 采样器
        /// </summary>
        Sampler,

        /// <summary>
        /// 仪表
        /// </summary>
        Gauge
    }

    /// <summary>
    /// 阈值比较类型
    /// </summary>
    public enum ThresholdComparison
    {
        /// <summary>
        /// 大于
        /// </summary>
        GreaterThan,

        /// <summary>
        /// 大于等于
        /// </summary>
        GreaterThanOrEqual,

        /// <summary>
        /// 小于
        /// </summary>
        LessThan,

        /// <summary>
        /// 小于等于
        /// </summary>
        LessThanOrEqual,

        /// <summary>
        /// 等于
        /// </summary>
        Equal,

        /// <summary>
        /// 不等于
        /// </summary>
        NotEqual
    }

    /// <summary>
    /// 性能数据导出格式
    /// </summary>
    public enum PerformanceDataFormat
    {
        /// <summary>
        /// JSON格式
        /// </summary>
        Json,

        /// <summary>
        /// XML格式
        /// </summary>
        Xml,

        /// <summary>
        /// CSV格式
        /// </summary>
        Csv,

        /// <summary>
        /// Excel格式
        /// </summary>
        Excel
    }

    /// <summary>
    /// 性能指标事件参数
    /// </summary>
    public class PerformanceMetricEventArgs : EventArgs
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="metric">性能指标</param>
        public PerformanceMetricEventArgs(PerformanceMetric metric)
        {
            Metric = metric;
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// 性能指标
        /// </summary>
        public PerformanceMetric Metric { get; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; }
    }

    /// <summary>
    /// 性能阈值事件参数
    /// </summary>
    public class PerformanceThresholdEventArgs : EventArgs
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="metricName">指标名称</param>
        /// <param name="currentValue">当前值</param>
        /// <param name="threshold">阈值</param>
        /// <param name="comparison">比较类型</param>
        public PerformanceThresholdEventArgs(string metricName, double currentValue, 
                                           double threshold, ThresholdComparison comparison)
        {
            MetricName = metricName;
            CurrentValue = currentValue;
            Threshold = threshold;
            Comparison = comparison;
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// 指标名称
        /// </summary>
        public string MetricName { get; }

        /// <summary>
        /// 当前值
        /// </summary>
        public double CurrentValue { get; }

        /// <summary>
        /// 阈值
        /// </summary>
        public double Threshold { get; }

        /// <summary>
        /// 比较类型
        /// </summary>
        public ThresholdComparison Comparison { get; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; }
    }

    /// <summary>
    /// 性能报告
    /// </summary>
    public class PerformanceReport
    {
        /// <summary>
        /// 报告生成时间
        /// </summary>
        public DateTime GeneratedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 报告开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 报告结束时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 报告持续时间
        /// </summary>
        public TimeSpan Duration => EndTime - StartTime;

        /// <summary>
        /// 性能指标
        /// </summary>
        public IDictionary<string, PerformanceMetric> Metrics { get; set; } = new Dictionary<string, PerformanceMetric>();

        /// <summary>
        /// 系统信息
        /// </summary>
        public SystemInfo SystemInfo { get; set; } = new SystemInfo();

        /// <summary>
        /// 摘要信息
        /// </summary>
        public string Summary { get; set; } = string.Empty;

        /// <summary>
        /// 建议
        /// </summary>
        public IList<string> Recommendations { get; set; } = new List<string>();
    }

    /// <summary>
    /// GC信息
    /// </summary>
    public class GCInfo
    {
        /// <summary>
        /// 第0代GC次数
        /// </summary>
        public int Gen0Collections { get; set; }

        /// <summary>
        /// 第1代GC次数
        /// </summary>
        public int Gen1Collections { get; set; }

        /// <summary>
        /// 第2代GC次数
        /// </summary>
        public int Gen2Collections { get; set; }

        /// <summary>
        /// 总分配字节数
        /// </summary>
        public long TotalAllocatedBytes { get; set; }

        /// <summary>
        /// 托管堆大小
        /// </summary>
        public long ManagedHeapSize { get; set; }

        /// <summary>
        /// 大对象堆大小
        /// </summary>
        public long LargeObjectHeapSize { get; set; }
    }

    /// <summary>
    /// 系统信息
    /// </summary>
    public class SystemInfo
    {
        /// <summary>
        /// 操作系统
        /// </summary>
        public string OperatingSystem { get; set; } = string.Empty;

        /// <summary>
        /// 处理器数量
        /// </summary>
        public int ProcessorCount { get; set; }

        /// <summary>
        /// 总内存
        /// </summary>
        public long TotalMemory { get; set; }

        /// <summary>
        /// 可用内存
        /// </summary>
        public long AvailableMemory { get; set; }

        /// <summary>
        /// 进程名称
        /// </summary>
        public string ProcessName { get; set; } = string.Empty;

        /// <summary>
        /// 进程ID
        /// </summary>
        public int ProcessId { get; set; }

        /// <summary>
        /// 启动时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 运行时间
        /// </summary>
        public TimeSpan UpTime => DateTime.Now - StartTime;
    }

    /// <summary>
    /// 内存快照
    /// </summary>
    public class MemorySnapshot
    {
        /// <summary>
        /// 快照名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 总内存使用量
        /// </summary>
        public long TotalMemoryUsage { get; set; }

        /// <summary>
        /// 托管堆大小
        /// </summary>
        public long ManagedHeapSize { get; set; }

        /// <summary>
        /// 对象数量
        /// </summary>
        public int ObjectCount { get; set; }

        /// <summary>
        /// 类型统计
        /// </summary>
        public IDictionary<string, TypeStatistics> TypeStatistics { get; set; } = new Dictionary<string, TypeStatistics>();
    }

    /// <summary>
    /// 类型统计
    /// </summary>
    public class TypeStatistics
    {
        /// <summary>
        /// 类型名称
        /// </summary>
        public string TypeName { get; set; } = string.Empty;

        /// <summary>
        /// 实例数量
        /// </summary>
        public int InstanceCount { get; set; }

        /// <summary>
        /// 总大小
        /// </summary>
        public long TotalSize { get; set; }

        /// <summary>
        /// 平均大小
        /// </summary>
        public double AverageSize => InstanceCount > 0 ? (double)TotalSize / InstanceCount : 0;
    }

    /// <summary>
    /// 内存快照比较结果
    /// </summary>
    public class MemorySnapshotComparison
    {
        /// <summary>
        /// 快照1
        /// </summary>
        public MemorySnapshot Snapshot1 { get; set; } = new MemorySnapshot();

        /// <summary>
        /// 快照2
        /// </summary>
        public MemorySnapshot Snapshot2 { get; set; } = new MemorySnapshot();

        /// <summary>
        /// 内存使用量差异
        /// </summary>
        public long MemoryUsageDifference { get; set; }

        /// <summary>
        /// 对象数量差异
        /// </summary>
        public int ObjectCountDifference { get; set; }

        /// <summary>
        /// 新增类型
        /// </summary>
        public IList<string> NewTypes { get; set; } = new List<string>();

        /// <summary>
        /// 移除类型
        /// </summary>
        public IList<string> RemovedTypes { get; set; } = new List<string>();

        /// <summary>
        /// 类型差异
        /// </summary>
        public IDictionary<string, TypeDifference> TypeDifferences { get; set; } = new Dictionary<string, TypeDifference>();
    }

    /// <summary>
    /// 类型差异
    /// </summary>
    public class TypeDifference
    {
        /// <summary>
        /// 类型名称
        /// </summary>
        public string TypeName { get; set; } = string.Empty;

        /// <summary>
        /// 实例数量差异
        /// </summary>
        public int InstanceCountDifference { get; set; }

        /// <summary>
        /// 大小差异
        /// </summary>
        public long SizeDifference { get; set; }
    }

    /// <summary>
    /// 内存泄漏信息
    /// </summary>
    public class MemoryLeakInfo
    {
        /// <summary>
        /// 类型名称
        /// </summary>
        public string TypeName { get; set; } = string.Empty;

        /// <summary>
        /// 泄漏对象数量
        /// </summary>
        public int LeakedObjectCount { get; set; }

        /// <summary>
        /// 泄漏内存大小
        /// </summary>
        public long LeakedMemorySize { get; set; }

        /// <summary>
        /// 检测时间
        /// </summary>
        public DateTime DetectedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 严重级别
        /// </summary>
        public MemoryLeakSeverity Severity { get; set; }
    }

    /// <summary>
    /// 内存泄漏严重级别
    /// </summary>
    public enum MemoryLeakSeverity
    {
        /// <summary>
        /// 低
        /// </summary>
        Low,

        /// <summary>
        /// 中
        /// </summary>
        Medium,

        /// <summary>
        /// 高
        /// </summary>
        High,

        /// <summary>
        /// 严重
        /// </summary>
        Critical
    }

    /// <summary>
    /// 内存泄漏事件参数
    /// </summary>
    public class MemoryLeakEventArgs : EventArgs
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="leakInfo">内存泄漏信息</param>
        public MemoryLeakEventArgs(MemoryLeakInfo leakInfo)
        {
            LeakInfo = leakInfo;
        }

        /// <summary>
        /// 内存泄漏信息
        /// </summary>
        public MemoryLeakInfo LeakInfo { get; }
    }

    /// <summary>
    /// 大对象堆分析结果
    /// </summary>
    public class LargeObjectHeapAnalysis
    {
        /// <summary>
        /// 大对象堆大小
        /// </summary>
        public long Size { get; set; }

        /// <summary>
        /// 大对象数量
        /// </summary>
        public int ObjectCount { get; set; }

        /// <summary>
        /// 碎片化程度
        /// </summary>
        public double FragmentationLevel { get; set; }

        /// <summary>
        /// 大对象类型统计
        /// </summary>
        public IDictionary<string, TypeStatistics> TypeStatistics { get; set; } = new Dictionary<string, TypeStatistics>();
    }

    /// <summary>
    /// 大对象堆事件参数
    /// </summary>
    public class LargeObjectHeapEventArgs : EventArgs
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="analysis">分析结果</param>
        public LargeObjectHeapEventArgs(LargeObjectHeapAnalysis analysis)
        {
            Analysis = analysis;
        }

        /// <summary>
        /// 分析结果
        /// </summary>
        public LargeObjectHeapAnalysis Analysis { get; }
    }

    /// <summary>
    /// 对象分配统计
    /// </summary>
    public class ObjectAllocationStatistics
    {
        /// <summary>
        /// 总分配次数
        /// </summary>
        public long TotalAllocations { get; set; }

        /// <summary>
        /// 总分配字节数
        /// </summary>
        public long TotalAllocatedBytes { get; set; }

        /// <summary>
        /// 按类型分组的分配统计
        /// </summary>
        public IDictionary<string, AllocationStatistics> AllocationsByType { get; set; } = new Dictionary<string, AllocationStatistics>();

        /// <summary>
        /// 分配速率（对象/秒）
        /// </summary>
        public double AllocationRate { get; set; }
    }

    /// <summary>
    /// 分配统计
    /// </summary>
    public class AllocationStatistics
    {
        /// <summary>
        /// 分配次数
        /// </summary>
        public long AllocationCount { get; set; }

        /// <summary>
        /// 分配字节数
        /// </summary>
        public long AllocatedBytes { get; set; }

        /// <summary>
        /// 平均对象大小
        /// </summary>
        public double AverageObjectSize => AllocationCount > 0 ? (double)AllocatedBytes / AllocationCount : 0;
    }
}
