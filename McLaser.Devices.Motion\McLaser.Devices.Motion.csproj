<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A3B4C5D6-E7F8-A9B0-C1D2-E3F4A5B6C7D8}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>McLaser.Devices.Motion</RootNamespace>
    <AssemblyName>McLaser.Devices.Motion</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <LangVersion>11.0</LangVersion>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\..\bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\..\bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>..\..\bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="WindowsBase" />
    <Reference Include="System.Xaml" />
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\..\bin\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="CommunityToolkit.Mvvm, Version=8.2.0.0, Culture=neutral, PublicKeyToken=4aff67a105548ee2, processorArchitecture=MSIL">
      <HintPath>..\..\bin\CommunityToolkit.Mvvm.dll</HintPath>
    </Reference>
    <Reference Include="ODT.PMACGlobal, Version=4.1.0.24, Culture=neutral, PublicKeyToken=4c28215a553f9efa, processorArchitecture=MSIL">
      <HintPath>..\..\bin\ODT.PMACGlobal.dll</HintPath>
    </Reference>
    <Reference Include="ODT.PowerPmacComLib, Version=4.1.0.24, Culture=neutral, PublicKeyToken=4c28215a553f9efa, processorArchitecture=MSIL">
      <HintPath>..\..\bin\ODT.PowerPmacComLib.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Pmac\CardCoordinate.cs" />
    <Compile Include="Pmac\CoordinateStatus.cs" />
    <Compile Include="Pmac\Views\PmacConfigControl.xaml.cs">
      <DependentUpon>PmacConfigControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Pmac\CardPmac.cs" />
    <Compile Include="Pmac\AxisPmac.cs" />
    <Compile Include="Pmac\AxisPmacStatus.cs" />
    <Compile Include="Pmac\StatusPmac.cs" />
    <Compile Include="Pmac\IOPmac.cs" />
    <Compile Include="Pmac\LimitPmac.cs" />
    <Compile Include="Virtual\AxisVirtual.cs" />
    <Compile Include="Virtual\CardVirtual.cs" />
    <Compile Include="Virtual\IOVirtual.cs" />
  </ItemGroup>
  <ItemGroup>
    <Page Include="Pmac\Views\PmacConfigControl.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\McLaser.Core\McLaser.Core.csproj">
      <Project>{B67B75F4-B65B-4FB4-9E9B-F86D2AA29B38}</Project>
      <Name>McLaser.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\McLaser.Device\McLaser.Device.csproj">
      <Project>{f87277ce-d77d-47e0-b602-335625f0dba3}</Project>
      <Name>McLaser.Device</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="GTS\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>