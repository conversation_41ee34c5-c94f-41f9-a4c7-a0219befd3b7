﻿using System;
using System.Collections.Generic;

namespace McLaser.Core.Modules.RecipeManager
{
    public interface IRecipe
    {
        Guid Guid { get; }
        string Name { get; set; }
        string Path { get; set; }
        string ModifyUser { get; set; }
        DateTime ModifyTime { get; set; }
        IEnumerable<IRecipeItem> RecipeItems { get; set; }


        bool Load();
        bool Save();

    }
}
