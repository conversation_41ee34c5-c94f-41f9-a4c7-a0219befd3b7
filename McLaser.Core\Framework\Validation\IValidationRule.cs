using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace McLaser.Core.Framework.Validation
{
    /// <summary>
    /// 验证规则接口
    /// </summary>
    /// <typeparam name="T">验证对象类型</typeparam>
    public interface IValidationRule<in T>
    {
        /// <summary>
        /// 规则名称
        /// </summary>
        string RuleName { get; }

        /// <summary>
        /// 错误消息
        /// </summary>
        string ErrorMessage { get; }

        /// <summary>
        /// 验证优先级
        /// </summary>
        int Priority { get; }

        /// <summary>
        /// 是否启用
        /// </summary>
        bool IsEnabled { get; }

        /// <summary>
        /// 验证对象
        /// </summary>
        /// <param name="value">要验证的值</param>
        /// <returns>验证结果</returns>
        ValidationResult Validate(T value);

        /// <summary>
        /// 异步验证对象
        /// </summary>
        /// <param name="value">要验证的值</param>
        /// <returns>验证结果</returns>
        Task<ValidationResult> ValidateAsync(T value);
    }

    /// <summary>
    /// 属性验证规则接口
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <typeparam name="TProperty">属性类型</typeparam>
    public interface IPropertyValidationRule<in T, TProperty> : IValidationRule<T>
    {
        /// <summary>
        /// 属性名称
        /// </summary>
        string PropertyName { get; }

        /// <summary>
        /// 属性选择器
        /// </summary>
        Func<T, TProperty> PropertySelector { get; }

        /// <summary>
        /// 验证属性值
        /// </summary>
        /// <param name="obj">对象实例</param>
        /// <param name="propertyValue">属性值</param>
        /// <returns>验证结果</returns>
        ValidationResult ValidateProperty(T obj, TProperty propertyValue);
    }

    /// <summary>
    /// 条件验证规则接口
    /// </summary>
    /// <typeparam name="T">验证对象类型</typeparam>
    public interface IConditionalValidationRule<in T> : IValidationRule<T>
    {
        /// <summary>
        /// 条件检查
        /// </summary>
        /// <param name="value">要验证的值</param>
        /// <returns>是否满足验证条件</returns>
        bool ShouldValidate(T value);
    }

    /// <summary>
    /// 复合验证规则接口
    /// </summary>
    /// <typeparam name="T">验证对象类型</typeparam>
    public interface ICompositeValidationRule<T> : IValidationRule<T>
    {
        /// <summary>
        /// 子规则列表
        /// </summary>
        IReadOnlyList<IValidationRule<T>> ChildRules { get; }

        /// <summary>
        /// 组合逻辑
        /// </summary>
        ValidationCompositionType CompositionType { get; }

        /// <summary>
        /// 添加子规则
        /// </summary>
        /// <param name="rule">验证规则</param>
        void AddRule(IValidationRule<T> rule);

        /// <summary>
        /// 移除子规则
        /// </summary>
        /// <param name="rule">验证规则</param>
        /// <returns>是否成功移除</returns>
        bool RemoveRule(IValidationRule<T> rule);
    }

    /// <summary>
    /// 验证结果
    /// </summary>
    public class ValidationResult
    {
        /// <summary>
        /// 构造函数 - 成功结果
        /// </summary>
        public ValidationResult()
        {
            IsValid = true;
            Errors = new List<ValidationError>();
        }

        /// <summary>
        /// 构造函数 - 失败结果
        /// </summary>
        /// <param name="error">验证错误</param>
        public ValidationResult(ValidationError error)
        {
            IsValid = false;
            Errors = new List<ValidationError> { error };
        }

        /// <summary>
        /// 构造函数 - 失败结果
        /// </summary>
        /// <param name="errors">验证错误列表</param>
        public ValidationResult(IEnumerable<ValidationError> errors)
        {
            var errorList = errors?.ToList() ?? new List<ValidationError>();
            IsValid = errorList.Count == 0;
            Errors = errorList;
        }

        /// <summary>
        /// 是否验证成功
        /// </summary>
        public bool IsValid { get; }

        /// <summary>
        /// 验证错误列表
        /// </summary>
        public IReadOnlyList<ValidationError> Errors { get; }

        /// <summary>
        /// 创建成功结果
        /// </summary>
        /// <returns>成功的验证结果</returns>
        public static ValidationResult Success()
        {
            return new ValidationResult();
        }

        /// <summary>
        /// 创建失败结果
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <param name="propertyName">属性名称</param>
        /// <returns>失败的验证结果</returns>
        public static ValidationResult Failure(string errorMessage, string? propertyName = null)
        {
            return new ValidationResult(new ValidationError(errorMessage, propertyName));
        }

        /// <summary>
        /// 创建失败结果
        /// </summary>
        /// <param name="errors">错误列表</param>
        /// <returns>失败的验证结果</returns>
        public static ValidationResult Failure(IEnumerable<ValidationError> errors)
        {
            return new ValidationResult(errors);
        }

        /// <summary>
        /// 合并验证结果
        /// </summary>
        /// <param name="other">其他验证结果</param>
        /// <returns>合并后的验证结果</returns>
        public ValidationResult Merge(ValidationResult other)
        {
            if (other == null)
                return this;

            if (IsValid && other.IsValid)
                return Success();

            var allErrors = new List<ValidationError>();
            allErrors.AddRange(Errors);
            allErrors.AddRange(other.Errors);

            return new ValidationResult(allErrors);
        }
    }

    /// <summary>
    /// 验证错误
    /// </summary>
    public class ValidationError
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <param name="propertyName">属性名称</param>
        /// <param name="errorCode">错误代码</param>
        public ValidationError(string errorMessage, string? propertyName = null, string? errorCode = null)
        {
            ErrorMessage = errorMessage ?? throw new ArgumentNullException(nameof(errorMessage));
            PropertyName = propertyName;
            ErrorCode = errorCode;
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; }

        /// <summary>
        /// 属性名称
        /// </summary>
        public string? PropertyName { get; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public string? ErrorCode { get; }

        /// <summary>
        /// 错误时间戳
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// 严重级别
        /// </summary>
        public ValidationSeverity Severity { get; set; } = ValidationSeverity.Error;

        /// <summary>
        /// 附加数据
        /// </summary>
        public Dictionary<string, object> AdditionalData { get; } = new Dictionary<string, object>();

        /// <summary>
        /// 转换为字符串
        /// </summary>
        /// <returns>错误描述</returns>
        public override string ToString()
        {
            if (string.IsNullOrEmpty(PropertyName))
                return ErrorMessage;

            return $"{PropertyName}: {ErrorMessage}";
        }
    }

    /// <summary>
    /// 验证严重级别
    /// </summary>
    public enum ValidationSeverity
    {
        /// <summary>
        /// 信息
        /// </summary>
        Info,

        /// <summary>
        /// 警告
        /// </summary>
        Warning,

        /// <summary>
        /// 错误
        /// </summary>
        Error,

        /// <summary>
        /// 致命错误
        /// </summary>
        Fatal
    }

    /// <summary>
    /// 验证组合类型
    /// </summary>
    public enum ValidationCompositionType
    {
        /// <summary>
        /// 所有规则都必须通过
        /// </summary>
        And,

        /// <summary>
        /// 至少一个规则通过
        /// </summary>
        Or,

        /// <summary>
        /// 只有一个规则通过
        /// </summary>
        Xor,

        /// <summary>
        /// 所有规则都不能通过
        /// </summary>
        Not
    }

    /// <summary>
    /// 验证上下文
    /// </summary>
    public class ValidationContext
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="objectInstance">对象实例</param>
        /// <param name="memberName">成员名称</param>
        public ValidationContext(object objectInstance, string? memberName = null)
        {
            ObjectInstance = objectInstance;
            MemberName = memberName;
            Items = new Dictionary<string, object>();
        }

        /// <summary>
        /// 对象实例
        /// </summary>
        public object ObjectInstance { get; }

        /// <summary>
        /// 成员名称
        /// </summary>
        public string? MemberName { get; }

        /// <summary>
        /// 附加项目
        /// </summary>
        public Dictionary<string, object> Items { get; }

        /// <summary>
        /// 显示名称
        /// </summary>
        public string? DisplayName { get; set; }

        /// <summary>
        /// 对象类型
        /// </summary>
        public Type ObjectType => ObjectInstance?.GetType() ?? typeof(object);
    }
}
