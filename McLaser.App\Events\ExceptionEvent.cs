using System;

namespace McLaser.App.Events
{
    /// <summary>
    /// 异常事件
    /// 用于传递系统中发生的异常信息
    /// </summary>
    public class ExceptionEvent : IEvent
    {
        /// <summary>
        /// 事件ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 事件时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 异常消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 事件来源
        /// </summary>
        public string Source { get; set; } = "System";

        /// <summary>
        /// 事件优先级
        /// </summary>
        public EventPriority Priority { get; set; } = EventPriority.High;

        /// <summary>
        /// 是否需要持久化
        /// </summary>
        public bool RequiresPersistence { get; set; } = true;

        /// <summary>
        /// 扩展数据
        /// </summary>
        public object? Data { get; set; }

        /// <summary>
        /// 异常类型
        /// </summary>
        public string ExceptionType { get; set; } = string.Empty;

        /// <summary>
        /// 堆栈跟踪
        /// </summary>
        public string StackTrace { get; set; } = string.Empty;

        /// <summary>
        /// 内部异常消息
        /// </summary>
        public string InnerExceptionMessage { get; set; } = string.Empty;

        /// <summary>
        /// 异常级别
        /// </summary>
        public ExceptionLevel Level { get; set; } = ExceptionLevel.Error;

        /// <summary>
        /// 模块名称
        /// </summary>
        public string ModuleName { get; set; } = string.Empty;

        /// <summary>
        /// 方法名称
        /// </summary>
        public string MethodName { get; set; } = string.Empty;

        /// <summary>
        /// 用户ID（如果适用）
        /// </summary>
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 是否已处理
        /// </summary>
        public bool IsHandled { get; set; } = false;

        /// <summary>
        /// 构造函数
        /// </summary>
        public ExceptionEvent()
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <param name="source">来源</param>
        public ExceptionEvent(Exception exception, string source = "System")
        {
            Message = exception.Message;
            Source = source;
            ExceptionType = exception.GetType().Name;
            StackTrace = exception.StackTrace ?? string.Empty;
            InnerExceptionMessage = exception.InnerException?.Message ?? string.Empty;
            
            // 根据异常类型设置级别和优先级
            SetLevelAndPriority(exception);
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="message">异常消息</param>
        /// <param name="source">来源</param>
        /// <param name="level">异常级别</param>
        public ExceptionEvent(string message, string source = "System", ExceptionLevel level = ExceptionLevel.Error)
        {
            Message = message;
            Source = source;
            Level = level;
            
            // 根据级别设置优先级
            Priority = level switch
            {
                ExceptionLevel.Critical => EventPriority.Critical,
                ExceptionLevel.Error => EventPriority.High,
                ExceptionLevel.Warning => EventPriority.Normal,
                ExceptionLevel.Info => EventPriority.Low,
                _ => EventPriority.High
            };
        }

        /// <summary>
        /// 根据异常类型设置级别和优先级
        /// </summary>
        /// <param name="exception">异常对象</param>
        private void SetLevelAndPriority(Exception exception)
        {
            switch (exception)
            {
                case OutOfMemoryException:
                case StackOverflowException:
                case AccessViolationException:
                    Level = ExceptionLevel.Critical;
                    Priority = EventPriority.Critical;
                    break;
                
                case ArgumentNullException:
                case ArgumentException:
                case InvalidOperationException:
                case NotSupportedException:
                    Level = ExceptionLevel.Error;
                    Priority = EventPriority.High;
                    break;
                
                case TimeoutException:
                case System.IO.IOException:
                    Level = ExceptionLevel.Warning;
                    Priority = EventPriority.Normal;
                    break;
                
                default:
                    Level = ExceptionLevel.Error;
                    Priority = EventPriority.High;
                    break;
            }
        }

        /// <summary>
        /// 获取事件描述
        /// </summary>
        /// <returns>事件描述</returns>
        public override string ToString()
        {
            return $"Exception: [{Level}] {ExceptionType} - {Message} (Source: {Source})";
        }
    }

    /// <summary>
    /// 异常级别枚举
    /// </summary>
    public enum ExceptionLevel
    {
        /// <summary>
        /// 信息
        /// </summary>
        Info,

        /// <summary>
        /// 警告
        /// </summary>
        Warning,

        /// <summary>
        /// 错误
        /// </summary>
        Error,

        /// <summary>
        /// 严重错误
        /// </summary>
        Critical
    }
}
