<Window x:Class="McLaser.App.Views.ExceptionDemoWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="异常处理演示"
        Height="700" Width="1000"
        MinHeight="600" MinWidth="800"
        WindowStartupLocation="CenterScreen">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        
        <!-- 顶部工具栏 -->
        <ToolBar Grid.Row="0">
            <Button Content="初始化异常处理器" Command="{Binding InitializeExceptionHandlerCommand}" />
            <Separator />
            <Button Content="触发Info异常" Command="{Binding TriggerInfoExceptionCommand}" />
            <Button Content="触发Warning异常" Command="{Binding TriggerWarningExceptionCommand}" />
            <Button Content="触发Error异常" Command="{Binding TriggerErrorExceptionCommand}" />
            <Button Content="触发Critical异常" Command="{Binding TriggerCriticalExceptionCommand}" />
            <Separator />
            <Button Content="清空异常日志" Command="{Binding ClearExceptionLogCommand}" />
            <Button Content="导出异常报告" Command="{Binding ExportExceptionReportCommand}" />
        </ToolBar>
        
        <!-- 主内容区域 -->
        <Grid Grid.Row="1" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="5" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            
            <!-- 左侧：异常触发控制面板 -->
            <GroupBox Grid.Column="0" Header="异常触发控制面板" Padding="10">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!-- 异常处理器状态 -->
                        <GroupBox Header="异常处理器状态" Margin="0,0,0,15">
                            <StackPanel>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    
                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="状态：" Margin="0,0,10,5" />
                                    <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding ExceptionHandlerStatus}" FontWeight="Bold" Margin="0,0,0,5" />
                                    
                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="处理器数量：" Margin="0,0,10,5" />
                                    <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding HandlerCount}" Margin="0,0,0,5" />
                                    
                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="已处理异常：" Margin="0,0,10,5" />
                                    <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding HandledException}" Margin="0,0,0,5" />
                                    
                                    <TextBlock Grid.Row="3" Grid.Column="0" Text="未处理异常：" Margin="0,0,10,5" />
                                    <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding UnhandledException}" Foreground="Red" Margin="0,0,0,5" />
                                    
                                    <TextBlock Grid.Row="4" Grid.Column="0" Text="恢复次数：" Margin="0,0,10,5" />
                                    <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding RecoveryCount}" Foreground="Green" Margin="0,0,0,5" />
                                </Grid>
                            </StackPanel>
                        </GroupBox>
                        
                        <!-- 异常级别测试 -->
                        <GroupBox Header="异常级别测试" Margin="0,0,0,15">
                            <StackPanel>
                                <Button Content="Info级别异常" Command="{Binding TriggerInfoExceptionCommand}" 
                                        Margin="0,0,0,5" Padding="10,5" Background="LightBlue" />
                                <Button Content="Warning级别异常" Command="{Binding TriggerWarningExceptionCommand}" 
                                        Margin="0,0,0,5" Padding="10,5" Background="Orange" />
                                <Button Content="Error级别异常" Command="{Binding TriggerErrorExceptionCommand}" 
                                        Margin="0,0,0,5" Padding="10,5" Background="LightCoral" />
                                <Button Content="Critical级别异常" Command="{Binding TriggerCriticalExceptionCommand}" 
                                        Margin="0,0,0,10" Padding="10,5" Background="Red" Foreground="White" />
                                
                                <TextBlock Text="自定义异常消息：" Margin="0,10,0,5" />
                                <TextBox Text="{Binding CustomExceptionMessage}" Height="60" TextWrapping="Wrap" 
                                         Margin="0,0,0,10" />
                                
                                <Button Content="触发自定义异常" Command="{Binding TriggerCustomExceptionCommand}" 
                                        Padding="10,5" />
                            </StackPanel>
                        </GroupBox>
                        
                        <!-- 异常恢复测试 -->
                        <GroupBox Header="异常恢复测试" Margin="0,0,0,15">
                            <StackPanel>
                                <CheckBox Content="启用自动恢复" IsChecked="{Binding EnableAutoRecovery}" Margin="0,0,0,5" />
                                <CheckBox Content="启用用户确认恢复" IsChecked="{Binding EnableUserConfirmRecovery}" Margin="0,0,0,5" />
                                <CheckBox Content="启用异常重试" IsChecked="{Binding EnableExceptionRetry}" Margin="0,0,0,10" />
                                
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                    <TextBlock Text="最大重试次数：" VerticalAlignment="Center" Margin="0,0,10,0" />
                                    <TextBox Text="{Binding MaxRetryCount}" Width="60" />
                                </StackPanel>
                                
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                    <TextBlock Text="重试间隔(秒)：" VerticalAlignment="Center" Margin="0,0,10,0" />
                                    <TextBox Text="{Binding RetryIntervalSeconds}" Width="60" />
                                </StackPanel>
                                
                                <Button Content="测试异常恢复" Command="{Binding TestExceptionRecoveryCommand}" 
                                        Padding="10,5" />
                            </StackPanel>
                        </GroupBox>
                        
                        <!-- 全局异常处理 -->
                        <GroupBox Header="全局异常处理">
                            <StackPanel>
                                <CheckBox Content="启用全局异常捕获" IsChecked="{Binding EnableGlobalExceptionHandling}" Margin="0,0,0,5" />
                                <CheckBox Content="启用未处理异常日志" IsChecked="{Binding EnableUnhandledExceptionLogging}" Margin="0,0,0,5" />
                                <CheckBox Content="启用异常通知" IsChecked="{Binding EnableExceptionNotification}" Margin="0,0,0,10" />
                                
                                <Button Content="触发未处理异常" Command="{Binding TriggerUnhandledExceptionCommand}" 
                                        Padding="10,5" Background="DarkRed" Foreground="White" />
                            </StackPanel>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </GroupBox>
            
            <!-- 分隔符 -->
            <GridSplitter Grid.Column="1" HorizontalAlignment="Stretch" Background="LightGray" />
            
            <!-- 右侧：异常日志和统计 -->
            <GroupBox Grid.Column="2" Header="异常日志和统计" Padding="10">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="150" />
                    </Grid.RowDefinitions>
                    
                    <!-- 统计信息 -->
                    <GroupBox Grid.Row="0" Header="异常统计" Margin="0,0,0,10">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="{Binding TotalExceptions, StringFormat='总异常数: {0}'}" Margin="0,0,0,3" />
                                <TextBlock Text="{Binding InfoExceptions, StringFormat='Info: {0}'}" Margin="0,0,0,3" Foreground="Blue" />
                                <TextBlock Text="{Binding WarningExceptions, StringFormat='Warning: {0}'}" Margin="0,0,0,3" Foreground="Orange" />
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="{Binding ErrorExceptions, StringFormat='Error: {0}'}" Margin="0,0,0,3" Foreground="Red" />
                                <TextBlock Text="{Binding CriticalExceptions, StringFormat='Critical: {0}'}" Margin="0,0,0,3" Foreground="DarkRed" />
                                <TextBlock Text="{Binding LastExceptionTime, StringFormat='最后异常: {0:HH:mm:ss}'}" Margin="0,0,0,3" />
                            </StackPanel>
                        </Grid>
                    </GroupBox>
                    
                    <!-- 异常日志 -->
                    <GroupBox Grid.Row="1" Header="异常日志" Margin="0,0,0,10">
                        <ListBox ItemsSource="{Binding ExceptionLogs}" 
                                 ScrollViewer.HorizontalScrollBarVisibility="Auto"
                                 ScrollViewer.VerticalScrollBarVisibility="Auto">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="80" />
                                            <ColumnDefinition Width="80" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        
                                        <TextBlock Grid.Column="0" Text="{Binding Timestamp, StringFormat='{}{0:HH:mm:ss}'}" 
                                                   FontFamily="Consolas" FontSize="10" />
                                        <TextBlock Grid.Column="1" Text="{Binding Level}" 
                                                   FontWeight="Bold" FontSize="10" />
                                        <TextBlock Grid.Column="2" Text="{Binding Message}" 
                                                   TextWrapping="Wrap" FontSize="10" />
                                    </Grid>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                    </GroupBox>
                    
                    <!-- 异常处理器列表 -->
                    <TextBlock Grid.Row="2" Text="活跃异常处理器" FontWeight="Bold" Margin="0,0,0,5" />
                    <ListBox Grid.Row="3" ItemsSource="{Binding ActiveHandlers}">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    
                                    <TextBlock Grid.Column="0" Text="{Binding Name}" FontWeight="Bold" />
                                    <TextBlock Grid.Column="1" Text="{Binding Level}" Margin="10,0,0,0" />
                                    <TextBlock Grid.Column="2" Text="{Binding HandledCount, StringFormat='处理: {0}'}" 
                                               Margin="10,0,0,0" FontSize="10" />
                                </Grid>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </Grid>
            </GroupBox>
        </Grid>
        
        <!-- 状态栏 -->
        <StatusBar Grid.Row="2">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}" />
            </StatusBarItem>
            <Separator />
            <StatusBarItem>
                <TextBlock Text="{Binding ExceptionHandlerStatus, StringFormat='异常处理器状态: {0}'}" />
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
