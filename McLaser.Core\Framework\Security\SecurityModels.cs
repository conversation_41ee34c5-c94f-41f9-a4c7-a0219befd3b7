using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace McLaser.Core.Framework.Security
{
    #region 用户模型

    /// <summary>
    /// 用户实体
    /// </summary>
    public class User
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 用户名
        /// </summary>
        [Required]
        [StringLength(50, MinimumLength = 3)]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 邮箱
        /// </summary>
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 显示名称
        /// </summary>
        [StringLength(100)]
        public string? DisplayName { get; set; }

        /// <summary>
        /// 名字
        /// </summary>
        [StringLength(50)]
        public string? FirstName { get; set; }

        /// <summary>
        /// 姓氏
        /// </summary>
        [StringLength(50)]
        public string? LastName { get; set; }

        /// <summary>
        /// 电话号码
        /// </summary>
        [Phone]
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// 头像URL
        /// </summary>
        public string? AvatarUrl { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 是否锁定
        /// </summary>
        public bool IsLocked { get; set; } = false;

        /// <summary>
        /// 锁定结束时间
        /// </summary>
        public DateTime? LockoutEnd { get; set; }

        /// <summary>
        /// 邮箱是否已确认
        /// </summary>
        public bool EmailConfirmed { get; set; } = false;

        /// <summary>
        /// 电话号码是否已确认
        /// </summary>
        public bool PhoneNumberConfirmed { get; set; } = false;

        /// <summary>
        /// 是否启用双因素认证
        /// </summary>
        public bool TwoFactorEnabled { get; set; } = false;

        /// <summary>
        /// 失败登录次数
        /// </summary>
        public int AccessFailedCount { get; set; } = 0;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 最后登录时间
        /// </summary>
        public DateTime? LastLoginAt { get; set; }

        /// <summary>
        /// 最后活动时间
        /// </summary>
        public DateTime? LastActivityAt { get; set; }

        /// <summary>
        /// 用户角色列表
        /// </summary>
        public List<string> RoleIds { get; set; } = new List<string>();

        /// <summary>
        /// 用户直接权限列表
        /// </summary>
        public List<string> PermissionIds { get; set; } = new List<string>();

        /// <summary>
        /// 用户属性（扩展字段）
        /// </summary>
        public Dictionary<string, object> Properties { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 获取全名
        /// </summary>
        public string FullName => $"{FirstName} {LastName}".Trim();

        /// <summary>
        /// 获取显示名称或用户名
        /// </summary>
        public string DisplayNameOrUsername => !string.IsNullOrEmpty(DisplayName) ? DisplayName : Username;
    }

    #endregion

    #region 角色模型

    /// <summary>
    /// 角色实体
    /// </summary>
    public class Role
    {
        /// <summary>
        /// 角色ID
        /// </summary>
        [Required]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 角色名称
        /// </summary>
        [Required]
        [StringLength(50, MinimumLength = 2)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 角色显示名称
        /// </summary>
        [StringLength(100)]
        public string? DisplayName { get; set; }

        /// <summary>
        /// 角色描述
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 是否系统角色
        /// </summary>
        public bool IsSystemRole { get; set; } = false;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 角色级别（用于层次结构）
        /// </summary>
        public int Level { get; set; } = 0;

        /// <summary>
        /// 父角色ID
        /// </summary>
        public string? ParentRoleId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 角色权限列表
        /// </summary>
        public List<string> PermissionIds { get; set; } = new List<string>();

        /// <summary>
        /// 角色属性（扩展字段）
        /// </summary>
        public Dictionary<string, object> Properties { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 获取显示名称或角色名称
        /// </summary>
        public string DisplayNameOrName => !string.IsNullOrEmpty(DisplayName) ? DisplayName : Name;
    }

    #endregion

    #region 权限模型

    /// <summary>
    /// 权限实体
    /// </summary>
    public class Permission
    {
        /// <summary>
        /// 权限ID
        /// </summary>
        [Required]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 权限名称（唯一标识符）
        /// </summary>
        [Required]
        [StringLength(100, MinimumLength = 3)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 权限显示名称
        /// </summary>
        [StringLength(100)]
        public string? DisplayName { get; set; }

        /// <summary>
        /// 权限描述
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 权限类别
        /// </summary>
        [StringLength(50)]
        public string? Category { get; set; }

        /// <summary>
        /// 权限资源（如控制器、页面等）
        /// </summary>
        [StringLength(100)]
        public string? Resource { get; set; }

        /// <summary>
        /// 权限操作（如读取、写入、删除等）
        /// </summary>
        [StringLength(50)]
        public string? Action { get; set; }

        /// <summary>
        /// 是否系统权限
        /// </summary>
        public bool IsSystemPermission { get; set; } = false;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 权限级别
        /// </summary>
        public int Level { get; set; } = 0;

        /// <summary>
        /// 父权限ID
        /// </summary>
        public string? ParentPermissionId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 权限属性（扩展字段）
        /// </summary>
        public Dictionary<string, object> Properties { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 获取显示名称或权限名称
        /// </summary>
        public string DisplayNameOrName => !string.IsNullOrEmpty(DisplayName) ? DisplayName : Name;

        /// <summary>
        /// 获取完整权限标识符
        /// </summary>
        public string FullName => !string.IsNullOrEmpty(Resource) && !string.IsNullOrEmpty(Action) 
            ? $"{Resource}.{Action}" 
            : Name;
    }

    #endregion

    #region 会话模型

    /// <summary>
    /// 用户会话
    /// </summary>
    public class UserSession
    {
        /// <summary>
        /// 会话ID
        /// </summary>
        [Required]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 用户ID
        /// </summary>
        [Required]
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 会话令牌
        /// </summary>
        public string? Token { get; set; }

        /// <summary>
        /// 刷新令牌
        /// </summary>
        public string? RefreshToken { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime ExpiresAt { get; set; }

        /// <summary>
        /// 最后访问时间
        /// </summary>
        public DateTime LastAccessedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 是否活跃
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 客户端IP地址
        /// </summary>
        public string? ClientIpAddress { get; set; }

        /// <summary>
        /// 用户代理
        /// </summary>
        public string? UserAgent { get; set; }

        /// <summary>
        /// 会话属性
        /// </summary>
        public Dictionary<string, object> Properties { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 检查会话是否有效
        /// </summary>
        public bool IsValid => IsActive && DateTime.UtcNow < ExpiresAt;

        /// <summary>
        /// 获取剩余时间
        /// </summary>
        public TimeSpan RemainingTime => ExpiresAt > DateTime.UtcNow 
            ? ExpiresAt - DateTime.UtcNow 
            : TimeSpan.Zero;
    }

    #endregion
}
