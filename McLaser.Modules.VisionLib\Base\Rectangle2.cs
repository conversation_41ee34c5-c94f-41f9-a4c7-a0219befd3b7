﻿using McLaser.Core.Common;
using System;

namespace McLaser.Modules.Vision
{
    [Serializable]
    public class Rectangle2 : ObservableObject
    {
        public bool Status;
        public double CenterY;
        public double CenterX;
        public double Phi;
        public double Length1;
        public double Length2;
        public Rectangle2()
        {
        }
        public Rectangle2(double m_Row_center, double m_Col_center, double m_Phi, double m_Length1, double m_Length2)
        {
            this.CenterY = m_Row_center;
            this.CenterX = m_Col_center;
            this.Phi = m_Phi;
            this.Length1 = m_Length1;
            this.Length2 = m_Length2;
            Status = true;
        }
    }
}
