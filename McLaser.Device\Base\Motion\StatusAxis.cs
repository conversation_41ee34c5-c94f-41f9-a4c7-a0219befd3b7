using System;
using <PERSON><PERSON><PERSON><PERSON><PERSON>.Device;

namespace McLaser.Devices
{
    /// <summary>
    /// 轴状态类
    /// 包含轴的运行状态信息
    /// </summary>
    [Serializable]
    public class StatusAxis : StatusBase
    {
        /// <summary>
        /// 是否使能
        /// </summary>
        public bool IsEnable { get; set; } = false;

        /// <summary>
        /// 上次使能状态（用于状态变化检测）
        /// </summary>
        public bool IsEnableLast { get; set; } = false;

        /// <summary>
        /// 是否已回零
        /// </summary>
        public bool IsHome { get; set; } = false;

        /// <summary>
        /// 是否正在回零
        /// </summary>
        public bool IsHoming { get; set; } = false;

        /// <summary>
        /// 是否触发正限位
        /// </summary>
        public bool IsPlusLimit { get; set; } = false;

        /// <summary>
        /// 是否触发负限位
        /// </summary>
        public bool IsMinusLimit { get; set; } = false;

        /// <summary>
        /// 是否在原点位置
        /// </summary>
        public bool IsOrigin { get; set; } = false;

        /// <summary>
        /// 是否有报警
        /// </summary>
        public bool IsError { get; set; } = false;

        /// <summary>
        /// 是否正在移动
        /// </summary>
        public bool IsMove { get; set; } = false;

        /// <summary>
        /// 是否到位
        /// </summary>
        public bool IsPos { get; set; } = false;

        /// <summary>
        /// 是否正向运动
        /// </summary>
        public bool IsPlus { get; set; } = false;

        /// <summary>
        /// 当前位置
        /// </summary>
        public double Pos { get; set; } = 0;

        /// <summary>
        /// 上次位置（用于位置变化检测）
        /// </summary>
        public double PosLast { get; set; } = 0;

        /// <summary>
        /// 规划位置
        /// </summary>
        public double PosPlan { get; set; } = 0;

        /// <summary>
        /// 当前速度
        /// </summary>
        public double Velocity { get; set; } = 0;

        /// <summary>
        /// 构造函数
        /// </summary>
        public StatusAxis()
        {
        }

        /// <summary>
        /// 重置状态
        /// </summary>
        public void Reset()
        {
            IsEnable = false;
            IsHome = false;
            IsHoming = false;
            IsPlusLimit = false;
            IsMinusLimit = false;
            IsOrigin = false;
            IsError = false;
            IsMove = false;
            IsPos = false;
            IsPlus = false;
            Pos = 0;
            PosPlan = 0;
            Velocity = 0;
        }

        /// <summary>
        /// 检查状态是否发生变化
        /// </summary>
        /// <returns>状态是否发生变化</returns>
        public bool IsChanged()
        {
            bool changed = false;
            
            if (IsEnableLast != IsEnable)
            {
                IsEnableLast = IsEnable;
                changed = true;
            }
            
            if (Math.Abs(PosLast - Pos) > 0.001)
            {
                PosLast = Pos;
                changed = true;
            }
            
            return changed;
        }

        /// <summary>
        /// 重写ToString方法
        /// </summary>
        /// <returns>状态信息字符串</returns>
        public override string ToString()
        {
            return $"Axis Status - Enable: {IsEnable}, Home: {IsHome}, " +
                   $"Moving: {IsMove}, InPos: {IsPos}, Error: {IsError}, " +
                   $"Position: {Pos:F3}, Velocity: {Velocity:F3}";
        }
    }

    /// <summary>
    /// 简化轴状态类（用于接口兼容）
    /// </summary>
    [Serializable]
    public class AxisStatus
    {
        /// <summary>
        /// 是否选中
        /// </summary>
        public bool IsSelected { get; set; } = false;
        
        /// <summary>
        /// 关联的轴对象
        /// </summary>
        public AxisBase Axis { get; set; }
        
        /// <summary>
        /// 当前位置
        /// </summary>
        public double Position { get; set; } = 0;
        
        /// <summary>
        /// 是否使能
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// 是否已回零
        /// </summary>
        public bool IsHome { get; set; } = false;

        /// <summary>
        /// 是否正在回零
        /// </summary>
        public bool IsHoming { get; set; } = false;

        /// <summary>
        /// 是否触发正限位
        /// </summary>
        public bool IsPlusLimit { get; set; } = false;

        /// <summary>
        /// 是否触发负限位
        /// </summary>
        public bool IsMinusLimit { get; set; } = false;

        /// <summary>
        /// 是否在原点位置
        /// </summary>
        public bool IsOrigin { get; set; } = false;

        /// <summary>
        /// 是否有报警
        /// </summary>
        public bool IsError { get; set; } = false;

        /// <summary>
        /// 是否正在移动
        /// </summary>
        public bool IsMoving { get; set; } = false;

        /// <summary>
        /// 是否到位
        /// </summary>
        public bool IsInPos { get; set; } = false;
    }
}
