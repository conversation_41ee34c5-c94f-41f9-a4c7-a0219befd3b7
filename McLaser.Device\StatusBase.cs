using McLaser.Core.Common;
using System;
using System.ComponentModel;
 

namespace McLaser.Device
{
    /// <summary>
    /// 状态基类
    /// 所有状态类的基础类
    /// </summary>
    [Serializable]
    public class StatusBase : ObservableObject
    {
    }

    /// <summary>
    /// 设备状态类
    /// 包含设备的基本状态信息
    /// </summary>
    [Serializable]
    public class StatusDevice : StatusBase
    {
        private bool _isOpen = false;
        private bool _isOpenLast = false;
        private bool _isConnected = false;
        private string _statusMessage = "未连接";

        /// <summary>
        /// 设备是否已打开
        /// </summary>
        [Browsable(false)]
        public virtual bool IsOpen
        {
            get { return _isOpen; }
            set { Set(ref _isOpen, value); }
        }

        /// <summary>
        /// 设备上次的打开状态（用于状态变化检测）
        /// </summary>
        [Browsable(false)]
        public virtual bool IsOpenLast
        {
            get { return _isOpenLast; }
            set { Set(ref _isOpenLast, value); }
        }

        /// <summary>
        /// 设备是否已连接
        /// </summary>
        [Browsable(false)]
        public virtual bool IsConnected
        {
            get { return _isConnected; }
            set { Set(ref _isConnected, value); }
        }

        /// <summary>
        /// 状态消息
        /// </summary>
        public virtual string StatusMessage
        {
            get { return _statusMessage; }
            set { Set(ref _statusMessage, value); }
        }

        /// <summary>
        /// 状态文本描述
        /// </summary>
        public virtual string StatusText
        {
            get
            {
                if (IsConnected)
                    return "设备已连接";
                return "设备未连接";
            }
        }

        /// <summary>
        /// 重置状态
        /// </summary>
        public virtual void Reset()
        {
            IsOpen = false;
            IsOpenLast = false;
            IsConnected = false;
            StatusMessage = "未连接";
        }

        /// <summary>
        /// 获取状态摘要
        /// </summary>
        /// <returns>状态摘要字符串</returns>
        public virtual string GetStatusSummary()
        {
            return $"设备状态 - {StatusText}";
        }
    }
}
