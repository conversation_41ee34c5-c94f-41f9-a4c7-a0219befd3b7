using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace McLaser.Core.Framework.Security
{
    /// <summary>
    /// 角色服务接口
    /// 提供角色管理功能
    /// </summary>
    public interface IRoleService
    {
        #region 角色管理

        /// <summary>
        /// 创建角色
        /// </summary>
        /// <param name="role">角色信息</param>
        /// <returns>创建结果</returns>
        Task<RoleOperationResult> CreateRoleAsync(Role role);

        /// <summary>
        /// 更新角色
        /// </summary>
        /// <param name="role">角色信息</param>
        /// <returns>更新结果</returns>
        Task<RoleOperationResult> UpdateRoleAsync(Role role);

        /// <summary>
        /// 删除角色
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>删除结果</returns>
        Task<RoleOperationResult> DeleteRoleAsync(string roleId);

        /// <summary>
        /// 根据ID获取角色
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>角色信息</returns>
        Task<Role?> GetRoleByIdAsync(string roleId);

        /// <summary>
        /// 根据名称获取角色
        /// </summary>
        /// <param name="roleName">角色名称</param>
        /// <returns>角色信息</returns>
        Task<Role?> GetRoleByNameAsync(string roleName);

        /// <summary>
        /// 获取所有角色
        /// </summary>
        /// <returns>角色列表</returns>
        Task<IEnumerable<Role>> GetAllRolesAsync();

        /// <summary>
        /// 搜索角色
        /// </summary>
        /// <param name="searchTerm">搜索条件</param>
        /// <returns>角色列表</returns>
        Task<IEnumerable<Role>> SearchRolesAsync(string searchTerm);

        #endregion

        #region 用户角色关联

        /// <summary>
        /// 为用户分配角色
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="roleId">角色ID</param>
        /// <returns>分配结果</returns>
        Task<RoleOperationResult> AssignRoleToUserAsync(string userId, string roleId);

        /// <summary>
        /// 为用户分配多个角色
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="roleIds">角色ID列表</param>
        /// <returns>分配结果</returns>
        Task<RoleOperationResult> AssignRolesToUserAsync(string userId, IEnumerable<string> roleIds);

        /// <summary>
        /// 移除用户角色
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="roleId">角色ID</param>
        /// <returns>移除结果</returns>
        Task<RoleOperationResult> RemoveRoleFromUserAsync(string userId, string roleId);

        /// <summary>
        /// 移除用户的所有角色
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>移除结果</returns>
        Task<RoleOperationResult> RemoveAllRolesFromUserAsync(string userId);

        /// <summary>
        /// 获取用户的所有角色
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>角色列表</returns>
        Task<IEnumerable<Role>> GetUserRolesAsync(string userId);

        /// <summary>
        /// 获取角色的所有用户
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>用户列表</returns>
        Task<IEnumerable<User>> GetRoleUsersAsync(string roleId);

        /// <summary>
        /// 检查用户是否拥有指定角色
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="roleId">角色ID</param>
        /// <returns>是否拥有</returns>
        Task<bool> UserHasRoleAsync(string userId, string roleId);

        /// <summary>
        /// 检查用户是否拥有指定角色（按名称）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="roleName">角色名称</param>
        /// <returns>是否拥有</returns>
        Task<bool> UserHasRoleByNameAsync(string userId, string roleName);

        #endregion

        #region 角色权限关联

        /// <summary>
        /// 为角色分配权限
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <param name="permissionId">权限ID</param>
        /// <returns>分配结果</returns>
        Task<RoleOperationResult> AssignPermissionToRoleAsync(string roleId, string permissionId);

        /// <summary>
        /// 为角色分配多个权限
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <param name="permissionIds">权限ID列表</param>
        /// <returns>分配结果</returns>
        Task<RoleOperationResult> AssignPermissionsToRoleAsync(string roleId, IEnumerable<string> permissionIds);

        /// <summary>
        /// 移除角色权限
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <param name="permissionId">权限ID</param>
        /// <returns>移除结果</returns>
        Task<RoleOperationResult> RemovePermissionFromRoleAsync(string roleId, string permissionId);

        /// <summary>
        /// 移除角色的所有权限
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>移除结果</returns>
        Task<RoleOperationResult> RemoveAllPermissionsFromRoleAsync(string roleId);

        /// <summary>
        /// 获取角色的所有权限
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>权限列表</returns>
        Task<IEnumerable<Permission>> GetRolePermissionsAsync(string roleId);

        /// <summary>
        /// 检查角色是否拥有指定权限
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <param name="permissionId">权限ID</param>
        /// <returns>是否拥有</returns>
        Task<bool> RoleHasPermissionAsync(string roleId, string permissionId);

        #endregion

        #region 角色层次结构

        /// <summary>
        /// 设置父角色
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <param name="parentRoleId">父角色ID</param>
        /// <returns>设置结果</returns>
        Task<RoleOperationResult> SetParentRoleAsync(string roleId, string parentRoleId);

        /// <summary>
        /// 移除父角色
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>移除结果</returns>
        Task<RoleOperationResult> RemoveParentRoleAsync(string roleId);

        /// <summary>
        /// 获取子角色
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>子角色列表</returns>
        Task<IEnumerable<Role>> GetChildRolesAsync(string roleId);

        /// <summary>
        /// 获取所有继承的权限（包括父角色权限）
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>权限列表</returns>
        Task<IEnumerable<Permission>> GetInheritedPermissionsAsync(string roleId);

        #endregion

        #region 事件

        /// <summary>
        /// 角色创建事件
        /// </summary>
        event EventHandler<RoleCreatedEventArgs>? RoleCreated;

        /// <summary>
        /// 角色更新事件
        /// </summary>
        event EventHandler<RoleUpdatedEventArgs>? RoleUpdated;

        /// <summary>
        /// 角色删除事件
        /// </summary>
        event EventHandler<RoleDeletedEventArgs>? RoleDeleted;

        /// <summary>
        /// 角色分配事件
        /// </summary>
        event EventHandler<RoleAssignedEventArgs>? RoleAssigned;

        /// <summary>
        /// 角色移除事件
        /// </summary>
        event EventHandler<RoleRemovedEventArgs>? RoleRemoved;

        #endregion
    }

    #region 结果类

    /// <summary>
    /// 角色操作结果
    /// </summary>
    public class RoleOperationResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public string? ErrorCode { get; set; }

        /// <summary>
        /// 附加数据
        /// </summary>
        public object? Data { get; set; }

        /// <summary>
        /// 创建成功结果
        /// </summary>
        public static RoleOperationResult CreateSuccess(object? data = null)
        {
            return new RoleOperationResult { Success = true, Data = data };
        }

        /// <summary>
        /// 创建失败结果
        /// </summary>
        public static RoleOperationResult CreateFailure(string errorMessage, string? errorCode = null)
        {
            return new RoleOperationResult 
            { 
                Success = false, 
                ErrorMessage = errorMessage, 
                ErrorCode = errorCode 
            };
        }
    }

    #endregion
}
