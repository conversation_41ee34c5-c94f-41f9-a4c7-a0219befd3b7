﻿using HalconDotNet;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace McLaser.Modules.Vision.Base
{
    [Serializable]
    public class ROICoordinates : ROI, IDrawable
    {
        public bool Status;
        /// <summary>起点行坐标</summary>
        public double StartY;
        /// <summary>起点列坐标</summary>
        public double StartX;
        /// <summary>终点行坐标</summary>
        public double EndY;
        /// <summary>终点列坐标</summary>
        public double EndX;
        /// <summary>中点行坐标</summary>
        public double MidX;
        /// <summary>中点列坐标</summary>
        public double MidY;
        /// <summary>直线角度</summary>
        public double Phi;
        /// <summary>直线长度</summary>
        public double Dist;
        /// <summary>行向量</summary>
        public double Nx;
        /// <summary>列向量</summary>
        public double Ny;
        /// <summary>X点集合</summary>
        public double[] X;
        /// <summary>Y点集合</summary>
        public double[] Y;
        /// <summary>直线上添加箭头显示</summary>
        private HObject arrowHandleXLD;

        public ROICoordinates(string name, double x, double y)
        {
            Name = name;
            Type = RoiType.ROICoordinate;
            StartY = y;
            StartX = x;
        }

        public ROICoordinates(double x, double y)
        {
            Type = RoiType.ROICoordinate;
            StartY = y;
            StartX = x;
        }

        public override bool IsSelected(double x, double y, double factor)
        {
            double dis = HMisc.DistancePp(y, x, StartX, StartY); // midpoint 

            if (dis < 5 * factor)
            {
                Cursor = RoiCursor.Hand;
                Selected = true;
                return true;
            }

            return false;
        }

        public void Draw(HWindow hWindow, double factor)
        {
            hWindow.SetColor("blue");
            ShowArrowHandle(hWindow, 100, 0);
            ShowArrowHandle(hWindow, 0, 100);
            hWindow.SetDraw("fill");
            hWindow.DispRectangle2(StartY, StartX, 0, 1.5 * factor, 1.5 * factor);
            hWindow.SetColor("red");
        }

        public override void Shape(double x, double y)
        {
            StartX = y;
            StartY = x;
        }

        private void ShowArrowHandle(HWindow hWindow, double x, double y)
        {

            EndX = StartX + x;
            EndY = StartY + y;


            double length, dr, dc, halfHW;
            double rrow1, ccol1, rowP1, colP1, rowP2, colP2;
            double headLength = 5;
            double headWidth = 5;
            arrowHandleXLD = new HXLDCont();
            arrowHandleXLD.GenEmptyObj();

            arrowHandleXLD.Dispose();
            arrowHandleXLD.GenEmptyObj();
            rrow1 = StartY + (EndY - StartY) * 0.9;
            ccol1 = StartX + (EndX - StartX) * 0.9;

            length = HMisc.DistancePp(rrow1, ccol1, EndY, EndX);
            if (length == 0)
                length = -1;

            dr = (EndY - rrow1) / length;
            dc = (EndX - ccol1) / length;

            halfHW = headWidth / 5;
            rowP1 = (rrow1 + (length - headLength) * dr + halfHW * dc);
            colP1 = (ccol1 + (length - headLength) * dc - halfHW * dr);
            rowP2 = (rrow1 + (length - headLength) * dr - halfHW * dc);
            colP2 = (ccol1 + (length - headLength) * dc + halfHW * dr);
            Phi = HMisc.AngleLx(StartY, StartX, EndY, EndX);


            if (length == -1)
                HOperatorSet.GenContourPolygonXld(out arrowHandleXLD, rrow1, ccol1);
            else
                HOperatorSet.GenContourPolygonXld(out arrowHandleXLD, new HTuple(new double[] { StartY, EndY, rowP1, EndY, rowP2, EndY
    }), new HTuple(new double[] { StartX, EndX, colP1, EndX, colP2, EndX
}));

            HOperatorSet.DispXld(arrowHandleXLD, hWindow);
        }
    }
}
