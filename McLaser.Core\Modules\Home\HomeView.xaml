<UserControl x:Class="McLaser.Core.Modules.Home.HomeView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- 标题区域 -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="McLaser 应用程序主页" 
                       FontSize="24" 
                       FontWeight="Bold"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,10"/>
            <TextBlock Text="欢迎使用 McLaser.Core 框架演示应用程序" 
                       FontSize="16"
                       HorizontalAlignment="Center"
                       Foreground="{DynamicResource {x:Static SystemColors.GrayTextBrushKey}}"/>
        </StackPanel>
        
        <!-- 主要内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- 左侧面板 -->
            <GroupBox Grid.Column="0" Header="McLaser.Core框架功能演示" Padding="15" Margin="0,0,10,0">
                <StackPanel>
                    <!-- 框架信息 -->
                    <TextBlock Text="McLaser.Core框架特性：" FontWeight="Bold" Margin="0,0,0,10" />
                    
                    <TextBlock TextWrapping="Wrap" Margin="0,0,0,15">
                        <Run Text="• 统一DI容器架构" /><LineBreak />
                        <Run Text="• 主题管理系统" /><LineBreak />
                        <Run Text="• 窗口管理器" /><LineBreak />
                        <Run Text="• 数据验证框架" /><LineBreak />
                        <Run Text="• 配置管理服务" /><LineBreak />
                        <Run Text="• MVVM模式支持" /><LineBreak />
                        <Run Text="• 事件总线系统" /><LineBreak />
                        <Run Text="• 异常处理机制" /><LineBreak />
                        <Run Text="• 插件管理系统" />
                    </TextBlock>
                    
                    <!-- 快速操作 -->
                    <TextBlock Text="快速操作：" FontWeight="Bold" Margin="0,0,0,10" />
                    
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                        <Button Content="设备管理" 
                                Padding="15,8" 
                                Margin="0,0,10,0"
                                Command="{Binding QuickNavigateCommand}"
                                CommandParameter="device-manager"/>
                        <Button Content="系统演示" 
                                Padding="15,8" 
                                Margin="0,0,10,0"
                                Command="{Binding QuickNavigateCommand}"
                                CommandParameter="eventbus-demo"/>
                        <Button Content="数据输入" 
                                Padding="15,8"
                                Command="{Binding QuickNavigateCommand}"
                                CommandParameter="data-input"/>
                    </StackPanel>
                    
                    <!-- 主题切换 -->
                    <TextBlock Text="主题切换：" FontWeight="Bold" Margin="0,15,0,10" />
                    
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                        <TextBlock Text="当前主题：" VerticalAlignment="Center" Margin="0,0,10,0" />
                        <TextBlock Text="{Binding CurrentTheme}" FontWeight="Bold" VerticalAlignment="Center" />
                    </StackPanel>
                    
                    <StackPanel Orientation="Horizontal">
                        <Button Content="浅色主题" 
                                Command="{Binding SwitchThemeCommand}" 
                                CommandParameter="Light" 
                                Margin="0,0,10,0" 
                                Padding="10,5" />
                        <Button Content="深色主题" 
                                Command="{Binding SwitchThemeCommand}" 
                                CommandParameter="Dark" 
                                Padding="10,5" />
                    </StackPanel>
                </StackPanel>
            </GroupBox>
            
            <!-- 右侧面板 -->
            <GroupBox Grid.Column="1" Header="系统信息" Padding="15" Margin="10,0,0,0">
                <StackPanel>
                    <!-- 当前页面信息 -->
                    <TextBlock Text="当前页面：" FontWeight="Bold" Margin="0,0,0,5" />
                    <TextBlock Text="{Binding CurrentPageTitle}" Margin="0,0,0,15" />
                    
                    <!-- 导航历史 -->
                    <TextBlock Text="导航历史：" FontWeight="Bold" Margin="0,0,0,5" />
                    <ListBox ItemsSource="{Binding NavigationHistory}" 
                             Height="100" 
                             Margin="0,0,0,15">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Title}"/>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                    
                    <!-- 系统状态 -->
                    <TextBlock Text="系统状态：" FontWeight="Bold" Margin="0,0,0,5" />
                    <TextBlock Text="{Binding StatusMessage}" 
                               TextWrapping="Wrap" 
                               Margin="0,0,0,15" />
                    
                    <!-- 操作按钮 -->
                    <Button Content="刷新状态" 
                            Command="{Binding RefreshStatusCommand}" 
                            Margin="0,0,0,10" 
                            Padding="8,5" />
                    <Button Content="关于" 
                            Command="{Binding ShowAboutCommand}" 
                            Margin="0,0,0,10" 
                            Padding="8,5" />
                </StackPanel>
            </GroupBox>
        </Grid>
    </Grid>
</UserControl>
