# 控制卡特定配置界面实现指南

本文档提供了在McLaser.Devices.Motion项目中实现特定控制卡配置界面的详细指南。

## 1. 基本原则

配置界面开发需遵循以下基本原则：

1. **接口分离原则**: McLaser.Device作为基类库只提供接口和基础实现，具体实现在McLaser.Devices.Motion中完成
2. **MVVM模式**: 所有UI交互都通过绑定和命令来实现
3. **工厂注册**: 所有特定控制卡配置控件都需注册到工厂中
4. **扩展而非替换**: 尽量扩展基类功能而非完全重写

## 2. 实现步骤

### 2.1 创建特定控制卡配置控件

以PMAC控制卡为例，创建配置控件：

1. 在`McLaser.Devices.Motion/Pmac`目录下创建`PmacCardConfigControl.xaml`和对应的代码文件

```xml
<!-- PmacCardConfigControl.xaml -->
<UserControl x:Class="McLaser.Devices.Motion.Pmac.PmacCardConfigControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:McLaser.Devices.Motion.Pmac"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <!-- 使用内部包含基础配置控件 -->
        <ContentControl x:Name="BaseConfigContainer" />
    </Grid>
</UserControl>
```

```csharp
// PmacCardConfigControl.xaml.cs
using System.Windows;
using System.Windows.Controls;
using McLaser.Devices;
using McLaser.Devices.Views;

namespace McLaser.Devices.Motion.Pmac
{
    /// <summary>
    /// PMAC控制卡配置界面
    /// </summary>
    public partial class PmacCardConfigControl : UserControl, ICardConfigControl
    {
        /// <summary>
        /// 基础配置控件
        /// </summary>
        private CardConfigControlBase _baseControl;
        
        /// <summary>
        /// 坐标系配置控件
        /// </summary>
        private PmacCoordinatesControl _coordinatesControl;
        
        /// <summary>
        /// 关联的控制卡
        /// </summary>
        private CardBase _card;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public PmacCardConfigControl()
        {
            InitializeComponent();
            
            // 初始化基础配置控件
            _baseControl = new CardConfigControlBase();
            BaseConfigContainer.Content = _baseControl;
            
            // 创建PMAC特有控件
            _coordinatesControl = new PmacCoordinatesControl();
            
            // 向基础控件添加特有Tab页
            _baseControl.AddCustomTab("坐标系配置", _coordinatesControl);
            
            // 示例：添加轴扩展内容
            _baseControl.SetAxisExtensionContent(new PmacAxisExtensionControl());
        }

        /// <summary>
        /// 设置控制卡
        /// </summary>
        public void SetCard(CardBase card)
        {
            _card = card;
            _baseControl.SetCard(card);
            
            // 设置特有控件的卡
            _coordinatesControl.SetCard(card);
        }

        /// <summary>
        /// 获取控件
        /// </summary>
        public UserControl GetControl()
        {
            return this;
        }

        /// <summary>
        /// 获取支持的卡类型
        /// </summary>
        public CardType GetSupportCardType()
        {
            return CardType.PMAC;
        }

        /// <summary>
        /// 更新所有轴状态
        /// </summary>
        public void UpdateAllAxisStatus()
        {
            _baseControl.UpdateAllAxisStatus();
            // 更新PMAC特有状态
            _coordinatesControl.UpdateStatus();
        }
    }
}
```

### 2.2 创建特有视图模型（可选）

如果需要特有功能，可以创建对应的视图模型：

```csharp
// PmacConfigViewModel.cs
using McLaser.Devices;
using System.Windows.Input;

namespace McLaser.Devices.Motion.Pmac
{
    /// <summary>
    /// PMAC控制卡配置视图模型
    /// </summary>
    public class PmacConfigViewModel : CardConfigViewModel
    {
        // PMAC特有属性
        public bool IsCoordinateSystemEnabled { get; set; }
        
        // PMAC特有命令
        public ICommand ExecutePmacCommandCommand { get; private set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public PmacConfigViewModel(CardBase card) : base(card)
        {
            // 初始化命令
            ExecutePmacCommandCommand = new RelayCommand<string>(ExecutePmacCommand);
        }
        
        /// <summary>
        /// 执行PMAC命令
        /// </summary>
        private void ExecutePmacCommand(string command)
        {
            // 实现PMAC命令执行逻辑
        }
        
        /// <summary>
        /// 更新PMAC特有状态
        /// </summary>
        public void UpdateCoordinateStatus()
        {
            // 更新坐标系状态
        }
    }
}
```

### 2.3 创建特有页面控件

为控制卡特有功能创建页面控件：

```csharp
// PmacCoordinatesControl.xaml.cs
using System.Windows.Controls;
using McLaser.Devices;

namespace McLaser.Devices.Motion.Pmac
{
    /// <summary>
    /// PMAC坐标系配置控件
    /// </summary>
    public partial class PmacCoordinatesControl : UserControl
    {
        private CardBase _card;
        
        public PmacCoordinatesControl()
        {
            InitializeComponent();
        }
        
        public void SetCard(CardBase card)
        {
            _card = card;
            // 初始化界面内容
        }
        
        public void UpdateStatus()
        {
            // 更新坐标系状态
        }
    }
}
```

### 2.4 注册控制卡配置控件到工厂

在应用初始化时注册控制卡配置控件创建器：

```csharp
// 在McLaser.Devices.Motion项目的初始化类中
public static void InitializeCardConfigControls()
{
    // 注册PMAC配置控件
    CardConfigControlFactory.RegisterCreator(CardType.PMAC, () => new PmacCardConfigControl());
    
    // 注册GTS配置控件
    CardConfigControlFactory.RegisterCreator(CardType.GTS, () => new GTSCardConfigControl());
}
```

## 3. 实现特定特性

### 3.1 添加坐标系管理

PMAC控制卡常见的坐标系管理功能实现：

```csharp
public class PmacCoordinatesViewModel : NotifyPropertyBase
{
    // 坐标系集合
    public ObservableCollection<CoordinateSystemInfo> Coordinates { get; } = new ObservableCollection<CoordinateSystemInfo>();
    
    // 选中的坐标系
    private CoordinateSystemInfo _selectedCoordinate;
    public CoordinateSystemInfo SelectedCoordinate
    {
        get => _selectedCoordinate;
        set
        {
            _selectedCoordinate = value;
            OnPropertyChanged();
        }
    }
    
    // 添加坐标系命令
    public ICommand AddCoordinateCommand { get; }
    
    // 删除坐标系命令
    public ICommand RemoveCoordinateCommand { get; }
    
    // 轴映射命令
    public ICommand MapAxisCommand { get; }
    
    // 构造函数
    public PmacCoordinatesViewModel(CardBase card)
    {
        // 初始化命令和数据
    }
}
```

### 3.2 添加位置比较输出功能

GTS控制卡常见的位置比较输出功能实现：

```csharp
public class GTSAxisExtensionControl : UserControl
{
    // 位置比较输出设置UI
    // ...
    
    // 设置比较点命令
    public ICommand SetComparePointsCommand { get; }
    
    // 启用位置比较命令
    public ICommand EnableCompareOutputCommand { get; }
    
    // 清除比较点命令
    public ICommand ClearComparePointsCommand { get; }
}
```

## 4. 建议与最佳实践

1. **接口优先**: 通过接口而非具体类型进行交互
2. **组合优于继承**: 尽量使用组合方式扩展功能，避免过深的继承层次
3. **资源释放**: 实现特定控制卡配置控件时注意资源释放
4. **异常处理**: 处理设备通信可能出现的异常，确保UI不会因此崩溃
5. **UI响应性**: 长时间操作应放在后台线程，避免UI线程阻塞
6. **参数验证**: 对用户输入的参数进行合理性验证
7. **状态可视化**: 使用颜色、图标等直观方式反映设备状态

## 5. 示例代码

完整的实现示例可参考`McLaser.Device/docs/MotionControl/Examples`目录下的示例代码。

## 6. 问题排查

1. **注册失败**: 确保工厂注册代码在应用启动时执行
2. **接口实现不完整**: 检查是否实现了`ICardConfigControl`的所有方法
3. **UI更新失败**: 确认是否正确实现了属性变更通知
4. **卡状态不更新**: 检查定时器是否正常工作
5. **资源泄漏**: 确认在控件卸载时正确释放资源 