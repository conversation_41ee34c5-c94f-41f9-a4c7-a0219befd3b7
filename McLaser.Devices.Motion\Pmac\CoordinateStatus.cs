﻿using McLaser.Device;
using System.Collections.Generic;

namespace McLaser.Devices.Motion
{
    public class CoordinateStatus
    {

        //高8位
        public bool TriggerMove { get; set; }
        public bool HomeInProgress { get; set; }
        public bool MinusLimit { get; set; }
        public bool PlusLimit { get; set; }
        public bool FeWarn { get; set; }
        public bool FeFatal { get; set; }
        public bool LimitStop { get; set; }
        public bool AmpFault { get; set; }
        public bool SoftMinusLimit { get; set; }
        public bool SoftPlusLimit { get; set; }
        public bool I2tFault { get; set; }
        public bool TriggerNotFound { get; set; }
        public bool AmpWarn { get; set; }
        public bool EncLoss { get; set; }
        public bool AuxFault { get; set; }
        public bool TimerEnabled { get; set; }
        public bool HomeComplete { get; set; }
        public bool DesVelZero { get; set; }
        public bool ClosedLoop { get; set; }
        public bool AmpEna { get; set; }
        public bool InPos { get; set; }
        public bool Reserved { get; set; }
        public bool BlockRequest { get; set; }
        public bool TimersEnabled { get; set; }
        public bool RadiusErrorBit1 { get; set; }
        public bool RadiusErrorBit0 { get; set; }
        public bool SoftLimit { get; set; }
        public bool RunTimeError { get; set; }
        public bool PvtError { get; set; }
        public bool LinToPvtError { get; set; }
        public bool ErrorStatusBit1 { get; set; }
        public bool ErrorStatusBit0 { get; set; }

        //低8位
        public bool Csolve { get; set; }
        public bool LinToPvtBuf { get; set; }
        public bool FeedHoldBit1 { get; set; }
        public bool FeedHoldBit0 { get; set; }
        public bool BlockActive { get; set; }
        public bool ContMotion { get; set; }
        public bool CCModeBit1 { get; set; }
        public bool CCModeBit0 { get; set; }
        public bool MoveModeBit1 { get; set; }
        public bool MoveModeBit0 { get; set; }
        public bool SegMoveBit1 { get; set; }
        public bool SegMoveBit0 { get; set; }
        public bool SegMoveAccel { get; set; }
        public bool SegMoveDecel { get; set; }
        public bool SegEnabled { get; set; }
        public bool SegStopReq { get; set; }
        public bool LookAheadWrap { get; set; }
        public bool LookAheadLookBack { get; set; }
        public bool LookAheadDir { get; set; }
        public bool LookAheadStop { get; set; }
        public bool LookAheadChange { get; set; }
        public bool LookAheadReCalc { get; set; }
        public bool LookAheadFlush { get; set; }
        public bool LookAheadActive { get; set; }
        public bool CCAddedArc { get; set; }
        public bool CCOffReq { get; set; }
        public bool CCMoveTypeBit1 { get; set; }
        public bool CCMoveTypeBit0 { get; set; }
        public bool EndDelayActive { get; set; }
        public bool CC3Active { get; set; }
        public bool SharpCornerStop { get; set; }
        public bool AddedDwellDis { get; set; }


        static readonly string[] FieldNames = {
        "TriggerMove",
        "HomeInProgress",
        "MinusLimit",
        "PlusLimit",
        "FeWarn",
        "FeFatal",
        "LimitStop",
        "AmpFault",
        "SoftMinusLimit",
        "SoftPlusLimit",
        "I2tFault",
        "TriggerNotFound",
        "AmpWarn",
        "EncLoss",
        "AuxFault",
        "TimerEnabled",
        "HomeComplete",
        "DesVelZero",
        "ClosedLoop",
        "AmpEna",
        "InPos",
        "Reserved",
        "BlockRequest",
        "TimersEnabled",
        "RadiusError (bit 1)",
        "RadiusError (bit 0)",
        "SoftLimit",
        "RunTimeError",
        "PvtError",
        "LinToPvtError",
        "ErrorStatus (bit 1)",
        "ErrorStatus (bit 0)"
    };

        readonly string[] FieldRemarks = {
        "触发搜索运动进行中      ",
        "回零搜索运动进行中      ",
        "硬件负限位被设定        ",
        "硬件正限位被设定        ",
        "跟随误差警告           ",
        "严重跟随误差           ",
        "停止在硬件限位         ",
        "放大器故障             ",
        "件负限位被设定         ",
        "软件正限位被设定        ",
        "集成电流（I2T）故障     ",
        "触发器未找到           ",
        "放大器警告             ",
        "传感器丢失错误         ",
        "辅助故障错误           ",
        "运动定时器已启用        ",
        "回零完成               ",
        "期望速度为零           ",
        "闭环模式               ",
        "放大器启用             ",
        "电机已到位             ",
        "预留位                ",
        "阻塞请求标志位已设置    ",
        "定时器已启用           ",
        "XX/YY/ZZ轴圆周半径误差 ",
        "X/Y/Z轴圆周半径误差    ",
        "软件位置限制           ",
        "运行时错误             ",
        "PVT模式错误            ",
        "线性到PVT模式错误      ",
        "缓冲区错误             ",
        "同步分配缓冲区错误      "
        };
        public List<UnitStatus> ToList()
        {
            List<UnitStatus> list = new List<UnitStatus>();
            list.Add(new UnitStatus(FieldNames[0], TriggerMove, FieldRemarks[0]));
            list.Add(new UnitStatus(FieldNames[1], HomeInProgress, FieldRemarks[2]));
            list.Add(new UnitStatus(FieldNames[2], MinusLimit, FieldRemarks[2]));
            list.Add(new UnitStatus(FieldNames[3], PlusLimit, FieldRemarks[3]));
            list.Add(new UnitStatus(FieldNames[4], FeWarn, FieldRemarks[4]));
            list.Add(new UnitStatus(FieldNames[5], FeFatal, FieldRemarks[5]));
            list.Add(new UnitStatus(FieldNames[6], LimitStop, FieldRemarks[6]));
            list.Add(new UnitStatus(FieldNames[7], AmpFault, FieldRemarks[7]));
            list.Add(new UnitStatus(FieldNames[8], SoftMinusLimit, FieldRemarks[8]));
            list.Add(new UnitStatus(FieldNames[9], SoftPlusLimit, FieldRemarks[9]));
            list.Add(new UnitStatus(FieldNames[10], I2tFault, FieldRemarks[10]));
            list.Add(new UnitStatus(FieldNames[11], TriggerNotFound, FieldRemarks[11]));
            list.Add(new UnitStatus(FieldNames[12], AmpWarn, FieldRemarks[12]));
            list.Add(new UnitStatus(FieldNames[13], EncLoss, FieldRemarks[13]));
            list.Add(new UnitStatus(FieldNames[14], AuxFault, FieldRemarks[14]));
            list.Add(new UnitStatus(FieldNames[15], TimerEnabled, FieldRemarks[15]));
            list.Add(new UnitStatus(FieldNames[16], HomeComplete, FieldRemarks[16]));
            list.Add(new UnitStatus(FieldNames[17], DesVelZero, FieldRemarks[17]));
            list.Add(new UnitStatus(FieldNames[18], ClosedLoop, FieldRemarks[18]));
            list.Add(new UnitStatus(FieldNames[19], AmpEna, FieldRemarks[19]));
            list.Add(new UnitStatus(FieldNames[20], InPos, FieldRemarks[20]));
            list.Add(new UnitStatus(FieldNames[21], Reserved, FieldRemarks[21]));
            list.Add(new UnitStatus(FieldNames[22], BlockRequest, FieldRemarks[22]));
            list.Add(new UnitStatus(FieldNames[23], TimersEnabled, FieldRemarks[23]));
            list.Add(new UnitStatus(FieldNames[24], RadiusErrorBit1, FieldRemarks[24]));
            list.Add(new UnitStatus(FieldNames[25], RadiusErrorBit0, FieldRemarks[25]));
            list.Add(new UnitStatus(FieldNames[26], SoftLimit, FieldRemarks[26]));
            list.Add(new UnitStatus(FieldNames[27], RunTimeError, FieldRemarks[27]));
            list.Add(new UnitStatus(FieldNames[28], PvtError, FieldRemarks[28]));
            list.Add(new UnitStatus(FieldNames[29], LinToPvtError, FieldRemarks[29]));
            list.Add(new UnitStatus(FieldNames[30], ErrorStatusBit1, FieldRemarks[30]));
            list.Add(new UnitStatus(FieldNames[31], ErrorStatusBit0, FieldRemarks[31]));
            return list;
        }


        readonly string[] FieldNames2 = {
        "Csolve",
        "LinToPvtBuf",
        "FeedHold (bit 1)",
        "FeedHold (bit 0)",
        "BlockActive",
        "ContMotion",
        "CCMode (bit 1)",
        "CCMode (bit 0)",
        "MoveMode (bit 1)",
        "MoveMode (bit 0)",
        "SegMove (bit 1)",
        "SegMove (bit 0)",
        "SegMoveAccel",
        "SegMoveDecel",
        "SegEnabled",
        "SegStopReq",
        "LookAheadWrap",
        "LookAheadLookBack",
        "LookAheadDir",
        "LookAheadStop",
        "LookAheadChange",
        "LookAheadReCalc",
        "LookAheadFlush",
        "LookAheadActive",
        "CCAddedArc",
        "CCOffReq",
        "CCMoveType (bit 1)",
        "CCMoveType (bit 0)",
        "EndDelayActive",
        "CC3Active",
        "SharpCornerStop",
        "AddedDwellDis"
    };

        readonly string[] FieldRemarks2 =
        {
            "PMATCH计算的有效坐标轴定义         " ,
            "线性到PVT移动缓冲区               " ,
            "进给保持加速度/减速度              " ,
            "进给保持时间基准源                 " ,
            "块激活状态                        " ,
            "连续运动请求                      " ,
            "刀具补偿模式位1                   " ,
            "刀具补偿模式位0                   " ,
            "移动模式位1（0融合和样条,1快速和PVT）",
            "移动模式位0（0融合和PVT，1快速和样条" ,
            "分段 PVT 模式移动进行中            " ,
            "分段线性模式移动进行中             " ,
            "第一个分段移动进行中               " ,
            "最后一个分段移动进行中             " ,
            "启用了分段模式                    " ,
            "请求停止分段移动                  " ,
            "预览环绕状态                      " ,
            "预览回顾状态                      " ,
            "预览方向                         " ,
            "预览停止状态                      " ,
            "预览变化状态                      " ,
            "预览重新计算状态                  " ,
            "预览刷新状态                      " ,
            "预览活动状态                      " ,
            "刀具补偿已添加弧段                 " ,
            "刀具补偿关闭请求                  " ,
            "刀具补偿移动类型位1                " ,
            "刀具补偿移动类型位0                " ,
            "自动添加的末端延迟正在进行中        " ,
            "刀具补偿正在活动中                 " ,
            "锐角停止，禁止平滑插补             " ,
            "禁用添加的停顿时间                 "
        };

        public List<UnitStatus> ToList2()
        {
            List<UnitStatus> list = new List<UnitStatus>();
            list.Add(new UnitStatus(FieldNames2[0], Csolve, FieldRemarks2[0]));
            list.Add(new UnitStatus(FieldNames2[1], LinToPvtBuf, FieldRemarks2[1]));
            list.Add(new UnitStatus(FieldNames2[2], FeedHoldBit1, FieldRemarks2[2]));
            list.Add(new UnitStatus(FieldNames2[3], FeedHoldBit0, FieldRemarks2[3]));
            list.Add(new UnitStatus(FieldNames2[4], BlockActive, FieldRemarks2[4]));
            list.Add(new UnitStatus(FieldNames2[5], ContMotion, FieldRemarks2[5]));
            list.Add(new UnitStatus(FieldNames2[6], CCModeBit1, FieldRemarks2[6]));
            list.Add(new UnitStatus(FieldNames2[7], CCModeBit0, FieldRemarks2[7]));
            list.Add(new UnitStatus(FieldNames2[8], MoveModeBit1, FieldRemarks2[8]));
            list.Add(new UnitStatus(FieldNames2[9], MoveModeBit0, FieldRemarks2[9]));
            list.Add(new UnitStatus(FieldNames2[10], SegMoveBit1, FieldRemarks2[10]));
            list.Add(new UnitStatus(FieldNames2[11], SegMoveBit0, FieldRemarks2[11]));
            list.Add(new UnitStatus(FieldNames2[12], SegMoveAccel, FieldRemarks2[12]));
            list.Add(new UnitStatus(FieldNames2[13], SegMoveDecel, FieldRemarks2[13]));
            list.Add(new UnitStatus(FieldNames2[14], SegEnabled, FieldRemarks2[14]));
            list.Add(new UnitStatus(FieldNames2[15], SegStopReq, FieldRemarks2[15]));
            list.Add(new UnitStatus(FieldNames2[16], LookAheadWrap, FieldRemarks2[16]));
            list.Add(new UnitStatus(FieldNames2[17], LookAheadLookBack, FieldRemarks2[17]));
            list.Add(new UnitStatus(FieldNames2[18], LookAheadDir, FieldRemarks2[18]));
            list.Add(new UnitStatus(FieldNames2[19], LookAheadStop, FieldRemarks2[19]));
            list.Add(new UnitStatus(FieldNames2[20], LookAheadChange, FieldRemarks2[20]));
            list.Add(new UnitStatus(FieldNames2[21], LookAheadReCalc, FieldRemarks2[21]));
            list.Add(new UnitStatus(FieldNames2[22], LookAheadFlush, FieldRemarks2[22]));
            list.Add(new UnitStatus(FieldNames2[23], LookAheadActive, FieldRemarks2[23]));
            list.Add(new UnitStatus(FieldNames2[24], CCAddedArc, FieldRemarks2[24]));
            list.Add(new UnitStatus(FieldNames2[25], CCOffReq, FieldRemarks2[25]));
            list.Add(new UnitStatus(FieldNames2[26], CCMoveTypeBit1, FieldRemarks2[26]));
            list.Add(new UnitStatus(FieldNames2[27], CCMoveTypeBit0, FieldRemarks2[27]));
            list.Add(new UnitStatus(FieldNames2[28], EndDelayActive, FieldRemarks2[28]));
            list.Add(new UnitStatus(FieldNames2[29], CC3Active, FieldRemarks2[29]));
            list.Add(new UnitStatus(FieldNames2[30], SharpCornerStop, FieldRemarks2[30]));
            list.Add(new UnitStatus(FieldNames2[31], AddedDwellDis, FieldRemarks2[31]));
            return list;
        }
    }
}