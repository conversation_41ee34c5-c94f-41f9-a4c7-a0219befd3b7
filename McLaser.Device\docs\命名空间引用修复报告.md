# 命名空间和引用问题修复报告

## 问题描述

在McLaser_V1项目中，发现了多个与命名空间和类型引用相关的问题：

1. 找不到`ICardConfigControl`类型或命名空间
2. 找不到`CardConfigControlBase`类型或命名空间
3. 命名空间"McLaser.Devices"中不存在类型或命名空间名"Views"
4. `CardConfigViewModel`未包含`AddPropertyChangedHandler`的定义
5. 找不到类型或命名空间名"CardGTS"

这些问题主要是由于命名空间不一致和缺少正确的项目引用导致的。

## 解决方案

### 1. 命名空间统一

将所有相关的接口和基类移至统一的`McLaser.Devices`命名空间：

- 将`ICardConfigControl`从`McLaser.Devices.Views`移至`McLaser.Devices`
- 将`CardConfigControlBase`从`McLaser.Devices.Views`移至`McLaser.Devices`
- 修改所有XAML文件中的命名空间引用，从`xmlns:views="clr-namespace:McLaser.Devices.Views"`改为`xmlns:devices="clr-namespace:McLaser.Devices;assembly=McLaser.Device"`

### 2. 修复类型引用

- 在`CardConfigViewModel`中添加`UpdateAllAxisCommand`属性，并在初始化命令时创建该命令
- 修改`CardConfigControlBase`中对`AddPropertyChangedHandler`的调用，改为直接设置`UpdateAllAxisCommand`
- 添加`SelectedAxisStatus`属性，确保UI中绑定的属性能够正常工作

### 3. 项目引用修复

- 在`McLaser.App`项目中添加对`McLaser.Devices.Motion`项目的引用，确保运行时能正确加载实现类

### 4. 初始化集成

- 在`AppCore.Initialize`方法中添加对`McLaser.Devices.Motion.MotionInitializer.Initialize()`的调用，确保应用启动时注册所有控制卡配置控件

## 实施细节

### 命名空间修改

```csharp
// 原始命名空间
namespace McLaser.Devices.Views
{
    public interface ICardConfigControl { ... }
}

// 修改后的命名空间
namespace McLaser.Devices
{
    public interface ICardConfigControl { ... }
}
```

### XAML命名空间引用修改

```xml
<!-- 原始引用 -->
xmlns:views="clr-namespace:McLaser.Devices.Views"

<!-- 修改后的引用 -->
xmlns:devices="clr-namespace:McLaser.Devices;assembly=McLaser.Device"
```

### 类型修改

在`CardConfigViewModel`中添加属性：

```csharp
public ICommand UpdateAllAxisCommand { get; set; }

// 在初始化命令中
UpdateAllAxisCommand = new RelayCommand(() => UpdateAllAxisStatus());
```

修改`CardConfigControlBase.SetCard`方法：

```csharp
// 原始代码
_viewModel.AddPropertyChangedHandler("UpdateAllAxisCommand", new RelayCommand(() => UpdateAllAxisStatus()));

// 修改后的代码
if (_viewModel != null)
{
    var updateCommand = new RelayCommand(() => UpdateAllAxisStatus());
    if (_viewModel.UpdateAllAxisCommand == null)
    {
        _viewModel.UpdateAllAxisCommand = updateCommand;
    }
}
```

### 初始化代码

在`AppCore.Initialize`方法中添加：

```csharp
// 初始化Motion模块
try
{
    McLaser.Devices.Motion.MotionInitializer.Initialize();
    System.Diagnostics.Debug.WriteLine("运动控制模块初始化成功");
}
catch (Exception ex)
{
    System.Diagnostics.Debug.WriteLine($"运动控制模块初始化失败: {ex.Message}");
}
```

## 架构设计要点

1. **接口隔离**：将接口定义放在基础项目中，实现放在具体模块中
2. **依赖倒置**：上层模块不应依赖下层模块，都应该依赖于抽象
3. **命名空间组织**：相关功能应在同一命名空间下，避免过度分散
4. **初始化顺序**：确保模块按正确顺序初始化，依赖项先初始化

## 使用示例

控制卡配置界面的使用方式:

```csharp
// 获取特定类型的控制卡配置界面
ICardConfigControl configControl = CardConfigControlFactory.Create(CardType.PMAC);

// 设置关联的控制卡
configControl.SetCard(card);

// 获取UI控件
UserControl control = configControl.GetControl();

// 显示控件
contentPanel.Content = control;
```

## 注意事项

1. 项目引用中使用了非标准格式的项目引用标签`<n>`而不是`<Name>`，这可能需要在将来进行修复
2. 确保在使用控制卡配置界面前先调用`MotionInitializer.Initialize()`进行初始化
3. 如果添加新的控制卡类型，需要实现相应的`ICardConfigControl`接口并在`MotionInitializer`中注册 