namespace McLaser.Devices
{
    /// <summary>
    /// 轴接口
    /// 定义单个轴的基本操作
    /// </summary>
    public interface IAxis
    {
        /// <summary>
        /// 移动到指定位置
        /// </summary>
        /// <param name="pos">目标位置</param>
        /// <returns>操作是否成功</returns>
        bool Move(double pos);

        /// <summary>
        /// 停止运动
        /// </summary>
        /// <returns>操作是否成功</returns>
        bool Stop();

        /// <summary>
        /// 设置轴参数
        /// </summary>
        /// <returns>操作是否成功</returns>
        bool SetParam();

        /// <summary>
        /// 获取轴参数
        /// </summary>
        /// <returns>操作是否成功</returns>
        bool GetParam();
    }
}
