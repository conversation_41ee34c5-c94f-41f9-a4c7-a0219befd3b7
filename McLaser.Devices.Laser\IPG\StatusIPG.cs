using System;
using System.ComponentModel;
using McLaser.Device;

namespace McLaser.Devices.Laser.IPG
{
    /// <summary>
    /// IPG激光器状态类
    /// 用于记录和监控IPG激光器的运行状态信息
    /// </summary>
    [Serializable]
    public class StatusIPG : StatusLaser
    {
        #region 私有字段

        private string _serialNumber = "未知";
        private string _firmwareVersion = "未知";
        private double _diodeTemperature = 0;
        private double _crystalTemperature = 0;
        private double _outputPower = 0;
        private bool _isEmissionEnabled = false;
        private bool _isKeySwitch = false;
        private bool _isInterlock = false;
        private string _alarmCode = string.Empty;
        private int _shotCount = 0;

        #endregion

        #region 属性

        /// <summary>
        /// 序列号
        /// </summary>
        [Category("IPG信息"), DisplayName("序列号")]
        public string SerialNumber
        {
            get => _serialNumber;
            set
            {
                if (_serialNumber != value)
                {
                    _serialNumber = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 固件版本
        /// </summary>
        [Category("IPG信息"), DisplayName("固件版本")]
        public string FirmwareVersion
        {
            get => _firmwareVersion;
            set
            {
                if (_firmwareVersion != value)
                {
                    _firmwareVersion = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 二极管温度(℃)
        /// </summary>
        [Category("IPG温度"), DisplayName("二极管温度(℃)")]
        public double DiodeTemperature
        {
            get => _diodeTemperature;
            set
            {
                if (Math.Abs(_diodeTemperature - value) > 0.1)
                {
                    _diodeTemperature = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 晶体温度(℃)
        /// </summary>
        [Category("IPG温度"), DisplayName("晶体温度(℃)")]
        public double CrystalTemperature
        {
            get => _crystalTemperature;
            set
            {
                if (Math.Abs(_crystalTemperature - value) > 0.1)
                {
                    _crystalTemperature = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 输出功率(W)
        /// </summary>
        [Category("IPG功率"), DisplayName("输出功率(W)")]
        public double OutputPower
        {
            get => _outputPower;
            set
            {
                if (Math.Abs(_outputPower - value) > 0.001)
                {
                    _outputPower = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 发射使能状态
        /// </summary>
        [Category("IPG状态"), DisplayName("发射使能")]
        public bool IsEmissionEnabled
        {
            get => _isEmissionEnabled;
            set
            {
                if (_isEmissionEnabled != value)
                {
                    _isEmissionEnabled = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 钥匙开关状态
        /// </summary>
        [Category("IPG状态"), DisplayName("钥匙开关")]
        public bool IsKeySwitch
        {
            get => _isKeySwitch;
            set
            {
                if (_isKeySwitch != value)
                {
                    _isKeySwitch = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 互锁状态
        /// </summary>
        [Category("IPG状态"), DisplayName("互锁状态")]
        public bool IsInterlock
        {
            get => _isInterlock;
            set
            {
                if (_isInterlock != value)
                {
                    _isInterlock = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 报警代码
        /// </summary>
        [Category("IPG状态"), DisplayName("报警代码")]
        public string AlarmCode
        {
            get => _alarmCode;
            set
            {
                if (_alarmCode != value)
                {
                    _alarmCode = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// 脉冲计数
        /// </summary>
        [Category("IPG状态"), DisplayName("脉冲计数")]
        public int ShotCount
        {
            get => _shotCount;
            set
            {
                if (_shotCount != value)
                {
                    _shotCount = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 状态文本描述
        /// </summary>
        [Category("IPG状态"), DisplayName("状态描述")]
        public override string StatusText
        {
            get
            {
                if (!string.IsNullOrEmpty(_alarmCode) && _alarmCode != "NO_ALARM")
                    return $"报警: {_alarmCode}";

                if (!string.IsNullOrEmpty(ErrorMessage))
                    return $"错误: {ErrorMessage}";

                if (!IsConnected)
                    return "设备未连接";

                if (!_isKeySwitch)
                    return "钥匙开关关闭";

                if (!_isInterlock)
                    return "互锁断开";

                if (IsLaserOn)
                    return $"激光开启 - 功率: {CurrentPower:F1}% ({_outputPower:F1}W)";

                return $"激光关闭 - 状态: {LaserStatus}";
            }
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public StatusIPG()
        {
            Reset();
        }

        #endregion

        #region 方法

        /// <summary>
        /// 重置状态
        /// </summary>
        public override void Reset()
        {
            base.Reset();
            
            SerialNumber = "未知";
            FirmwareVersion = "未知";
            DiodeTemperature = 0;
            CrystalTemperature = 0;
            OutputPower = 0;
            IsEmissionEnabled = false;
            IsKeySwitch = false;
            IsInterlock = false;
            AlarmCode = string.Empty;
            ShotCount = 0;
        }

        /// <summary>
        /// 更新IPG特有状态
        /// </summary>
        /// <param name="diodeTemp">二极管温度</param>
        /// <param name="crystalTemp">晶体温度</param>
        /// <param name="outputPower">输出功率</param>
        /// <param name="shotCount">脉冲计数</param>
        public void UpdateIPGStatus(double diodeTemp, double crystalTemp, double outputPower, int shotCount)
        {
            DiodeTemperature = diodeTemp;
            CrystalTemperature = crystalTemp;
            OutputPower = outputPower;
            ShotCount = shotCount;
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 更新安全状态
        /// </summary>
        /// <param name="isKeySwitch">钥匙开关状态</param>
        /// <param name="isInterlock">互锁状态</param>
        /// <param name="isEmissionEnabled">发射使能状态</param>
        public void UpdateSafetyStatus(bool isKeySwitch, bool isInterlock, bool isEmissionEnabled)
        {
            IsKeySwitch = isKeySwitch;
            IsInterlock = isInterlock;
            IsEmissionEnabled = isEmissionEnabled;
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 设置报警代码
        /// </summary>
        /// <param name="alarmCode">报警代码</param>
        public void SetAlarm(string alarmCode)
        {
            AlarmCode = alarmCode;
            if (!string.IsNullOrEmpty(alarmCode) && alarmCode != "NO_ALARM")
            {
                LaserStatus = LaserStatus.Warning;
            }
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 清除报警
        /// </summary>
        public void ClearAlarm()
        {
            AlarmCode = string.Empty;
            if (LaserStatus == LaserStatus.Warning)
            {
                LaserStatus = IsConnected ? LaserStatus.Ready : LaserStatus.Offline;
            }
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 获取状态摘要
        /// </summary>
        /// <returns>状态摘要字符串</returns>
        public override string GetStatusSummary()
        {
            return $"IPG激光器 - {StatusText} | 二极管温度: {DiodeTemperature:F1}℃ | 输出功率: {OutputPower:F1}W | 脉冲数: {ShotCount}";
        }

        /// <summary>
        /// 检查是否有安全问题
        /// </summary>
        /// <returns>是否有安全问题</returns>
        public bool HasSafetyIssue()
        {
            return !IsKeySwitch || !IsInterlock || (!string.IsNullOrEmpty(AlarmCode) && AlarmCode != "NO_ALARM");
        }

        /// <summary>
        /// 获取安全状态描述
        /// </summary>
        /// <returns>安全状态描述</returns>
        public string GetSafetyStatusDescription()
        {
            var issues = new System.Collections.Generic.List<string>();
            
            if (!IsKeySwitch)
                issues.Add("钥匙开关关闭");
            
            if (!IsInterlock)
                issues.Add("互锁断开");
            
            if (!string.IsNullOrEmpty(AlarmCode) && AlarmCode != "NO_ALARM")
                issues.Add($"报警: {AlarmCode}");
            
            return issues.Count > 0 ? string.Join(", ", issues) : "安全状态正常";
        }

        #endregion
    }
}
