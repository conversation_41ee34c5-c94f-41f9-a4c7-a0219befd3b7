﻿using McLaser.Core.Common;
using McLaser.Core.Container;
using System;
using System.Collections.Generic;

namespace McLaser.Core.Modules.RecipeManager
{
    public class Recipe : IRecipe
    {
        public string Path { get; set; } = PathDefine.FolderRecipe;
        public Guid Guid => Guid.NewGuid();
        public string Name { get; set; } = "Default";
        public string ModifyUser { get; set; } = "User1";
        public DateTime ModifyTime { get; set; } = DateTime.Now;
        public IEnumerable<IRecipeItem> RecipeItems { get; set; }

        public Recipe()
        {
            RecipeItems = IoC.GetAll<IRecipeItem>();
            foreach (var item in RecipeItems)
            {
                item.Owner = this;
            }
        }

        public bool Load()
        {
            try
            {
                //string json = File.ReadAllText(Path);
                //var recipe = JsonHelper.DeserializeObject<Recipe>(json);

                //获取一下系统RecipeItems
                var items = IoC.GetAll<IRecipeItem>();
                foreach (var old in this.RecipeItems)
                {
                    old.Dispose();
                }
                foreach (var item in items)
                {
                     item.Load();
                }
                this.RecipeItems = items;
                return true;
                //var diffItems = items.Where(a => !recipe.RecipeItems.Any(b => b.GetType() == a.GetType()));
                //var list = recipe.RecipeItems.ToList();
                //if (diffItems != null && diffItems.Count() > 0)
                //{
                //    foreach (var item in diffItems)
                //    {
                //        list.Add(item);
                //        // recipe.RecipeItems.Append(item);
                //    }
                //    recipe.RecipeItems = list;
                //}
                //return recipe;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public bool Save()
        {
            try
            {
                foreach (var item in this.RecipeItems)
                {
                    item.Owner = this;
                    item.Save();
                }
                return true;
            }
            catch(Exception ex)
            {
                return false;
            }
        }
    }
}
