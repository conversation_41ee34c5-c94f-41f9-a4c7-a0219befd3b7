using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace McLaser.Core.Plugins
{
    /// <summary>
    /// 插件管理器实现
    /// 负责插件的生命周期管理
    /// </summary>
    public class PluginManager : IPluginManager, IDisposable
    {
        #region 字段

        private readonly ConcurrentDictionary<string, IPlugin> _loadedPlugins = new ConcurrentDictionary<string, IPlugin>();
        private readonly ConcurrentDictionary<string, PluginInfo> _availablePlugins = new ConcurrentDictionary<string, PluginInfo>();
        private readonly IPluginManagerConfiguration _configuration;
        private readonly IPluginFactory _pluginFactory;
        private readonly PluginManagerStatistics _statistics = new PluginManagerStatistics();
        private readonly SemaphoreSlim _loadSemaphore;
        private object? _applicationContext;
        private bool _disposed = false;

        #endregion

        #region 属性

        /// <summary>
        /// 已加载的插件列表
        /// </summary>
        public IReadOnlyList<IPlugin> LoadedPlugins => _loadedPlugins.Values.ToList();

        /// <summary>
        /// 可用的插件列表
        /// </summary>
        public IReadOnlyList<PluginInfo> AvailablePlugins => _availablePlugins.Values.ToList();

        /// <summary>
        /// 插件目录
        /// </summary>
        public string PluginDirectory 
        { 
            get => _configuration.PluginDirectory; 
            set => _configuration.PluginDirectory = value; 
        }

        /// <summary>
        /// 是否启用插件隔离
        /// </summary>
        public bool EnablePluginIsolation 
        { 
            get => _configuration.EnablePluginIsolation; 
            set => _configuration.EnablePluginIsolation = value; 
        }

        /// <summary>
        /// 是否启用热更新
        /// </summary>
        public bool EnableHotSwap 
        { 
            get => _configuration.EnableHotSwap; 
            set => _configuration.EnableHotSwap = value; 
        }

        #endregion

        #region 事件

        /// <summary>
        /// 插件加载事件
        /// </summary>
        public event EventHandler<PluginLoadedEventArgs>? PluginLoaded;

        /// <summary>
        /// 插件卸载事件
        /// </summary>
        public event EventHandler<PluginUnloadedEventArgs>? PluginUnloaded;

        /// <summary>
        /// 插件启动事件
        /// </summary>
        public event EventHandler<PluginStartedEventArgs>? PluginStarted;

        /// <summary>
        /// 插件停止事件
        /// </summary>
        public event EventHandler<PluginStoppedEventArgs>? PluginStopped;

        /// <summary>
        /// 插件错误事件
        /// </summary>
        public event EventHandler<PluginErrorEventArgs>? PluginError;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化插件管理器
        /// </summary>
        /// <param name="configuration">配置</param>
        /// <param name="pluginFactory">插件工厂</param>
        public PluginManager(IPluginManagerConfiguration configuration, IPluginFactory pluginFactory)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _pluginFactory = pluginFactory ?? throw new ArgumentNullException(nameof(pluginFactory));
            _loadSemaphore = new SemaphoreSlim(_configuration.MaxConcurrentLoads, _configuration.MaxConcurrentLoads);
        }

        /// <summary>
        /// 初始化插件管理器（使用默认配置）
        /// </summary>
        public PluginManager() : this(new DefaultPluginManagerConfiguration(), new DefaultPluginFactory())
        {
        }

        #endregion

        #region 初始化

        /// <summary>
        /// 初始化插件管理器
        /// </summary>
        /// <param name="context">应用程序上下文</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>初始化任务</returns>
        public async Task InitializeAsync(object context, CancellationToken cancellationToken = default)
        {
            _applicationContext = context;
            _statistics.StartTime = DateTime.Now;

            // 确保目录存在
            EnsureDirectoriesExist();

            // 扫描可用插件
            await ScanPluginsAsync(cancellationToken: cancellationToken);
        }

        private void EnsureDirectoriesExist()
        {
            var directories = new[]
            {
                _configuration.PluginDirectory,
                _configuration.DataDirectory,
                _configuration.ConfigurationDirectory,
                _configuration.BackupDirectory
            };

            foreach (var directory in directories)
            {
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
            }
        }

        #endregion

        #region 插件扫描

        /// <summary>
        /// 扫描插件目录
        /// </summary>
        /// <param name="directory">目录路径</param>
        /// <param name="recursive">是否递归扫描</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>扫描任务</returns>
        public async Task<IList<PluginInfo>> ScanPluginsAsync(string? directory = null, bool recursive = true, CancellationToken cancellationToken = default)
        {
            directory ??= _configuration.PluginDirectory;
            
            if (string.IsNullOrEmpty(directory) || !Directory.Exists(directory))
            {
                return new List<PluginInfo>();
            }

            var plugins = new List<PluginInfo>();
            var searchOption = recursive ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;

            try
            {
                var files = Directory.GetFiles(directory, "*.*", searchOption)
                    .Where(file => _configuration.AllowedExtensions.Any(ext => file.EndsWith(ext, StringComparison.OrdinalIgnoreCase)))
                    .ToArray();

                var tasks = files.Select(async file =>
                {
                    try
                    {
                        var pluginInfo = await ScanPluginFileAsync(file, cancellationToken);
                        if (pluginInfo != null)
                        {
                            return pluginInfo;
                        }
                    }
                    catch (Exception ex)
                    {
                        OnPluginError(null, $"扫描插件文件失败: {file}", ex);
                    }
                    return null;
                }).ToArray();

                var results = await Task.WhenAll(tasks);
                plugins.AddRange(results.Where(p => p != null)!);

                // 更新可用插件列表
                foreach (var plugin in plugins)
                {
                    _availablePlugins.AddOrUpdate(plugin.Metadata.Id, plugin, (key, oldValue) => plugin);
                }

                _statistics.TotalPlugins = _availablePlugins.Count;
            }
            catch (Exception ex)
            {
                OnPluginError(null, $"扫描插件目录失败: {directory}", ex);
            }

            return plugins;
        }

        private async Task<PluginInfo?> ScanPluginFileAsync(string filePath, CancellationToken cancellationToken)
        {
            try
            {
                // 验证文件
                if (!File.Exists(filePath))
                    return null;

                var fileInfo = new FileInfo(filePath);
                
                // 检查文件大小
                if (fileInfo.Length == 0)
                    return null;

                // 加载程序集
                var assembly = await Task.Run(() => Assembly.LoadFrom(filePath), cancellationToken);
                
                // 查找插件类型
                var pluginTypes = assembly.GetTypes()
                    .Where(type => _pluginFactory.IsValidPluginType(type))
                    .ToArray();

                if (pluginTypes.Length == 0)
                    return null;

                // 使用第一个找到的插件类型
                var pluginType = pluginTypes[0];
                var metadata = _pluginFactory.GetPluginMetadata(pluginType);
                
                if (metadata == null)
                    return null;

                // 创建插件信息
                var pluginInfo = new PluginInfo
                {
                    Metadata = metadata,
                    FilePath = filePath,
                    Directory = Path.GetDirectoryName(filePath) ?? string.Empty,
                    AssemblyName = assembly.GetName().Name ?? string.Empty,
                    TypeName = pluginType.FullName ?? string.Empty,
                    FileSize = fileInfo.Length,
                    FileHash = await CalculateFileHashAsync(filePath, cancellationToken)
                };

                // 验证插件
                var validationResult = await ValidatePluginInfoAsync(pluginInfo, cancellationToken);
                pluginInfo.IsVerified = validationResult.IsValid;
                pluginInfo.VerifiedTime = DateTime.Now;

                return pluginInfo;
            }
            catch (Exception ex)
            {
                OnPluginError(null, $"扫描插件文件失败: {filePath}", ex);
                return null;
            }
        }

        private async Task<string> CalculateFileHashAsync(string filePath, CancellationToken cancellationToken)
        {
            using (var stream = File.OpenRead(filePath))
            using (var sha256 = System.Security.Cryptography.SHA256.Create())
            {
                var hashBytes = await Task.Run(() => sha256.ComputeHash(stream), cancellationToken);
                return Convert.ToBase64String(hashBytes);
            }
        }

        private async Task<PluginValidationResult> ValidatePluginInfoAsync(PluginInfo pluginInfo, CancellationToken cancellationToken)
        {
            var result = new PluginValidationResult();

            try
            {
                // 基本验证
                if (string.IsNullOrEmpty(pluginInfo.Metadata.Id))
                {
                    result.AddError("插件ID不能为空");
                }

                if (string.IsNullOrEmpty(pluginInfo.Metadata.Name))
                {
                    result.AddError("插件名称不能为空");
                }

                if (pluginInfo.Metadata.Version == null)
                {
                    result.AddError("插件版本不能为空");
                }

                // 检查黑名单
                if (_configuration.BlacklistedPlugins.Contains(pluginInfo.Metadata.Id))
                {
                    result.AddError("插件在黑名单中");
                }

                // 检查白名单（如果启用）
                if (_configuration.WhitelistedPlugins.Length > 0 && 
                    !_configuration.WhitelistedPlugins.Contains(pluginInfo.Metadata.Id))
                {
                    result.AddError("插件不在白名单中");
                }

                // 检查框架版本兼容性
                var currentFrameworkVersion = Assembly.GetExecutingAssembly().GetName().Version;
                if (currentFrameworkVersion != null)
                {
                    //if (pluginInfo.Metadata.MinFrameworkVersion > currentFrameworkVersion)
                    //{
                    //    result.AddError($"插件需要框架版本 {pluginInfo.Metadata.MinFrameworkVersion}，当前版本 {currentFrameworkVersion}");
                    //}

                    //if (pluginInfo.Metadata.MaxFrameworkVersion != null && 
                    //    pluginInfo.Metadata.MaxFrameworkVersion < currentFrameworkVersion)
                    //{
                    //    result.AddWarning($"插件最大支持框架版本 {pluginInfo.Metadata.MaxFrameworkVersion}，当前版本 {currentFrameworkVersion}");
                    //}
                }

                // 检查平台兼容性
                var currentPlatform = Environment.OSVersion.Platform.ToString();
                if (pluginInfo.Metadata.SupportedPlatforms.Length > 0 && 
                    !pluginInfo.Metadata.SupportedPlatforms.Contains(currentPlatform))
                {
                   // result.AddWarning($"插件可能不支持当前平台: {currentPlatform}");
                }

                // 检查文件完整性
                if (!File.Exists(pluginInfo.FilePath))
                {
                    result.AddError("插件文件不存在");
                }

                // 检查数字签名（如果启用安全检查）
                if (_configuration.EnableSecurityCheck)
                {
                    // TODO: 实现数字签名验证
                    result.AddInformation("数字签名验证已跳过");
                }
            }
            catch (Exception ex)
            {
                result.AddError($"验证过程中发生异常: {ex.Message}");
            }

            return result;
        }

        #endregion

        #region 插件加载和卸载

        /// <summary>
        /// 加载插件
        /// </summary>
        /// <param name="pluginPath">插件路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>加载任务</returns>
        public async Task<IPlugin?> LoadPluginAsync(string pluginPath, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(pluginPath) || !File.Exists(pluginPath))
            {
                OnPluginError(null, $"插件文件不存在: {pluginPath}");
                return null;
            }

            // 先扫描插件信息
            var pluginInfo = await ScanPluginFileAsync(pluginPath, cancellationToken);
            if (pluginInfo == null)
            {
                OnPluginError(null, $"无法扫描插件信息: {pluginPath}");
                return null;
            }

            return await LoadPluginAsync(pluginInfo, cancellationToken);
        }

        /// <summary>
        /// 加载插件
        /// </summary>
        /// <param name="pluginInfo">插件信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>加载任务</returns>
        public async Task<IPlugin?> LoadPluginAsync(PluginInfo pluginInfo, CancellationToken cancellationToken = default)
        {
            if (pluginInfo == null)
                throw new ArgumentNullException(nameof(pluginInfo));

            // 检查是否已加载
            if (_loadedPlugins.ContainsKey(pluginInfo.Metadata.Id))
            {
                OnPluginError(null, $"插件已加载: {pluginInfo.Metadata.Id}");
                return _loadedPlugins[pluginInfo.Metadata.Id];
            }

            await _loadSemaphore.WaitAsync(cancellationToken);
            var startTime = DateTime.Now;

            try
            {
                // 验证插件
                var validationResult = await ValidatePluginAsync(pluginInfo.FilePath);
                if (!validationResult.IsValid)
                {
                    var errors = string.Join(", ", validationResult.Errors);
                    OnPluginError(null, $"插件验证失败: {errors}");
                    return null;
                }

                // 检查依赖项
                if (_configuration.EnableDependencyCheck)
                {
                    var dependencyResult = CheckDependencies(pluginInfo);
                    if (!dependencyResult.IsResolved)
                    {
                        var errors = string.Join(", ", dependencyResult.Errors);
                        OnPluginError(null, $"插件依赖检查失败: {errors}");
                        return null;
                    }
                }

                // 加载程序集
                var assembly = Assembly.LoadFrom(pluginInfo.FilePath);
                var pluginType = assembly.GetType(pluginInfo.TypeName);

                if (pluginType == null)
                {
                    OnPluginError(null, $"找不到插件类型: {pluginInfo.TypeName}");
                    return null;
                }

                // 创建插件实例
                var plugin = _pluginFactory.CreatePlugin(pluginType, pluginInfo.Metadata);
                if (plugin == null)
                {
                    OnPluginError(null, $"创建插件实例失败: {pluginInfo.Metadata.Id}");
                    return null;
                }

                // 注册事件处理器
                plugin.StatusChanged += OnPluginStatusChanged;
                plugin.ErrorOccurred += OnPluginErrorOccurred;

                // 添加到已加载列表
                _loadedPlugins.TryAdd(pluginInfo.Metadata.Id, plugin);

                // 更新统计信息
                var loadTime = (DateTime.Now - startTime).TotalMilliseconds;
                _statistics.TotalLoads++;
                _statistics.LoadedPlugins = _loadedPlugins.Count;
                _statistics.AverageLoadTime = (_statistics.AverageLoadTime * (_statistics.TotalLoads - 1) + loadTime) / _statistics.TotalLoads;

                // 触发事件
                OnPluginLoaded(plugin, (long)loadTime);

                return plugin;
            }
            catch (Exception ex)
            {
                OnPluginError(null, $"加载插件失败: {pluginInfo.Metadata.Id}", ex);
                _statistics.TotalErrors++;
                return null;
            }
            finally
            {
                _loadSemaphore.Release();
            }
        }

        /// <summary>
        /// 卸载插件
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>卸载任务</returns>
        public async Task<bool> UnloadPluginAsync(string pluginId, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(pluginId))
                throw new ArgumentException("插件ID不能为空", nameof(pluginId));

            if (!_loadedPlugins.TryGetValue(pluginId, out var plugin))
            {
                OnPluginError(null, $"插件未加载: {pluginId}");
                return false;
            }

            return await UnloadPluginAsync(plugin, cancellationToken);
        }

        /// <summary>
        /// 卸载插件
        /// </summary>
        /// <param name="plugin">插件实例</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>卸载任务</returns>
        public async Task<bool> UnloadPluginAsync(IPlugin plugin, CancellationToken cancellationToken = default)
        {
            if (plugin == null)
                throw new ArgumentNullException(nameof(plugin));

            var startTime = DateTime.Now;

            try
            {
                // 先停止插件
                if (plugin.Status == PluginStatus.Running)
                {
                    await plugin.StopAsync(cancellationToken);
                }

                // 注销事件处理器
                plugin.StatusChanged -= OnPluginStatusChanged;
                plugin.ErrorOccurred -= OnPluginErrorOccurred;

                // 释放插件资源
                plugin.Dispose();

                // 从已加载列表中移除
                _loadedPlugins.TryRemove(plugin.Id, out _);

                // 更新统计信息
                var unloadTime = (DateTime.Now - startTime).TotalMilliseconds;
                _statistics.TotalUnloads++;
                _statistics.LoadedPlugins = _loadedPlugins.Count;

                // 触发事件
                OnPluginUnloaded(plugin.Id, plugin.Name, (long)unloadTime);

                return true;
            }
            catch (Exception ex)
            {
                OnPluginError(plugin, $"卸载插件失败: {plugin.Id}", ex);
                _statistics.TotalErrors++;
                return false;
            }
        }

        #endregion

        #region 插件启动和停止

        /// <summary>
        /// 启动插件
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>启动任务</returns>
        public async Task<bool> StartPluginAsync(string pluginId, CancellationToken cancellationToken = default)
        {
            if (!_loadedPlugins.TryGetValue(pluginId, out var plugin))
            {
                OnPluginError(null, $"插件未加载: {pluginId}");
                return false;
            }

            var startTime = DateTime.Now;

            try
            {
                // 创建插件上下文
                var context = CreatePluginContext(plugin);

                // 初始化插件（如果需要）
                if (plugin.Status == PluginStatus.Loaded)
                {
                    await plugin.InitializeAsync(context, cancellationToken);
                }

                // 启动插件
                await plugin.StartAsync(cancellationToken);

                // 更新统计信息
                var elapsedTime = (DateTime.Now - startTime).TotalMilliseconds;
                _statistics.TotalStarts++;
                _statistics.RunningPlugins = _loadedPlugins.Values.Count(p => p.Status == PluginStatus.Running);
                _statistics.AverageStartTime = (_statistics.AverageStartTime * (_statistics.TotalStarts - 1) + elapsedTime) / _statistics.TotalStarts;

                // 触发事件
                OnPluginStarted(plugin, (long)elapsedTime);

                return true;
            }
            catch (Exception ex)
            {
                OnPluginError(plugin, $"启动插件失败: {pluginId}", ex);
                _statistics.TotalErrors++;
                return false;
            }
        }

        /// <summary>
        /// 停止插件
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>停止任务</returns>
        public async Task<bool> StopPluginAsync(string pluginId, CancellationToken cancellationToken = default)
        {
            if (!_loadedPlugins.TryGetValue(pluginId, out var plugin))
            {
                OnPluginError(null, $"插件未加载: {pluginId}");
                return false;
            }

            var startTime = DateTime.Now;

            try
            {
                await plugin.StopAsync(cancellationToken);

                // 更新统计信息
                var stopTime = (DateTime.Now - startTime).TotalMilliseconds;
                _statistics.TotalStops++;
                _statistics.RunningPlugins = _loadedPlugins.Values.Count(p => p.Status == PluginStatus.Running);

                // 触发事件
                OnPluginStopped(plugin, (long)stopTime);

                return true;
            }
            catch (Exception ex)
            {
                OnPluginError(plugin, $"停止插件失败: {pluginId}", ex);
                _statistics.TotalErrors++;
                return false;
            }
        }

        /// <summary>
        /// 重启插件
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>重启任务</returns>
        public async Task<bool> RestartPluginAsync(string pluginId, CancellationToken cancellationToken = default)
        {
            if (!_loadedPlugins.TryGetValue(pluginId, out var plugin))
            {
                OnPluginError(null, $"插件未加载: {pluginId}");
                return false;
            }

            try
            {
                await plugin.RestartAsync(cancellationToken);
                return true;
            }
            catch (Exception ex)
            {
                OnPluginError(plugin, $"重启插件失败: {pluginId}", ex);
                _statistics.TotalErrors++;
                return false;
            }
        }

        /// <summary>
        /// 启动所有插件
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>启动任务</returns>
        public async Task StartAllPluginsAsync(CancellationToken cancellationToken = default)
        {
            var tasks = _loadedPlugins.Values
                .Where(p => p.Status != PluginStatus.Running && p.Status != PluginStatus.Error)
                .Select(p => StartPluginAsync(p.Id, cancellationToken))
                .ToArray();

            await Task.WhenAll(tasks);
        }

        /// <summary>
        /// 停止所有插件
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>停止任务</returns>
        public async Task StopAllPluginsAsync(CancellationToken cancellationToken = default)
        {
            var tasks = _loadedPlugins.Values
                .Where(p => p.Status == PluginStatus.Running)
                .Select(p => StopPluginAsync(p.Id, cancellationToken))
                .ToArray();

            await Task.WhenAll(tasks);
        }

        #endregion

        #region 插件查询

        /// <summary>
        /// 获取插件
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <returns>插件实例</returns>
        public IPlugin? GetPlugin(string pluginId)
        {
            return _loadedPlugins.TryGetValue(pluginId, out var plugin) ? plugin : null;
        }

        /// <summary>
        /// 获取插件
        /// </summary>
        /// <typeparam name="T">插件类型</typeparam>
        /// <returns>插件实例</returns>
        public T? GetPlugin<T>() where T : class, IPlugin
        {
            return _loadedPlugins.Values.OfType<T>().FirstOrDefault();
        }

        /// <summary>
        /// 获取插件
        /// </summary>
        /// <param name="pluginType">插件类型</param>
        /// <returns>插件实例</returns>
        public IPlugin? GetPlugin(Type pluginType)
        {
            return _loadedPlugins.Values.FirstOrDefault(p => pluginType.IsInstanceOfType(p));
        }

        /// <summary>
        /// 检查插件是否已加载
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <returns>是否已加载</returns>
        public bool IsPluginLoaded(string pluginId)
        {
            return _loadedPlugins.ContainsKey(pluginId);
        }

        /// <summary>
        /// 检查插件是否正在运行
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <returns>是否正在运行</returns>
        public bool IsPluginRunning(string pluginId)
        {
            return _loadedPlugins.TryGetValue(pluginId, out var plugin) && plugin.Status == PluginStatus.Running;
        }

        #endregion

        #region 插件验证

        /// <summary>
        /// 验证插件
        /// </summary>
        /// <param name="pluginPath">插件路径</param>
        /// <returns>验证结果</returns>
        public async Task<PluginValidationResult> ValidatePluginAsync(string pluginPath)
        {
            var result = new PluginValidationResult();

            try
            {
                if (!File.Exists(pluginPath))
                {
                    result.AddError("插件文件不存在");
                    return result;
                }

                // 扫描插件信息
                var pluginInfo = await ScanPluginFileAsync(pluginPath, CancellationToken.None);
                if (pluginInfo == null)
                {
                    result.AddError("无法解析插件信息");
                    return result;
                }

                // 验证插件信息
                return await ValidatePluginInfoAsync(pluginInfo, CancellationToken.None);
            }
            catch (Exception ex)
            {
                result.AddError($"验证过程中发生异常: {ex.Message}");
                return result;
            }
        }

        #endregion

        #region 插件安装和更新

        /// <summary>
        /// 安装插件
        /// </summary>
        /// <param name="pluginPackagePath">插件包路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>安装任务</returns>
        public async Task<bool> InstallPluginAsync(string pluginPackagePath, CancellationToken cancellationToken = default)
        {
            try
            {
                // TODO: 实现插件包解压和安装逻辑
                // 1. 验证插件包
                // 2. 解压到临时目录
                // 3. 验证插件
                // 4. 复制到插件目录
                // 5. 更新插件列表

                await Task.Delay(100, cancellationToken); // 占位符
                return true;
            }
            catch (Exception ex)
            {
                OnPluginError(null, $"安装插件失败: {pluginPackagePath}", ex);
                return false;
            }
        }

        /// <summary>
        /// 卸载插件
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <param name="removeFiles">是否删除文件</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>卸载任务</returns>
        public async Task<bool> UninstallPluginAsync(string pluginId, bool removeFiles = true, CancellationToken cancellationToken = default)
        {
            try
            {
                // 先卸载插件
                var unloadResult = await UnloadPluginAsync(pluginId, cancellationToken);
                if (!unloadResult)
                    return false;

                // 删除文件（如果需要）
                if (removeFiles && _availablePlugins.TryGetValue(pluginId, out var pluginInfo))
                {
                    if (File.Exists(pluginInfo.FilePath))
                    {
                        File.Delete(pluginInfo.FilePath);
                    }

                    // 删除插件目录（如果为空）
                    var pluginDir = Path.GetDirectoryName(pluginInfo.FilePath);
                    if (!string.IsNullOrEmpty(pluginDir) && Directory.Exists(pluginDir))
                    {
                        try
                        {
                            Directory.Delete(pluginDir, false); // 只删除空目录
                        }
                        catch
                        {
                            // 目录不为空，忽略错误
                        }
                    }
                }

                // 从可用插件列表中移除
                _availablePlugins.TryRemove(pluginId, out _);
                _statistics.TotalPlugins = _availablePlugins.Count;

                return true;
            }
            catch (Exception ex)
            {
                OnPluginError(null, $"卸载插件失败: {pluginId}", ex);
                return false;
            }
        }

        /// <summary>
        /// 更新插件
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <param name="newPluginPackagePath">新插件包路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>更新任务</returns>
        public async Task<bool> UpdatePluginAsync(string pluginId, string newPluginPackagePath, CancellationToken cancellationToken = default)
        {
            try
            {
                // TODO: 实现插件更新逻辑
                // 1. 备份当前插件
                // 2. 安装新插件
                // 3. 如果失败，恢复备份

                await Task.Delay(100, cancellationToken); // 占位符
                return true;
            }
            catch (Exception ex)
            {
                OnPluginError(null, $"更新插件失败: {pluginId}", ex);
                return false;
            }
        }

        /// <summary>
        /// 热更新插件
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <param name="newPluginPath">新插件路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>热更新任务</returns>
        public async Task<bool> HotSwapPluginAsync(string pluginId, string newPluginPath, CancellationToken cancellationToken = default)
        {
            if (!EnableHotSwap)
            {
                OnPluginError(null, "热更新功能未启用");
                return false;
            }

            if (!_loadedPlugins.TryGetValue(pluginId, out var plugin))
            {
                OnPluginError(null, $"插件未加载: {pluginId}");
                return false;
            }

            if (!(plugin is IHotSwappablePlugin hotSwappablePlugin))
            {
                OnPluginError(plugin, "插件不支持热更新");
                return false;
            }

            try
            {
                // 验证新插件
                var validationResult = await ValidatePluginAsync(newPluginPath);
                if (!validationResult.IsValid)
                {
                    OnPluginError(plugin, $"新插件验证失败: {string.Join(", ", validationResult.Errors)}");
                    return false;
                }

                // 执行热更新
                return await hotSwappablePlugin.HotSwapAsync(newPluginPath, cancellationToken);
            }
            catch (Exception ex)
            {
                OnPluginError(plugin, $"热更新失败: {pluginId}", ex);
                return false;
            }
        }

        #endregion

        #region 依赖管理

        /// <summary>
        /// 获取插件依赖关系
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <returns>依赖关系</returns>
        public IList<PluginDependency> GetPluginDependencies(string pluginId)
        {
            if (_availablePlugins.TryGetValue(pluginId, out var pluginInfo))
            {
                return pluginInfo.Dependencies;
            }
            return new List<PluginDependency>();
        }

        /// <summary>
        /// 解析插件依赖关系
        /// </summary>
        /// <param name="plugins">插件列表</param>
        /// <returns>解析结果</returns>
        public PluginDependencyResolutionResult ResolveDependencies(IList<PluginInfo> plugins)
        {
            var result = new PluginDependencyResolutionResult();

            try
            {
                // 创建插件字典
                var pluginDict = plugins.ToDictionary(p => p.Metadata.Id, p => p);

                // 检查缺失的依赖项
                foreach (var plugin in plugins)
                {
                    foreach (var dependency in plugin.Dependencies)
                    {
                        if (!pluginDict.ContainsKey(dependency.PluginId))
                        {
                            if (!dependency.IsOptional)
                            {
                                result.MissingDependencies.Add(dependency);
                                result.IsResolved = false;
                            }
                        }
                        else
                        {
                            // 检查版本兼容性
                            var dependentPlugin = pluginDict[dependency.PluginId];
                            if (dependentPlugin.Metadata.Version < dependency.MinVersion ||
                                (dependency.MaxVersion != null && dependentPlugin.Metadata.Version > dependency.MaxVersion))
                            {
                                result.VersionConflicts.Add($"插件 {plugin.Metadata.Id} 需要 {dependency.PluginId} 版本 {dependency.MinVersion}-{dependency.MaxVersion}，但找到版本 {dependentPlugin.Metadata.Version}");
                                result.IsResolved = false;
                            }
                        }
                    }
                }

                // 检查循环依赖
                var circularDeps = DetectCircularDependencies(plugins);
                if (circularDeps.Count > 0)
                {
                    foreach (var dep in circularDeps)
                    {
                        result.CircularDependencies.Add(dep);
                    }
                    result.IsResolved = false;
                }

                // 如果解析成功，计算加载顺序
                if (result.IsResolved)
                {
                    result.LoadOrder = GetLoadOrder(plugins);
                }
            }
            catch (Exception ex)
            {
                result.Errors.Add($"依赖解析失败: {ex.Message}");
                result.IsResolved = false;
            }

            return result;
        }

        /// <summary>
        /// 获取插件加载顺序
        /// </summary>
        /// <param name="plugins">插件列表</param>
        /// <returns>加载顺序</returns>
        public IList<PluginInfo> GetLoadOrder(IList<PluginInfo> plugins)
        {
            var result = new List<PluginInfo>();
            var remaining = new List<PluginInfo>(plugins);
            var pluginDict = plugins.ToDictionary(p => p.Metadata.Id, p => p);

            while (remaining.Count > 0)
            {
                var canLoad = remaining.Where(p =>
                    p.Dependencies.All(dep =>
                        dep.IsOptional ||
                        result.Any(loaded => loaded.Metadata.Id == dep.PluginId)
                    )
                ).ToList();

                if (canLoad.Count == 0)
                {
                    // 无法解析剩余依赖，按优先级排序
                    canLoad = remaining.OrderByDescending(p => p.Metadata.Priority).Take(1).ToList();
                }

                foreach (var plugin in canLoad)
                {
                    result.Add(plugin);
                    remaining.Remove(plugin);
                }
            }

            return result;
        }

        private IList<string> DetectCircularDependencies(IList<PluginInfo> plugins)
        {
            var result = new List<string>();
            var pluginDict = plugins.ToDictionary(p => p.Metadata.Id, p => p);

            foreach (var plugin in plugins)
            {
                var visited = new HashSet<string>();
                var path = new List<string>();

                if (HasCircularDependency(plugin.Metadata.Id, pluginDict, visited, path))
                {
                    result.Add(string.Join(" -> ", path));
                }
            }

            return result.Distinct().ToList();
        }

        private bool HasCircularDependency(string pluginId, Dictionary<string, PluginInfo> pluginDict, HashSet<string> visited, List<string> path)
        {
            if (path.Contains(pluginId))
            {
                path.Add(pluginId);
                return true;
            }

            if (visited.Contains(pluginId))
                return false;

            visited.Add(pluginId);
            path.Add(pluginId);

            if (pluginDict.TryGetValue(pluginId, out var plugin))
            {
                foreach (var dependency in plugin.Dependencies.Where(d => !d.IsOptional))
                {
                    if (HasCircularDependency(dependency.PluginId, pluginDict, visited, path))
                        return true;
                }
            }

            path.RemoveAt(path.Count - 1);
            return false;
        }

        private PluginDependencyResolutionResult CheckDependencies(PluginInfo pluginInfo)
        {
            var result = new PluginDependencyResolutionResult();

            foreach (var dependency in pluginInfo.Dependencies)
            {
                if (!_loadedPlugins.ContainsKey(dependency.PluginId))
                {
                    if (!dependency.IsOptional)
                    {
                        result.MissingDependencies.Add(dependency);
                        result.IsResolved = false;
                    }
                }
                else
                {
                    var dependentPlugin = _loadedPlugins[dependency.PluginId];
                    if (dependentPlugin.Version < dependency.MinVersion ||
                        (dependency.MaxVersion != null && dependentPlugin.Version > dependency.MaxVersion))
                    {
                        result.VersionConflicts.Add($"版本不兼容: {dependency.PluginId}");
                        result.IsResolved = false;
                    }
                }
            }

            return result;
        }

        #endregion

        #region 统计和配置

        /// <summary>
        /// 获取插件统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public PluginManagerStatistics GetStatistics()
        {
            _statistics.LastUpdated = DateTime.Now;
            _statistics.TotalPlugins = _availablePlugins.Count;
            _statistics.LoadedPlugins = _loadedPlugins.Count;
            _statistics.RunningPlugins = _loadedPlugins.Values.Count(p => p.Status == PluginStatus.Running);
            _statistics.ErrorPlugins = _loadedPlugins.Values.Count(p => p.Status == PluginStatus.Error);
            _statistics.DisabledPlugins = _loadedPlugins.Values.Count(p => p.Status == PluginStatus.Disabled);

            return _statistics;
        }

        /// <summary>
        /// 重置统计信息
        /// </summary>
        public void ResetStatistics()
        {
            _statistics.TotalLoads = 0;
            _statistics.TotalUnloads = 0;
            _statistics.TotalStarts = 0;
            _statistics.TotalStops = 0;
            _statistics.TotalErrors = 0;
            _statistics.AverageLoadTime = 0;
            _statistics.AverageStartTime = 0;
            _statistics.StartTime = DateTime.Now;
        }

        /// <summary>
        /// 导出插件配置
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>导出任务</returns>
        public async Task ExportConfigurationAsync(string filePath, CancellationToken cancellationToken = default)
        {
            try
            {
                var config = new
                {
                    Plugins = _loadedPlugins.Values.Select(p => new
                    {
                        p.Id,
                        p.Name,
                        p.Version,
                        p.Status,
                        Configuration = p.Configuration
                    }).ToArray(),
                    Settings = new
                    {
                        _configuration.PluginDirectory,
                        _configuration.EnablePluginIsolation,
                        _configuration.EnableHotSwap,
                        _configuration.EnableDependencyCheck,
                        _configuration.EnableSecurityCheck
                    },
                    ExportTime = DateTime.Now
                };

                var json = JsonConvert.SerializeObject(config, Formatting.Indented);
                File.WriteAllText(filePath, json);
            }
            catch (Exception ex)
            {
                OnPluginError(null, $"导出配置失败: {filePath}", ex);
                throw;
            }
        }

        /// <summary>
        /// 导入插件配置
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>导入任务</returns>
        public async Task ImportConfigurationAsync(string filePath, CancellationToken cancellationToken = default)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    OnPluginError(null, $"配置文件不存在: {filePath}");
                    return;
                }

                var json = File.ReadAllText(filePath);
                // TODO: 实现配置导入逻辑
                await Task.Delay(100, cancellationToken); // 占位符
            }
            catch (Exception ex)
            {
                OnPluginError(null, $"导入配置失败: {filePath}", ex);
                throw;
            }
        }

        /// <summary>
        /// 备份插件
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <param name="backupPath">备份路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>备份任务</returns>
        public async Task<bool> BackupPluginAsync(string pluginId, string backupPath, CancellationToken cancellationToken = default)
        {
            try
            {
                if (!_availablePlugins.TryGetValue(pluginId, out var pluginInfo))
                {
                    OnPluginError(null, $"插件不存在: {pluginId}");
                    return false;
                }

                // 创建备份目录
                var backupDir = Path.GetDirectoryName(backupPath);
                if (!string.IsNullOrEmpty(backupDir) && !Directory.Exists(backupDir))
                {
                    Directory.CreateDirectory(backupDir);
                }

                // 复制插件文件
                await Task.Run(() => File.Copy(pluginInfo.FilePath, backupPath, true), cancellationToken);

                return true;
            }
            catch (Exception ex)
            {
                OnPluginError(null, $"备份插件失败: {pluginId}", ex);
                return false;
            }
        }

        /// <summary>
        /// 恢复插件
        /// </summary>
        /// <param name="backupPath">备份路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>恢复任务</returns>
        public async Task<bool> RestorePluginAsync(string backupPath, CancellationToken cancellationToken = default)
        {
            try
            {
                if (!File.Exists(backupPath))
                {
                    OnPluginError(null, $"备份文件不存在: {backupPath}");
                    return false;
                }

                // TODO: 实现插件恢复逻辑
                await Task.Delay(100, cancellationToken); // 占位符
                return true;
            }
            catch (Exception ex)
            {
                OnPluginError(null, $"恢复插件失败: {backupPath}", ex);
                return false;
            }
        }

        #endregion

        #region 辅助方法

        private IPluginContext CreatePluginContext(IPlugin plugin)
        {
            return new PluginContext
            {
                ApplicationContext = _applicationContext,
                ServiceProvider = null, // TODO: 注入服务提供者
                PluginManager = this,
                PluginDirectory = _configuration.PluginDirectory,
                DataDirectory = _configuration.DataDirectory,
                ConfigurationDirectory = _configuration.ConfigurationDirectory,
                Logger = null // TODO: 注入日志记录器
            };
        }

        #endregion

        #region 事件处理

        private void OnPluginStatusChanged(object? sender, PluginStatusChangedEventArgs e)
        {
            // 更新统计信息
            _statistics.RunningPlugins = _loadedPlugins.Values.Count(p => p.Status == PluginStatus.Running);
            _statistics.ErrorPlugins = _loadedPlugins.Values.Count(p => p.Status == PluginStatus.Error);
        }

        private void OnPluginErrorOccurred(object? sender, PluginErrorEventArgs e)
        {
            _statistics.TotalErrors++;
            PluginError?.Invoke(this, e);
        }

        private void OnPluginLoaded(IPlugin plugin, long loadTime)
        {
            PluginLoaded?.Invoke(this, new PluginLoadedEventArgs(plugin, loadTime));
        }

        private void OnPluginUnloaded(string pluginId, string pluginName, long unloadTime)
        {
            PluginUnloaded?.Invoke(this, new PluginUnloadedEventArgs(pluginId, pluginName, unloadTime));
        }

        private void OnPluginStarted(IPlugin plugin, long startTime)
        {
            PluginStarted?.Invoke(this, new PluginStartedEventArgs(plugin, startTime));
        }

        private void OnPluginStopped(IPlugin plugin, long stopTime)
        {
            PluginStopped?.Invoke(this, new PluginStoppedEventArgs(plugin, stopTime));
        }

        private void OnPluginError(IPlugin? plugin, string errorMessage, Exception? exception = null)
        {
            _statistics.TotalErrors++;
            PluginError?.Invoke(this, new PluginErrorEventArgs(plugin, errorMessage, exception));
        }

        #endregion

        #region IDisposable

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否正在释放</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                try
                {
                    // 停止所有插件
                    StopAllPluginsAsync().Wait(5000);

                    // 卸载所有插件
                    var unloadTasks = _loadedPlugins.Values.Select(p => UnloadPluginAsync(p, CancellationToken.None)).ToArray();
                    Task.WhenAll(unloadTasks).Wait(5000);

                    // 释放信号量
                    _loadSemaphore?.Dispose();
                }
                catch
                {
                    // 忽略释放时的异常
                }

                _disposed = true;
            }
        }

        /// <summary>
        /// 析构函数
        /// </summary>
        ~PluginManager()
        {
            Dispose(false);
        }

        #endregion
    }

    /// <summary>
    /// 默认插件管理器配置
    /// </summary>
    public class DefaultPluginManagerConfiguration : IPluginManagerConfiguration
    {
        public string PluginDirectory { get; set; } = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Plugins");
        public string DataDirectory { get; set; } = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
        public string ConfigurationDirectory { get; set; } = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config");
        public string BackupDirectory { get; set; } = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Backup");
        public bool EnablePluginIsolation { get; set; } = true;
        public bool EnableHotSwap { get; set; } = false;
        public bool EnableDependencyCheck { get; set; } = true;
        public bool EnableSecurityCheck { get; set; } = false;
        public int LoadTimeout { get; set; } = 30000;
        public int StartTimeout { get; set; } = 10000;
        public int StopTimeout { get; set; } = 10000;
        public int MaxConcurrentLoads { get; set; } = 5;
        public string[] AllowedExtensions { get; set; } = { ".dll", ".exe" };
        public string[] BlacklistedPlugins { get; set; } = Array.Empty<string>();
        public string[] WhitelistedPlugins { get; set; } = Array.Empty<string>();
    }

    /// <summary>
    /// 默认插件工厂
    /// </summary>
    public class DefaultPluginFactory : IPluginFactory
    {
        public IPlugin CreatePlugin(Type pluginType, PluginMetadata metadata)
        {
            if (!IsValidPluginType(pluginType))
                throw new ArgumentException($"类型 {pluginType.Name} 不是有效的插件类型");

            var plugin = Activator.CreateInstance(pluginType) as IPlugin;
            if (plugin == null)
                throw new InvalidOperationException($"无法创建插件实例: {pluginType.Name}");

            return plugin;
        }

        public T CreatePlugin<T>(PluginMetadata metadata) where T : class, IPlugin
        {
            return CreatePlugin(typeof(T), metadata) as T ?? throw new InvalidOperationException($"无法创建插件实例: {typeof(T).Name}");
        }

        public bool IsValidPluginType(Type type)
        {
            return type.IsClass && !type.IsAbstract && typeof(IPlugin).IsAssignableFrom(type);
        }

        public PluginMetadata? GetPluginMetadata(Type type)
        {
            // 尝试从特性中读取插件元数据
            var attributes = type.GetCustomAttributes(false);
            foreach (var attr in attributes)
            {
                // 检查是否为插件元数据特性（支持不同命名空间的特性）
                var attrType = attr.GetType();
                if (attrType.Name == "PluginMetadataAttribute")
                {
                    try
                    {
                        var id = GetAttributeProperty(attr, "Id") as string ?? type.FullName ?? type.Name;
                        var name = GetAttributeProperty(attr, "Name") as string ?? type.Name;
                        var versionStr = GetAttributeProperty(attr, "Version") as string ?? "1.0.0";
                        var description = GetAttributeProperty(attr, "Description") as string ?? $"插件: {type.Name}";
                        var author = GetAttributeProperty(attr, "Author") as string ?? "Unknown";
                        var category = GetAttributeProperty(attr, "Category") as string ?? "General";
                        var supportedPlatforms = GetAttributeProperty(attr, "SupportedPlatforms") as string[] ?? new[] { "Windows" };
                        var minFrameworkVersionStr = GetAttributeProperty(attr, "MinFrameworkVersion") as string ?? "4.7.2";

                        return new PluginMetadata
                        {
                            Id = id,
                            Name = name,
                            Version = Version.TryParse(versionStr, out var version) ? version : new Version(1, 0, 0, 0),
                            Description = description,
                            Author = author,
                            Category = category,
                            SupportedPlatforms = supportedPlatforms,
                            MinFrameworkVersion = Version.TryParse(minFrameworkVersionStr, out var minVersion) ? minVersion : new Version(4, 7, 2, 0)
                        };
                    }
                    catch (Exception ex)
                    {
                        // 如果读取特性失败，记录错误但继续使用默认值
                        System.Diagnostics.Debug.WriteLine($"读取插件元数据特性失败: {ex.Message}");
                    }
                }
            }

            // 如果没有找到特性，返回默认元数据
            return new PluginMetadata
            {
                Id = type.FullName ?? type.Name,
                Name = type.Name,
                Version = type.Assembly.GetName().Version ?? new Version(1, 0, 0, 0),
                Description = $"插件: {type.Name}",
                Author = "Unknown",
                Category = "General"
            };
        }

        /// <summary>
        /// 获取特性属性值
        /// </summary>
        private object? GetAttributeProperty(object attribute, string propertyName)
        {
            try
            {
                var property = attribute.GetType().GetProperty(propertyName);
                return property?.GetValue(attribute);
            }
            catch
            {
                return null;
            }
        }
    }

    /// <summary>
    /// 插件上下文实现
    /// </summary>
    public class PluginContext : IPluginContext
    {
        public object? ApplicationContext { get; set; }
        public IServiceProvider? ServiceProvider { get; set; }
        public IPluginManager? PluginManager { get; set; }
        public string PluginDirectory { get; set; } = string.Empty;
        public string DataDirectory { get; set; } = string.Empty;
        public string ConfigurationDirectory { get; set; } = string.Empty;
        public object? Logger { get; set; }

        public T GetService<T>() where T : class
        {
            return ServiceProvider?.GetService(typeof(T)) as T ?? throw new InvalidOperationException($"服务 {typeof(T).Name} 未注册");
        }

        public object? GetService(Type serviceType)
        {
            return ServiceProvider?.GetService(serviceType);
        }

        public IPlugin? GetPlugin(string pluginId)
        {
            return PluginManager?.GetPlugin(pluginId);
        }

        public T? GetPlugin<T>() where T : class, IPlugin
        {
            return PluginManager?.GetPlugin<T>();
        }

        public async Task PublishEventAsync(object eventData, CancellationToken cancellationToken = default)
        {
            // TODO: 实现事件发布
            await Task.CompletedTask;
        }

        public void SubscribeEvent<T>(Func<T, Task> handler)
        {
            // TODO: 实现事件订阅
        }

        public void UnsubscribeEvent<T>(Func<T, Task> handler)
        {
            // TODO: 实现事件取消订阅
        }
    }
}
