using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace McLaser.Device
{
    /// <summary>
    /// 布尔值到连接状态文本的转换器
    /// </summary>
    public class BoolToConnectionStatusConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value is bool isConnected && isConnected ? "已连接" : "未连接";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 布尔值到连接状态颜色的转换器
    /// </summary>
    public class BoolToConnectionColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value is bool isConnected && isConnected
                ? new SolidColorBrush(Color.FromRgb(76, 175, 80))  // #4CAF50 绿色
                : new SolidColorBrush(Color.FromRgb(244, 67, 54)); // #F44336 红色
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 