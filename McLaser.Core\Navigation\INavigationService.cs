#nullable enable
using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Windows;

namespace McLaser.Core.Navigation
{

    /// <summary>
    /// 导航服务接口
    /// 提供页面导航和管理功能
    /// </summary>
 
    public interface INavigationService
    {
        /// <summary>
        /// 当前页面信息
        /// </summary>
        PageInfo? CurrentPage { get; }

        /// <summary>
        /// 当前页面实例
        /// </summary>
        FrameworkElement? CurrentPageInstance { get; }

        /// <summary>
        /// 所有注册的页面
        /// </summary>
        IReadOnlyList<PageInfo> RegisteredPages { get; }

        /// <summary>
        /// 导航历史
        /// </summary>
        IReadOnlyList<PageInfo> NavigationHistory { get; }

        /// <summary>
        /// 页面变更事件
        /// </summary>
        event EventHandler<PageChangedEventArgs>? PageChanged;

        /// <summary>
        /// 注册页面
        /// </summary>
        /// <param name="pageInfo">页面信息</param>
        void RegisterPage(PageInfo pageInfo);

        /// <summary>
        /// 批量注册页面
        /// </summary>
        /// <param name="pages">页面信息列表</param>
        void RegisterPages(IEnumerable<PageInfo> pages);

        /// <summary>
        /// 导航到指定页面
        /// </summary>
        /// <param name="pageId">页面ID</param>
        /// <param name="parameters">导航参数</param>
        /// <returns>是否导航成功</returns>
        bool NavigateTo(string pageId, object? parameters = null);

        /// <summary>
        /// 导航到指定页面
        /// </summary>
        /// <param name="pageInfo">页面信息</param>
        /// <param name="parameters">导航参数</param>
        /// <returns>是否导航成功</returns>
        bool NavigateTo(PageInfo pageInfo, object? parameters = null);

        /// <summary>
        /// 返回上一页
        /// </summary>
        /// <returns>是否返回成功</returns>
        bool GoBack();

        /// <summary>
        /// 前进到下一页
        /// </summary>
        /// <returns>是否前进成功</returns>
        bool GoForward();

        /// <summary>
        /// 清除导航历史
        /// </summary>
        void ClearHistory();

        /// <summary>
        /// 获取页面实例
        /// </summary>
        /// <param name="pageId">页面ID</param>
        /// <returns>页面实例</returns>
        FrameworkElement? GetPageInstance(string pageId);

        /// <summary>
        /// 释放页面实例
        /// </summary>
        /// <param name="pageId">页面ID</param>
        void ReleasePage(string pageId);

        /// <summary>
        /// 检查页面是否存在
        /// </summary>
        /// <param name="pageId">页面ID</param>
        /// <returns>是否存在</returns>
        bool PageExists(string pageId);
    }

    /// <summary>
    /// 页面变更事件参数
    /// </summary>
    public class PageChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="oldPage">旧页面</param>
        /// <param name="newPage">新页面</param>
        /// <param name="parameters">导航参数</param>
        public PageChangedEventArgs(PageInfo? oldPage, PageInfo? newPage, object? parameters = null)
        {
            OldPage = oldPage;
            NewPage = newPage;
            Parameters = parameters;
        }

        /// <summary>
        /// 旧页面
        /// </summary>
        public PageInfo? OldPage { get; }

        /// <summary>
        /// 新页面
        /// </summary>
        public PageInfo? NewPage { get; }

        /// <summary>
        /// 导航参数
        /// </summary>
        public object? Parameters { get; }
    }
}
