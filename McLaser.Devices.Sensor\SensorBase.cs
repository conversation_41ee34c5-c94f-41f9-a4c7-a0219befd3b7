using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Threading;
using System.Threading.Tasks;
using System.Xml.Serialization;
using McLaser.Device;

namespace McLaser.Devices.Sensor
{
    /// <summary>
    /// 传感器基础类
    /// 提供传感器设备的基本实现和通用功能
    /// </summary>
    [Serializable]
    public abstract class SensorBase : DeviceBase, ISensor
    {
        #region 私有字段

        private double _samplingRate = 10;
        private bool _autoSampling = false;
        private double _currentValue = 0;
        private bool _isCalibrated = false;
        private double _lowThreshold = double.MinValue;
        private double _highThreshold = double.MaxValue;
        private bool _alarmEnabled = false;
        private CancellationTokenSource _samplingCancellationToken;
        private readonly List<SensorDataPoint> _historyData = new List<SensorDataPoint>();
        private readonly object _historyLock = new object();

        #endregion

        #region 属性

        /// <summary>
        /// 传感器类型
        /// </summary>
        [Category("传感器信息"), DisplayName("传感器类型")]
        public abstract SensorType SensorType { get; }

        /// <summary>
        /// 测量单位
        /// </summary>
        [Category("传感器信息"), DisplayName("测量单位")]
        public abstract string Unit { get; }

        /// <summary>
        /// 测量范围最小值
        /// </summary>
        [Category("传感器信息"), DisplayName("测量范围最小值")]
        public abstract double MinValue { get; }

        /// <summary>
        /// 测量范围最大值
        /// </summary>
        [Category("传感器信息"), DisplayName("测量范围最大值")]
        public abstract double MaxValue { get; }

        /// <summary>
        /// 测量精度
        /// </summary>
        [Category("传感器信息"), DisplayName("测量精度")]
        public abstract double Accuracy { get; }

        /// <summary>
        /// 采样频率(Hz)
        /// </summary>
        [Category("传感器参数"), DisplayName("采样频率(Hz)")]
        public virtual double SamplingRate
        {
            get => _samplingRate;
            set
            {
                if (Math.Abs(_samplingRate - value) > 0.001)
                {
                    _samplingRate = Math.Max(0.1, Math.Min(1000, value));
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 是否启用自动采样
        /// </summary>
        [Category("传感器参数"), DisplayName("自动采样")]
        public virtual bool AutoSampling
        {
            get => _autoSampling;
            set
            {
                if (_autoSampling != value)
                {
                    _autoSampling = value;
                    OnPropertyChanged();
                    
                    if (value)
                        StartSampling();
                    else
                        StopSampling();
                }
            }
        }

        /// <summary>
        /// 当前测量值
        /// </summary>
        [Category("传感器状态"), DisplayName("当前测量值")]
        public virtual double CurrentValue
        {
            get => _currentValue;
            protected set
            {
                if (Math.Abs(_currentValue - value) > 0.001)
                {
                    double oldValue = _currentValue;
                    _currentValue = value;
                    OnPropertyChanged();
                    
                    // 检查报警
                    CheckAlarm(value);
                    
                    // 添加到历史数据
                    AddToHistory(value);
                    
                    // 触发数据更新事件
                    OnDataUpdated(new SensorDataPoint(value));
                }
            }
        }

        /// <summary>
        /// 传感器状态
        /// </summary>
        [Category("传感器状态"), DisplayName("传感器状态")]
        public abstract SensorStatus SensorStatus { get; }

        /// <summary>
        /// 校准状态
        /// </summary>
        [Category("传感器状态"), DisplayName("校准状态")]
        public virtual bool IsCalibrated
        {
            get => _isCalibrated;
            protected set
            {
                if (_isCalibrated != value)
                {
                    _isCalibrated = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 低报警阈值
        /// </summary>
        [Category("传感器报警"), DisplayName("低报警阈值")]
        public double LowThreshold
        {
            get => _lowThreshold;
            set
            {
                if (Math.Abs(_lowThreshold - value) > 0.001)
                {
                    _lowThreshold = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 高报警阈值
        /// </summary>
        [Category("传感器报警"), DisplayName("高报警阈值")]
        public double HighThreshold
        {
            get => _highThreshold;
            set
            {
                if (Math.Abs(_highThreshold - value) > 0.001)
                {
                    _highThreshold = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 是否启用报警
        /// </summary>
        [Category("传感器报警"), DisplayName("启用报警")]
        public bool AlarmEnabled
        {
            get => _alarmEnabled;
            set
            {
                if (_alarmEnabled != value)
                {
                    _alarmEnabled = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        protected SensorBase()
        {
            DeviceType = DeviceType.Sensor;
        }

        #endregion

        #region 抽象方法

        /// <summary>
        /// 读取传感器值
        /// </summary>
        /// <param name="value">测量值</param>
        /// <returns>是否成功</returns>
        public abstract bool ReadValue(ref double value);

        /// <summary>
        /// 读取多个传感器值
        /// </summary>
        /// <param name="values">测量值数组</param>
        /// <param name="count">读取数量</param>
        /// <returns>是否成功</returns>
        public abstract bool ReadValues(ref double[] values, int count);

        /// <summary>
        /// 校准传感器
        /// </summary>
        /// <param name="referenceValue">参考值</param>
        /// <returns>是否成功</returns>
        public abstract bool Calibrate(double referenceValue);

        /// <summary>
        /// 零点校准
        /// </summary>
        /// <returns>是否成功</returns>
        public abstract bool ZeroCalibration();

        /// <summary>
        /// 满量程校准
        /// </summary>
        /// <param name="fullScaleValue">满量程值</param>
        /// <returns>是否成功</returns>
        public abstract bool FullScaleCalibration(double fullScaleValue);

        /// <summary>
        /// 重置校准
        /// </summary>
        /// <returns>是否成功</returns>
        public abstract bool ResetCalibration();

        /// <summary>
        /// 传感器自检
        /// </summary>
        /// <returns>自检结果</returns>
        public abstract bool SelfTest();

        #endregion

        #region 虚方法

        /// <summary>
        /// 开始连续采样
        /// </summary>
        /// <returns>是否成功</returns>
        public virtual bool StartSampling()
        {
            try
            {
                if (_samplingCancellationToken != null)
                    StopSampling();

                _samplingCancellationToken = new CancellationTokenSource();
                Task.Run(() => SamplingLoop(_samplingCancellationToken.Token));
                
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"启动传感器采样异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 停止连续采样
        /// </summary>
        /// <returns>是否成功</returns>
        public virtual bool StopSampling()
        {
            try
            {
                _samplingCancellationToken?.Cancel();
                _samplingCancellationToken = null;
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"停止传感器采样异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查是否正在采样
        /// </summary>
        /// <returns>是否正在采样</returns>
        public virtual bool IsSampling()
        {
            return _samplingCancellationToken != null && !_samplingCancellationToken.Token.IsCancellationRequested;
        }

        /// <summary>
        /// 设置采样参数
        /// </summary>
        /// <param name="rate">采样频率</param>
        /// <param name="bufferSize">缓冲区大小</param>
        /// <returns>是否成功</returns>
        public virtual bool SetSamplingParameters(double rate, int bufferSize)
        {
            try
            {
                SamplingRate = rate;
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置传感器采样参数异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置报警阈值
        /// </summary>
        /// <param name="lowThreshold">低阈值</param>
        /// <param name="highThreshold">高阈值</param>
        /// <returns>是否成功</returns>
        public virtual bool SetAlarmThresholds(double lowThreshold, double highThreshold)
        {
            try
            {
                if (lowThreshold >= highThreshold)
                    return false;

                LowThreshold = lowThreshold;
                HighThreshold = highThreshold;
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置传感器报警阈值异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取报警阈值
        /// </summary>
        /// <param name="lowThreshold">低阈值</param>
        /// <param name="highThreshold">高阈值</param>
        /// <returns>是否成功</returns>
        public virtual bool GetAlarmThresholds(ref double lowThreshold, ref double highThreshold)
        {
            lowThreshold = LowThreshold;
            highThreshold = HighThreshold;
            return true;
        }

        /// <summary>
        /// 启用/禁用报警
        /// </summary>
        /// <param name="enable">是否启用</param>
        /// <returns>是否成功</returns>
        public virtual bool EnableAlarm(bool enable)
        {
            AlarmEnabled = enable;
            return true;
        }

        /// <summary>
        /// 获取传感器信息
        /// </summary>
        /// <returns>传感器信息</returns>
        public virtual SensorInfo GetSensorInfo()
        {
            return new SensorInfo
            {
                Model = Name,
                Manufacturer = "未知",
                SerialNumber = "未知",
                FirmwareVersion = "未知",
                CalibrationDate = DateTime.MinValue,
                NextCalibrationDate = DateTime.MinValue
            };
        }

        /// <summary>
        /// 获取历史数据
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>历史数据列表</returns>
        public virtual List<SensorDataPoint> GetHistoryData(DateTime startTime, DateTime endTime)
        {
            lock (_historyLock)
            {
                var result = new List<SensorDataPoint>();
                foreach (var data in _historyData)
                {
                    if (data.Timestamp >= startTime && data.Timestamp <= endTime)
                    {
                        result.Add(data);
                    }
                }
                return result;
            }
        }

        /// <summary>
        /// 清除历史数据
        /// </summary>
        /// <returns>是否成功</returns>
        public virtual bool ClearHistoryData()
        {
            try
            {
                lock (_historyLock)
                {
                    _historyData.Clear();
                }
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清除传感器历史数据异常：{ex.Message}");
                return false;
            }
        }

        #endregion

        #region 事件

        /// <summary>
        /// 数据更新事件
        /// </summary>
        public event EventHandler<SensorDataEventArgs> DataUpdated;

        /// <summary>
        /// 报警事件
        /// </summary>
        public event EventHandler<SensorAlarmEventArgs> AlarmTriggered;

        /// <summary>
        /// 传感器状态变化事件
        /// </summary>
        public event EventHandler<SensorStatusChangedEventArgs> StatusChanged;

        /// <summary>
        /// 校准完成事件
        /// </summary>
        public event EventHandler<SensorCalibrationEventArgs> CalibrationCompleted;

        #endregion

        #region 受保护方法

        /// <summary>
        /// 触发数据更新事件
        /// </summary>
        /// <param name="dataPoint">数据点</param>
        protected virtual void OnDataUpdated(SensorDataPoint dataPoint)
        {
            DataUpdated?.Invoke(this, new SensorDataEventArgs(dataPoint));
        }

        /// <summary>
        /// 触发报警事件
        /// </summary>
        /// <param name="alarmType">报警类型</param>
        /// <param name="currentValue">当前值</param>
        /// <param name="threshold">阈值</param>
        /// <param name="message">报警信息</param>
        protected virtual void OnAlarmTriggered(AlarmType alarmType, double currentValue, double threshold, string message)
        {
            AlarmTriggered?.Invoke(this, new SensorAlarmEventArgs(alarmType, currentValue, threshold, message));
        }

        /// <summary>
        /// 触发状态变化事件
        /// </summary>
        /// <param name="oldStatus">旧状态</param>
        /// <param name="newStatus">新状态</param>
        protected virtual void OnStatusChanged(SensorStatus oldStatus, SensorStatus newStatus)
        {
            StatusChanged?.Invoke(this, new SensorStatusChangedEventArgs(oldStatus, newStatus));
        }

        /// <summary>
        /// 触发校准完成事件
        /// </summary>
        /// <param name="calibrationType">校准类型</param>
        /// <param name="success">校准结果</param>
        /// <param name="message">校准信息</param>
        protected virtual void OnCalibrationCompleted(CalibrationType calibrationType, bool success, string message)
        {
            CalibrationCompleted?.Invoke(this, new SensorCalibrationEventArgs(calibrationType, success, message));
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 采样循环
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        private void SamplingLoop(CancellationToken cancellationToken)
        {
            int interval = (int)(1000 / SamplingRate);
            
            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    double value = 0;
                    if (ReadValue(ref value))
                    {
                        CurrentValue = value;
                    }
                    
                    Thread.Sleep(interval);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"传感器采样循环异常：{ex.Message}");
                    break;
                }
            }
        }

        /// <summary>
        /// 检查报警
        /// </summary>
        /// <param name="value">当前值</param>
        private void CheckAlarm(double value)
        {
            if (!AlarmEnabled) return;

            if (value < LowThreshold)
            {
                OnAlarmTriggered(AlarmType.Low, value, LowThreshold, $"传感器值 {value:F3} 低于下限 {LowThreshold:F3}");
            }
            else if (value > HighThreshold)
            {
                OnAlarmTriggered(AlarmType.High, value, HighThreshold, $"传感器值 {value:F3} 高于上限 {HighThreshold:F3}");
            }
        }

        /// <summary>
        /// 添加到历史数据
        /// </summary>
        /// <param name="value">测量值</param>
        private void AddToHistory(double value)
        {
            lock (_historyLock)
            {
                _historyData.Add(new SensorDataPoint(value));
                
                // 限制历史数据数量，保留最近1000条
                if (_historyData.Count > 1000)
                {
                    _historyData.RemoveAt(0);
                }
            }
        }

        #endregion
    }
}
