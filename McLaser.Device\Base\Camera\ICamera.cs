using System.Collections.Generic;
using McLaser.Device;

namespace McLaser.Devices
{
    /// <summary>
    /// 相机接口
    /// 定义相机设备必须实现的功能
    /// </summary>
    public interface ICamera : IDevice
    {
        /// <summary>
        /// 搜索可用的相机
        /// </summary>
        /// <returns>相机信息列表</returns>
        List<CameraInfo> SearchCameras();

        /// <summary>
        /// 开始采集
        /// </summary>
        /// <returns>操作是否成功</returns>
        bool Start();

        /// <summary>
        /// 检查是否正在采集
        /// </summary>
        /// <returns>是否正在采集</returns>
        bool IsStart();

        /// <summary>
        /// 停止采集
        /// </summary>
        /// <returns>操作是否成功</returns>
        bool Stop();

        /// <summary>
        /// 单次采集图像
        /// </summary>
        /// <param name="source">输出的图像数据</param>
        /// <returns>采集是否成功</returns>
        bool GrabImageData(out ImageData source);

        /// <summary>
        /// 开始连续采集
        /// </summary>
        /// <returns>操作是否成功</returns>
        bool GrabContinue();

        /// <summary>
        /// 获取当前曝光时间
        /// </summary>
        /// <param name="value">曝光时间值</param>
        /// <returns>操作是否成功</returns>
        bool GetExposure(ref double value);

        /// <summary>
        /// 设置曝光时间
        /// </summary>
        /// <param name="value">曝光时间值</param>
        /// <returns>操作是否成功</returns>
        bool SetExposure(double value);

        /// <summary>
        /// 获取当前增益值
        /// </summary>
        /// <param name="value">增益值</param>
        /// <returns>操作是否成功</returns>
        bool GetGain(ref double value);

        /// <summary>
        /// 设置增益值
        /// </summary>
        /// <param name="value">增益值</param>
        /// <returns>操作是否成功</returns>
        bool SetGain(double value);

        /// <summary>
        /// 获取图像宽度
        /// </summary>
        /// <param name="value">宽度值</param>
        /// <returns>操作是否成功</returns>
        bool GetWidth(ref int value);

        /// <summary>
        /// 设置图像宽度
        /// </summary>
        /// <param name="value">宽度值</param>
        /// <returns>操作是否成功</returns>
        bool SetWidth(int value);

        /// <summary>
        /// 获取图像高度
        /// </summary>
        /// <param name="value">高度值</param>
        /// <returns>操作是否成功</returns>
        bool GetHeight(ref int value);

        /// <summary>
        /// 设置图像高度
        /// </summary>
        /// <param name="value">高度值</param>
        /// <returns>操作是否成功</returns>
        bool SetHeight(int value);

        /// <summary>
        /// 获取X方向偏移
        /// </summary>
        /// <param name="value">偏移值</param>
        /// <returns>操作是否成功</returns>
        bool GetOffsetX(ref int value);

        /// <summary>
        /// 设置X方向偏移
        /// </summary>
        /// <param name="value">偏移值</param>
        /// <returns>操作是否成功</returns>
        bool SetOffsetX(int value);

        /// <summary>
        /// 获取Y方向偏移
        /// </summary>
        /// <param name="value">偏移值</param>
        /// <returns>操作是否成功</returns>
        bool GetOffsetY(ref int value);

        /// <summary>
        /// 设置Y方向偏移
        /// </summary>
        /// <param name="value">偏移值</param>
        /// <returns>操作是否成功</returns>
        bool SetOffsetY(int value);

        /// <summary>
        /// 获取触发模式
        /// </summary>
        /// <param name="isTrigger">是否为触发模式</param>
        /// <returns>操作是否成功</returns>
        bool GetTriggerMode(ref bool isTrigger);

        /// <summary>
        /// 设置触发模式
        /// </summary>
        /// <param name="mode">触发模式</param>
        /// <returns>操作是否成功</returns>
        bool SetTriggerMode(TrigMode mode);
    }
}
