using System;
using System.Globalization;
using System.Windows.Data;

namespace McLaser.Device.Converters
{
    /// <summary>
    /// 设备类型到图标文本的转换器
    /// </summary>
    public class DeviceTypeToIconConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is DevicesType deviceType)
            {
                return deviceType switch
                {
                    DevicesType.Camera => "📷",
                    DevicesType.MotionCard => "🎛️",
                    DevicesType.Laser => "🔴",
                    DevicesType.Sensor => "📡",
                    _ => "🔧"
                };
            }

            return "🔧";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 