﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;
using System.Windows.Media;
using System.Xml.Linq;

namespace McLaser.Device
{
    public class UnitStatus
    {
        public SolidColorBrush StatusColor => Status ? new SolidColorBrush(Colors.Green) : new SolidColorBrush(Colors.Gray);

        public string StatusDisplay => Status ? "True" : "False";

        public string Name { get; set; }
        public bool Status { get; set; }
        public string Remark { get; set; }

        public UnitStatus(string name, bool value, string remark)
        {
            Name = name;
            Status = value;
            Remark = remark;
        }
    }
}
