﻿using System;

namespace McLaser.Devices.Motion
{
    public class CardCoordinate
    {
        public CoordinateStatus CoordinateStatus { get; set; } = new CoordinateStatus();

        readonly uint[] Masks = {
        0x80000000, // TriggerMove
        0x40000000, // HomeInProgress
        0x20000000, // MinusLimit
        0x10000000, // PlusLimit
        0x08000000, // FeWarn
        0x04000000, // FeFatal
        0x02000000, // LimitStop
        0x01000000, // AmpFault
        0x00800000, // SoftMinusLimit
        0x00400000, // SoftPlusLimit
        0x00200000, // I2tFault
        0x00100000, // TriggerNotFound
        0x00080000, // AmpWarn
        0x00040000, // EncLoss
        0x00020000, // AuxFault
        0x00010000, // TimerEnabled
        0x00008000, // HomeComplete
        0x00004000, // DesVelZero
        0x00002000, // ClosedLoop
        0x00001000, // AmpEna
        0x00000800, // InPos
        0x00000400, // Reserved (预留位)
        0x00000200, // BlockRequest
        0x00000100, // TimersEnabled
        0x00000080, // RadiusError (bit 1)
        0x00000040, // RadiusError (bit 0)
        0x00000020, // SoftLimit
        0x00000010, // RunTimeError
        0x00000008, // PvtError
        0x00000004, // LinToPvtError
        0x00000002, // ErrorStatus (bit 1)
        0x00000001  // ErrorStatus (bit 0)
    };


        static readonly string[] FieldNames = {
        "TriggerMove        |触发搜索运动进行中      ",
        "HomeInProgress     |回零搜索运动进行中      ",
        "MinusLimit         |硬件负限位被设定        ",
        "PlusLimit          |硬件正限位被设定        ",
        "FeWarn             |跟随误差警告           ",
        "FeFatal            |严重跟随误差           ",
        "LimitStop          |停止在硬件限位         ",
        "AmpFault           |放大器故障             ",
        "SoftMinusLimit     |件负限位被设定         ",
        "SoftPlusLimit      |软件正限位被设定        ",
        "I2tFault           |集成电流（I2T）故障     ",
        "TriggerNotFound    |触发器未找到           ",
        "AmpWarn            |放大器警告             ",
        "EncLoss            |传感器丢失错误         ",
        "AuxFault           |辅助故障错误           ",
        "TimerEnabled       |运动定时器已启用        ",
        "HomeComplete       |回零完成               ",
        "DesVelZero         |期望速度为零           ",
        "ClosedLoop         |闭环模式               ",
        "AmpEna             |放大器启用             ",
        "InPos              |电机已到位             ",
        "Reserved           |预留位                ",
        "BlockRequest       |阻塞请求标志位已设置    ",
        "TimersEnabled      |定时器已启用           ",
        "RadiusError (bit 1)|XX/YY/ZZ轴圆周半径误差 ",
        "RadiusError (bit 0)|X/Y/Z轴圆周半径误差    ",
        "SoftLimit          |软件位置限制           ",
        "RunTimeError       |运行时错误             ",
        "PvtError           |PVT模式错误            ",
        "LinToPvtError      |线性到PVT模式错误      ",
        "ErrorStatus (bit 1)|缓冲区错误             ",
        "ErrorStatus (bit 0)|同步分配缓冲区错误      "
    };

        readonly uint[] Masks2 = {
        0x80000000, // Csolve
        0x40000000, // LinToPvtBuf
        0x20000000, // FeedHold (bit 1)
        0x10000000, // FeedHold (bit 0)
        0x08000000, // BlockActive
        0x04000000, // ContMotion
        0x02000000, // CCMode (bit 1)
        0x01000000, // CCMode (bit 0)
        0x00800000, // MoveMode (bit 1)
        0x00400000, // MoveMode (bit 0)
        0x00200000, // SegMove (bit 1)
        0x00100000, // SegMove (bit 0)
        0x00080000, // SegMoveAccel
        0x00040000, // SegMoveDecel
        0x00020000, // SegEnabled
        0x00010000, // SegStopReq
        0x00008000, // LookAheadWrap
        0x00004000, // LookAheadLookBack
        0x00002000, // LookAheadDir
        0x00001000, // LookAheadStop
        0x00000800, // LookAheadChange
        0x00000400, // LookAheadReCalc
        0x00000200, // LookAheadFlush
        0x00000100, // LookAheadActive
        0x00000080, // CCAddedArc
        0x00000040, // CCOffReq
        0x00000020, // CCMoveType (bit 1)
        0x00000010, // CCMoveType (bit 0)
        0x00000008, // EndDelayActive
        0x00000004, // CC3Active
        0x00000002, // SharpCornerStop
        0x00000001  // AddedDwellDis
    };

        readonly string[] FieldNames2 = {
        "Csolve             |PMATCH计算的有效坐标轴定义         " ,
        "LinToPvtBuf        |线性到PVT移动缓冲区               " ,
        "FeedHold (bit 1)   |进给保持加速度/减速度              " ,
        "FeedHold (bit 0)   |进给保持时间基准源                 " ,
        "BlockActive        |块激活状态                        " ,
        "ContMotion         |连续运动请求                      " ,
        "CCMode (bit 1)     |刀具补偿模式位1                   " ,
        "CCMode (bit 0)     |刀具补偿模式位0                   " ,
        "MoveMode (bit 1)   |移动模式位1（0融合和样条,1快速和PVT）",
        "MoveMode (bit 0)   |移动模式位0（0融合和PVT，1快速和样条" ,
        "SegMove (bit 1)    |分段 PVT 模式移动进行中            " ,
        "SegMove (bit 0)    |分段线性模式移动进行中             " ,
        "SegMoveAccel       |第一个分段移动进行中               " ,
        "SegMoveDecel       |最后一个分段移动进行中             " ,
        "SegEnabled         |启用了分段模式                    " ,
        "SegStopReq         |请求停止分段移动                  " ,
        "LookAheadWrap      |预览环绕状态                      " ,
        "LookAheadLookBack  |预览回顾状态                      " ,
        "LookAheadDir       |预览方向                         " ,
        "LookAheadStop      |预览停止状态                      " ,
        "LookAheadChange    |预览变化状态                      " ,
        "LookAheadReCalc    |预览重新计算状态                  " ,
        "LookAheadFlush     |预览刷新状态                      " ,
        "LookAheadActive    |预览活动状态                      " ,
        "CCAddedArc         |刀具补偿已添加弧段                 " ,
        "CCOffReq           |刀具补偿关闭请求                  " ,
        "CCMoveType (bit 1) |刀具补偿移动类型位1                " ,
        "CCMoveType (bit 0) |刀具补偿移动类型位0                " ,
        "EndDelayActive     |自动添加的末端延迟正在进行中        " ,
        "CC3Active          |刀具补偿正在活动中                 " ,
        "SharpCornerStop    |锐角停止，禁止平滑插补             " ,
        "AddedDwellDis      |禁用添加的停顿时间                 "
    };

        //backup Coord[x].Status   获取详细状态
        //TriggerMove = 0
        //HomeInProgress=0
        //MinusLimit=0
        //PlusLimit=0
        //FeWarn=0
        //FeFatal=0
        //LimitStop=0
        //AmpFault=0
        //SoftMinusLimit=0
        //SoftPlusLimit=0
        //I2tFault=0
        //TriggerNotFound=0
        //AmpWarn=0
        //EncLoss=0
        //AuxFault=0
        //TimerEnabled=0
        //HomeComplete=0
        //DesVelZero=1
        //ClosedLoop=1
        //AmpEna=1
        //InPos=1
        //BlockRequest=0
        //TimersEnabled=0
        //ErrorStatus=0
        //LinToPvtBuf=0
        //Csolve=1
        //SegHaltReq=0
        //FeedHold=0
        //BlockActive=0
        //ContMotion=0
        //CCMode=0
        //MoveMode=0
        //SegMove=0
        //SegMoveAccel=0
        //SegMoveDecel=1
        //SegEnabled=1
        //SegStopReq=0
        //LookAheadWrap=0
        //LookAheadLookBack=0
        //LookAheadDir=0
        //LookAheadStop=0
        //LookAheadChange=0
        //LookAheadReCalc=0
        //LookAheadFlush=0
        //LookAheadActive=0
        //CCAddedArc=0
        //CCOffReq=0
        //CCMoveType=0
        //EndDelayActive=0
        //CC3Active=0
        //SharpCornerStop=0
        //AddedDwellDis=1
        //ProgRunning=0
        //ProgActive=0
        //ProgProceeding=0
        //BufferWarn=0
        //InterlockStop=0

        public void ParseStatus(string strStatus)
        {
            var status = Convert.ToUInt32(strStatus.Substring(0, 8), 16);
            var status2 = Convert.ToUInt32(strStatus.Substring(8), 16);

            //高8位
            CoordinateStatus.TriggerMove = (status & Masks[0]) != 0;
            CoordinateStatus.HomeInProgress = (status & Masks[1]) != 0;
            CoordinateStatus.MinusLimit = (status & Masks[2]) != 0;
            CoordinateStatus.PlusLimit = (status & Masks[3]) != 0;
            CoordinateStatus.FeWarn = (status & Masks[4]) != 0;
            CoordinateStatus.FeFatal = (status & Masks[5]) != 0;
            CoordinateStatus.LimitStop = (status & Masks[6]) != 0;
            CoordinateStatus.AmpFault = (status & Masks[7]) != 0;
            CoordinateStatus.SoftMinusLimit = (status & Masks[8]) != 0;
            CoordinateStatus.SoftPlusLimit = (status & Masks[9]) != 0;
            CoordinateStatus.I2tFault = (status & Masks[10]) != 0;
            CoordinateStatus.TriggerNotFound = (status & Masks[11]) != 0;
            CoordinateStatus.AmpWarn = (status & Masks[12]) != 0;
            CoordinateStatus.EncLoss = (status & Masks[13]) != 0;
            CoordinateStatus.AuxFault = (status & Masks[14]) != 0;
            CoordinateStatus.TimerEnabled = (status & Masks[15]) != 0;
            CoordinateStatus.HomeComplete = (status & Masks[16]) != 0;
            CoordinateStatus.DesVelZero = (status & Masks[17]) != 0;
            CoordinateStatus.ClosedLoop = (status & Masks[18]) != 0;
            CoordinateStatus.AmpEna = (status & Masks[19]) != 0;
            CoordinateStatus.InPos = (status & Masks[20]) != 0;
            CoordinateStatus.Reserved = (status & Masks[21]) != 0;
            CoordinateStatus.BlockRequest = (status & Masks[22]) != 0;
            CoordinateStatus.TimersEnabled = (status & Masks[23]) != 0;
            CoordinateStatus.RadiusErrorBit1 = (status & Masks[24]) != 0;
            CoordinateStatus.RadiusErrorBit0 = (status & Masks[25]) != 0;
            CoordinateStatus.SoftLimit = (status & Masks[26]) != 0;
            CoordinateStatus.RunTimeError = (status & Masks[27]) != 0;
            CoordinateStatus.PvtError = (status & Masks[28]) != 0;
            CoordinateStatus.LinToPvtError = (status & Masks[29]) != 0;
            CoordinateStatus.ErrorStatusBit1 = (status & Masks[30]) != 0;
            CoordinateStatus.ErrorStatusBit0 = (status & Masks[31]) != 0;

            //低8位
            CoordinateStatus.Csolve = (status2 & Masks[0]) != 0;
            CoordinateStatus.LinToPvtBuf = (status2 & Masks[1]) != 0;
            CoordinateStatus.FeedHoldBit1 = (status2 & Masks[2]) != 0;
            CoordinateStatus.FeedHoldBit0 = (status2 & Masks[3]) != 0;
            CoordinateStatus.BlockActive = (status2 & Masks[4]) != 0;
            CoordinateStatus.ContMotion = (status2 & Masks[5]) != 0;
            CoordinateStatus.CCModeBit1 = (status2 & Masks[6]) != 0;
            CoordinateStatus.CCModeBit0 = (status2 & Masks[7]) != 0;
            CoordinateStatus.MoveModeBit1 = (status2 & Masks[8]) != 0;
            CoordinateStatus.MoveModeBit0 = (status2 & Masks[9]) != 0;
            CoordinateStatus.SegMoveBit1 = (status2 & Masks[10]) != 0;
            CoordinateStatus.SegMoveBit0 = (status2 & Masks[11]) != 0;
            CoordinateStatus.SegMoveAccel = (status2 & Masks[12]) != 0;
            CoordinateStatus.SegMoveDecel = (status2 & Masks[13]) != 0;
            CoordinateStatus.SegEnabled = (status2 & Masks[14]) != 0;
            CoordinateStatus.SegStopReq = (status2 & Masks[15]) != 0;
            CoordinateStatus.LookAheadWrap = (status2 & Masks[16]) != 0;
            CoordinateStatus.LookAheadLookBack = (status2 & Masks[17]) != 0;
            CoordinateStatus.LookAheadDir = (status2 & Masks[18]) != 0;
            CoordinateStatus.LookAheadStop = (status2 & Masks[19]) != 0;
            CoordinateStatus.LookAheadChange = (status2 & Masks[20]) != 0;
            CoordinateStatus.LookAheadReCalc = (status2 & Masks[21]) != 0;
            CoordinateStatus.LookAheadFlush = (status2 & Masks[22]) != 0;
            CoordinateStatus.LookAheadActive = (status2 & Masks[23]) != 0;
            CoordinateStatus.CCAddedArc = (status2 & Masks[24]) != 0;
            CoordinateStatus.CCOffReq = (status2 & Masks[25]) != 0;
            CoordinateStatus.CCMoveTypeBit1 = (status2 & Masks[26]) != 0;
            CoordinateStatus.CCMoveTypeBit0 = (status2 & Masks[27]) != 0;
            CoordinateStatus.EndDelayActive = (status2 & Masks[28]) != 0;
            CoordinateStatus.CC3Active = (status2 & Masks[29]) != 0;
            CoordinateStatus.SharpCornerStop = (status2 & Masks[30]) != 0;
            CoordinateStatus.AddedDwellDis = (status2 & Masks[31]) != 0;
        }

    }
}