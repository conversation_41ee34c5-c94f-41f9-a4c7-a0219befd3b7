﻿using HalconDotNet;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace McLaser.Modules.Vision
{
    public class Fit
    {
        public static bool FitLine(List<double> rows, List<double> cols, out ROILine line)
        {
            line = new ROILine();
            try
            {
                Gen.SortPairs(ref rows, ref cols);
                HXLDCont lineXLD = new HXLDCont(new HTuple(rows.ToArray()), new HTuple(cols.ToArray()));
                lineXLD.FitLineContourXld("tukey", -1, 0, 5, 2, out double rowBegin, out double colBegin, out double rowEnd, out double colEnd, out double nr, out double nc, out double dist);//tukey剔除算法为halcon推荐算法
                line = new ROILine(Math.Round(rowBegin, 4), Math.Round(colBegin, 4), Math.Round(rowEnd, 4), Math.Round(colEnd, 4));
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public static bool FitLine(double X1, double Y1, double X2, double Y2, out ROILine line)
        {
            List<double> rows = new List<double> { X1, X2 };
            List<double> cols = new List<double> { Y1, Y2 };
            line = new ROILine();
            try
            {
                Gen.SortPairs(ref rows, ref cols);
                HXLDCont lineXLD = new HXLDCont(new HTuple(rows.ToArray()), new HTuple(cols.ToArray()));
                //tukey剔除算法为halcon推荐算法
                lineXLD.FitLineContourXld("tukey", -1, 0, 5, 2, out double rowBegin, out double colBegin, out double rowEnd, out double colEnd, out double nr, out double nc, out double dist);
                line = new ROILine(rowBegin, colBegin, rowEnd, colEnd);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 最小二乘法直线拟合 y=kx+b
        /// </summary>
        /// <param name="xv">x点序列</param>
        /// <param name="zv">y点序列</param>
        /// <param name="num">个数</param>
        /// <param name="k">斜率</param>
        /// <param name="b">b</param>
        /// <returns>是否拟合成功</returns>
        public static bool FitLine(double[] xv, double[] zv, int num, out double k, out double b)
        {
            if (num < 3)
            {
                k = 0; b = 0;
                return false;
            }
            double A = 0.0;
            double B = 0.0;
            double C = 0.0;
            double D = 0.0;
            for (int i = 0; i < num; i++)
            {
                A += (xv[i] * xv[i]);
                B += xv[i];
                C += (zv[i] * xv[i]);
                D += zv[i];
            }
            double tmp = 0;
            tmp = (A * num - B * B);
            if (Math.Abs(tmp) > 0.000001)
            {
                k = (C * num - B * D) / tmp;
                b = (A * D - C * B) / tmp;
            }
            else
            {
                k = 1;
                b = 0;
            }
            return true;
        }

        /// <summary>
        /// 最小二乘法圆拟合
        /// </summary>
        /// <param name="rows">点云 行坐标</param>
        /// <param name="cols">点云 列坐标</param>
        /// <param name="circle">返回圆</param>
        /// <returns>是否拟合成功</returns>
        public static bool FitCircle(List<double> rows, List<double> cols, out Circle circle)
        {
            circle = new Circle();
            try
            {
                if (cols.Count < 3)
                {
                    return false;
                }
                double sum_x = 0.0f, sum_y = 0.0f;
                double sum_x2 = 0.0f, sum_y2 = 0.0f;
                double sum_x3 = 0.0f, sum_y3 = 0.0f;
                double sum_xy = 0.0f, sum_x1y2 = 0.0f, sum_x2y1 = 0.0f;
                int N = cols.Count;
                for (int i = 0; i < N; i++)
                {
                    double x = rows[i];
                    double y = cols[i];
                    double x2 = x * x;
                    double y2 = y * y;
                    sum_x += x;
                    sum_y += y;
                    sum_x2 += x2;
                    sum_y2 += y2;
                    sum_x3 += x2 * x;
                    sum_y3 += y2 * y;
                    sum_xy += x * y;
                    sum_x1y2 += x * y2;
                    sum_x2y1 += x2 * y;
                }
                double C, D, E, G, H;
                double a, b, c;
                C = N * sum_x2 - sum_x * sum_x;
                D = N * sum_xy - sum_x * sum_y;
                E = N * sum_x3 + N * sum_x1y2 - (sum_x2 + sum_y2) * sum_x;
                G = N * sum_y2 - sum_y * sum_y;
                H = N * sum_x2y1 + N * sum_y3 - (sum_x2 + sum_y2) * sum_y;
                a = (H * D - E * G) / (C * G - D * D);
                b = (H * C - E * D) / (D * D - G * C);
                c = -(a * sum_x + b * sum_y + sum_x2 + sum_y2) / N;
                circle.CenterY = Math.Round(a / (-2), 4);
                circle.CenterX = Math.Round(b / (-2), 4);
                circle.Radius = Math.Round(Math.Sqrt(a * a + b * b - 4 * c) / 2, 4);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
    }

}
