using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using McLaser.Core.Framework.Container;

namespace McLaser.Core.Tests
{
    /// <summary>
    /// 统一DI容器单元测试
    /// </summary>
    [TestClass]
    public class ContainerTests
    {
        [TestInitialize]
        public void Setup()
        {
            // 每个测试前重置容器
            ContainerManager.Reset();
        }

        [TestCleanup]
        public void Cleanup()
        {
            // 每个测试后重置容器
            ContainerManager.Reset();
        }

        [TestMethod]
        public void DefaultContainer_ShouldBeCreated()
        {
            // Arrange & Act
            ContainerManager.UseDefaultContainer();
            var container = ContainerManager.Current;

            // Assert
            Assert.IsNotNull(container);
            Assert.AreEqual(ContainerType.Default, ContainerManager.CurrentType);
        }

        [TestMethod]
        public void SingletonService_ShouldReturnSameInstance()
        {
            // Arrange
            ContainerManager.UseDefaultContainer();
            var container = ContainerManager.Current;
            var testService = new TestService { Name = "Singleton" };

            // Act
            container.RegisterSingleton<ITestService>(testService);
            var resolved1 = container.Resolve<ITestService>();
            var resolved2 = container.Resolve<ITestService>();

            // Assert
            Assert.AreSame(testService, resolved1);
            Assert.AreSame(resolved1, resolved2);
        }

        [TestMethod]
        public void TransientService_ShouldReturnDifferentInstances()
        {
            // Arrange
            ContainerManager.UseDefaultContainer();
            var container = ContainerManager.Current;

            // Act
            container.RegisterTransient<ITestService, TestService>();
            var resolved1 = container.Resolve<ITestService>();
            var resolved2 = container.Resolve<ITestService>();

            // Assert
            Assert.IsNotNull(resolved1);
            Assert.IsNotNull(resolved2);
            Assert.AreNotSame(resolved1, resolved2);
        }

        [TestMethod]
        public void FactoryService_ShouldUseFactory()
        {
            // Arrange
            ContainerManager.UseDefaultContainer();
            var container = ContainerManager.Current;

            // Act
            container.RegisterFactory<ITestService>(c => new TestService { Name = "Factory" });
            var resolved = container.Resolve<ITestService>();

            // Assert
            Assert.IsNotNull(resolved);
            Assert.AreEqual("Factory", ((TestService)resolved).Name);
        }

        [TestMethod]
        public void KeyedService_ShouldResolveCorrectly()
        {
            // Arrange
            ContainerManager.UseDefaultContainer();
            var container = ContainerManager.Current;
            var keyedService = new TestService { Name = "Keyed" };

            // Act
            container.RegisterKeyed<ITestService>("test-key", keyedService);
            var resolved = container.ResolveKeyed<ITestService>("test-key");

            // Assert
            Assert.AreSame(keyedService, resolved);
        }

        [TestMethod]
        public void IsRegistered_ShouldReturnCorrectStatus()
        {
            // Arrange
            ContainerManager.UseDefaultContainer();
            var container = ContainerManager.Current;

            // Act & Assert
            Assert.IsFalse(container.IsRegistered<ITestService>());
            
            container.RegisterSingleton<ITestService, TestService>();
            Assert.IsTrue(container.IsRegistered<ITestService>());
        }

        [TestMethod]
        public void TryResolve_ShouldReturnNullForUnregisteredService()
        {
            // Arrange
            ContainerManager.UseDefaultContainer();
            var container = ContainerManager.Current;

            // Act
            var resolved = container.TryResolve<ITestService>();

            // Assert
            Assert.IsNull(resolved);
        }

        [TestMethod]
        [ExpectedException(typeof(InvalidOperationException))]
        public void Resolve_ShouldThrowForUnregisteredService()
        {
            // Arrange
            ContainerManager.UseDefaultContainer();
            var container = ContainerManager.Current;

            // Act
            container.Resolve<ITestService>();

            // Assert - Exception expected
        }

        [TestMethod]
        public void ContainerStatistics_ShouldTrackOperations()
        {
            // Arrange
            ContainerManager.UseDefaultContainer();
            var container = ContainerManager.Current;
            container.RegisterSingleton<ITestService, TestService>();

            // Act
            container.Resolve<ITestService>();
            container.Resolve<ITestService>();
            var stats = ContainerManager.GetStatistics();

            // Assert
            Assert.IsTrue(stats.TotalRegistrations > 0);
            Assert.IsTrue(stats.TotalResolutions > 0);
        }

        [TestMethod]
        public void ContainerValidation_ShouldValidateCoreServices()
        {
            // Arrange
            ContainerManager.UseDefaultContainer();

            // Act
            var validation = ContainerManager.ValidateConfiguration();

            // Assert
            Assert.IsNotNull(validation);
            // 注意：由于我们注册了核心服务，验证应该通过
            // 但如果某些服务缺失，可能会失败，这是正常的
        }

        [TestMethod]
        public void MefContainer_ShouldBeCreated()
        {
            // Arrange & Act
            ContainerManager.UseMefContainer();
            var container = ContainerManager.Current;

            // Assert
            Assert.IsNotNull(container);
            Assert.AreEqual(ContainerType.Mef, ContainerManager.CurrentType);
        }

        [TestMethod]
        public void ContainerChange_ShouldTriggerEvent()
        {
            // Arrange
            bool eventTriggered = false;
            ContainerManager.ContainerChanged += (sender, args) => eventTriggered = true;

            // Act
            ContainerManager.UseDefaultContainer();

            // Assert
            Assert.IsTrue(eventTriggered);
        }
    }

    /// <summary>
    /// 测试服务接口
    /// </summary>
    public interface ITestService
    {
        string Name { get; set; }
    }

    /// <summary>
    /// 测试服务实现
    /// </summary>
    public class TestService : ITestService
    {
        public string Name { get; set; } = "Default";
    }
}
