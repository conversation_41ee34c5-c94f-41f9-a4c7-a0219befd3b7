# 修复项目文件中的错误引用标签
Write-Host "=== 修复项目文件引用标签 ===" -ForegroundColor Cyan

# 查找所有.csproj文件
$projectFiles = Get-ChildItem -Path . -Name "*.csproj" -Recurse

foreach ($file in $projectFiles) {
    Write-Host "检查文件: $file" -ForegroundColor Yellow
    
    try {
        # 读取文件内容
        $content = Get-Content $file -Raw -Encoding UTF8
        
        # 检查是否包含错误的<n>标签
        if ($content -match '<n>([^<]+)</n>') {
            Write-Host "  发现错误的<n>标签，正在修复..." -ForegroundColor Red
            
            # 替换错误的标签
            $newContent = $content -replace '<n>([^<]+)</n>', '<Name>$1</Name>'
            
            # 写回文件
            Set-Content -Path $file -Value $newContent -Encoding UTF8
            
            Write-Host "  ✅ 修复完成" -ForegroundColor Green
        } else {
            Write-Host "  ✅ 无需修复" -ForegroundColor Green
        }
    }
    catch {
        Write-Host "  ❌ 修复失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== 修复完成 ===" -ForegroundColor Cyan
