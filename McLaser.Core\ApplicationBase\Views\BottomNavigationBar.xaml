<UserControl x:Class="McLaser.Core.ApplicationBase.BottomNavigationBar"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:McLaser.Core.ApplicationBase"
             mc:Ignorable="d" 
             d:DesignHeight="80" d:DesignWidth="800">
    
    <UserControl.Resources>
        <!-- 导航栏背景样式 -->
        <Style x:Key="NavigationBarStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource {x:Static SystemColors.ControlBrushKey}}"/>
            <Setter Property="BorderBrush" Value="{DynamicResource {x:Static SystemColors.ActiveBorderBrushKey}}"/>
            <Setter Property="BorderThickness" Value="0,1,0,0"/>
            <Setter Property="Padding" Value="8,4"/>
        </Style>

        <!-- 导航项数据模板 -->
        <DataTemplate x:Key="NavigationItemTemplate">
            <Grid>
                <!-- 导航按钮 -->
                <local:NavigationButton DataContext="{Binding}"/>
                
                <!-- 分类弹出框 -->
                <Popup x:Name="CategoryPopup"
                       IsOpen="{Binding IsPopupOpen, Mode=TwoWay}"
                       Placement="Top"
                       PlacementTarget="{Binding RelativeSource={RelativeSource AncestorType=Grid}}"
                       StaysOpen="False"
                       AllowsTransparency="True"
                       PopupAnimation="Slide">
                    <local:CategoryPopup DataContext="{Binding}">
                        <local:CategoryPopup.Style>
                            <Style TargetType="local:CategoryPopup">
                                <Setter Property="Visibility" Value="Collapsed"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsCategory}" Value="True">
                                        <Setter Property="Visibility" Value="Visible"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </local:CategoryPopup.Style>
                    </local:CategoryPopup>
                </Popup>
            </Grid>
        </DataTemplate>
    </UserControl.Resources>

    <Border Style="{StaticResource NavigationBarStyle}">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <!-- 导航按钮区域 -->
            <ItemsControl Grid.Column="0"
                          ItemsSource="{Binding NavigationItems}"
                          ItemTemplate="{StaticResource NavigationItemTemplate}">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <StackPanel Orientation="Horizontal"
                                   HorizontalAlignment="Left"/>
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
            </ItemsControl>
            
            <!-- 右侧工具区域 -->
            <StackPanel Grid.Column="1" 
                        Orientation="Horizontal"
                        VerticalAlignment="Center">
                
                <!-- 返回按钮 -->
                <Button Content="◀"
                        FontFamily="Segoe UI Symbol"
                        FontSize="16"
                        Width="32" Height="32"
                        Margin="4,0"
                        Command="{Binding GoBackCommand}"
                        ToolTip="返回上一页"
                        Style="{DynamicResource {x:Static ToolBar.ButtonStyleKey}}"/>

                <!-- 前进按钮 -->
                <Button Content="▶"
                        FontFamily="Segoe UI Symbol"
                        FontSize="16"
                        Width="32" Height="32"
                        Margin="4,0"
                        Command="{Binding GoForwardCommand}"
                        ToolTip="前进到下一页"
                        Style="{DynamicResource {x:Static ToolBar.ButtonStyleKey}}"/>

                <!-- 分隔线 -->
                <Separator Style="{DynamicResource {x:Static ToolBar.SeparatorStyleKey}}"
                           Margin="8,0"/>

                <!-- 主题切换 -->
                <ComboBox ItemsSource="{Binding AvailableThemes}"
                          SelectedItem="{Binding CurrentTheme}"
                          Width="80"
                          Margin="4,0"
                          ToolTip="切换主题"/>

                <!-- 设置按钮 -->
                <Button Content="⚙"
                        FontFamily="Segoe UI Symbol"
                        FontSize="16"
                        Width="32" Height="32"
                        Margin="4,0"
                        Command="{Binding OpenSettingsCommand}"
                        ToolTip="设置"
                        Style="{DynamicResource {x:Static ToolBar.ButtonStyleKey}}"/>
            </StackPanel>
        </Grid>
    </Border>
</UserControl>
