using System;
using System.ComponentModel;
using McLaser.Device;

namespace McLaser.Devices.Laser
{
    /// <summary>
    /// 激光器状态基类
    /// 提供激光器设备状态的基本实现和通用功能
    /// </summary>
    [Serializable]
    public abstract class StatusLaser : StatusDevice
    {
        #region 私有字段

        private LaserStatus _laserStatus = LaserStatus.Offline;
        private double _currentPower = 0;
        private double _currentFrequency = 0;
        private double _currentPulseWidth = 0;
        private double _temperature = 0;
        private double _workingHours = 0;
        private string _errorMessage = string.Empty;
        private DateTime _lastUpdateTime = DateTime.MinValue;
        private bool _isLaserOn = false;
        private LaserMode _currentMode = LaserMode.Continuous;

        #endregion

        #region 属性

        /// <summary>
        /// 激光器状态
        /// </summary>
        [Category("激光器状态"), DisplayName("激光器状态")]
        public LaserStatus LaserStatus
        {
            get => _laserStatus;
            set
            {
                if (_laserStatus != value)
                {
                    _laserStatus = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// 当前功率(%)
        /// </summary>
        [Category("激光器参数"), DisplayName("当前功率(%)")]
        public double CurrentPower
        {
            get => _currentPower;
            set
            {
                if (Math.Abs(_currentPower - value) > 0.001)
                {
                    _currentPower = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 当前频率(Hz)
        /// </summary>
        [Category("激光器参数"), DisplayName("当前频率(Hz)")]
        public double CurrentFrequency
        {
            get => _currentFrequency;
            set
            {
                if (Math.Abs(_currentFrequency - value) > 0.001)
                {
                    _currentFrequency = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 当前脉冲宽度(μs)
        /// </summary>
        [Category("激光器参数"), DisplayName("当前脉冲宽度(μs)")]
        public double CurrentPulseWidth
        {
            get => _currentPulseWidth;
            set
            {
                if (Math.Abs(_currentPulseWidth - value) > 0.001)
                {
                    _currentPulseWidth = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 激光器温度(℃)
        /// </summary>
        [Category("激光器状态"), DisplayName("温度(℃)")]
        public double Temperature
        {
            get => _temperature;
            set
            {
                if (Math.Abs(_temperature - value) > 0.1)
                {
                    _temperature = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 工作时间(小时)
        /// </summary>
        [Category("激光器状态"), DisplayName("工作时间(小时)")]
        public double WorkingHours
        {
            get => _workingHours;
            set
            {
                if (Math.Abs(_workingHours - value) > 0.01)
                {
                    _workingHours = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 错误信息
        /// </summary>
        [Category("激光器状态"), DisplayName("错误信息")]
        public string ErrorMessage
        {
            get => _errorMessage;
            set
            {
                if (_errorMessage != value)
                {
                    _errorMessage = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        [Category("激光器状态"), DisplayName("最后更新时间")]
        public DateTime LastUpdateTime
        {
            get => _lastUpdateTime;
            set
            {
                if (_lastUpdateTime != value)
                {
                    _lastUpdateTime = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 激光器是否开启
        /// </summary>
        [Category("激光器状态"), DisplayName("激光器开启")]
        public bool IsLaserOn
        {
            get => _isLaserOn;
            set
            {
                if (_isLaserOn != value)
                {
                    _isLaserOn = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// 当前模式
        /// </summary>
        [Category("激光器参数"), DisplayName("当前模式")]
        public LaserMode CurrentMode
        {
            get => _currentMode;
            set
            {
                if (_currentMode != value)
                {
                    _currentMode = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 状态文本描述
        /// </summary>
        [Category("激光器状态"), DisplayName("状态描述")]
        public override string StatusText
        {
            get
            {
                if (!string.IsNullOrEmpty(_errorMessage))
                    return $"错误: {_errorMessage}";

                if (!IsConnected)
                    return "设备未连接";

                if (_laserStatus == LaserStatus.Error)
                    return "激光器错误";

                if (_isLaserOn)
                    return $"激光开启 - 功率: {_currentPower:F1}% - 频率: {_currentFrequency:F0}Hz";

                return $"激光关闭 - 状态: {_laserStatus}";
            }
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        protected StatusLaser()
        {
            Reset();
        }

        #endregion

        #region 方法

        /// <summary>
        /// 重置状态
        /// </summary>
        public override void Reset()
        {
            IsConnected = false;
            LaserStatus = LaserStatus.Offline;
            CurrentPower = 0;
            CurrentFrequency = 0;
            CurrentPulseWidth = 0;
            Temperature = 0;
            WorkingHours = 0;
            ErrorMessage = string.Empty;
            LastUpdateTime = DateTime.MinValue;
            IsLaserOn = false;
            CurrentMode = LaserMode.Continuous;
        }

        /// <summary>
        /// 设置错误信息
        /// </summary>
        /// <param name="error">错误信息</param>
        public void SetError(string error)
        {
            ErrorMessage = error;
            LaserStatus = LaserStatus.Error;
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 清除错误信息
        /// </summary>
        public void ClearError()
        {
            ErrorMessage = string.Empty;
            if (_laserStatus == LaserStatus.Error)
            {
                _laserStatus = IsConnected ? LaserStatus.Ready : LaserStatus.Offline;
            }
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 更新激光器参数
        /// </summary>
        /// <param name="power">功率</param>
        /// <param name="frequency">频率</param>
        /// <param name="pulseWidth">脉冲宽度</param>
        public void UpdateLaserParameters(double power, double frequency, double pulseWidth)
        {
            CurrentPower = power;
            CurrentFrequency = frequency;
            CurrentPulseWidth = pulseWidth;
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 更新激光器状态信息
        /// </summary>
        /// <param name="temperature">温度</param>
        /// <param name="workingHours">工作时间</param>
        public void UpdateStatusInfo(double temperature, double workingHours)
        {
            Temperature = temperature;
            WorkingHours = workingHours;
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 获取状态摘要
        /// </summary>
        /// <returns>状态摘要字符串</returns>
        public override string GetStatusSummary()
        {
            return $"激光器 - {StatusText} | 温度: {Temperature:F1}℃ | 工作时间: {WorkingHours:F1}h";
        }

        /// <summary>
        /// 检查是否需要更新状态
        /// </summary>
        /// <param name="intervalSeconds">更新间隔(秒)</param>
        /// <returns>是否需要更新</returns>
        public bool ShouldUpdate(double intervalSeconds = 1.0)
        {
            if (LastUpdateTime == DateTime.MinValue)
                return true;

            return (DateTime.Now - LastUpdateTime).TotalSeconds >= intervalSeconds;
        }

        #endregion
    }
}
