using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Xml.Serialization;
using McLaser.Device;
using ODT.PowerPmacComLib;

namespace McLaser.Devices.Motion
{

 
    [DeviceItem("运动控制卡","Pmac","兼容CK3M/CK5M系列", "🎛️")]
    public class CardPmac : CardBase
    {

        private static readonly object objLock = new object();
        private static readonly object objLockIO = new object();
        private static readonly object objLockBuffer = new object();
        private static readonly object objLockStatus = new object();

        private IFTPClientInterface Ftp;
        private ISyncTerminalCommunicationInterface Terminal;
        private ISyncGpasciiCommunicationInterface Client;
        private ISyncGpasciiCommunicationInterface statusClient;
        private bool IsDownload = false;

        private double[] arrPos = new double[32];
        private bool[,] arrIOIn = new bool[4, 16];
        private bool[,] arrIOOut = new bool[4, 16];
        private string strReadAxesPos = string.Empty;
        private Thread thread_ReadCrdInfo;

        [Category("PMAC连接"), DisplayName("IP地址")]
        public string IP { get; set; } = "*************";


        [Category("PMAC连接"), DisplayName("端口")]
        public int Port { get; set; } = 22;


        [Category("PMAC连接"), DisplayName("用户名")]
        public string UserName { get; set; } = "root";


        [Category("PMAC连接"), DisplayName("密码")]
        public string Password { get; set; } = "deltatau";


        [Category("PMAC卡"), DisplayName("轴列表")]
        public override ObservableCollection<AxisBase> ListAxis { get; set; } = new ObservableCollection<AxisBase>();


        [Category("PMAC卡"), DisplayName("DI列表")]
        public override List<IOBase> ListDi { get; set; } = new List<IOBase>();


        [Category("PMAC卡"), DisplayName("DO列表")]
        public override List<IOBase> ListDo { get; set; } = new List<IOBase>();


        public CardCoordinate[] ListCardCoordinates = new CardCoordinate[16];

        public CardPmac()
        {
            Name = "PMAC运动控制卡";

            for (int i = 0; i < ListCardCoordinates.Length; i++)
            {
                ListCardCoordinates[i] = new CardCoordinate();
            }
        }


        #region 基类重写

        public override bool Open()
        {
            try
            {
                if (!IsEnabled) return false;
                if (Status.IsConnected) Close();

                Ftp ??= Connect.CreateFTPClient(CommunicationGlobals.FTPConnectionTypes.FTP, null);
                Terminal ??= Connect.CreateSyncTerminal(CommunicationGlobals.ConnectionTypes.SSH, null);
                Client ??= Connect.CreateSyncGpascii(CommunicationGlobals.ConnectionTypes.SSH, null);
                statusClient ??= Connect.CreateSyncGpascii(CommunicationGlobals.ConnectionTypes.SSH, null);

                // 连接FTP
                if (!Ftp.ConnectFTP(IP, UserName, Password))
                {
                    Close();
                    return false;
                }

                // 连接终端
                if (!Terminal.ConnectTerminal(IP, Port, UserName, Password))
                {
                    Close();
                    return false;
                }

                // 连接GPASCII客户端
                Client.ConnectGpAscii(IP, Port, UserName, Password);
                if (!Client.GpAsciiConnected)
                {
                    Close();
                    return false;
                }

                // 连接状态查询客户端
                statusClient.ConnectGpAscii(IP, Port, UserName, Password);
                if (!statusClient.GpAsciiConnected)
                {
                    Close();
                    return false;
                }

                // 注册错误事件
                Client.ComERROR += ComError;
                statusClient.ComERROR += ComError;

                Status.IsConnected = true;

                // 设置中止状态
                if (!SetAbort()) return false;

                // 使能所有轴
                for (int i = 0; i < ListAxis.Count; i++)
                {
                    Enable(i, true);
                }

                // 启动状态监控任务
                StartStatusMonitoring();

                return IsOpen();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"打开PMAC控制卡异常：{ex.Message}");
                return false;
            }
        }

        public override bool IsOpen()
        {
            return Status.IsConnected && Client != null && Client.GpAsciiConnected;
        }

        public override bool Close()
        {
            try
            {
                // 停止状态监控
                StopStatusMonitoring();

                // 注销错误事件
                if (Client != null)
                {
                    Client.ComERROR -= ComError;
                    Client.DisconnectGpascii();
                }

                if (statusClient != null)
                {
                    statusClient.ComERROR -= ComError;
                    statusClient.DisconnectGpascii();
                }

                // 关闭连接
                Terminal?.DisconnectTerminal();
                Ftp?.DisconnectFTP();

                Status.IsConnected = false;
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"关闭PMAC控制卡异常：{ex.Message}");
                return false;
            }
        }

        public override bool Enable(int index, bool isEnable)
        {
            try
            {
                if (!CheckAxis(index)) return false;

                AxisPmac axis = ListAxis[index] as AxisPmac;
                string str = isEnable ? $"#{axis.ID}j/" : $"#{axis.ID}k";

                if (!Send(str)) return false;

                axis.IsEnable = isEnable;
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"PMAC轴使能异常：{ex.Message}");
                return false;
            }
        }

        public override bool Home(int index, double vel = 100, int timeout = 10000)
        {
            try
            {
                if (!CheckAxis(index)) return false;

                AxisPmac axis = ListAxis[index] as AxisPmac;

                // 停止轴运动
                if (!Stop(index)) return false;

                // 执行回零程序
                string str = $"#{axis.ID}hm";
                if (!Send(str)) return false;

                // 等待回零完成
                return WaitForHome(index, timeout);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"PMAC轴回零异常：{ex.Message}");
                return false;
            }
        }

        public override bool Move(int index, double pos, bool isAbs = true, int timeout = 5000)
        {
            try
            {
                if (!CheckAxis(index)) return false;

                AxisPmac axis = ListAxis[index] as AxisPmac;
                if (!Stop(index)) return false;

                // 运动
                double movepos = MMToPulse(axis, pos);
                string str = isAbs ? $"#{axis.ID}j={movepos}" : $"#{axis.ID}j^{movepos}";

                if (!Send(str)) return false;

                // 等待到位
                return WaitForInPos(index, pos, timeout);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"PMAC轴移动异常：{ex.Message}");
                return false;
            }
        }

        public override bool Jog(int index, bool isPlus)
        {
            try
            {
                if (!CheckAxis(index)) return false;

                AxisPmac axis = ListAxis[index] as AxisPmac;
                if (!Stop(index)) return false;

                string str = isPlus ? $"#{axis.ID}j+" : $"#{axis.ID}j-";
                if (!Send(str)) return false;

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"PMAC轴点动异常：{ex.Message}");
                return false;
            }
        }

        public override bool Stop(int index)
        {
            try
            {
                if (!CheckAxis(index)) return false;

                AxisPmac axis = ListAxis[index] as AxisPmac;
                string str = $"#{axis.ID}j/";

                if (!Send(str)) return false;
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"PMAC轴停止异常：{ex.Message}");
                return false;
            }
        }

        public override bool GetPos(int index, ref double pos)
        {
            try
            {
                if (!CheckAxis(index)) return false;

                AxisPmac axis = ListAxis[index] as AxisPmac;
                axis.CurPos = arrPos[index];
                pos = arrPos[index];
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取PMAC轴位置异常：{ex.Message}");
                return false;
            }
        }

        public override bool IsHome(int index, ref bool bIsHome)
        {
            try
            {
                if (!CheckAxis(index)) return false;

                AxisPmac axis = ListAxis[index] as AxisPmac;
                string str = $"#{axis.ID}?";
                string response = string.Empty;

                var result = SendCommand(str, ref response);
                if (result)
                {
                    // 解析状态字，检查回零位
                    if (int.TryParse(response, out int status))
                    {
                        bIsHome = (status & 0x800) != 0; // 检查bit 11
                        axis.IsHome = bIsHome;
                        return true;
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查PMAC轴回零状态异常：{ex.Message}");
                return false;
            }
        }

        protected override void AddAxis()
        {
            try
            {
                AxisPmac axisPmac = new AxisPmac();
                axisPmac.ID = ListAxis.Count;
                axisPmac.Name = $"Axis{axisPmac.ID}";
                axisPmac.Card = this;
                ListAxis.Add(axisPmac);
            }
            catch(Exception ex)
            {

            }
        }

        protected override bool CheckAxis(int index)
        {
            return index >= 0 && index < ListAxis.Count;
        }

        public override bool WaitForInPos(int index, double targetPos, int timeout)
        {
            try
            {
                var startTime = DateTime.Now;
                AxisPmac axis = ListAxis[index] as AxisPmac;

                while ((DateTime.Now - startTime).TotalMilliseconds < timeout)
                {
                    double currentPos = 0;
                    if (GetPos(index, ref currentPos))
                    {
                        if (Math.Abs(currentPos - targetPos) <= axis.FollowError)
                        {
                            return true;
                        }
                    }
                    Thread.Sleep(10);
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"等待PMAC轴到位异常：{ex.Message}");
                return false;
            }
        }

        #endregion





        private bool Send(string command)
        {
            try
            {
                string response = string.Empty;
                if (!SendCommand(command, ref response)) return false;
                if (string.IsNullOrEmpty(response)) return false;
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"PMAC发送命令异常：{ex.Message}");
                return false;
            }
        }

        private bool SendStatusCommand(string send, ref string rcv)
        {
            if (!IsOpen()) return false;

            var ret = ODT.PowerPmacComLib.Status.TimeOut;
            try
            {
                Monitor.Enter(objLockStatus);
                ret = statusClient.GetResponse(send, out string response);
                rcv = response.ToString().TrimEnd('\n');
            }
            catch (Exception ex)
            {

            }
            finally
            {
                Monitor.Exit(objLockStatus);
            }

            if (ret != ODT.PowerPmacComLib.Status.Ok) return false;
            return true;
        }

        public bool SendCommand(string send, ref string rcv)
        {
            var ret = ODT.PowerPmacComLib.Status.TimeOut;
            try
            {
                Monitor.Enter(objLock);
                ret = Client.GetResponse(send, out string response);
                rcv = response.ToString();
            }
            catch (Exception ex)
            {

            }
            finally
            {
                Monitor.Exit(objLock);
            }

            if (ret != ODT.PowerPmacComLib.Status.Ok) return false;
            return true;
        }

        private void ComError(object sender, ComErArgs e)
        {
            try
            {
                if (e.ErrorMessage.Contains("插座错误"))
                {
                    Status.IsConnected = false;
                    System.Diagnostics.Debug.WriteLine($"{Name} 连接断开，尝试重连");

                    // 尝试重连
                    Task.Run(() =>
                    {
                        Thread.Sleep(1000);
                        if (!Open())
                        {
                            System.Diagnostics.Debug.WriteLine($"{Name} 重连失败");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"{Name} 重连成功");
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"PMAC通信错误处理异常：{ex.Message}");
            }
        }

        private bool SetAbort()
        {
            try
            {
                string str = "abort";
                return Send(str);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置PMAC中止状态异常：{ex.Message}");
                return false;
            }
        }

        private bool WaitForHome(int index, int timeout)
        {
            try
            {
                var startTime = DateTime.Now;

                while ((DateTime.Now - startTime).TotalMilliseconds < timeout)
                {
                    bool isHome = false;
                    if (IsHome(index, ref isHome) && isHome)
                    {
                        return true;
                    }
                    Thread.Sleep(100);
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"等待PMAC轴回零异常：{ex.Message}");
                return false;
            }
        }

        private void StartStatusMonitoring()
        {
            //开启扫描
            if (thread_ReadCrdInfo != null && thread_ReadCrdInfo.ThreadState == System.Threading.ThreadState.Running)
            {
                thread_ReadCrdInfo.Join();
                thread_ReadCrdInfo.Abort();
                thread_ReadCrdInfo = null;
            }
            thread_ReadCrdInfo = new Thread(StatusMonitoringLoop);
            thread_ReadCrdInfo.IsBackground = true;
            thread_ReadCrdInfo.Start();


        }

        private void StopStatusMonitoring()
        {
            //关闭扫描
            if (thread_ReadCrdInfo != null && thread_ReadCrdInfo.ThreadState == System.Threading.ThreadState.Running)
            {
                thread_ReadCrdInfo.Join();
                thread_ReadCrdInfo.Abort();
                thread_ReadCrdInfo = null;
            }
        }

        private void StatusMonitoringLoop()
        {
            while (true)
            {
                try
                {
                    Thread.Sleep(20);
                    if (!this.IsOpen()) continue;

                    //读取IO
                    ReadIOStatus();

                    //读取轴位置
                    ReadAllAxesPosition();

                    //读取轴状态
                    ReadAllAxesStatus();

                    //读取坐标系状态
                    ReadCoordinateStatus();

                }
                catch (Exception ex)
                {
                    continue;
                }

            }
        }

        private void ReadIOStatus()
        {
            try
            {
                // 读取IO状态的具体实现
                for (int i = 0; i < 4; i++)
                {
                    string cmd = $"Gate3[{i}].GpioData[0]";
                    string response = string.Empty;

                    var result = SendStatusCommand(cmd, ref response);
                    if (result)
                    {
                        if (response.Contains("="))
                        {
                            string valueStr = response.Split('=')[1].TrimEnd('\n');
                            if (long.TryParse(valueStr, out long valueIn))
                            {
                                // 解析IO状态
                                for (int j = 0; j < 16; j++)
                                {
                                    arrIOIn[i, j] = (valueIn & (1L << j)) != 0;
                                }
                                for (int j = 16; j < 32; j++)
                                {
                                    arrIOOut[i, j - 16] = (valueIn & (1L << j)) != 0;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新PMAC IO状态异常：{ex.Message}");
            }
        }

        public bool ReadAllAxesPosition()
        {
            try
            {
                //读取轴位置
                if (!IsOpen()) return false;
                string strReadAxesPos = "#0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31p";

                string response = string.Empty;
                var result = SendStatusCommand(strReadAxesPos, ref response);
                if (result)
                {
                    string[] arr = response.Split(' ');
                    for (int i = 0; i < arr.Length; i++)
                    {
                        arrPos[i] = double.Parse(arr[i]);
                    }
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"批量读取PMAC轴位置异常：{ex.Message}");
                return false;
            }
        }

        public bool ReadAllAxesStatus()
        {
            try
            {
                if (!IsOpen()) return false;
                string strReadAxesStatus = "#0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31?";

                string response = string.Empty;
                var result = SendStatusCommand(strReadAxesStatus, ref response);
                if (result)
                {
                    string[] arrAxisStatus = response.Split(' ');
                    for (int i = 0; i < ListAxis.Count; i++)
                    {
                        var str = arrAxisStatus[i].TrimStart('$').TrimEnd('\n');
                        (ListAxis[i] as AxisPmac).ParseStatus(str);
                    }
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"批量读取PMAC轴状态异常：{ex.Message}");
                return false;
            }
        }

        public bool ReadCoordinateStatus()
        {
            try
            {
                if (!IsOpen()) return false;

                string strReadCoorStatus = "&0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15?";
                string response = string.Empty;
                var result = SendStatusCommand(strReadCoorStatus, ref response);
                if (result)
                {
                    string[] lines = response.Split('\n');
                    string[] arrCoorStatus = response.Split(' ');
                    for (int i = 0; i < ListCardCoordinates.Length; i++)
                    {
                        var str = arrCoorStatus[i].TrimStart('$').TrimEnd('\n');
                        ListCardCoordinates[i].ParseStatus(str);
                    }
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"批量读取PMAC坐标系状态异常：{ex.Message}");
                return false;
            }
        }

        public bool RunProgram(string programName)
        {
            try
            {
                if (!IsOpen() || string.IsNullOrEmpty(programName)) return false;

                string command = $"&1 b{programName} r";
                return Send(command);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"执行PMAC程序异常：{ex.Message}");
                return false;
            }
        }

        public bool StopProgram()
        {
            try
            {
                if (!IsOpen()) return false;

                string command = "&1 a";
                return Send(command);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"停止PMAC程序异常：{ex.Message}");
                return false;
            }
        }
    }
}
