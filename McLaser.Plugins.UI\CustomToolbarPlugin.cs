using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using McLaser.Core.Plugins;
using McLaser.Core.UI;

namespace McLaser.Plugins.UI
{
    /// <summary>
    /// 自定义工具栏插件
    /// 为激光加工软件添加常用功能的快捷工具栏
    /// </summary>
    [PluginMetadata(
        Id = "McLaser.Plugins.CustomToolbar",
        Name = "自定义工具栏",
        Version = "1.1.0",
        Description = "提供激光加工常用功能的快捷工具栏，包括快速设置、模板加载、质量检测等",
        Author = "McLaser UI Team",
        Category = "界面扩展",
        SupportedPlatforms = new[] { "Windows" },
        MinFrameworkVersion = "4.7.2"
    )]
    public class CustomToolbarPlugin : IPlugin, IUIExtension
    {
        #region 私有字段

        private IPluginContext _context;
        private ToolBar _customToolbar;
        private readonly List<ICommand> _commands;
        private readonly Dictionary<string, ToolbarItem> _toolbarItems;

        #endregion

        #region 构造函数

        public CustomToolbarPlugin()
        {
            _commands = new List<ICommand>();
            _toolbarItems = new Dictionary<string, ToolbarItem>();
            InitializeToolbarItems();
        }

        #endregion

        #region IPlugin 实现

        public string Id => "McLaser.Plugins.CustomToolbar";
        public string Name => "自定义工具栏";
        public Version Version => new Version(1, 1, 0);
        public string Description => "激光加工常用功能工具栏";
        public PluginStatus Status { get; private set; } = PluginStatus.Stopped;

        public event EventHandler<PluginStatusChangedEventArgs> StatusChanged;
        public event EventHandler<PluginErrorEventArgs> ErrorOccurred;

        public async Task InitializeAsync(IPluginContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            
            try
            {
                // 创建工具栏UI
                await CreateToolbarUIAsync();
                
                // 注册命令
                RegisterCommands();
                
                Status = PluginStatus.Initialized;
                OnStatusChanged(PluginStatus.Stopped, PluginStatus.Initialized);
            }
            catch (Exception ex)
            {
                Status = PluginStatus.Error;
                OnErrorOccurred($"自定义工具栏初始化失败: {ex.Message}", ex);
                throw;
            }
        }

        public async Task StartAsync()
        {
            try
            {
                if (Status != PluginStatus.Initialized && Status != PluginStatus.Stopped)
                    throw new InvalidOperationException($"插件状态错误: {Status}");

                // 显示工具栏
                if (_customToolbar != null)
                {
                    _customToolbar.Visibility = Visibility.Visible;
                }
                
                Status = PluginStatus.Running;
                OnStatusChanged(PluginStatus.Initialized, PluginStatus.Running);
                
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                Status = PluginStatus.Error;
                OnErrorOccurred($"自定义工具栏启动失败: {ex.Message}", ex);
                throw;
            }
        }

        public async Task StopAsync()
        {
            try
            {
                // 隐藏工具栏
                if (_customToolbar != null)
                {
                    _customToolbar.Visibility = Visibility.Collapsed;
                }
                
                Status = PluginStatus.Stopped;
                OnStatusChanged(PluginStatus.Running, PluginStatus.Stopped);
                
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"自定义工具栏停止失败: {ex.Message}", ex);
            }
        }

        public async Task ShutdownAsync()
        {
            try
            {
                if (Status == PluginStatus.Running)
                {
                    await StopAsync();
                }

                // 清理UI资源
                CleanupUI();
                
                Status = PluginStatus.Shutdown;
                OnStatusChanged(Status, PluginStatus.Shutdown);
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"自定义工具栏关闭失败: {ex.Message}", ex);
            }
        }

        #endregion

        #region IUIExtension 实现

        public string ExtensionName => "CustomToolbar";
        public UIExtensionType ExtensionType => UIExtensionType.Toolbar;
        public FrameworkElement UIElement => _customToolbar;
        public bool IsVisible => _customToolbar?.Visibility == Visibility.Visible;

        public event EventHandler<UIExtensionEventArgs> UIExtensionEvent;

        public async Task<bool> AttachToHostAsync(IUIHost uiHost)
        {
            try
            {
                if (uiHost == null)
                    throw new ArgumentNullException(nameof(uiHost));

                // 将工具栏添加到主界面
                await uiHost.AddToolbarAsync(_customToolbar, "CustomToolbar");
                
                OnUIExtensionEvent("Attached", "工具栏已添加到主界面");
                return true;
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"附加工具栏到主界面失败: {ex.Message}", ex);
                return false;
            }
        }

        public async Task<bool> DetachFromHostAsync(IUIHost uiHost)
        {
            try
            {
                if (uiHost == null)
                    return true;

                // 从主界面移除工具栏
                await uiHost.RemoveToolbarAsync("CustomToolbar");
                
                OnUIExtensionEvent("Detached", "工具栏已从主界面移除");
                return true;
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"从主界面分离工具栏失败: {ex.Message}", ex);
                return false;
            }
        }

        public void ShowExtension()
        {
            if (_customToolbar != null)
            {
                _customToolbar.Visibility = Visibility.Visible;
                OnUIExtensionEvent("Shown", "工具栏已显示");
            }
        }

        public void HideExtension()
        {
            if (_customToolbar != null)
            {
                _customToolbar.Visibility = Visibility.Collapsed;
                OnUIExtensionEvent("Hidden", "工具栏已隐藏");
            }
        }

        #endregion

        #region 私有方法

        private void InitializeToolbarItems()
        {
            _toolbarItems["QuickStart"] = new ToolbarItem
            {
                Id = "QuickStart",
                Name = "快速启动",
                Icon = "PlayIcon",
                ToolTip = "快速启动激光加工流程",
                Command = new RelayCommand(ExecuteQuickStart, CanExecuteQuickStart)
            };

            _toolbarItems["EmergencyStop"] = new ToolbarItem
            {
                Id = "EmergencyStop",
                Name = "紧急停止",
                Icon = "StopIcon",
                ToolTip = "紧急停止所有操作",
                Command = new RelayCommand(ExecuteEmergencyStop, () => true)
            };

            _toolbarItems["LoadTemplate"] = new ToolbarItem
            {
                Id = "LoadTemplate",
                Name = "加载模板",
                Icon = "TemplateIcon",
                ToolTip = "加载加工模板",
                Command = new RelayCommand(ExecuteLoadTemplate, () => true)
            };

            _toolbarItems["QualityCheck"] = new ToolbarItem
            {
                Id = "QualityCheck",
                Name = "质量检测",
                Icon = "CheckIcon",
                ToolTip = "启动质量检测",
                Command = new RelayCommand(ExecuteQualityCheck, CanExecuteQualityCheck)
            };

            _toolbarItems["Settings"] = new ToolbarItem
            {
                Id = "Settings",
                Name = "快速设置",
                Icon = "SettingsIcon",
                ToolTip = "打开快速设置面板",
                Command = new RelayCommand(ExecuteSettings, () => true)
            };
        }

        private async Task CreateToolbarUIAsync()
        {
            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                _customToolbar = new ToolBar
                {
                    Name = "CustomToolbar",
                    Height = 40,
                    Margin = new Thickness(5)
                };

                // 添加工具栏按钮
                foreach (var item in _toolbarItems.Values)
                {
                    var button = new Button
                    {
                        Content = CreateButtonContent(item),
                        Command = item.Command,
                        ToolTip = item.ToolTip,
                        Width = 80,
                        Height = 35,
                        Margin = new Thickness(2)
                    };

                    _customToolbar.Items.Add(button);
                }

                // 添加分隔符
                _customToolbar.Items.Add(new Separator());

                // 添加状态指示器
                var statusIndicator = new TextBlock
                {
                    Text = "就绪",
                    VerticalAlignment = VerticalAlignment.Center,
                    Margin = new Thickness(10, 0, 0, 0),
                    FontWeight = FontWeights.Bold
                };

                _customToolbar.Items.Add(statusIndicator);
            });
        }

        private StackPanel CreateButtonContent(ToolbarItem item)
        {
            var panel = new StackPanel
            {
                Orientation = Orientation.Vertical,
                HorizontalAlignment = HorizontalAlignment.Center
            };

            // 图标（这里用文本代替，实际应用中可以使用图标字体或图片）
            var icon = new TextBlock
            {
                Text = GetIconText(item.Icon),
                FontSize = 16,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 2)
            };

            // 文本
            var text = new TextBlock
            {
                Text = item.Name,
                FontSize = 10,
                HorizontalAlignment = HorizontalAlignment.Center
            };

            panel.Children.Add(icon);
            panel.Children.Add(text);

            return panel;
        }

        private string GetIconText(string iconName)
        {
            return iconName switch
            {
                "PlayIcon" => "▶",
                "StopIcon" => "⏹",
                "TemplateIcon" => "📄",
                "CheckIcon" => "✓",
                "SettingsIcon" => "⚙",
                _ => "●"
            };
        }

        private void RegisterCommands()
        {
            foreach (var item in _toolbarItems.Values)
            {
                _commands.Add(item.Command);
            }
        }

        private void CleanupUI()
        {
            _customToolbar?.Items.Clear();
            _customToolbar = null;
        }

        #region 命令执行方法

        private void ExecuteQuickStart()
        {
            try
            {
                // 执行快速启动逻辑
                OnUIExtensionEvent("QuickStart", "执行快速启动");
                
                // 这里应该调用主程序的快速启动功能
                // 例如：_context.GetService<ILaserController>().QuickStart();
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"快速启动失败: {ex.Message}", ex);
            }
        }

        private bool CanExecuteQuickStart()
        {
            // 检查是否可以执行快速启动
            return Status == PluginStatus.Running;
        }

        private void ExecuteEmergencyStop()
        {
            try
            {
                // 执行紧急停止逻辑
                OnUIExtensionEvent("EmergencyStop", "执行紧急停止");
                
                // 这里应该调用主程序的紧急停止功能
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"紧急停止失败: {ex.Message}", ex);
            }
        }

        private void ExecuteLoadTemplate()
        {
            try
            {
                // 执行加载模板逻辑
                OnUIExtensionEvent("LoadTemplate", "加载加工模板");
                
                // 这里应该打开模板选择对话框
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"加载模板失败: {ex.Message}", ex);
            }
        }

        private void ExecuteQualityCheck()
        {
            try
            {
                // 执行质量检测逻辑
                OnUIExtensionEvent("QualityCheck", "启动质量检测");
                
                // 这里应该调用质量检测功能
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"质量检测失败: {ex.Message}", ex);
            }
        }

        private bool CanExecuteQualityCheck()
        {
            // 检查是否可以执行质量检测
            return Status == PluginStatus.Running;
        }

        private void ExecuteSettings()
        {
            try
            {
                // 打开快速设置面板
                OnUIExtensionEvent("Settings", "打开快速设置");
                
                // 这里应该打开设置对话框
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"打开设置失败: {ex.Message}", ex);
            }
        }

        #endregion

        private void OnStatusChanged(PluginStatus oldStatus, PluginStatus newStatus)
        {
            StatusChanged?.Invoke(this, new PluginStatusChangedEventArgs(oldStatus, newStatus));
        }

        private void OnErrorOccurred(string message, Exception exception = null)
        {
            ErrorOccurred?.Invoke(this, new PluginErrorEventArgs(this, message, exception));
        }

        private void OnUIExtensionEvent(string eventType, string message)
        {
            UIExtensionEvent?.Invoke(this, new UIExtensionEventArgs(eventType, message));
        }

        #endregion
    }

    #region 辅助类

    public class ToolbarItem
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Icon { get; set; }
        public string ToolTip { get; set; }
        public ICommand Command { get; set; }
    }

    public class RelayCommand : ICommand
    {
        private readonly Action _execute;
        private readonly Func<bool> _canExecute;

        public RelayCommand(Action execute, Func<bool> canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public bool CanExecute(object parameter)
        {
            return _canExecute?.Invoke() ?? true;
        }

        public void Execute(object parameter)
        {
            _execute();
        }
    }

    #endregion
}
