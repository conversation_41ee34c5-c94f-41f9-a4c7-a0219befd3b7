<Window x:Class="McLaser.App.Views.ModuleDemoWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="McLaser 核心模块演示" Height="800" Width="1200"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">
    
    <Window.Resources>
        <!-- 样式定义 -->
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
            <Setter Property="Foreground" Value="#2E86AB"/>
        </Style>
        
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="MinWidth" Value="100"/>
        </Style>
        
        <Style x:Key="TextBoxStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="VerticalScrollBarVisibility" Value="Auto"/>
            <Setter Property="HorizontalScrollBarVisibility" Value="Auto"/>
        </Style>
        
        <Style x:Key="ListBoxStyle" TargetType="ListBox">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="MaxHeight" Value="200"/>
        </Style>
    </Window.Resources>
    
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="200"/>
        </Grid.RowDefinitions>
        
        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="McLaser 核心模块功能演示" 
                   FontSize="20" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,10"/>
        
        <!-- 主要内容区域 -->
        <TabControl Grid.Row="1" Margin="0,0,0,10">
            
            <!-- JSON序列化模块 -->
            <TabItem Header="JSON序列化服务">
                <Grid Margin="10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- 输入区域 -->
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="JSON输入" Style="{StaticResource SectionHeaderStyle}"/>
                        <TextBox Text="{Binding JsonInput, UpdateSourceTrigger=PropertyChanged}" 
                                 Style="{StaticResource TextBoxStyle}"
                                 Height="300" AcceptsReturn="True" TextWrapping="Wrap"/>
                    </StackPanel>
                    
                    <!-- 操作按钮 -->
                    <StackPanel Grid.Column="1" VerticalAlignment="Center" Margin="10">
                        <Button Content="序列化" Command="{Binding SerializeJsonCommand}" 
                                Style="{StaticResource ButtonStyle}"/>
                        <Button Content="反序列化" Command="{Binding DeserializeJsonCommand}" 
                                Style="{StaticResource ButtonStyle}"/>
                        <Button Content="格式化" Command="{Binding FormatJsonCommand}" 
                                Style="{StaticResource ButtonStyle}"/>
                    </StackPanel>
                    
                    <!-- 输出区域 -->
                    <StackPanel Grid.Column="2">
                        <TextBlock Text="JSON输出" Style="{StaticResource SectionHeaderStyle}"/>
                        <TextBox Text="{Binding JsonOutput}" 
                                 Style="{StaticResource TextBoxStyle}"
                                 Height="300" IsReadOnly="True" TextWrapping="Wrap"/>
                    </StackPanel>
                </Grid>
            </TabItem>
            
            <!-- 样式系统模块 -->
            <TabItem Header="统一样式系统">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- 样式控制 -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal">
                        <TextBlock Text="当前样式集合:" VerticalAlignment="Center" Margin="5"/>
                        <TextBlock Text="{Binding CurrentStyleSet}" FontWeight="Bold" 
                                   VerticalAlignment="Center" Margin="5"/>
                        <Button Content="清除缓存" Command="{Binding ClearStyleCacheCommand}" 
                                Style="{StaticResource ButtonStyle}"/>
                    </StackPanel>
                    
                    <!-- 可用样式列表 -->
                    <StackPanel Grid.Row="1">
                        <TextBlock Text="可用样式集合" Style="{StaticResource SectionHeaderStyle}"/>
                        <ItemsControl ItemsSource="{Binding AvailableStyleSets}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <WrapPanel/>
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Button Content="{Binding}" 
                                            Command="{Binding DataContext.ApplyStyleCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                            CommandParameter="{Binding}"
                                            Style="{StaticResource ButtonStyle}"/>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                    
                    <!-- 样式演示区域 -->
                    <StackPanel Grid.Row="2">
                        <TextBlock Text="样式演示" Style="{StaticResource SectionHeaderStyle}"/>
                        <StackPanel Orientation="Horizontal" Margin="10">
                            <Button Content="默认按钮" Style="{DynamicResource DefaultButton}" Margin="5"/>
                            <Button Content="现代按钮" Style="{DynamicResource ModernButton}" Margin="5"/>
                            <Button Content="经典按钮" Style="{DynamicResource ClassicButton}" Margin="5"/>
                        </StackPanel>
                        <TextBox Text="样式演示文本框" Style="{DynamicResource DefaultTextBox}" Margin="10"/>
                    </StackPanel>
                </Grid>
            </TabItem>
            
            <!-- 用户权限管理模块 -->
            <TabItem Header="用户权限管理">
                <Grid Margin="10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- 登录区域 -->
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="用户认证" Style="{StaticResource SectionHeaderStyle}"/>
                        
                        <StackPanel Orientation="Horizontal" Margin="5">
                            <TextBlock Text="用户名:" VerticalAlignment="Center" Width="60"/>
                            <TextBox Text="{Binding LoginUsername}" Width="150" Margin="5"/>
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal" Margin="5">
                            <TextBlock Text="密码:" VerticalAlignment="Center" Width="60"/>
                            <PasswordBox x:Name="PasswordBox" Width="150" Margin="5"/>
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal" Margin="5">
                            <Button Content="登录" Command="{Binding LoginCommand}" 
                                    Style="{StaticResource ButtonStyle}"/>
                            <Button Content="登出" Command="{Binding LogoutCommand}" 
                                    Style="{StaticResource ButtonStyle}"/>
                        </StackPanel>
                        
                        <TextBlock Text="{Binding LoginStatus}" Margin="5" FontWeight="Bold"/>
                        
                        <TextBlock Text="用户列表" Style="{StaticResource SectionHeaderStyle}"/>
                        <Button Content="刷新用户列表" Command="{Binding RefreshUsersCommand}" 
                                Style="{StaticResource ButtonStyle}" HorizontalAlignment="Left"/>
                        
                        <ListBox ItemsSource="{Binding Users}" Style="{StaticResource ListBoxStyle}">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <StackPanel>
                                        <TextBlock Text="{Binding DisplayNameOrUsername}" FontWeight="Bold"/>
                                        <TextBlock Text="{Binding Email}" FontSize="10" Foreground="Gray"/>
                                    </StackPanel>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                    </StackPanel>
                    
                    <!-- 权限信息区域 -->
                    <StackPanel Grid.Column="1">
                        <TextBlock Text="权限信息" Style="{StaticResource SectionHeaderStyle}"/>
                        <TextBlock Text="当前用户权限功能演示区域" Margin="5"/>
                        <TextBlock Text="• 用户管理" Margin="10,2"/>
                        <TextBlock Text="• 角色分配" Margin="10,2"/>
                        <TextBlock Text="• 权限控制" Margin="10,2"/>
                        <TextBlock Text="• 会话管理" Margin="10,2"/>
                    </StackPanel>
                </Grid>
            </TabItem>
        </TabControl>
        
        <!-- 日志区域 -->
        <GroupBox Grid.Row="2" Header="操作日志">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <ListBox Grid.Row="0" ItemsSource="{Binding LogMessages}" 
                         Style="{StaticResource ListBoxStyle}" MaxHeight="150"/>
                
                <Button Grid.Row="1" Content="清除日志" Command="{Binding ClearLogCommand}" 
                        Style="{StaticResource ButtonStyle}" HorizontalAlignment="Right"/>
            </Grid>
        </GroupBox>
    </Grid>
</Window>
