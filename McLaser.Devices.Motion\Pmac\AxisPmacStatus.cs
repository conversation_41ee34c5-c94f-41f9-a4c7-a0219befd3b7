using McLaser.Device;
using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace McLaser.Devices.Motion
{
    
    [Serializable]
    public class AxisPmacStatus
    {

        uint[] Masks = {
        0x80000000, // TriggerMove
        0x40000000, // HomeInProgress
        0x20000000, // MinusLimit
        0x10000000, // PlusLimit
        0x08000000, // FeWarn
        0x04000000, // FeFatal
        0x02000000, // LimitStop
        0x01000000, // AmpFault
        0x00800000, // SoftMinusLimit
        0x00400000, // SoftPlusLimit
        0x00200000, // I2tFault
        0x00100000, // TriggerNotFound
        0x00080000, // AmpWarn
        0x00040000, // EncLoss
        0x00020000, // AuxFault
        0x00010000, // Reserved (Future Use)
        0x00008000, // HomeComplete
        0x00004000, // DesVelZero
        0x00002000, // ClosedLoop
        0x00001000, // AmpEna
        0x00000800, // InPos
        0x00000400, // Reserved (Future Use)
        0x00000200, // BlockRequest
        0x00000100, // PhaseFound
        0x00000080, // TriggerSpeedSel
        0x00000040, // GantryHomed
        0x00000020, // SpindleMotor (bit 1)
        0x00000010, // SpindleMotor (bit 0)
        0x0000000F  // Reserved (Future Use) -- Bits 0-3
    };




        readonly string[] FieldNames = {
        "TriggerMove          ",
        "HomeInProgress      ",
        "MinusLimit          ",
        "PlusLimit           ",
        "FeWarn               ",
        "FeFatal              ",
        "LimitStop            ",
        "AmpFault             ",
        "SoftMinusLimit      ",
        "SoftPlusLimit       ",
        "I2tFault             ",
        "TriggerNotFound      ",
        "AmpWarn              ",
        "EncLoss              ",
        "AuxFault             ",
        "Reserved            ",
        "HomeComplete        ",
        "DesVelZero           ",
        "ClosedLoop           ",
        "AmpEna               ",
        "InPos                ",
        "Reserved            ",
        "BlockRequest        ",
        "PhaseFound           ",
        "TriggerSpeedSel      ",
        "GantryHomed          ",
        "SpindleMotor (bit 1)",
        "SpindleMotor (bit 0)",
        "Reserved            "
    };

        readonly string[] FieldRemarks = {
            "触发搜索运动正在进行  ",
            "回零运动正在进行     ",
            "硬件负限位已设置     ",
            "硬件正限位已设置     ",
            "跟随误差警告         ",
            "严重的跟随误差错误    ",
            "因硬件位置限制而停止  ",
            "放大器故障错误       ",
            "软件负限位已设置     ",
            "软件正限位已设置     ",
            "集成电流I2T故障      ",
            "未找到触发器         ",
            "放大器警告           ",
            "编码器传感器丢失错误  ",
            "辅助故障错误         ",
            "备用",
            "回零完成            ",
            "期望速度为零         ",
            "闭环模式已启用       ",
            "放大器已启用         ",
            "位置已到达目标       ",
            "备用",
            "块请求标志已设置     ",
            "相位参考已建立       ",
            "触发器运动速度选择    ",
            "龙门回零完成         ",
            "主轴轴定义状态（位 1）",
            "主轴轴定义状态（位 0）",
            "备用"
        };
        readonly uint[] Masks1 = {
        0x80000000, // Csolve
        0x40000000, // SoftLimit
        0x20000000, // DacLimit
        0x10000000, // BlDir
        0x08000000, // SoftLimitDir
    };

        readonly string[] FieldNames1 = {
        "Csolve",
        "SoftLimit",
        "DacLimit",
        "BlDir",
        "SoftLimitDir",
    };

        readonly string[] FieldRemarks2 = {
            "电机用于 PMATCH 计算",
            "伺服输出受限",
            "反向间隙方向",
            "软件限制引起的运动修改方向（0=正向，1=负向）",
            "保留字段"
        };

        public bool TriggerMove { get; set; }
        public bool HomeInProgress { get; set; }
        public bool MinusLimit { get; set; }
        public bool PlusLimit { get; set; }
        public bool FeWarn { get; set; }
        public bool FeFatal { get; set; }
        public bool LimitStop { get; set; }
        public bool AmpFault { get; set; }
        public bool SoftMinusLimit { get; set; }
        public bool SoftPlusLimit { get; set; }
        public bool I2tFault { get; set; }
        public bool TriggerNotFound { get; set; }
        public bool AmpWarn { get; set; }
        public bool EncLoss { get; set; }
        public bool AuxFault { get; set; }
        public bool Reserved1 { get; set; }
        public bool HomeComplete { get; set; }
        public bool DesVelZero { get; set; }
        public bool ClosedLoop { get; set; }
        public bool AmpEna { get; set; }
        public bool InPos { get; set; }
        public bool Reserved2 { get; set; }
        public bool BlockRequest { get; set; }
        public bool PhaseFound { get; set; }
        public bool TriggerSpeedSel { get; set; }
        public bool GantryHomed { get; set; }
        public bool SpindleMotorBit1 { get; set; }
        public bool SpindleMotorBit0 { get; set; }
        public bool Reserved3 { get; set; }


        //低8位
        public bool Csolve { get; set; }
        public bool SoftLimit { get; set; }
        public bool DacLimit { get; set; }
        public bool BlDir { get; set; }
        public bool SoftLimitDir { get; set; }





        public List<UnitStatus> ToList()
        {
            List<UnitStatus> list = new List<UnitStatus>();
            list.Add(new UnitStatus(FieldNames[0], TriggerMove, FieldRemarks[0]));
            list.Add(new UnitStatus(FieldNames[1], HomeInProgress, FieldRemarks[1]));
            list.Add(new UnitStatus(FieldNames[2], MinusLimit, FieldRemarks[2]));
            list.Add(new UnitStatus(FieldNames[3], PlusLimit, FieldRemarks[3]));
            list.Add(new UnitStatus(FieldNames[4], FeWarn, FieldRemarks[4]));
            list.Add(new UnitStatus(FieldNames[5], FeFatal, FieldRemarks[5]));
            list.Add(new UnitStatus(FieldNames[6], LimitStop, FieldRemarks[6]));
            list.Add(new UnitStatus(FieldNames[7], AmpFault, FieldRemarks[7]));
            list.Add(new UnitStatus(FieldNames[8], SoftMinusLimit, FieldRemarks[8]));
            list.Add(new UnitStatus(FieldNames[9], SoftPlusLimit, FieldRemarks[9]));
            list.Add(new UnitStatus(FieldNames[10], I2tFault, FieldRemarks[10]));
            list.Add(new UnitStatus(FieldNames[11], TriggerNotFound, FieldRemarks[11]));
            list.Add(new UnitStatus(FieldNames[12], AmpWarn, FieldRemarks[12]));
            list.Add(new UnitStatus(FieldNames[13], EncLoss, FieldRemarks[13]));
            list.Add(new UnitStatus(FieldNames[14], AuxFault, FieldRemarks[14]));
            list.Add(new UnitStatus(FieldNames[15], Reserved1, FieldRemarks[15]));
            list.Add(new UnitStatus(FieldNames[16], HomeComplete, FieldRemarks[16]));
            list.Add(new UnitStatus(FieldNames[17], DesVelZero, FieldRemarks[17]));
            list.Add(new UnitStatus(FieldNames[18], ClosedLoop, FieldRemarks[18]));
            list.Add(new UnitStatus(FieldNames[19], AmpEna, FieldRemarks[19]));
            list.Add(new UnitStatus(FieldNames[20], InPos, FieldRemarks[20]));
            list.Add(new UnitStatus(FieldNames[21], Reserved2, FieldRemarks[21]));
            list.Add(new UnitStatus(FieldNames[22], BlockRequest, FieldRemarks[22]));
            list.Add(new UnitStatus(FieldNames[23], PhaseFound, FieldRemarks[23]));
            list.Add(new UnitStatus(FieldNames[24], TriggerSpeedSel, FieldRemarks[24]));
            list.Add(new UnitStatus(FieldNames[25], GantryHomed, FieldRemarks[25]));
            list.Add(new UnitStatus(FieldNames[26], SpindleMotorBit1, FieldRemarks[26]));
            list.Add(new UnitStatus(FieldNames[27], SpindleMotorBit0, FieldRemarks[27]));
            list.Add(new UnitStatus(FieldNames[28], Reserved3, FieldRemarks[28]));

            list.Add(new UnitStatus(FieldNames1[0], Csolve, FieldRemarks2[0]));
            list.Add(new UnitStatus(FieldNames1[1], SoftLimit, FieldRemarks2[1]));
            list.Add(new UnitStatus(FieldNames1[2], DacLimit, FieldRemarks2[2]));
            list.Add(new UnitStatus(FieldNames1[3], BlDir, FieldRemarks2[3]));
            list.Add(new UnitStatus(FieldNames1[4], SoftLimitDir, FieldRemarks2[4]));
            return list;
        }

    }
}
