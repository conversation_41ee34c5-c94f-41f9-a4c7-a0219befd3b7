using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace McLaser.Core.Plugins
{
    /// <summary>
    /// 插件接口
    /// 定义了插件的基本规范
    /// </summary>
    public interface IPlugin : IDisposable
    {
        /// <summary>
        /// 插件唯一标识符
        /// </summary>
        string Id { get; }

        /// <summary>
        /// 插件名称
        /// </summary>
        string Name { get; }

        /// <summary>
        /// 插件版本
        /// </summary>
        Version Version { get; }

        /// <summary>
        /// 插件描述
        /// </summary>
        string Description { get; }

        /// <summary>
        /// 插件作者
        /// </summary>
        string Author { get; }

        /// <summary>
        /// 插件状态
        /// </summary>
        PluginStatus Status { get; }

        /// <summary>
        /// 插件元数据
        /// </summary>
        PluginMetadata Metadata { get; }

        /// <summary>
        /// 插件依赖项
        /// </summary>
        IList<PluginDependency> Dependencies { get; }

        /// <summary>
        /// 插件配置
        /// </summary>
        Dictionary<string, object> Configuration { get; set; }

        /// <summary>
        /// 插件状态变更事件
        /// </summary>
        event EventHandler<PluginStatusChangedEventArgs> StatusChanged;

        /// <summary>
        /// 插件错误事件
        /// </summary>
        event EventHandler<PluginErrorEventArgs> ErrorOccurred;

        /// <summary>
        /// 初始化插件
        /// </summary>
        /// <param name="context">插件上下文</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>初始化任务</returns>
        Task InitializeAsync(IPluginContext context, CancellationToken cancellationToken = default);

        /// <summary>
        /// 启动插件
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>启动任务</returns>
        Task StartAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 停止插件
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>停止任务</returns>
        Task StopAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 暂停插件
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>暂停任务</returns>
        Task PauseAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 恢复插件
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>恢复任务</returns>
        Task ResumeAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 重启插件
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>重启任务</returns>
        Task RestartAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 验证插件
        /// </summary>
        /// <returns>验证结果</returns>
        PluginValidationResult Validate();

        /// <summary>
        /// 获取插件信息
        /// </summary>
        /// <returns>插件信息</returns>
        PluginInfo GetInfo();

        /// <summary>
        /// 更新配置
        /// </summary>
        /// <param name="configuration">新配置</param>
        /// <returns>是否成功更新</returns>
        bool UpdateConfiguration(Dictionary<string, object> configuration);

        /// <summary>
        /// 获取配置值
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        T GetConfigurationValue<T>(string key, T defaultValue = default!);

        /// <summary>
        /// 设置配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        void SetConfigurationValue(string key, object value);
    }

    /// <summary>
    /// 可配置插件接口
    /// </summary>
    public interface IConfigurablePlugin : IPlugin
    {
        /// <summary>
        /// 配置架构
        /// </summary>
        PluginConfigurationSchema ConfigurationSchema { get; }

        /// <summary>
        /// 验证配置
        /// </summary>
        /// <param name="configuration">配置</param>
        /// <returns>验证结果</returns>
        ConfigurationValidationResult ValidateConfiguration(Dictionary<string, object> configuration);

        /// <summary>
        /// 获取默认配置
        /// </summary>
        /// <returns>默认配置</returns>
        Dictionary<string, object> GetDefaultConfiguration();

        /// <summary>
        /// 配置变更事件
        /// </summary>
        event EventHandler<PluginConfigurationChangedEventArgs> ConfigurationChanged;
    }

    /// <summary>
    /// 可热更新插件接口
    /// </summary>
    public interface IHotSwappablePlugin : IPlugin
    {
        /// <summary>
        /// 是否支持热更新
        /// </summary>
        bool SupportsHotSwap { get; }

        /// <summary>
        /// 准备热更新
        /// </summary>
        /// <param name="newVersion">新版本</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>准备任务</returns>
        Task PrepareHotSwapAsync(Version newVersion, CancellationToken cancellationToken = default);

        /// <summary>
        /// 执行热更新
        /// </summary>
        /// <param name="newPluginPath">新插件路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>更新任务</returns>
        Task<bool> HotSwapAsync(string newPluginPath, CancellationToken cancellationToken = default);

        /// <summary>
        /// 回滚热更新
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>回滚任务</returns>
        Task RollbackHotSwapAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 热更新事件
        /// </summary>
        event EventHandler<PluginHotSwapEventArgs> HotSwapCompleted;
    }

    /// <summary>
    /// 插件上下文接口
    /// </summary>
    public interface IPluginContext
    {
        /// <summary>
        /// 应用程序上下文
        /// </summary>
        object ApplicationContext { get; }

        /// <summary>
        /// 服务提供者
        /// </summary>
        IServiceProvider ServiceProvider { get; }

        /// <summary>
        /// 插件管理器
        /// </summary>
        IPluginManager PluginManager { get; }

        /// <summary>
        /// 插件目录
        /// </summary>
        string PluginDirectory { get; }

        /// <summary>
        /// 数据目录
        /// </summary>
        string DataDirectory { get; }

        /// <summary>
        /// 配置目录
        /// </summary>
        string ConfigurationDirectory { get; }

        /// <summary>
        /// 日志记录器
        /// </summary>
        object Logger { get; }

        /// <summary>
        /// 获取服务
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <returns>服务实例</returns>
        T GetService<T>() where T : class;

        /// <summary>
        /// 获取服务
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <returns>服务实例</returns>
        object? GetService(Type serviceType);

        /// <summary>
        /// 获取其他插件
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <returns>插件实例</returns>
        IPlugin? GetPlugin(string pluginId);

        /// <summary>
        /// 获取其他插件
        /// </summary>
        /// <typeparam name="T">插件类型</typeparam>
        /// <returns>插件实例</returns>
        T? GetPlugin<T>() where T : class, IPlugin;

        /// <summary>
        /// 发布事件
        /// </summary>
        /// <param name="eventData">事件数据</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>发布任务</returns>
        Task PublishEventAsync(object eventData, CancellationToken cancellationToken = default);

        /// <summary>
        /// 订阅事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        void SubscribeEvent<T>(Func<T, Task> handler);

        /// <summary>
        /// 取消订阅事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        void UnsubscribeEvent<T>(Func<T, Task> handler);
    }

    /// <summary>
    /// 插件工厂接口
    /// </summary>
    public interface IPluginFactory
    {
        /// <summary>
        /// 创建插件实例
        /// </summary>
        /// <param name="pluginType">插件类型</param>
        /// <param name="metadata">插件元数据</param>
        /// <returns>插件实例</returns>
        IPlugin CreatePlugin(Type pluginType, PluginMetadata metadata);

        /// <summary>
        /// 创建插件实例
        /// </summary>
        /// <typeparam name="T">插件类型</typeparam>
        /// <param name="metadata">插件元数据</param>
        /// <returns>插件实例</returns>
        T CreatePlugin<T>(PluginMetadata metadata) where T : class, IPlugin;

        /// <summary>
        /// 检查类型是否为有效插件
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>是否为有效插件</returns>
        bool IsValidPluginType(Type type);

        /// <summary>
        /// 获取插件元数据
        /// </summary>
        /// <param name="type">插件类型</param>
        /// <returns>插件元数据</returns>
        PluginMetadata? GetPluginMetadata(Type type);
    }
}
