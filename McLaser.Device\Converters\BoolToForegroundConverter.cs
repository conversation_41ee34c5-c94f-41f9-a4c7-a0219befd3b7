using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace McLaser.Devices
{
    /// <summary>
    /// 布尔值转前景色转换器
    /// </summary>
    [ValueConversion(typeof(bool), typeof(Brush))]
    public class BoolToForegroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool isTrue = false;
            
            // 处理null值
            if (value == null)
                return new SolidColorBrush(Colors.Gray);
                
            // 尝试获取布尔值
            if (value is bool boolValue)
                isTrue = boolValue;
                
            // 根据布尔值选择颜色
            return new SolidColorBrush(isTrue ? Colors.Green : Colors.Gray);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 