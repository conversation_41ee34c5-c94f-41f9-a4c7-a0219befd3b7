using System;

namespace McLaser.Plugins.Samples
{
    /// <summary>
    /// 插件元数据特性
    /// 用于标记插件的基本信息
    /// </summary>
    [AttributeUsage(AttributeTargets.Class, AllowMultiple = false, Inherited = false)]
    public class PluginMetadataAttribute : Attribute
    {
        #region 属性

        /// <summary>
        /// 插件唯一标识符
        /// </summary>
        public string Id { get; set; } = "";

        /// <summary>
        /// 插件名称
        /// </summary>
        public string Name { get; set; } = "";

        /// <summary>
        /// 插件版本
        /// </summary>
        public string Version { get; set; } = "1.0.0";

        /// <summary>
        /// 插件描述
        /// </summary>
        public string Description { get; set; } = "";

        /// <summary>
        /// 插件作者
        /// </summary>
        public string Author { get; set; } = "";

        /// <summary>
        /// 插件类别
        /// </summary>
        public string Category { get; set; } = "General";

        /// <summary>
        /// 支持的平台
        /// </summary>
        public string[] SupportedPlatforms { get; set; } = new[] { "Windows" };

        /// <summary>
        /// 最低框架版本要求
        /// </summary>
        public string MinFrameworkVersion { get; set; } = "4.7.2";

        /// <summary>
        /// 插件网站
        /// </summary>
        public string Website { get; set; } = "";

        /// <summary>
        /// 许可证
        /// </summary>
        public string License { get; set; } = "";

        /// <summary>
        /// 标签
        /// </summary>
        public string[] Tags { get; set; } = new string[0];

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化插件元数据特性
        /// </summary>
        public PluginMetadataAttribute()
        {
        }

        /// <summary>
        /// 初始化插件元数据特性
        /// </summary>
        /// <param name="id">插件ID</param>
        /// <param name="name">插件名称</param>
        /// <param name="version">插件版本</param>
        public PluginMetadataAttribute(string id, string name, string version = "1.0.0")
        {
            Id = id;
            Name = name;
            Version = version;
        }

        #endregion
    }
}
