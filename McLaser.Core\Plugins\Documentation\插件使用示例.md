# McLaser.Core 插件使用示例

## 1. 插件开发示例

### 1.1 创建设备驱动插件

```csharp
// 1. 实现IPlugin和ILaserDevice接口
[PluginMetadata(
    Id = "McLaser.Plugins.CustomLaser",
    Name = "自定义激光器驱动",
    Version = "1.0.0",
    Description = "自定义品牌激光器控制插件"
)]
public class CustomLaserPlugin : IPlugin, ILaserDevice
{
    // 实现接口方法...
}

// 2. 编译为独立DLL
// 3. 部署到插件目录
// 4. 系统自动发现和加载
```

### 1.2 创建算法插件

```csharp
// 1. 实现IPlugin和IImageProcessingAlgorithm接口
[PluginMetadata(
    Id = "McLaser.Plugins.CustomAlgorithm",
    Name = "自定义图像算法",
    Version = "1.0.0",
    Description = "专用图像处理算法"
)]
public class CustomAlgorithmPlugin : IPlugin, IImageProcessingAlgorithm
{
    public async Task<ImageProcessingResult> ProcessImageAsync(
        Bitmap inputImage, 
        Dictionary<string, object> parameters = null)
    {
        // 实现具体的图像处理算法
        // ...
        
        return new ImageProcessingResult
        {
            Success = true,
            ProcessedImage = processedImage,
            ProcessingTime = processingTime
        };
    }
}
```

## 2. 插件管理示例

### 2.1 加载和管理插件

```csharp
// 创建插件管理器
var pluginManager = new PluginManager();

// 初始化插件管理器
await pluginManager.InitializeAsync();

// 扫描并加载插件
var loadResults = await pluginManager.LoadPluginsFromDirectoryAsync("Plugins");

// 获取所有已加载的插件
var loadedPlugins = pluginManager.GetLoadedPlugins();

// 启动特定插件
var plugin = pluginManager.GetPlugin("McLaser.Plugins.IPGLaser");
if (plugin != null)
{
    await plugin.StartAsync();
}

// 停止插件
await plugin.StopAsync();

// 卸载插件
await pluginManager.UnloadPluginAsync("McLaser.Plugins.IPGLaser");
```

### 2.2 插件事件处理

```csharp
// 订阅插件状态变更事件
pluginManager.PluginStatusChanged += (sender, e) =>
{
    Console.WriteLine($"插件 {e.PluginId} 状态从 {e.OldStatus} 变更为 {e.NewStatus}");
};

// 订阅插件错误事件
pluginManager.PluginError += (sender, e) =>
{
    Console.WriteLine($"插件 {e.PluginId} 发生错误: {e.ErrorMessage}");
    // 记录错误日志或执行恢复操作
};

// 订阅插件加载事件
pluginManager.PluginLoaded += (sender, e) =>
{
    Console.WriteLine($"插件 {e.PluginId} 已加载");
    // 执行插件加载后的初始化操作
};
```

## 3. 设备插件使用示例

### 3.1 激光器控制

```csharp
// 获取激光器插件
var laserPlugin = pluginManager.GetPlugin<ILaserDevice>("McLaser.Plugins.IPGLaser");

if (laserPlugin != null)
{
    // 连接激光器
    bool connected = await laserPlugin.ConnectAsync();
    if (connected)
    {
        // 启用激光器
        await laserPlugin.EnableAsync();
        
        // 设置功率
        await laserPlugin.SetPowerAsync(500); // 设置为500W
        
        // 监控状态变化
        laserPlugin.LaserStatusChanged += (sender, e) =>
        {
            Console.WriteLine($"激光器状态: {e.OldStatus} -> {e.NewStatus}");
        };
        
        // 监控功率变化
        laserPlugin.PowerChanged += (sender, e) =>
        {
            Console.WriteLine($"激光器功率: {e.OldPower}W -> {e.NewPower}W");
        };
        
        // 获取设备信息
        var deviceInfo = await laserPlugin.GetDeviceInfoAsync();
        Console.WriteLine($"设备: {deviceInfo.DeviceName}, 序列号: {deviceInfo.SerialNumber}");
    }
}
```

### 3.2 多设备协调

```csharp
// 获取多个设备插件
var laser = pluginManager.GetPlugin<ILaserDevice>("McLaser.Plugins.IPGLaser");
var camera = pluginManager.GetPlugin<ICameraDevice>("McLaser.Plugins.HikCamera");
var motion = pluginManager.GetPlugin<IMotionDevice>("McLaser.Plugins.PMACMotion");

// 协调多设备操作
async Task PerformLaserProcessing()
{
    try
    {
        // 1. 移动到指定位置
        await motion.MoveToPositionAsync(100, 200);
        
        // 2. 拍照检测
        var image = await camera.CaptureImageAsync();
        
        // 3. 启动激光加工
        await laser.EnableAsync();
        await laser.SetPowerAsync(800);
        
        // 4. 执行加工路径
        await motion.ExecutePathAsync(processingPath);
        
        // 5. 关闭激光
        await laser.DisableAsync();
        
        // 6. 质量检测
        var finalImage = await camera.CaptureImageAsync();
    }
    catch (Exception ex)
    {
        // 错误处理和设备安全停止
        await laser.DisableAsync();
        await motion.StopAsync();
    }
}
```

## 4. 算法插件使用示例

### 4.1 图像处理

```csharp
// 获取边缘检测算法插件
var edgeDetection = pluginManager.GetPlugin<IImageProcessingAlgorithm>("McLaser.Plugins.EdgeDetection");

if (edgeDetection != null)
{
    // 设置算法参数
    var parameters = new Dictionary<string, object>
    {
        ["DetectorType"] = "Canny",
        ["Threshold1"] = 50.0,
        ["Threshold2"] = 150.0,
        ["KernelSize"] = 3
    };
    
    // 验证参数
    bool isValid = await edgeDetection.ValidateParametersAsync(parameters);
    if (isValid)
    {
        // 处理图像
        var result = await edgeDetection.ProcessImageAsync(inputImage, parameters);
        
        if (result.Success)
        {
            // 使用处理结果
            var edgeImage = result.ProcessedImage;
            var processingTime = result.ProcessingTime;
            var edgeCount = (int)result.Metadata["EdgeCount"];
            
            Console.WriteLine($"边缘检测完成，耗时: {processingTime.TotalMilliseconds}ms");
            Console.WriteLine($"检测到 {edgeCount} 个边缘点");
        }
    }
}
```

### 4.2 算法链式处理

```csharp
// 创建算法处理链
async Task<Bitmap> ProcessImageChain(Bitmap inputImage)
{
    var currentImage = inputImage;
    
    // 1. 噪声去除
    var denoiseAlgorithm = pluginManager.GetPlugin<IImageProcessingAlgorithm>("McLaser.Plugins.Denoise");
    var denoiseResult = await denoiseAlgorithm.ProcessImageAsync(currentImage);
    currentImage = denoiseResult.ProcessedImage;
    
    // 2. 边缘检测
    var edgeAlgorithm = pluginManager.GetPlugin<IImageProcessingAlgorithm>("McLaser.Plugins.EdgeDetection");
    var edgeResult = await edgeAlgorithm.ProcessImageAsync(currentImage);
    currentImage = edgeResult.ProcessedImage;
    
    // 3. 特征提取
    var featureAlgorithm = pluginManager.GetPlugin<IImageProcessingAlgorithm>("McLaser.Plugins.FeatureExtraction");
    var featureResult = await featureAlgorithm.ProcessImageAsync(currentImage);
    
    return featureResult.ProcessedImage;
}
```

## 5. UI插件使用示例

### 5.1 工具栏扩展

```csharp
// 获取UI主机
var uiHost = serviceProvider.GetService<IUIHost>();

// 获取工具栏插件
var toolbarPlugin = pluginManager.GetPlugin<IUIExtension>("McLaser.Plugins.CustomToolbar");

if (toolbarPlugin != null)
{
    // 附加到主界面
    await toolbarPlugin.AttachToHostAsync(uiHost);
    
    // 显示工具栏
    toolbarPlugin.ShowExtension();
    
    // 订阅UI事件
    toolbarPlugin.UIExtensionEvent += (sender, e) =>
    {
        switch (e.EventType)
        {
            case "QuickStart":
                // 处理快速启动事件
                HandleQuickStart();
                break;
            case "EmergencyStop":
                // 处理紧急停止事件
                HandleEmergencyStop();
                break;
        }
    };
}
```

### 5.2 动态界面组合

```csharp
// 根据用户角色动态加载UI插件
async Task LoadUserInterface(UserRole role)
{
    var uiHost = serviceProvider.GetService<IUIHost>();
    
    switch (role)
    {
        case UserRole.Operator:
            // 操作员界面：简化工具栏
            var operatorToolbar = pluginManager.GetPlugin<IUIExtension>("McLaser.Plugins.OperatorToolbar");
            await operatorToolbar.AttachToHostAsync(uiHost);
            break;
            
        case UserRole.Engineer:
            // 工程师界面：完整工具栏 + 调试面板
            var engineerToolbar = pluginManager.GetPlugin<IUIExtension>("McLaser.Plugins.EngineerToolbar");
            var debugPanel = pluginManager.GetPlugin<IUIExtension>("McLaser.Plugins.DebugPanel");
            
            await engineerToolbar.AttachToHostAsync(uiHost);
            await debugPanel.AttachToHostAsync(uiHost);
            break;
            
        case UserRole.Administrator:
            // 管理员界面：所有功能
            var adminToolbar = pluginManager.GetPlugin<IUIExtension>("McLaser.Plugins.AdminToolbar");
            var configPanel = pluginManager.GetPlugin<IUIExtension>("McLaser.Plugins.ConfigPanel");
            var monitorPanel = pluginManager.GetPlugin<IUIExtension>("McLaser.Plugins.MonitorPanel");
            
            await adminToolbar.AttachToHostAsync(uiHost);
            await configPanel.AttachToHostAsync(uiHost);
            await monitorPanel.AttachToHostAsync(uiHost);
            break;
    }
}
```

## 6. 数据处理插件使用示例

### 6.1 生产数据分析

```csharp
// 获取数据分析插件
var dataAnalyzer = pluginManager.GetPlugin<IDataProcessor>("McLaser.Plugins.ProductionDataAnalyzer");

if (dataAnalyzer != null)
{
    // 准备分析数据
    var productionData = GetProductionDataSet();
    
    // 设置分析参数
    var parameters = new Dictionary<string, object>
    {
        ["AnalyzerTypes"] = new[] { "Production", "Quality", "Efficiency" },
        ["ReportFormats"] = new[] { "Excel", "PDF" },
        ["GenerateReports"] = true,
        ["GroupBy"] = "Day"
    };
    
    // 执行数据分析
    var result = await dataAnalyzer.ProcessDataAsync(productionData, parameters);
    
    if (result.Success)
    {
        // 获取分析结果
        var analysisResults = result.Metadata["AnalysisResults"];
        var reports = result.Metadata["Reports"] as Dictionary<string, byte[]>;
        
        // 保存报表文件
        foreach (var report in reports)
        {
            var fileName = $"ProductionReport_{DateTime.Now:yyyyMMdd}.{report.Key.ToLower()}";
            File.WriteAllBytes(fileName, report.Value);
        }
        
        Console.WriteLine($"数据分析完成，耗时: {result.ProcessingTime.TotalSeconds}秒");
    }
}
```

### 6.2 实时数据监控

```csharp
// 实时数据处理示例
async Task StartRealTimeDataProcessing()
{
    var dataProcessor = pluginManager.GetPlugin<IDataProcessor>("McLaser.Plugins.RealTimeAnalyzer");
    
    // 创建数据流处理管道
    var dataStream = new DataStreamProcessor();
    
    dataStream.DataReceived += async (sender, data) =>
    {
        try
        {
            // 实时处理数据
            var result = await dataProcessor.ProcessDataAsync(data);
            
            if (result.Success)
            {
                // 更新实时显示
                UpdateRealTimeDisplay(result);
                
                // 检查异常情况
                CheckForAnomalies(result);
            }
        }
        catch (Exception ex)
        {
            // 记录处理错误
            LogError($"实时数据处理失败: {ex.Message}");
        }
    };
    
    // 启动数据流
    await dataStream.StartAsync();
}
```

## 7. 插件配置管理示例

### 7.1 插件配置

```csharp
// 配置插件参数
var pluginConfig = new PluginConfiguration
{
    PluginId = "McLaser.Plugins.IPGLaser",
    Settings = new Dictionary<string, object>
    {
        ["ConnectionTimeout"] = 5000,
        ["MaxPower"] = 1000,
        ["SafetyMode"] = true,
        ["LogLevel"] = "Info"
    }
};

// 应用配置
await pluginManager.ConfigurePluginAsync(pluginConfig);

// 保存配置
await pluginManager.SaveConfigurationAsync();
```

### 7.2 插件依赖管理

```csharp
// 检查插件依赖
var dependencyResult = await pluginManager.ResolveDependenciesAsync();

if (!dependencyResult.IsResolved)
{
    foreach (var missingDep in dependencyResult.MissingDependencies)
    {
        Console.WriteLine($"缺少依赖: {missingDep}");
    }
    
    foreach (var circularDep in dependencyResult.CircularDependencies)
    {
        Console.WriteLine($"循环依赖: {circularDep}");
    }
}
```

这些示例展示了McLaser.Core插件系统的强大功能和灵活性，开发者可以根据具体需求选择合适的插件类型和使用方式。
