using System;
using System.ComponentModel;
using McLaser.Device;

namespace McLaser.Devices.Sensor.Displacement
{
    /// <summary>
    /// 位移传感器状态类
    /// 用于记录和监控位移传感器的运行状态信息
    /// </summary>
    [Serializable]
    public class StatusDisplacement : StatusSensor
    {
        #region 私有字段

        private double _displacementAlarmHigh = 50.0;
        private double _displacementAlarmLow = 0.0;
        private bool _isOutOfRange = false;
        private bool _isTargetLost = false;
        private double _signalStrength = 0;
        private int _laserPowerLevel = 50;
        private DisplacementMode _measurementMode = DisplacementMode.Distance;
        private string _sensorModel = "未知";
        private string _serialNumber = "未知";
        private double _velocity = 0;
        private DateTime _lastPositionTime = DateTime.MinValue;
        private double _lastPosition = 0;

        #endregion

        #region 属性

        /// <summary>
        /// 高位移报警阈值(mm)
        /// </summary>
        [Category("位移报警"), DisplayName("高位移报警阈值(mm)")]
        public double DisplacementAlarmHigh
        {
            get => _displacementAlarmHigh;
            set
            {
                if (Math.Abs(_displacementAlarmHigh - value) > 0.001)
                {
                    _displacementAlarmHigh = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 低位移报警阈值(mm)
        /// </summary>
        [Category("位移报警"), DisplayName("低位移报警阈值(mm)")]
        public double DisplacementAlarmLow
        {
            get => _displacementAlarmLow;
            set
            {
                if (Math.Abs(_displacementAlarmLow - value) > 0.001)
                {
                    _displacementAlarmLow = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 是否超出测量范围
        /// </summary>
        [Category("位移状态"), DisplayName("超出测量范围")]
        public bool IsOutOfRange
        {
            get => _isOutOfRange;
            set
            {
                if (_isOutOfRange != value)
                {
                    _isOutOfRange = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// 是否丢失目标
        /// </summary>
        [Category("位移状态"), DisplayName("目标丢失")]
        public bool IsTargetLost
        {
            get => _isTargetLost;
            set
            {
                if (_isTargetLost != value)
                {
                    _isTargetLost = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// 信号强度(0-100%)
        /// </summary>
        [Category("位移状态"), DisplayName("信号强度(%)")]
        public double SignalStrength
        {
            get => _signalStrength;
            set
            {
                if (Math.Abs(_signalStrength - value) > 0.1)
                {
                    _signalStrength = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 激光功率等级(0-100%)
        /// </summary>
        [Category("位移状态"), DisplayName("激光功率等级(%)")]
        public int LaserPowerLevel
        {
            get => _laserPowerLevel;
            set
            {
                if (_laserPowerLevel != value)
                {
                    _laserPowerLevel = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 测量模式
        /// </summary>
        [Category("位移状态"), DisplayName("测量模式")]
        public DisplacementMode MeasurementMode
        {
            get => _measurementMode;
            set
            {
                if (_measurementMode != value)
                {
                    _measurementMode = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 传感器型号
        /// </summary>
        [Category("位移传感器信息"), DisplayName("传感器型号")]
        public string SensorModel
        {
            get => _sensorModel;
            set
            {
                if (_sensorModel != value)
                {
                    _sensorModel = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 序列号
        /// </summary>
        [Category("位移传感器信息"), DisplayName("序列号")]
        public string SerialNumber
        {
            get => _serialNumber;
            set
            {
                if (_serialNumber != value)
                {
                    _serialNumber = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 速度(mm/s)
        /// </summary>
        [Category("位移状态"), DisplayName("速度(mm/s)")]
        public double Velocity
        {
            get => _velocity;
            set
            {
                if (Math.Abs(_velocity - value) > 0.001)
                {
                    _velocity = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 状态文本描述
        /// </summary>
        [Category("位移状态"), DisplayName("状态描述")]
        public override string StatusText
        {
            get
            {
                if (!string.IsNullOrEmpty(ErrorMessage))
                    return $"错误: {ErrorMessage}";

                if (!IsConnected)
                    return "设备未连接";

                if (_isTargetLost)
                    return "目标丢失";

                if (_isOutOfRange)
                    return $"超出测量范围 - 位移: {CurrentValue:F3}mm";

                if (CurrentValue > _displacementAlarmHigh)
                    return $"高位移报警 - 位移: {CurrentValue:F3}mm (>{_displacementAlarmHigh:F3}mm)";

                if (CurrentValue < _displacementAlarmLow)
                    return $"低位移报警 - 位移: {CurrentValue:F3}mm (<{_displacementAlarmLow:F3}mm)";

                if (!IsCalibrated)
                    return "设备未校准";

                if (IsSampling)
                    return $"正在采样 - 位移: {CurrentValue:F3}mm - 速度: {_velocity:F2}mm/s";

                return $"就绪 - 当前位移: {CurrentValue:F3}mm";
            }
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public StatusDisplacement()
        {
            Reset();
        }

        #endregion

        #region 方法

        /// <summary>
        /// 重置状态
        /// </summary>
        public override void Reset()
        {
            base.Reset();
            
            DisplacementAlarmHigh = 50.0;
            DisplacementAlarmLow = 0.0;
            IsOutOfRange = false;
            IsTargetLost = false;
            SignalStrength = 0;
            LaserPowerLevel = 50;
            MeasurementMode = DisplacementMode.Distance;
            SensorModel = "未知";
            SerialNumber = "未知";
            Velocity = 0;
            _lastPositionTime = DateTime.MinValue;
            _lastPosition = 0;
        }

        /// <summary>
        /// 更新位移值
        /// </summary>
        /// <param name="displacement">位移值</param>
        public new void UpdateValue(double displacement)
        {
            // 计算速度
            if (_lastPositionTime != DateTime.MinValue)
            {
                var timeSpan = DateTime.Now - _lastPositionTime;
                if (timeSpan.TotalSeconds > 0)
                {
                    Velocity = (displacement - _lastPosition) / timeSpan.TotalSeconds;
                }
            }

            _lastPosition = displacement;
            _lastPositionTime = DateTime.Now;

            // 检查位移报警
            CheckDisplacementAlarms(displacement);

            // 调用基类方法
            base.UpdateValue(displacement);
        }

        /// <summary>
        /// 设置位移报警阈值
        /// </summary>
        /// <param name="lowThreshold">低位移阈值</param>
        /// <param name="highThreshold">高位移阈值</param>
        public void SetDisplacementAlarmThresholds(double lowThreshold, double highThreshold)
        {
            if (lowThreshold < highThreshold)
            {
                DisplacementAlarmLow = lowThreshold;
                DisplacementAlarmHigh = highThreshold;
            }
        }

        /// <summary>
        /// 更新传感器信息
        /// </summary>
        /// <param name="model">型号</param>
        /// <param name="serialNumber">序列号</param>
        /// <param name="laserPowerLevel">激光功率等级</param>
        /// <param name="measurementMode">测量模式</param>
        public void UpdateSensorInfo(string model, string serialNumber, int laserPowerLevel, DisplacementMode measurementMode)
        {
            SensorModel = model;
            SerialNumber = serialNumber;
            LaserPowerLevel = laserPowerLevel;
            MeasurementMode = measurementMode;
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 更新信号状态
        /// </summary>
        /// <param name="signalStrength">信号强度</param>
        /// <param name="isTargetLost">是否丢失目标</param>
        /// <param name="isOutOfRange">是否超出范围</param>
        public void UpdateSignalStatus(double signalStrength, bool isTargetLost, bool isOutOfRange)
        {
            SignalStrength = signalStrength;
            IsTargetLost = isTargetLost;
            IsOutOfRange = isOutOfRange;
            
            // 更新传感器状态
            if (isTargetLost || isOutOfRange)
            {
                SensorStatus = SensorStatus.Error;
            }
            else if (SensorStatus == SensorStatus.Error)
            {
                SensorStatus = IsSampling ? SensorStatus.Sampling : SensorStatus.Ready;
            }
            
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 获取状态摘要
        /// </summary>
        /// <returns>状态摘要字符串</returns>
        public override string GetStatusSummary()
        {
            return $"位移传感器 - {StatusText} | 范围: [{MinValue:F3}, {MaxValue:F3}]mm | 平均: {AverageValue:F3}mm | 速度: {Velocity:F2}mm/s | 信号: {SignalStrength:F0}%";
        }

        /// <summary>
        /// 检查是否有位移报警
        /// </summary>
        /// <returns>是否有位移报警</returns>
        public bool HasDisplacementAlarm()
        {
            return CurrentValue > DisplacementAlarmHigh || CurrentValue < DisplacementAlarmLow;
        }

        /// <summary>
        /// 获取位移报警描述
        /// </summary>
        /// <returns>位移报警描述</returns>
        public string GetDisplacementAlarmDescription()
        {
            if (CurrentValue > DisplacementAlarmHigh)
                return $"高位移报警: 当前位移 {CurrentValue:F3}mm 超过上限 {DisplacementAlarmHigh:F3}mm";
            
            if (CurrentValue < DisplacementAlarmLow)
                return $"低位移报警: 当前位移 {CurrentValue:F3}mm 低于下限 {DisplacementAlarmLow:F3}mm";
            
            return "位移正常";
        }

        /// <summary>
        /// 检查信号质量
        /// </summary>
        /// <returns>信号质量描述</returns>
        public string GetSignalQualityDescription()
        {
            if (IsTargetLost)
                return "目标丢失";
            
            if (IsOutOfRange)
                return "超出测量范围";
            
            if (SignalStrength < 30)
                return $"信号弱 ({SignalStrength:F0}%)";
            
            if (SignalStrength < 70)
                return $"信号一般 ({SignalStrength:F0}%)";
            
            return $"信号良好 ({SignalStrength:F0}%)";
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 检查位移报警
        /// </summary>
        /// <param name="displacement">当前位移</param>
        private void CheckDisplacementAlarms(double displacement)
        {
            bool hasAlarm = displacement > DisplacementAlarmHigh || displacement < DisplacementAlarmLow;
            
            if (hasAlarm && SensorStatus != SensorStatus.Warning && SensorStatus != SensorStatus.Error)
            {
                SensorStatus = SensorStatus.Warning;
            }
            else if (!hasAlarm && SensorStatus == SensorStatus.Warning)
            {
                SensorStatus = IsSampling ? SensorStatus.Sampling : SensorStatus.Ready;
            }
        }

        #endregion
    }
}
