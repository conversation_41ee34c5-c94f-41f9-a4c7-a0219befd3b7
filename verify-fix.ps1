# McLaser_V1 Error Fix Verification Script
Write-Host "=== McLaser_V1 Error Fix Verification ===" -ForegroundColor Cyan
Write-Host ""

# 1. Build Verification
Write-Host "1. Build Verification..." -ForegroundColor Yellow
Write-Host "  Building McLaser.Core..." -ForegroundColor Gray
dotnet build McLaser.Core --verbosity quiet
if ($LASTEXITCODE -eq 0) {
    Write-Host "  ✅ McLaser.Core build successful" -ForegroundColor Green
} else {
    Write-Host "  ❌ McLaser.Core build failed" -ForegroundColor Red
}

Write-Host "  Building McLaser.App..." -ForegroundColor Gray
dotnet build McLaser.App --verbosity quiet
if ($LASTEXITCODE -eq 0) {
    Write-Host "  ✅ McLaser.App build successful" -ForegroundColor Green
} else {
    Write-Host "  ❌ McLaser.App build failed" -ForegroundColor Red
}

Write-Host ""

# 2. File Check
Write-Host "2. Key Files Check..." -ForegroundColor Yellow
$files = @(
    "McLaser.App\Core\AppCore.cs",
    "McLaser.App\Core\ConsoleLogger.cs",
    "错误修复总结.md"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "  ✅ $file" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $file" -ForegroundColor Red
    }
}

Write-Host ""

# 3. Key Fix Points Verification
Write-Host "3. Key Fix Points Verification..." -ForegroundColor Yellow

# Check AppCore.cs
if (Test-Path "McLaser.App\Core\AppCore.cs") {
    $content = Get-Content "McLaser.App\Core\AppCore.cs" -Raw
    if ($content -match "RegisterFactory") {
        Write-Host "  ✅ Using factory pattern for service registration" -ForegroundColor Green
    }
    if ($content -match "TryResolve") {
        Write-Host "  ✅ Using safe resolution methods" -ForegroundColor Green
    }
}

# Check ConsoleLogger
if (Test-Path "McLaser.App\Core\ConsoleLogger.cs") {
    Write-Host "  ✅ ConsoleLogger fallback implementation added" -ForegroundColor Green
}

# Check DefaultServiceRegistry
if (Test-Path "McLaser.Core\Framework\Bootstrapper\DefaultServiceRegistry.cs") {
    $content = Get-Content "McLaser.Core\Framework\Bootstrapper\DefaultServiceRegistry.cs" -Raw
    if ($content -match "_resolvingTypes") {
        Write-Host "  ✅ Circular dependency detection added" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "=== Verification Complete ===" -ForegroundColor Cyan
Write-Host "Main fixes applied:" -ForegroundColor Yellow
Write-Host "• Optimized service registration order" -ForegroundColor White
Write-Host "• Added circular dependency detection" -ForegroundColor White
Write-Host "• Implemented fallback logger" -ForegroundColor White
Write-Host "• Used safe resolution methods" -ForegroundColor White
