using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;

namespace McLaser.Core.Framework.Container
{
    /// <summary>
    /// 现代化服务定位器
    /// 替代传统的IoC.cs，提供更好的性能和可维护性
    /// </summary>
    public static class ServiceLocator
    {
        private static IContainer? _container;
        private static readonly object _lockObject = new object();
        private static bool _isInitialized = false;

        // 智能缓存机制
        private static readonly ConcurrentDictionary<Type, object> _singletonCache = new();
        private static readonly ConcurrentDictionary<string, object> _keyedCache = new();
        private static readonly ConcurrentDictionary<Type, bool> _isSingletonCache = new();
        private static volatile bool _cacheEnabled = true;

        /// <summary>
        /// 当前容器实例
        /// </summary>
        public static IContainer Current
        {
            get
            {
                if (_container == null)
                {
                    lock (_lockObject)
                    {
                        if (_container == null)
                        {
                            Initialize();
                        }
                    }
                }
                return _container!;
            }
        }

        /// <summary>
        /// 是否已初始化
        /// </summary>
        public static bool IsInitialized => _isInitialized;

        /// <summary>
        /// 初始化服务定位器
        /// </summary>
        /// <param name="container">可选的容器实例，如果不提供则使用默认容器</param>
        public static void Initialize(IContainer? container = null)
        {
            lock (_lockObject)
            {
                if (_isInitialized)
                {
                    throw new InvalidOperationException("ServiceLocator已经初始化，请先调用Reset()");
                }

                // 使用提供的容器或创建默认容器
                _container = container ?? ContainerManager.Current;

                // 注册所有服务
                ServiceRegistration.RegisterAllServices(_container);

                // 验证注册
                var validationResult = ServiceRegistration.ValidateRegistrations(_container);
                if (!validationResult.IsValid)
                {
                    var errors = string.Join(Environment.NewLine, validationResult.Errors);
                    throw new InvalidOperationException($"服务注册验证失败:{Environment.NewLine}{errors}");
                }

                _isInitialized = true;
            }
        }

        /// <summary>
        /// 重置服务定位器
        /// </summary>
        public static void Reset()
        {
            lock (_lockObject)
            {
                _container?.Dispose();
                _container = null;
                _isInitialized = false;

                // 清理缓存
                ClearCache();
            }
        }

        /// <summary>
        /// 清理所有缓存
        /// </summary>
        public static void ClearCache()
        {
            _singletonCache.Clear();
            _keyedCache.Clear();
            _isSingletonCache.Clear();
        }

        /// <summary>
        /// 启用或禁用缓存
        /// </summary>
        /// <param name="enabled">是否启用缓存</param>
        public static void SetCacheEnabled(bool enabled)
        {
            _cacheEnabled = enabled;
            if (!enabled)
            {
                ClearCache();
            }
        }

        /// <summary>
        /// 获取服务实例
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <returns>服务实例</returns>
        /// <exception cref="InvalidOperationException">服务未注册或解析失败</exception>
        public static T GetService<T>() where T : class
        {
            EnsureInitialized();

            var serviceType = typeof(T);

            // 检查缓存（仅对单例服务）
            if (_cacheEnabled && IsSingleton(serviceType))
            {
                if (_singletonCache.TryGetValue(serviceType, out var cachedService))
                {
                    return (T)cachedService;
                }
            }

            var stopwatch = Stopwatch.StartNew();
            try
            {
                var service = Current.Resolve<T>();
                stopwatch.Stop();

                // 缓存单例服务
                if (_cacheEnabled && IsSingleton(serviceType))
                {
                    _singletonCache.TryAdd(serviceType, service);
                }

                // 记录性能信息（如果需要）
                if (stopwatch.ElapsedMilliseconds > 100) // 超过100ms记录警告
                {
                    Debug.WriteLine($"警告: 服务 {typeof(T).Name} 解析耗时 {stopwatch.ElapsedMilliseconds}ms");
                }

                return service;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                throw new InvalidOperationException($"无法解析服务 {typeof(T).Name}: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 尝试获取服务实例
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <returns>服务实例，如果未注册则返回null</returns>
        public static T? TryGetService<T>() where T : class
        {
            EnsureInitialized();
            
            try
            {
                return Current.TryResolve<T>();
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 获取服务实例（非泛型版本）
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <returns>服务实例</returns>
        /// <exception cref="InvalidOperationException">服务未注册或解析失败</exception>
        public static object GetService(Type serviceType)
        {
            EnsureInitialized();

            // 检查缓存（仅对单例服务）
            if (_cacheEnabled && IsSingleton(serviceType))
            {
                if (_singletonCache.TryGetValue(serviceType, out var cachedService))
                {
                    return cachedService;
                }
            }

            var stopwatch = Stopwatch.StartNew();
            try
            {
                var service = Current.Resolve(serviceType);
                stopwatch.Stop();

                // 缓存单例服务
                if (_cacheEnabled && IsSingleton(serviceType))
                {
                    _singletonCache.TryAdd(serviceType, service);
                }

                // 记录性能信息（如果需要）
                if (stopwatch.ElapsedMilliseconds > 100) // 超过100ms记录警告
                {
                    Debug.WriteLine($"警告: 服务 {serviceType.Name} 解析耗时 {stopwatch.ElapsedMilliseconds}ms");
                }

                return service;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                throw new InvalidOperationException($"无法解析服务 {serviceType.Name}: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 尝试获取服务实例（非泛型版本）
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <returns>服务实例，如果未注册则返回null</returns>
        public static object? TryGetService(Type serviceType)
        {
            EnsureInitialized();
            
            try
            {
                return Current.TryResolve(serviceType);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 获取所有指定类型的服务实例
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <returns>服务实例集合</returns>
        public static IEnumerable<T> GetServices<T>() where T : class
        {
            EnsureInitialized();

            try
            {
                return Current.ResolveAll<T>();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"无法解析服务集合 {typeof(T).Name}: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取所有指定类型的服务实例（非泛型版本）
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <returns>服务实例集合</returns>
        public static IEnumerable<object> GetServices(Type serviceType)
        {
            EnsureInitialized();

            try
            {
                return Current.ResolveAll(serviceType);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"无法解析服务集合 {serviceType.Name}: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取带键的服务实例
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <param name="key">服务键</param>
        /// <returns>服务实例</returns>
        public static T GetKeyedService<T>(string key) where T : class
        {
            EnsureInitialized();

            try
            {
                return Current.ResolveKeyed<T>(key);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"无法解析带键服务 {typeof(T).Name}[{key}]: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取带键的服务实例（非泛型版本）
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <param name="key">服务键</param>
        /// <returns>服务实例</returns>
        public static object GetKeyedService(Type serviceType, string key)
        {
            EnsureInitialized();

            try
            {
                // 使用反射调用泛型方法
                var method = typeof(IContainer).GetMethod("ResolveKeyed");
                var genericMethod = method?.MakeGenericMethod(serviceType);
                var result = genericMethod?.Invoke(Current, new object[] { key });

                if (result == null)
                    throw new InvalidOperationException($"服务 {serviceType.Name}[{key}] 返回了null");

                return result;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"无法解析带键服务 {serviceType.Name}[{key}]: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 检查服务是否已注册
        /// </summary>
        /// <typeparam name="T">服务类型</typeparam>
        /// <returns>是否已注册</returns>
        public static bool IsRegistered<T>() where T : class
        {
            EnsureInitialized();
            return Current.IsRegistered<T>();
        }

        /// <summary>
        /// 检查服务是否已注册（非泛型版本）
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <returns>是否已注册</returns>
        public static bool IsRegistered(Type serviceType)
        {
            EnsureInitialized();
            return Current.IsRegistered(serviceType);
        }

        /// <summary>
        /// 获取容器统计信息
        /// </summary>
        /// <returns>容器统计信息</returns>
        public static IContainerStatistics GetStatistics()
        {
            EnsureInitialized();
            return Current.Statistics;
        }

        /// <summary>
        /// 确保服务定位器已初始化
        /// </summary>
        private static void EnsureInitialized()
        {
            if (!_isInitialized)
            {
                lock (_lockObject)
                {
                    if (!_isInitialized)
                    {
                        Initialize();
                    }
                }
            }
        }

        /// <summary>
        /// 设置自定义容器
        /// </summary>
        /// <param name="container">自定义容器实例</param>
        public static void SetContainer(IContainer container)
        {
            if (container == null)
                throw new ArgumentNullException(nameof(container));

            lock (_lockObject)
            {
                if (_isInitialized)
                {
                    throw new InvalidOperationException("ServiceLocator已经初始化，请先调用Reset()");
                }

                _container = container;
            }
        }

        /// <summary>
        /// 获取所有已注册的服务类型
        /// </summary>
        /// <returns>服务类型集合</returns>
        public static IEnumerable<Type> GetRegisteredTypes()
        {
            EnsureInitialized();
            return Current.GetRegisteredTypes();
        }

        /// <summary>
        /// 检查服务是否为单例
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <returns>是否为单例</returns>
        private static bool IsSingleton(Type serviceType)
        {
            // 使用缓存避免重复检查
            if (_isSingletonCache.TryGetValue(serviceType, out var isSingleton))
            {
                return isSingleton;
            }

            try
            {
                // 检查容器中的服务是否为单例
                // 这里假设如果服务已注册且可以解析，则检查其生命周期
                var result = Current.IsRegistered(serviceType);
                if (result)
                {
                    // 大多数核心服务都是单例，这里可以根据具体容器实现来优化
                    // 暂时假设所有已注册的服务都可以缓存（保守策略）
                    isSingleton = true;
                }
                else
                {
                    isSingleton = false;
                }

                _isSingletonCache.TryAdd(serviceType, isSingleton);
                return isSingleton;
            }
            catch
            {
                // 如果检查失败，默认不缓存
                return false;
            }
        }
    }
}
