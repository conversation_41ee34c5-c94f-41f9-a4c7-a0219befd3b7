using System;
using System.ComponentModel;
using McLaser.Device;

namespace McLaser.Devices.Sensor.Temperature
{
    /// <summary>
    /// 温度传感器状态类
    /// 用于记录和监控温度传感器的运行状态信息
    /// </summary>
    [Serializable]
    public class StatusTemperature : StatusSensor
    {
        #region 私有字段

        private double _temperatureAlarmHigh = 80.0;
        private double _temperatureAlarmLow = -10.0;
        private bool _isOverheating = false;
        private bool _isUndercooling = false;
        private double _temperatureRate = 0;
        private DateTime _lastTemperatureTime = DateTime.MinValue;
        private double _lastTemperature = 0;
        private string _sensorModel = "未知";
        private string _serialNumber = "未知";

        #endregion

        #region 属性

        /// <summary>
        /// 高温报警阈值(℃)
        /// </summary>
        [Category("温度报警"), DisplayName("高温报警阈值(℃)")]
        public double TemperatureAlarmHigh
        {
            get => _temperatureAlarmHigh;
            set
            {
                if (Math.Abs(_temperatureAlarmHigh - value) > 0.1)
                {
                    _temperatureAlarmHigh = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 低温报警阈值(℃)
        /// </summary>
        [Category("温度报警"), DisplayName("低温报警阈值(℃)")]
        public double TemperatureAlarmLow
        {
            get => _temperatureAlarmLow;
            set
            {
                if (Math.Abs(_temperatureAlarmLow - value) > 0.1)
                {
                    _temperatureAlarmLow = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 是否过热
        /// </summary>
        [Category("温度状态"), DisplayName("过热状态")]
        public bool IsOverheating
        {
            get => _isOverheating;
            set
            {
                if (_isOverheating != value)
                {
                    _isOverheating = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// 是否过冷
        /// </summary>
        [Category("温度状态"), DisplayName("过冷状态")]
        public bool IsUndercooling
        {
            get => _isUndercooling;
            set
            {
                if (_isUndercooling != value)
                {
                    _isUndercooling = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// 温度变化率(℃/秒)
        /// </summary>
        [Category("温度状态"), DisplayName("温度变化率(℃/s)")]
        public double TemperatureRate
        {
            get => _temperatureRate;
            set
            {
                if (Math.Abs(_temperatureRate - value) > 0.001)
                {
                    _temperatureRate = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 传感器型号
        /// </summary>
        [Category("温度传感器信息"), DisplayName("传感器型号")]
        public string SensorModel
        {
            get => _sensorModel;
            set
            {
                if (_sensorModel != value)
                {
                    _sensorModel = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 序列号
        /// </summary>
        [Category("温度传感器信息"), DisplayName("序列号")]
        public string SerialNumber
        {
            get => _serialNumber;
            set
            {
                if (_serialNumber != value)
                {
                    _serialNumber = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 状态文本描述
        /// </summary>
        [Category("温度状态"), DisplayName("状态描述")]
        public override string StatusText
        {
            get
            {
                if (!string.IsNullOrEmpty(ErrorMessage))
                    return $"错误: {ErrorMessage}";

                if (!IsConnected)
                    return "设备未连接";

                if (_isOverheating)
                    return $"过热报警 - 温度: {CurrentValue:F1}℃ (>{_temperatureAlarmHigh:F1}℃)";

                if (_isUndercooling)
                    return $"过冷报警 - 温度: {CurrentValue:F1}℃ (<{_temperatureAlarmLow:F1}℃)";

                if (!IsCalibrated)
                    return "设备未校准";

                if (IsSampling)
                    return $"正在采样 - 温度: {CurrentValue:F1}℃ - 变化率: {_temperatureRate:F2}℃/s";

                return $"就绪 - 当前温度: {CurrentValue:F1}℃";
            }
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public StatusTemperature()
        {
            Reset();
        }

        #endregion

        #region 方法

        /// <summary>
        /// 重置状态
        /// </summary>
        public override void Reset()
        {
            base.Reset();
            
            TemperatureAlarmHigh = 80.0;
            TemperatureAlarmLow = -10.0;
            IsOverheating = false;
            IsUndercooling = false;
            TemperatureRate = 0;
            _lastTemperatureTime = DateTime.MinValue;
            _lastTemperature = 0;
            SensorModel = "未知";
            SerialNumber = "未知";
        }

        /// <summary>
        /// 更新温度值
        /// </summary>
        /// <param name="temperature">温度值</param>
        public new void UpdateValue(double temperature)
        {
            // 计算温度变化率
            if (_lastTemperatureTime != DateTime.MinValue)
            {
                var timeSpan = DateTime.Now - _lastTemperatureTime;
                if (timeSpan.TotalSeconds > 0)
                {
                    TemperatureRate = (temperature - _lastTemperature) / timeSpan.TotalSeconds;
                }
            }

            _lastTemperature = temperature;
            _lastTemperatureTime = DateTime.Now;

            // 检查温度报警
            CheckTemperatureAlarms(temperature);

            // 调用基类方法
            base.UpdateValue(temperature);
        }

        /// <summary>
        /// 设置温度报警阈值
        /// </summary>
        /// <param name="lowThreshold">低温阈值</param>
        /// <param name="highThreshold">高温阈值</param>
        public void SetTemperatureAlarmThresholds(double lowThreshold, double highThreshold)
        {
            if (lowThreshold < highThreshold)
            {
                TemperatureAlarmLow = lowThreshold;
                TemperatureAlarmHigh = highThreshold;
            }
        }

        /// <summary>
        /// 更新传感器信息
        /// </summary>
        /// <param name="model">型号</param>
        /// <param name="serialNumber">序列号</param>
        public void UpdateSensorInfo(string model, string serialNumber)
        {
            SensorModel = model;
            SerialNumber = serialNumber;
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 获取状态摘要
        /// </summary>
        /// <returns>状态摘要字符串</returns>
        public override string GetStatusSummary()
        {
            return $"温度传感器 - {StatusText} | 范围: [{MinValue:F1}, {MaxValue:F1}]℃ | 平均: {AverageValue:F1}℃ | 变化率: {TemperatureRate:F2}℃/s";
        }

        /// <summary>
        /// 检查是否有温度报警
        /// </summary>
        /// <returns>是否有温度报警</returns>
        public bool HasTemperatureAlarm()
        {
            return IsOverheating || IsUndercooling;
        }

        /// <summary>
        /// 获取温度报警描述
        /// </summary>
        /// <returns>温度报警描述</returns>
        public string GetTemperatureAlarmDescription()
        {
            if (IsOverheating)
                return $"过热报警: 当前温度 {CurrentValue:F1}℃ 超过上限 {TemperatureAlarmHigh:F1}℃";
            
            if (IsUndercooling)
                return $"过冷报警: 当前温度 {CurrentValue:F1}℃ 低于下限 {TemperatureAlarmLow:F1}℃";
            
            return "温度正常";
        }

        /// <summary>
        /// 清除温度报警
        /// </summary>
        public void ClearTemperatureAlarms()
        {
            IsOverheating = false;
            IsUndercooling = false;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 检查温度报警
        /// </summary>
        /// <param name="temperature">当前温度</param>
        private void CheckTemperatureAlarms(double temperature)
        {
            bool wasOverheating = IsOverheating;
            bool wasUndercooling = IsUndercooling;

            IsOverheating = temperature > TemperatureAlarmHigh;
            IsUndercooling = temperature < TemperatureAlarmLow;

            // 如果报警状态发生变化，更新传感器状态
            if (IsOverheating != wasOverheating || IsUndercooling != wasUndercooling)
            {
                if (IsOverheating || IsUndercooling)
                {
                    SensorStatus = SensorStatus.Warning;
                }
                else if (SensorStatus == SensorStatus.Warning)
                {
                    SensorStatus = IsSampling ? SensorStatus.Sampling : SensorStatus.Ready;
                }
            }
        }

        #endregion
    }
}
