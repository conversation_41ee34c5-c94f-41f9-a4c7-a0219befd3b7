﻿using HalconDotNet;
using System;

namespace McLaser.Modules.RenderView.Base
{
    // 用于展示的HObject
    public class HRoi
    {

        /// <summary>
        /// 画笔颜色
        /// </summary>
        public string DrawColor { get; set; }
        /// <summary>
        /// 展示的HObject
        /// </summary>
        public HObject DispHobject { get; set; }

        /// <summary>
        /// Fill显示
        /// </summary>
        public bool IsFillDisp { get; set; }

        /// <summary>
        /// 是否虚线显示
        /// </summary>
        public bool IsDashLine { get; set; }

        public HRoi() { }

        public HRoi(HObject dispHobject, string drawColor = "red", bool isFillDisp = true, bool isDashLine = false)
        {
            DrawColor = drawColor;
            DispHobject = dispHobject;
            IsFillDisp = isFillDisp;
            IsDashLine = isDashLine;
        }

        ~HRoi()
        {
            Dispose();
        }

        public void Dispose()
        {
            if (DispHobject != null && DispHobject.IsInitialized())
            {
                DispHobject.Dispose();
                DispHobject = null;
            }
            //通知垃圾回收器不再调用终结器
            GC.SuppressFinalize(this);
        }
    }
}
