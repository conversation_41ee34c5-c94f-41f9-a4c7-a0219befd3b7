using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace McLaser.Core.Modules.RecipeManager
{
    [Export(typeof(IRecipeManager))]
    public class RecipeManager : IRecipeManager
    {
        private readonly IRecipeManagerOption _option;
        private IRecipe _current;

        [ImportingConstructor]
        public RecipeManager(IRecipeManagerOption option)
        {
            _option = option;
        }

        public IRecipe Current
        {
            get => _current;
            set
            {
                if (_current != value)
                {
                    var oldValue = _current;
                    _current = value;
                    OnCurrentChanged(new RecipeChangedEventArgs(oldValue, _current));
                }
            }
        }

        public event EventHandler<RecipeChangedEventArgs> CurrentChanged;

        protected virtual void OnCurrentChanged(RecipeChangedEventArgs e)
        {
            CurrentChanged?.Invoke(this, e);
        }

        // 其他方法保持不变
    }

    public class RecipeChangedEventArgs : EventArgs
    {
        public RecipeChangedEventArgs(IRecipe oldRecipe, IRecipe newRecipe)
        {
            OldRecipe = oldRecipe;
            NewRecipe = newRecipe;
        }

        public IRecipe OldRecipe { get; }
        public IRecipe NewRecipe { get; }
    }
}
