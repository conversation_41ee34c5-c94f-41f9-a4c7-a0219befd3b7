{"RootPath": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\McLaser_V1\\McLaser.Core", "ProjectFileName": "McLaser.Core.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "ApplicationBase\\ViewModels\\MainViewModel.cs"}, {"SourceFile": "ApplicationBase\\ViewModels\\NavigationItemViewModel.cs"}, {"SourceFile": "ApplicationBase\\ViewModels\\NavigationViewModel.cs"}, {"SourceFile": "ApplicationBase\\Views\\BottomNavigationBar.xaml.cs"}, {"SourceFile": "ApplicationBase\\Views\\CategoryPopup.xaml.cs"}, {"SourceFile": "ApplicationBase\\Views\\MainWindow.xaml.cs"}, {"SourceFile": "ApplicationBase\\Views\\NavigationButton.xaml.cs"}, {"SourceFile": "Common\\AsyncLock.cs"}, {"SourceFile": "Common\\AsyncRelayCommand.cs"}, {"SourceFile": "Common\\DisposableBase.cs"}, {"SourceFile": "Common\\JsonHelper.cs"}, {"SourceFile": "Common\\NotifyPropertyBase.cs"}, {"SourceFile": "Common\\RelayCommand.cs"}, {"SourceFile": "Common\\RetryHelper.cs"}, {"SourceFile": "Common\\TypeHelper.cs"}, {"SourceFile": "Common\\ViewModelBase.cs"}, {"SourceFile": "Communication\\CommunicationModels.cs"}, {"SourceFile": "Communication\\ICommunicationChannel.cs"}, {"SourceFile": "Communication\\IMessageProtocol.cs"}, {"SourceFile": "Communication\\MessageProtocolBase.cs"}, {"SourceFile": "Communication\\SerialPortChannel.cs"}, {"SourceFile": "Communication\\TcpCommunicationChannel.cs"}, {"SourceFile": "Communication\\UdpCommunicationChannel.cs"}, {"SourceFile": "Configuration\\ConfigurationManager.cs"}, {"SourceFile": "Configuration\\ConfigurationModels.cs"}, {"SourceFile": "Configuration\\IConfigurationManager.cs"}, {"SourceFile": "Configuration\\IConfigurationProvider.cs"}, {"SourceFile": "Configuration\\JsonConfigurationProvider.cs"}, {"SourceFile": "EventBus\\EventBus.cs"}, {"SourceFile": "EventBus\\EventBusModels.cs"}, {"SourceFile": "EventBus\\IEventBus.cs"}, {"SourceFile": "ExceptionHandling\\ExceptionModels.cs"}, {"SourceFile": "ExceptionHandling\\ExceptionPolicy.cs"}, {"SourceFile": "ExceptionHandling\\GlobalExceptionHandler.cs"}, {"SourceFile": "ExceptionHandling\\IExceptionHandler.cs"}, {"SourceFile": "ApplicationBase\\ApplicationBase.cs"}, {"SourceFile": "Framework\\Bootstrapper\\ApplicationInfo.cs"}, {"SourceFile": "Framework\\Bootstrapper\\DefaultServiceRegistry.cs"}, {"SourceFile": "Framework\\Bootstrapper\\IApplicationBuilder.cs"}, {"SourceFile": "Framework\\Bootstrapper\\IApplicationCore.cs"}, {"SourceFile": "Framework\\Bootstrapper\\IConfigureableApplication.cs"}, {"SourceFile": "Framework\\Bootstrapper\\IServiceProvider.cs"}, {"SourceFile": "Framework\\Bootstrapper\\IServiceRegistry.cs"}, {"SourceFile": "Framework\\Caching\\ICacheManager.cs"}, {"SourceFile": "Framework\\Configuration\\DefaultConfigurationService.cs"}, {"SourceFile": "Framework\\Configuration\\IConfigurationService.cs"}, {"SourceFile": "Framework\\Container\\ContainerManager.cs"}, {"SourceFile": "Framework\\Container\\ContainerServiceProviderAdapter.cs"}, {"SourceFile": "Framework\\Container\\ContainerTests.cs"}, {"SourceFile": "Framework\\Container\\DefaultContainerAdapter.cs"}, {"SourceFile": "Framework\\Container\\IContainer.cs"}, {"SourceFile": "Framework\\Container\\IoC.cs"}, {"SourceFile": "Framework\\Container\\MefContainerAdapter.cs"}, {"SourceFile": "Framework\\Container\\ServiceLocator.cs"}, {"SourceFile": "Framework\\Container\\ServiceRegistration.cs"}, {"SourceFile": "Framework\\Data\\IRepository.cs"}, {"SourceFile": "Framework\\Data\\IUnitOfWork.cs"}, {"SourceFile": "Framework\\IO\\AppPaths.cs"}, {"SourceFile": "Framework\\IO\\AppPathService.cs"}, {"SourceFile": "Framework\\IO\\IAppPathServce.cs"}, {"SourceFile": "Framework\\IO\\IFileSystemService.cs"}, {"SourceFile": "Framework\\Logging\\DefaultLogger.cs"}, {"SourceFile": "Framework\\Logging\\ILogger.cs"}, {"SourceFile": "Framework\\Logging\\ILoggerFactory.cs"}, {"SourceFile": "Framework\\Logging\\LoggerExtensions.cs"}, {"SourceFile": "Framework\\Logging\\LogLevel.cs"}, {"SourceFile": "Framework\\Network\\IHttpClientService.cs"}, {"SourceFile": "Framework\\Performance\\IPerformanceCounter.cs"}, {"SourceFile": "Framework\\Security\\DefaultPermissionService.cs"}, {"SourceFile": "Framework\\Security\\DefaultRoleService.cs"}, {"SourceFile": "Framework\\Security\\DefaultUserService.cs"}, {"SourceFile": "Framework\\Security\\IPermissionService.cs"}, {"SourceFile": "Framework\\Security\\IRoleService.cs"}, {"SourceFile": "Framework\\Security\\IUserService.cs"}, {"SourceFile": "Framework\\Security\\SecurityEventArgs.cs"}, {"SourceFile": "Framework\\Security\\SecurityModels.cs"}, {"SourceFile": "Framework\\Serialization\\DefaultJsonService.cs"}, {"SourceFile": "Framework\\Serialization\\IJsonService.cs"}, {"SourceFile": "Framework\\Services\\DefaultDialogService.cs"}, {"SourceFile": "Framework\\Services\\DefaultExceptionHandlingService.cs"}, {"SourceFile": "Framework\\Services\\DefaultNavigationService.cs"}, {"SourceFile": "Framework\\Services\\IDialogService.cs"}, {"SourceFile": "Framework\\Services\\IExceptionHandlingService.cs"}, {"SourceFile": "Framework\\Services\\INavigationService.cs"}, {"SourceFile": "Framework\\Styles\\DefaultStyleService.cs"}, {"SourceFile": "Framework\\Styles\\IStyleService.cs"}, {"SourceFile": "Framework\\UI\\BindingProxy.cs"}, {"SourceFile": "Framework\\UI\\IThemeService.cs"}, {"SourceFile": "Framework\\UI\\IWindowManager.cs"}, {"SourceFile": "Framework\\UI\\ThemeManager.cs"}, {"SourceFile": "Framework\\UI\\WindowManager.cs"}, {"SourceFile": "Framework\\Validation\\IValidationRule.cs"}, {"SourceFile": "Framework\\Validation\\ValidationEngine.cs"}, {"SourceFile": "Modules\\Home\\HomeViewModel.cs"}, {"SourceFile": "Modules\\Home\\HomeView.xaml.cs"}, {"SourceFile": "Modules\\RecipeManager\\ILoadable.cs"}, {"SourceFile": "Modules\\RecipeManager\\IRecipeItem.cs"}, {"SourceFile": "Modules\\RecipeManager\\IRecipeManager.cs"}, {"SourceFile": "Modules\\RecipeManager\\ISaveable.cs"}, {"SourceFile": "Modules\\RecipeManager\\RecipeManager.cs"}, {"SourceFile": "Modules\\RecipeManager\\ViewModels\\RecipeManagerViewModel.cs"}, {"SourceFile": "Modules\\RecipeManager\\Views\\RecipeManagerView.xaml.cs"}, {"SourceFile": "Navigation\\INavigationPage.cs"}, {"SourceFile": "Navigation\\INavigationService.cs"}, {"SourceFile": "Navigation\\NavigationCategory.cs"}, {"SourceFile": "Navigation\\NavigationItem.cs"}, {"SourceFile": "Navigation\\NavigationService.cs"}, {"SourceFile": "Navigation\\PageInfo.cs"}, {"SourceFile": "Plugins\\DataAnalyzers.cs"}, {"SourceFile": "Plugins\\IPlugin.cs"}, {"SourceFile": "Plugins\\IPluginManager.cs"}, {"SourceFile": "Plugins\\PluginInterfaces.cs"}, {"SourceFile": "Plugins\\PluginLoader.cs"}, {"SourceFile": "Plugins\\PluginManager.cs"}, {"SourceFile": "Plugins\\PluginModels.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\McLaser_V1\\McLaser.Core\\obj\\Debug\\ApplicationBase\\Views\\BottomNavigationBar.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\McLaser_V1\\McLaser.Core\\obj\\Debug\\ApplicationBase\\Views\\CategoryPopup.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\McLaser_V1\\McLaser.Core\\obj\\Debug\\ApplicationBase\\Views\\MainWindow.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\McLaser_V1\\McLaser.Core\\obj\\Debug\\ApplicationBase\\Views\\NavigationButton.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\McLaser_V1\\McLaser.Core\\obj\\Debug\\Modules\\Home\\HomeView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\McLaser_V1\\McLaser.Core\\obj\\Debug\\Modules\\RecipeManager\\Views\\RecipeManagerView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\McLaser_V1\\McLaser.Core\\obj\\Debug\\GeneratedInternalTypeHelper.g.cs"}], "References": [{"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\bin\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\PresentationCore.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\PresentationFramework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.ComponentModel.Composition.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.ComponentModel.DataAnnotations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Xaml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\WindowsBase.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\bin\\McLaser.Core.dll", "OutputItemRelativePath": "McLaser.Core.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}