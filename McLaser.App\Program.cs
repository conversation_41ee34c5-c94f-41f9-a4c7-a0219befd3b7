using System;
using System.Windows;

namespace McLaser.App
{
    /// <summary>
    /// 程序入口点
    /// 提供Main方法作为应用程序启动点
    /// </summary>
    public static class Program
    {
        ///// <summary>
        ///// 应用程序主入口点
        ///// </summary>
        ///// <param name="args">命令行参数</param>
        //[STAThread]
        //public static void Main(string[] args)
        //{
        //    try
        //    {
        //        // 设置环境变量以禁用UI自动化
        //        Environment.SetEnvironmentVariable("DISABLE_XAMLDIAGNOSTICS", "1");

        //        // 创建WPF应用程序实例
        //        var app = new App();

        //        // 初始化应用程序
        //        app.Initialize();

        //        // 运行应用程序
        //        app.Run();
        //    }
        //    catch (Exception ex)
        //    {
        //        // 显示启动错误
        //        MessageBox.Show(
        //            $"应用程序启动失败：\n\n{ex.Message}\n\n详细信息：\n{ex}",
        //            "McLaser示例应用程序 - 启动错误",
        //            MessageBoxButton.OK,
        //            MessageBoxImage.Error);
        //    }
        //}
    }
}
