using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Linq;
using System.Windows;
using System.Windows.Media;
using McLaser.Core.Framework.Logging;

namespace McLaser.Core.Framework.Styles
{
    /// <summary>
    /// 默认样式服务实现
    /// 提供完整的WPF样式和主题管理功能
    /// </summary>
    [Export(typeof(IStyleService))]
    public class DefaultStyleService : IStyleService
    {
        #region 字段和属性

        private readonly ILogger? _logger;
        private readonly Dictionary<string, ResourceDictionary> _styleSets;
        private readonly ConcurrentDictionary<string, object> _styleCache;
        private readonly StyleCacheStatistics _cacheStatistics;
        
        private string _currentStyleSet;
        private double _dpiScale;
        private ScreenSizeCategory _screenSizeCategory;

        /// <summary>
        /// 当前样式集合名称
        /// </summary>
        public string CurrentStyleSet => _currentStyleSet;

        /// <summary>
        /// 可用样式集合列表
        /// </summary>
        public IReadOnlyList<string> AvailableStyleSets => _styleSets.Keys.ToList().AsReadOnly();

        /// <summary>
        /// 当前DPI缩放比例
        /// </summary>
        public double DpiScale => _dpiScale;

        /// <summary>
        /// 当前屏幕尺寸类别
        /// </summary>
        public ScreenSizeCategory ScreenSizeCategory => _screenSizeCategory;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志服务（可选）</param>
        public DefaultStyleService(ILogger? logger = null)
        {
            _logger = logger;
            _styleSets = new Dictionary<string, ResourceDictionary>();
            _styleCache = new ConcurrentDictionary<string, object>();
            _cacheStatistics = new StyleCacheStatistics();
            _currentStyleSet = string.Empty;
            
            // 初始化DPI和屏幕尺寸
            InitializeDpiAndScreenSize();
            
            // 注册默认样式集合
            RegisterDefaultStyleSets();
            
            _logger?.LogInfo("样式服务已初始化");
        }

        #endregion

        #region 初始化

        /// <summary>
        /// 初始化DPI和屏幕尺寸
        /// </summary>
        private void InitializeDpiAndScreenSize()
        {
            try
            {
                // 获取主屏幕DPI
                var dpiXProperty = typeof(SystemParameters).GetProperty("DpiX", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
                var dpiYProperty = typeof(SystemParameters).GetProperty("DpiY", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
                
                var dpiX = (int)(dpiXProperty?.GetValue(null, null) ?? 96);
                var dpiY = (int)(dpiYProperty?.GetValue(null, null) ?? 96);
                
                _dpiScale = Math.Max(dpiX, dpiY) / 96.0;
                
                // 确定屏幕尺寸类别
                var screenWidth = SystemParameters.PrimaryScreenWidth;
                var screenHeight = SystemParameters.PrimaryScreenHeight;
                
                _screenSizeCategory = DetermineScreenSizeCategory(screenWidth, screenHeight);
                
                _logger?.LogInfo($"DPI缩放比例: {_dpiScale:F2}, 屏幕尺寸类别: {_screenSizeCategory}");
            }
            catch (Exception ex)
            {
                _logger?.LogWarning($"初始化DPI和屏幕尺寸失败: {ex.Message}");
                _dpiScale = 1.0;
                _screenSizeCategory = ScreenSizeCategory.Medium;
            }
        }

        /// <summary>
        /// 确定屏幕尺寸类别
        /// </summary>
        private static ScreenSizeCategory DetermineScreenSizeCategory(double width, double height)
        {
            var diagonal = Math.Sqrt(width * width + height * height);
            
            return diagonal switch
            {
                < 1000 => ScreenSizeCategory.Small,
                < 1500 => ScreenSizeCategory.Medium,
                < 2500 => ScreenSizeCategory.Large,
                _ => ScreenSizeCategory.ExtraLarge
            };
        }

        /// <summary>
        /// 注册默认样式集合
        /// </summary>
        private void RegisterDefaultStyleSets()
        {
            try
            {
                // 注册默认样式集合
                var defaultStyles = CreateDefaultStyleSet();
                RegisterStyleSet("Default", defaultStyles);
                
                // 注册现代样式集合
                var modernStyles = CreateModernStyleSet();
                RegisterStyleSet("Modern", modernStyles);
                
                // 注册经典样式集合
                var classicStyles = CreateClassicStyleSet();
                RegisterStyleSet("Classic", classicStyles);
                
                // 应用默认样式
                ApplyStyleSet("Default");
                
                _logger?.LogInfo("默认样式集合已注册");
            }
            catch (Exception ex)
            {
                _logger?.LogError("注册默认样式集合失败", ex);
            }
        }

        #endregion

        #region 样式管理

        /// <summary>
        /// 注册样式集合
        /// </summary>
        /// <param name="name">样式集合名称</param>
        /// <param name="resourceDictionary">样式资源字典</param>
        public void RegisterStyleSet(string name, ResourceDictionary resourceDictionary)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("样式集合名称不能为空", nameof(name));
            
            if (resourceDictionary == null)
                throw new ArgumentNullException(nameof(resourceDictionary));

            try
            {
                _styleSets[name] = resourceDictionary;
                _logger?.LogInfo($"样式集合已注册: {name}");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"注册样式集合失败: {name}", ex);
                throw;
            }
        }

        /// <summary>
        /// 注册样式集合（从URI加载）
        /// </summary>
        /// <param name="name">样式集合名称</param>
        /// <param name="resourceUri">资源URI</param>
        public void RegisterStyleSet(string name, Uri resourceUri)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("样式集合名称不能为空", nameof(name));
            
            if (resourceUri == null)
                throw new ArgumentNullException(nameof(resourceUri));

            try
            {
                var resourceDictionary = new ResourceDictionary { Source = resourceUri };
                RegisterStyleSet(name, resourceDictionary);
            }
            catch (Exception ex)
            {
                _logger?.LogError($"从URI注册样式集合失败: {name}, URI: {resourceUri}", ex);
                throw;
            }
        }

        /// <summary>
        /// 移除样式集合
        /// </summary>
        /// <param name="name">样式集合名称</param>
        /// <returns>是否成功移除</returns>
        public bool RemoveStyleSet(string name)
        {
            if (string.IsNullOrWhiteSpace(name))
                return false;

            try
            {
                var removed = _styleSets.Remove(name);
                if (removed)
                {
                    // 如果移除的是当前样式集合，切换到默认样式
                    if (_currentStyleSet == name)
                    {
                        ApplyStyleSet("Default");
                    }
                    
                    _logger?.LogInfo($"样式集合已移除: {name}");
                }
                return removed;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"移除样式集合失败: {name}", ex);
                return false;
            }
        }

        /// <summary>
        /// 应用样式集合
        /// </summary>
        /// <param name="name">样式集合名称</param>
        /// <returns>是否成功应用</returns>
        public bool ApplyStyleSet(string name)
        {
            if (string.IsNullOrWhiteSpace(name) || !_styleSets.ContainsKey(name))
                return false;

            try
            {
                var oldStyleSet = _currentStyleSet;
                var resourceDictionary = _styleSets[name];
                
                // 清除当前样式
                ClearCurrentStyles();
                
                // 应用新样式
                Application.Current.Resources.MergedDictionaries.Add(resourceDictionary);
                
                _currentStyleSet = name;
                
                // 清除样式缓存
                ClearStyleCache();
                
                // 触发样式集合变更事件
                StyleSetChanged?.Invoke(this, new StyleSetChangedEventArgs(oldStyleSet, name));
                
                _logger?.LogInfo($"样式集合已应用: {name}");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"应用样式集合失败: {name}", ex);
                return false;
            }
        }

        /// <summary>
        /// 清除当前样式
        /// </summary>
        private void ClearCurrentStyles()
        {
            try
            {
                if (!string.IsNullOrEmpty(_currentStyleSet) && _styleSets.ContainsKey(_currentStyleSet))
                {
                    var currentResource = _styleSets[_currentStyleSet];
                    Application.Current.Resources.MergedDictionaries.Remove(currentResource);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning($"清除当前样式时发生错误: {ex.Message}");
            }
        }

        #endregion

        #region 样式查询

        /// <summary>
        /// 获取样式资源
        /// </summary>
        /// <param name="key">样式键</param>
        /// <returns>样式资源</returns>
        public Style? GetStyle(string key)
        {
            return GetResource<Style>(key);
        }

        /// <summary>
        /// 获取样式资源（泛型）
        /// </summary>
        /// <typeparam name="T">资源类型</typeparam>
        /// <param name="key">资源键</param>
        /// <returns>资源对象</returns>
        public T? GetResource<T>(string key) where T : class
        {
            if (string.IsNullOrWhiteSpace(key))
                return null;

            try
            {
                // 尝试从缓存获取
                var cacheKey = $"{typeof(T).Name}:{key}";
                if (_styleCache.TryGetValue(cacheKey, out var cachedValue))
                {
                    _cacheStatistics.CacheHits++;
                    return cachedValue as T;
                }

                // 从应用程序资源查找
                var resource = Application.Current.TryFindResource(key) as T;

                // 缓存结果
                if (resource != null)
                {
                    _styleCache.TryAdd(cacheKey, resource);
                }

                _cacheStatistics.CacheMisses++;
                return resource;
            }
            catch (Exception ex)
            {
                _logger?.LogWarning($"获取样式资源失败: {key}", ex);
                return null;
            }
        }

        /// <summary>
        /// 检查样式是否存在
        /// </summary>
        /// <param name="key">样式键</param>
        /// <returns>是否存在</returns>
        public bool HasStyle(string key)
        {
            if (string.IsNullOrWhiteSpace(key))
                return false;

            try
            {
                return Application.Current.TryFindResource(key) != null;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取所有样式键
        /// </summary>
        /// <returns>样式键列表</returns>
        public IEnumerable<string> GetAllStyleKeys()
        {
            var keys = new List<string>();

            try
            {
                // 遍历所有资源字典
                foreach (var dictionary in Application.Current.Resources.MergedDictionaries)
                {
                    foreach (var key in dictionary.Keys)
                    {
                        if (key is string stringKey && dictionary[key] is Style)
                        {
                            keys.Add(stringKey);
                        }
                    }
                }

                // 遍历主资源字典
                foreach (var key in Application.Current.Resources.Keys)
                {
                    if (key is string stringKey && Application.Current.Resources[key] is Style)
                    {
                        keys.Add(stringKey);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning($"获取样式键列表失败: {ex.Message}");
            }

            return keys.Distinct();
        }

        #endregion

        #region 动态样式

        /// <summary>
        /// 动态设置样式属性
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="property">依赖属性</param>
        /// <param name="value">属性值</param>
        public void SetStyleProperty(FrameworkElement element, DependencyProperty property, object value)
        {
            if (element == null)
                throw new ArgumentNullException(nameof(element));

            if (property == null)
                throw new ArgumentNullException(nameof(property));

            try
            {
                element.SetValue(property, value);
                _logger?.LogInfo($"样式属性已设置: {element.GetType().Name}.{property.Name} = {value}");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"设置样式属性失败: {element.GetType().Name}.{property.Name}", ex);
                throw;
            }
        }

        /// <summary>
        /// 动态获取样式属性
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="property">依赖属性</param>
        /// <returns>属性值</returns>
        public object? GetStyleProperty(FrameworkElement element, DependencyProperty property)
        {
            if (element == null)
                throw new ArgumentNullException(nameof(element));

            if (property == null)
                throw new ArgumentNullException(nameof(property));

            try
            {
                return element.GetValue(property);
            }
            catch (Exception ex)
            {
                _logger?.LogError($"获取样式属性失败: {element.GetType().Name}.{property.Name}", ex);
                return null;
            }
        }

        /// <summary>
        /// 应用样式到元素
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="styleKey">样式键</param>
        public void ApplyStyleToElement(FrameworkElement element, string styleKey)
        {
            if (element == null)
                throw new ArgumentNullException(nameof(element));

            if (string.IsNullOrWhiteSpace(styleKey))
                throw new ArgumentException("样式键不能为空", nameof(styleKey));

            try
            {
                var style = GetStyle(styleKey);
                if (style != null)
                {
                    element.Style = style;

                    // 触发样式应用事件
                    StyleApplied?.Invoke(this, new StyleAppliedEventArgs(element, styleKey, style));

                    _logger?.LogInfo($"样式已应用到元素: {element.GetType().Name}, 样式: {styleKey}");
                }
                else
                {
                    _logger?.LogWarning($"未找到样式: {styleKey}");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"应用样式到元素失败: {styleKey}", ex);
                throw;
            }
        }

        #endregion

        #region 响应式设计

        /// <summary>
        /// 根据DPI获取适应的样式
        /// </summary>
        /// <param name="baseStyleKey">基础样式键</param>
        /// <returns>适应的样式</returns>
        public Style? GetDpiAdaptiveStyle(string baseStyleKey)
        {
            if (string.IsNullOrWhiteSpace(baseStyleKey))
                return null;

            try
            {
                // 根据DPI缩放比例选择合适的样式
                var adaptiveKey = _dpiScale switch
                {
                    >= 2.0 => $"{baseStyleKey}.HighDpi",
                    >= 1.5 => $"{baseStyleKey}.MediumDpi",
                    _ => baseStyleKey
                };

                return GetStyle(adaptiveKey) ?? GetStyle(baseStyleKey);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning($"获取DPI适应样式失败: {baseStyleKey}", ex);
                return GetStyle(baseStyleKey);
            }
        }

        /// <summary>
        /// 根据屏幕尺寸获取适应的样式
        /// </summary>
        /// <param name="baseStyleKey">基础样式键</param>
        /// <returns>适应的样式</returns>
        public Style? GetScreenSizeAdaptiveStyle(string baseStyleKey)
        {
            if (string.IsNullOrWhiteSpace(baseStyleKey))
                return null;

            try
            {
                // 根据屏幕尺寸类别选择合适的样式
                var adaptiveKey = _screenSizeCategory switch
                {
                    ScreenSizeCategory.Small => $"{baseStyleKey}.Small",
                    ScreenSizeCategory.Medium => $"{baseStyleKey}.Medium",
                    ScreenSizeCategory.Large => $"{baseStyleKey}.Large",
                    ScreenSizeCategory.ExtraLarge => $"{baseStyleKey}.XLarge",
                    _ => baseStyleKey
                };

                return GetStyle(adaptiveKey) ?? GetStyle(baseStyleKey);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning($"获取屏幕尺寸适应样式失败: {baseStyleKey}", ex);
                return GetStyle(baseStyleKey);
            }
        }

        #endregion

        #region 样式缓存

        /// <summary>
        /// 清除样式缓存
        /// </summary>
        public void ClearStyleCache()
        {
            try
            {
                _styleCache.Clear();
                _cacheStatistics.CacheHits = 0;
                _cacheStatistics.CacheMisses = 0;
                _cacheStatistics.CachedStylesCount = 0;
                _cacheStatistics.MemoryUsage = 0;

                _logger?.LogInfo("样式缓存已清除");
            }
            catch (Exception ex)
            {
                _logger?.LogError("清除样式缓存失败", ex);
            }
        }

        /// <summary>
        /// 预加载样式集合
        /// </summary>
        /// <param name="styleSetName">样式集合名称</param>
        public void PreloadStyleSet(string styleSetName)
        {
            if (string.IsNullOrWhiteSpace(styleSetName) || !_styleSets.ContainsKey(styleSetName))
                return;

            try
            {
                var resourceDictionary = _styleSets[styleSetName];

                // 预加载所有样式到缓存
                foreach (var key in resourceDictionary.Keys)
                {
                    if (key is string stringKey && resourceDictionary[key] is Style style)
                    {
                        var cacheKey = $"Style:{stringKey}";
                        _styleCache.TryAdd(cacheKey, style);
                    }
                }

                _cacheStatistics.CachedStylesCount = _styleCache.Count;
                _logger?.LogInfo($"样式集合已预加载: {styleSetName}");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"预加载样式集合失败: {styleSetName}", ex);
            }
        }

        /// <summary>
        /// 获取样式缓存统计信息
        /// </summary>
        /// <returns>缓存统计信息</returns>
        public StyleCacheStatistics GetCacheStatistics()
        {
            try
            {
                _cacheStatistics.CachedStylesCount = _styleCache.Count;

                // 估算内存使用量（简单估算）
                _cacheStatistics.MemoryUsage = _styleCache.Count * 1024; // 假设每个样式占用1KB

                return _cacheStatistics;
            }
            catch (Exception ex)
            {
                _logger?.LogError("获取缓存统计信息失败", ex);
                return new StyleCacheStatistics();
            }
        }

        #endregion

        #region 默认样式创建

        /// <summary>
        /// 创建默认样式集合
        /// </summary>
        private ResourceDictionary CreateDefaultStyleSet()
        {
            var dictionary = new ResourceDictionary();

            try
            {
                // 默认按钮样式
                var buttonStyle = new Style(typeof(System.Windows.Controls.Button));
                buttonStyle.Setters.Add(new Setter(System.Windows.Controls.Control.PaddingProperty, new Thickness(8, 4, 8, 4)));
                buttonStyle.Setters.Add(new Setter(System.Windows.Controls.Control.MarginProperty, new Thickness(2)));
                buttonStyle.Setters.Add(new Setter(System.Windows.Controls.Control.MinWidthProperty, 75.0));
                dictionary.Add("DefaultButton", buttonStyle);

                // 默认文本框样式
                var textBoxStyle = new Style(typeof(System.Windows.Controls.TextBox));
                textBoxStyle.Setters.Add(new Setter(System.Windows.Controls.Control.PaddingProperty, new Thickness(4, 2, 4, 2)));
                textBoxStyle.Setters.Add(new Setter(System.Windows.Controls.Control.MarginProperty, new Thickness(2)));
                textBoxStyle.Setters.Add(new Setter(System.Windows.Controls.Control.BorderThicknessProperty, new Thickness(1)));
                dictionary.Add("DefaultTextBox", textBoxStyle);

                _logger?.LogInfo("默认样式集合已创建");
            }
            catch (Exception ex)
            {
                _logger?.LogError("创建默认样式集合失败", ex);
            }

            return dictionary;
        }

        /// <summary>
        /// 创建现代样式集合
        /// </summary>
        private ResourceDictionary CreateModernStyleSet()
        {
            var dictionary = new ResourceDictionary();

            try
            {
                // 现代按钮样式
                var buttonStyle = new Style(typeof(System.Windows.Controls.Button));
                buttonStyle.Setters.Add(new Setter(System.Windows.Controls.Control.PaddingProperty, new Thickness(12, 6, 12, 6)));
                buttonStyle.Setters.Add(new Setter(System.Windows.Controls.Control.MarginProperty, new Thickness(4)));
                buttonStyle.Setters.Add(new Setter(System.Windows.Controls.Control.BorderThicknessProperty, new Thickness(0)));
                buttonStyle.Setters.Add(new Setter(System.Windows.Controls.Control.BackgroundProperty, new SolidColorBrush(Color.FromRgb(0, 120, 215))));
                buttonStyle.Setters.Add(new Setter(System.Windows.Controls.Control.ForegroundProperty, Brushes.White));
                dictionary.Add("ModernButton", buttonStyle);

                _logger?.LogInfo("现代样式集合已创建");
            }
            catch (Exception ex)
            {
                _logger?.LogError("创建现代样式集合失败", ex);
            }

            return dictionary;
        }

        /// <summary>
        /// 创建经典样式集合
        /// </summary>
        private ResourceDictionary CreateClassicStyleSet()
        {
            var dictionary = new ResourceDictionary();

            try
            {
                // 经典按钮样式
                var buttonStyle = new Style(typeof(System.Windows.Controls.Button));
                buttonStyle.Setters.Add(new Setter(System.Windows.Controls.Control.PaddingProperty, new Thickness(6, 3, 6, 3)));
                buttonStyle.Setters.Add(new Setter(System.Windows.Controls.Control.MarginProperty, new Thickness(1)));
                buttonStyle.Setters.Add(new Setter(System.Windows.Controls.Control.BorderThicknessProperty, new Thickness(1)));
                buttonStyle.Setters.Add(new Setter(System.Windows.Controls.Control.BackgroundProperty, SystemColors.ControlBrush));
                dictionary.Add("ClassicButton", buttonStyle);

                _logger?.LogInfo("经典样式集合已创建");
            }
            catch (Exception ex)
            {
                _logger?.LogError("创建经典样式集合失败", ex);
            }

            return dictionary;
        }

        #endregion

        #region 事件

        /// <summary>
        /// 样式集合变更事件
        /// </summary>
        public event EventHandler<StyleSetChangedEventArgs>? StyleSetChanged;

        /// <summary>
        /// 样式应用事件
        /// </summary>
        public event EventHandler<StyleAppliedEventArgs>? StyleApplied;

        /// <summary>
        /// DPI变更事件
        /// </summary>
        public event EventHandler<DpiChangedEventArgs>? DpiChanged;

        #endregion
    }
}
