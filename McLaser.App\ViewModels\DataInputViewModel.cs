using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using McLaser.Core.Common;
using McLaser.Core.Framework.Logging;
using McLaser.Core.Framework.Services;
using McLaser.Core.Framework.Container;
using System.ComponentModel.DataAnnotations;

namespace McLaser.App.ViewModels
{
    /// <summary>
    /// 数据输入窗口ViewModel
    /// 展示数据验证框架的使用
    /// </summary>
    public class DataInputViewModel : ViewModelBase
    {
        private readonly IDialogService _dialogService;
        private readonly ILogger _logger;

        private string _name = string.Empty;
        private string _email = string.Empty;
        private int _age;
        private string _phone = string.Empty;
        private string _website = string.Empty;
        private string _description = string.Empty;
        private bool _isValid;

        /// <summary>
        /// 构造函数（依赖注入）
        /// </summary>
        /// <param name="dialogService">对话框服务</param>
        /// <param name="logger">日志服务</param>
        public DataInputViewModel(
            IDialogService dialogService,
            ILogger logger)
        {
            _dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            InitializeCommands();
            InitializeValidation();
        }

        /// <summary>
        /// 默认构造函数（备用方案）
        /// </summary>
        public DataInputViewModel()
        {
            // 尝试从容器获取服务
            var container = ContainerManager.Current;
            if (container != null)
            {
                _dialogService = container.TryResolve<IDialogService>();
                _logger = container.TryResolve<ILogger>();
            }

            // 如果任何服务为null，使用备用实现
            _dialogService ??= null!;
            _logger = _logger ?? new McLaser.App.Core.ConsoleLogger("DataInputViewModel");

            InitializeCommands();
            InitializeValidationSafely();
        }

        #region 属性

        /// <summary>
        /// 姓名
        /// </summary>
        [Required(ErrorMessage = "姓名不能为空")]
        [StringLength(50, MinimumLength = 2, ErrorMessage = "姓名长度必须在2-50个字符之间")]
        public string Name
        {
            get => _name;
            set
            {
                if (SetProperty(ref _name, value))
                {
                    ValidateProperty();
                }
            }
        }

        /// <summary>
        /// 电子邮箱
        /// </summary>
        [Required(ErrorMessage = "电子邮箱不能为空")]
        [EmailAddress(ErrorMessage = "请输入有效的电子邮箱地址")]
        public string Email
        {
            get => _email;
            set
            {
                if (SetProperty(ref _email, value))
                {
                    ValidateProperty();
                }
            }
        }

        /// <summary>
        /// 年龄
        /// </summary>
        [Range(1, 120, ErrorMessage = "年龄必须在1-120之间")]
        public int Age
        {
            get => _age;
            set
            {
                if (SetProperty(ref _age, value))
                {
                    ValidateProperty();
                }
            }
        }

        /// <summary>
        /// 电话号码
        /// </summary>
        [Phone(ErrorMessage = "请输入有效的电话号码")]
        public string Phone
        {
            get => _phone;
            set
            {
                if (SetProperty(ref _phone, value))
                {
                    ValidateProperty();
                }
            }
        }

        /// <summary>
        /// 网站地址
        /// </summary>
        [Url(ErrorMessage = "请输入有效的网站地址")]
        public string Website
        {
            get => _website;
            set
            {
                if (SetProperty(ref _website, value))
                {
                    ValidateProperty();
                }
            }
        }

        /// <summary>
        /// 描述
        /// </summary>
        [StringLength(500, ErrorMessage = "描述不能超过500个字符")]
        public string Description
        {
            get => _description;
            set
            {
                if (SetProperty(ref _description, value))
                {
                    ValidateProperty();
                }
            }
        }

        /// <summary>
        /// 是否验证通过
        /// </summary>
        public bool IsValid
        {
            get => _isValid;
            private set => SetProperty(ref _isValid, value);
        }

        /// <summary>
        /// 验证错误列表
        /// </summary>
        public ObservableCollection<string> ValidationErrors { get; } = new ObservableCollection<string>();

        #endregion

        #region 命令

        /// <summary>
        /// 验证数据命令
        /// </summary>
        public ICommand ValidateCommand { get; private set; } = null!;

        /// <summary>
        /// 保存数据命令
        /// </summary>
        public ICommand SaveCommand { get; private set; } = null!;

        /// <summary>
        /// 清空数据命令
        /// </summary>
        public ICommand ClearCommand { get; private set; } = null!;

        /// <summary>
        /// 关闭窗口命令
        /// </summary>
        public ICommand CloseCommand { get; private set; } = null!;

        /// <summary>
        /// 生成测试数据命令
        /// </summary>
        public ICommand GenerateTestDataCommand { get; private set; } = null!;

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化命令
        /// </summary>
        private void InitializeCommands()
        {
            ValidateCommand = new RelayCommand(ExecuteValidate);
            SaveCommand = new RelayCommand(ExecuteSave, CanExecuteSave);
            ClearCommand = new RelayCommand(ExecuteClear);
            CloseCommand = new RelayCommand<System.Windows.Window>(ExecuteClose);
            GenerateTestDataCommand = new RelayCommand(ExecuteGenerateTestData);
        }

        /// <summary>
        /// 初始化验证
        /// </summary>
        private void InitializeValidation()
        {
            try
            {
                // 注册自定义验证规则
                RegisterCustomValidationRules();

                _logger?.LogInfo("数据验证初始化完成");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"数据验证初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 安全初始化验证
        /// </summary>
        private void InitializeValidationSafely()
        {
            try
            {
                // 注册自定义验证规则
                RegisterCustomValidationRules();

                _logger?.LogInfo("数据验证初始化完成");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"数据验证初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 注册自定义验证规则
        /// </summary>
        private void RegisterCustomValidationRules()
        {
            // 这里可以注册自定义的验证规则
            // 例如：业务特定的验证逻辑
        }

        /// <summary>
        /// 验证属性
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        private void ValidateProperty([CallerMemberName] string? propertyName = null)
        {
            if (string.IsNullOrEmpty(propertyName)) return;

            try
            {
                // 使用DataAnnotations进行验证
                var context = new ValidationContext(this);
                var results = new List<ValidationResult>();
                var property = GetType().GetProperty(propertyName);

                if (property != null)
                {
                    var value = property.GetValue(this);
                    System.ComponentModel.DataAnnotations.Validator.TryValidateProperty(value, context, results);

                    // 清除该属性的错误
                    ClearErrors(propertyName ?? string.Empty);

                    // 添加新的错误
                    foreach (var result in results)
                    {
                        AddError(propertyName ?? string.Empty, result.ErrorMessage ?? "验证失败");
                    }
                }

                // 更新整体验证状态
                UpdateValidationStatus();
            }
            catch (Exception ex)
            {
                _logger?.LogError($"属性验证失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证所有属性
        /// </summary>
        private void ValidateAll()
        {
            try
            {
                // 清除所有错误
                ClearAllErrors();
                ValidationErrors.Clear();

                // 验证所有属性
                var context = new ValidationContext(this);
                var results = new List<ValidationResult>();
                var isValid = System.ComponentModel.DataAnnotations.Validator.TryValidateObject(this, context, results, true);

                // 添加验证错误
                foreach (var result in results)
                {
                    var errorMessage = result.ErrorMessage ?? "验证失败";
                    ValidationErrors.Add(errorMessage);

                    if (result.MemberNames != null)
                    {
                        foreach (var memberName in result.MemberNames)
                        {
                            AddError(memberName, errorMessage);
                        }
                    }
                }

                IsValid = isValid && !HasErrors;
                _logger?.LogInfo($"数据验证完成，结果: {(IsValid ? "通过" : "失败")}");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"数据验证失败: {ex.Message}");
                IsValid = false;
            }
        }

        /// <summary>
        /// 更新验证状态
        /// </summary>
        private void UpdateValidationStatus()
        {
            IsValid = !HasErrors;
        }

        #endregion

        #region 命令执行方法

        /// <summary>
        /// 执行验证命令
        /// </summary>
        private void ExecuteValidate()
        {
            ValidateAll();

            if (IsValid)
            {
                if (_dialogService != null)
                {
                    _dialogService.ShowMessage("数据验证通过！", "验证结果", MessageType.Information);
                }
                else
                {
                    System.Windows.MessageBox.Show("数据验证通过！", "验证结果");
                }
            }
            else
            {
                var errorMessage = "数据验证失败：\n\n" + string.Join("\n", ValidationErrors);
                if (_dialogService != null)
                {
                    _dialogService.ShowMessage(errorMessage, "验证结果", MessageType.Warning);
                }
                else
                {
                    System.Windows.MessageBox.Show(errorMessage, "验证结果");
                }
            }
        }

        /// <summary>
        /// 执行保存命令
        /// </summary>
        private void ExecuteSave()
        {
            try
            {
                ValidateAll();

                if (!IsValid)
                {
                    var warningMessage = "请先修正验证错误。";
                    if (_dialogService != null)
                    {
                        _dialogService.ShowMessage(warningMessage, "保存失败", MessageType.Warning);
                    }
                    else
                    {
                        System.Windows.MessageBox.Show(warningMessage, "保存失败");
                    }
                    return;
                }

                // 模拟保存数据
                var data = $"姓名: {Name}\n" +
                          $"邮箱: {Email}\n" +
                          $"年龄: {Age}\n" +
                          $"电话: {Phone}\n" +
                          $"网站: {Website}\n" +
                          $"描述: {Description}";

                var successMessage = $"数据保存成功！\n\n{data}";
                if (_dialogService != null)
                {
                    _dialogService.ShowMessage(successMessage, "保存成功", MessageType.Information);
                }
                else
                {
                    System.Windows.MessageBox.Show(successMessage, "保存成功");
                }
                _logger?.LogInfo("数据保存成功");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"保存数据失败: {ex.Message}");
                var errorMessage = $"保存数据失败：{ex.Message}";
                if (_dialogService != null)
                {
                    _dialogService.ShowMessage(errorMessage, "错误", MessageType.Error);
                }
                else
                {
                    System.Windows.MessageBox.Show(errorMessage, "错误");
                }
            }
        }

        /// <summary>
        /// 判断是否可以执行保存命令
        /// </summary>
        /// <returns>是否可以执行</returns>
        private bool CanExecuteSave()
        {
            return IsValid;
        }

        /// <summary>
        /// 执行清空命令
        /// </summary>
        private void ExecuteClear()
        {
            bool shouldClear = false;

            if (_dialogService != null)
            {
                var result = _dialogService.ShowMessage("确定要清空所有数据吗？", "确认清空", MessageType.Question);
                shouldClear = result == true;
            }
            else
            {
                var result = System.Windows.MessageBox.Show("确定要清空所有数据吗？", "确认清空",
                    System.Windows.MessageBoxButton.YesNo, System.Windows.MessageBoxImage.Question);
                shouldClear = result == System.Windows.MessageBoxResult.Yes;
            }

            if (!shouldClear) return;

            Name = string.Empty;
            Email = string.Empty;
            Age = 0;
            Phone = string.Empty;
            Website = string.Empty;
            Description = string.Empty;

            ClearAllErrors();
            ValidationErrors.Clear();
            IsValid = false;

            _logger?.LogInfo("数据已清空");
        }

        /// <summary>
        /// 执行关闭窗口命令
        /// </summary>
        /// <param name="window">窗口实例</param>
        private void ExecuteClose(System.Windows.Window? window)
        {
            window?.Close();
        }

        /// <summary>
        /// 执行生成测试数据命令
        /// </summary>
        private void ExecuteGenerateTestData()
        {
            try
            {
                var random = new Random();
                var names = new[] { "张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十" };
                var domains = new[] { "example.com", "test.com", "demo.org", "sample.net" };

                Name = names[random.Next(names.Length)];
                Email = $"{Name.ToLower()}{random.Next(100, 999)}@{domains[random.Next(domains.Length)]}";
                Age = random.Next(18, 65);
                Phone = $"1{random.Next(3, 9)}{random.Next(100000000, 999999999)}";
                Website = $"https://www.{Name.ToLower()}{random.Next(1, 100)}.com";
                Description = $"这是{Name}的个人描述信息，用于演示数据验证功能。";

                _logger?.LogInfo("测试数据已生成");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"生成测试数据失败: {ex.Message}");
                var errorMessage = $"生成测试数据失败：{ex.Message}";
                if (_dialogService != null)
                {
                    _dialogService.ShowMessage(errorMessage, "错误", MessageType.Error);
                }
                else
                {
                    System.Windows.MessageBox.Show(errorMessage, "错误");
                }
            }
        }

        #endregion
    }
}
