# McLaser.Core 插件系统 - 兼容现有架构使用指南

## 概述

本指南说明如何在现有的McLaser设备架构基础上使用插件系统，确保插件与现有的DeviceBase、LaserBase等基类完全兼容。

## 架构兼容性

### 1. 现有设备架构
```
DeviceBase (McLaser.Device)
├── LaserBase (McLaser.Devices.Laser)
├── CameraBase (McLaser.Devices.Camera)
├── MotionBase (McLaser.Devices.Motion)
└── SensorBase (McLaser.Devices.Sensor)
```

### 2. 插件架构集成
```
插件类继承现有基类 + 实现IPlugin接口
例如：IPGLaserPlugin : LaserBase, IPlugin
```

## 设备插件开发指南

### 1. 激光器插件开发

```csharp
[PluginMetadata(
    Id = "McLaser.Plugins.IPGLaser",
    Name = "IPG激光器驱动",
    Version = "1.0.0",
    Description = "IPG品牌激光器控制插件"
)]
public class IPGLaserPlugin : LaserBase, IPlugin
{
    #region IPlugin 实现
    
    public string Id => "McLaser.Plugins.IPGLaser";
    public string Name => "IPG激光器驱动";
    public Version Version => new Version(1, 0, 0);
    public string Description => "IPG品牌激光器控制插件";
    public PluginStatus Status { get; private set; } = PluginStatus.Stopped;

    public event EventHandler<PluginStatusChangedEventArgs> StatusChanged;
    public event EventHandler<PluginErrorEventArgs> ErrorOccurred;

    public async Task InitializeAsync(IPluginContext context)
    {
        // 设置设备基本信息
        Name = "IPG激光器";
        DeviceType = DeviceType.Laser;
        
        // 初始化激光器通信
        await InitializeLaserCommunication();
        
        Status = PluginStatus.Initialized;
    }

    public async Task StartAsync()
    {
        // 连接激光器
        bool connected = await ConnectLaserAsync();
        if (connected)
        {
            Status = PluginStatus.Running;
        }
    }

    public async Task StopAsync()
    {
        // 关闭激光器
        if (IsLaserEnabled)
        {
            LaserOff();
        }
        Close();
        Status = PluginStatus.Stopped;
    }

    public async Task ShutdownAsync()
    {
        await StopAsync();
        Status = PluginStatus.Shutdown;
    }
    
    #endregion

    #region LaserBase 抽象方法实现
    
    public override LaserStatus LaserStatus => _laserStatus;
    public override double MaxPower => 1000.0; // 1000W

    public override bool LaserOn()
    {
        // 实际的IPG激光器启用代码
        IsLaserEnabled = true;
        _laserStatus = LaserStatus.Ready;
        return true;
    }

    public override bool LaserOff()
    {
        // 实际的IPG激光器关闭代码
        IsLaserEnabled = false;
        Power = 0;
        _laserStatus = LaserStatus.Standby;
        return true;
    }

    public override bool SetPower(double power)
    {
        if (power < 0 || power > 100)
            return false;
        
        Power = power;
        return true;
    }

    // ... 其他抽象方法实现
    
    #endregion
}
```

### 2. 相机插件开发

```csharp
[PluginMetadata(
    Id = "McLaser.Plugins.HikCamera",
    Name = "海康威视相机驱动",
    Version = "1.0.0"
)]
public class HikCameraPlugin : CameraBase, IPlugin
{
    // 类似的实现模式
    // 继承CameraBase并实现IPlugin接口
}
```

## 插件管理器使用

### 1. 插件加载和管理

```csharp
// 创建插件管理器
var pluginManager = new PluginManager();
await pluginManager.InitializeAsync();

// 加载插件
var loadResults = await pluginManager.LoadPluginsFromDirectoryAsync("Plugins");

// 获取激光器插件
var laserPlugin = pluginManager.GetPlugin("McLaser.Plugins.IPGLaser") as LaserBase;

if (laserPlugin != null)
{
    // 作为普通设备使用
    bool opened = laserPlugin.Open();
    if (opened)
    {
        laserPlugin.SetPower(50); // 设置50%功率
        laserPlugin.LaserOn();    // 启用激光器
    }
}
```

### 2. 设备管理器集成

```csharp
public class DeviceManager
{
    private PluginManager _pluginManager;
    private List<DeviceBase> _devices;

    public async Task InitializeAsync()
    {
        _pluginManager = new PluginManager();
        await _pluginManager.InitializeAsync();
        
        // 加载设备插件
        await _pluginManager.LoadPluginsFromDirectoryAsync("Plugins");
        
        // 将插件设备添加到设备列表
        foreach (var plugin in _pluginManager.GetLoadedPlugins())
        {
            if (plugin is DeviceBase device)
            {
                _devices.Add(device);
            }
        }
    }

    public T GetDevice<T>(string deviceId) where T : DeviceBase
    {
        return _devices.OfType<T>().FirstOrDefault(d => d.Id == deviceId);
    }

    public List<T> GetDevices<T>() where T : DeviceBase
    {
        return _devices.OfType<T>().ToList();
    }
}
```

## 配置管理

### 1. 插件配置

```csharp
// 插件配置文件 (Plugins/IPGLaser/config.json)
{
    "ConnectionType": "Ethernet",
    "IPAddress": "*************",
    "Port": 8080,
    "Timeout": 5000,
    "MaxPower": 1000,
    "SafetyMode": true
}

// 在插件中加载配置
public async Task InitializeAsync(IPluginContext context)
{
    var configPath = Path.Combine(context.PluginDirectory, "config.json");
    if (File.Exists(configPath))
    {
        var json = File.ReadAllText(configPath);
        var config = JsonConvert.DeserializeObject<IPGLaserConfig>(json);
        ApplyConfiguration(config);
    }
}
```

### 2. 设备参数配置

```csharp
public class IPGLaserConfig
{
    public string ConnectionType { get; set; } = "Ethernet";
    public string IPAddress { get; set; } = "*************";
    public int Port { get; set; } = 8080;
    public int Timeout { get; set; } = 5000;
    public double MaxPower { get; set; } = 1000;
    public bool SafetyMode { get; set; } = true;
}
```

## 事件处理

### 1. 设备状态事件

```csharp
// 订阅设备状态变化
laserPlugin.DeviceStatusChanged += (sender, e) =>
{
    Console.WriteLine($"激光器状态变化: {laserPlugin.Status.StatusMessage}");
};

// 订阅插件状态变化
laserPlugin.StatusChanged += (sender, e) =>
{
    Console.WriteLine($"插件状态: {e.OldStatus} -> {e.NewStatus}");
};
```

### 2. 错误处理

```csharp
// 订阅插件错误事件
laserPlugin.ErrorOccurred += (sender, e) =>
{
    Console.WriteLine($"插件错误: {e.ErrorMessage}");
    
    // 记录错误日志
    Logger.Error($"插件 {e.Plugin.Id} 发生错误: {e.ErrorMessage}", e.Exception);
    
    // 执行错误恢复
    if (e.Plugin.Status == PluginStatus.Error)
    {
        // 尝试重启插件
        _ = Task.Run(async () =>
        {
            await Task.Delay(5000);
            await e.Plugin.StartAsync();
        });
    }
};
```

## 最佳实践

### 1. 插件开发最佳实践

```csharp
// 1. 始终继承现有的设备基类
public class CustomLaserPlugin : LaserBase, IPlugin

// 2. 正确实现抽象方法
public override bool LaserOn()
{
    try
    {
        // 实际设备操作
        return true;
    }
    catch (Exception ex)
    {
        // 错误处理
        OnErrorOccurred($"启用激光器失败: {ex.Message}", ex);
        return false;
    }
}

// 3. 使用异步方法处理耗时操作
private async Task<bool> ConnectLaserAsync()
{
    // 异步连接操作
    await Task.Delay(1000);
    return true;
}

// 4. 正确管理资源
public async Task ShutdownAsync()
{
    try
    {
        if (Status == PluginStatus.Running)
        {
            await StopAsync();
        }
        // 清理资源
        CleanupResources();
    }
    catch (Exception ex)
    {
        OnErrorOccurred($"关闭插件失败: {ex.Message}", ex);
    }
}
```

### 2. 插件部署最佳实践

```
项目结构:
McLaser_V1/
├── Plugins/
│   ├── IPGLaser/
│   │   ├── McLaser.Plugins.Devices.dll
│   │   ├── config.json
│   │   └── readme.txt
│   ├── HikCamera/
│   │   ├── McLaser.Plugins.Camera.dll
│   │   ├── config.json
│   │   └── drivers/
│   └── CustomAlgorithm/
│       ├── McLaser.Plugins.Algorithm.dll
│       └── config.json
```

### 3. 错误处理和日志

```csharp
// 统一的错误处理
private void OnErrorOccurred(string message, Exception exception = null)
{
    // 触发错误事件
    ErrorOccurred?.Invoke(this, new PluginErrorEventArgs(this, message, exception));
    
    // 记录日志
    if (exception != null)
    {
        Logger.Error(message, exception);
    }
    else
    {
        Logger.Warning(message);
    }
    
    // 更新设备状态
    if (Status != null)
    {
        Status.StatusMessage = message;
    }
}
```

## 总结

通过继承现有的设备基类并实现IPlugin接口，插件系统完美集成到现有的McLaser架构中，既保持了原有设备管理的一致性，又提供了插件化的灵活性和扩展性。这种设计确保了：

1. **兼容性** - 插件设备可以像普通设备一样使用
2. **一致性** - 保持统一的设备接口和行为
3. **扩展性** - 支持动态加载和管理插件
4. **稳定性** - 插件错误不影响主系统运行
5. **可维护性** - 清晰的架构和标准化的开发模式
