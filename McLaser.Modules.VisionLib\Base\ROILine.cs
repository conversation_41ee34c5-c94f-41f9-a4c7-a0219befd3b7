﻿using HalconDotNet;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace McLaser.Modules.Vision
{
    [Serializable]
    public class ROILine : ROI, IDrawable
    {
        // 直线起始点
        private double row1, col1;
        public double Row1 { get { return row1; } set { Set(ref row1, value); } }
        public double Col1 { get { return col1; } set { Set(ref col1, value); } }
        // 直线终点
        private double row2, col2;
        public double Row2 { get { return row2; } set { Set(ref row2, value); } }
        public double Col2 { get { return col2; } set { Set(ref col2, value); } }
        // 直线角度
        private double phi;
        public double Phi { get { return phi; } set { Set(ref phi, value); } }
        // 直线中点
        private double centerRow, centerCol;
        // 直线上添加箭头显示
        private HXLDCont arrowHandleXLD;

        public ROILine() { }

        public ROILine(double row1, double col1, double row2, double col2)
        {
            this.row1 = row1;
            this.col1 = col1;
            this.row2 = row2;
            this.col2 = col2;
            centerRow = (this.row1 + this.row2) / 2;
            centerCol = (this.col1 + this.col2) / 2;
            phi = HMisc.AngleLx(col1, row1, col2, row2);
            arrowHandleXLD = new HXLDCont();
            arrowHandleXLD.GenEmptyObj();
        }

        public void Draw(HWindow hWindow, double factor)
        {
            hWindow.SetColor("blue");
            hWindow.DispLine(row1, col1, row2, col2);

            ShowArrowHandle(hWindow, 100, 0);
        }

        public override bool IsSelected(double x, double y, double factor)
        {
            double[] val = new double[3];

            val[0] = HMisc.DistancePp(x, y, row1, col1); // upper left 
            val[1] = HMisc.DistancePp(x, y, row2, col2); // upper right 
            val[2] = HMisc.DistancePp(x, y, centerRow, centerCol); // midpoint 

            for (int i = 0; i < 3; i++)
            {
                if (val[i] < 3 * factor)
                {
                    ActiveHandleId = i;
                    switch (i)
                    {
                        case 0:
                        case 1:
                            Cursor = RoiCursor.Hand;
                            break;
                        case 2:
                            Cursor = RoiCursor.SizeAll;
                            break;
                    }
                    return true;
                }
                Cursor = RoiCursor.Default;
            }
            return false;
        }

        public override void Shape(double x, double y)
        {
            double lenR, lenC;

            switch (ActiveHandleId)
            {
                case 0: // first end point
                    row1 = x;
                    col1 = y;

                    centerRow = (row1 + row2) / 2;
                    centerCol = (col1 + col2) / 2;
                    break;
                case 1: // last end point
                    row2 = x;
                    col2 = y;

                    centerRow = (row1 + row2) / 2;
                    centerCol = (col1 + col2) / 2;
                    break;
                case 2: // midpoint 
                    lenR = row1 - centerRow;
                    lenC = col1 - centerCol;

                    centerRow = x;
                    centerCol = y;

                    row1 = centerRow + lenR;
                    col1 = centerCol + lenC;
                    row2 = centerRow - lenR;
                    col2 = centerCol - lenC;
                    break;
            }
            phi = HMisc.AngleLx(col1, row1, col2, row2);
        }

        private void ShowArrowHandle(HWindow hWindow, double x, double y)
        {

            double length, dr, dc, halfHW;
            double rrow1, ccol1, rowP1, colP1, rowP2, colP2;

            double headLength = 30;
            double headWidth = 30;

            arrowHandleXLD.Dispose();
            arrowHandleXLD.GenEmptyObj();

            rrow1 = row1 + (row2 - row1) * 0.8;
            ccol1 = col1 + (col2 - col1) * 0.8;

            length = HMisc.DistancePp(rrow1, ccol1, row2, col2);
            if (length == 0)
                length = -1;

            dr = (centerRow - rrow1) / length;
            dc = (centerCol - ccol1) / length;

            halfHW = headWidth / 2;
            rowP1 = rrow1 + (length - headLength) * dr + halfHW * dc;
            rowP2 = rrow1 + (length - headLength) * dr - halfHW * dc;
            colP1 = ccol1 + (length - headLength) * dc - halfHW * dr;
            colP2 = ccol1 + (length - headLength) * dc + halfHW * dr;

            if (length == -1)
                arrowHandleXLD.GenContourPolygonXld(rrow1, ccol1);
            else
                arrowHandleXLD.GenContourPolygonXld(new HTuple(new double[] { centerRow, rowP1, rowP2, centerRow }),
                                                    new HTuple(new double[] { centerCol, colP1, colP2, centerCol }));
            HOperatorSet.DispXld(arrowHandleXLD, hWindow);
        }


    }
}
