using System;
using System.ComponentModel;

namespace McLaser.Device
{
    /// <summary>
    /// 激光器设备接口
    /// 定义激光器设备的基本操作和属性
    /// </summary>
    public interface ILaser : IDevice
    {
        #region 属性

        /// <summary>
        /// 激光器功率(0-100%)
        /// </summary>
        double Power { get; set; }

        /// <summary>
        /// 激光器频率(Hz)
        /// </summary>
        double Frequency { get; set; }

        /// <summary>
        /// 脉冲宽度(μs)
        /// </summary>
        double PulseWidth { get; set; }

        /// <summary>
        /// 是否启用激光器
        /// </summary>
        bool IsLaserEnabled { get; set; }

        /// <summary>
        /// 激光器类型
        /// </summary>
        LaserType LaserType { get; }

        /// <summary>
        /// 最大功率(W)
        /// </summary>
        double MaxPower { get; set; }

        /// <summary>
        /// 最小功率(W)
        /// </summary>
        double MinPower { get; }

        /// <summary>
        /// 最大频率(Hz)
        /// </summary>
        double MaxFrequency { get; }

        /// <summary>
        /// 最小频率(Hz)
        /// </summary>
        double MinFrequency { get; }

        /// <summary>
        /// 激光器状态
        /// </summary>
        LaserStatus LaserStatus { get; }

        #endregion

        #region 方法

        /// <summary>
        /// 打开激光器
        /// </summary>
        /// <returns>是否成功</returns>
        bool LaserOn();

        /// <summary>
        /// 关闭激光器
        /// </summary>
        /// <returns>是否成功</returns>
        bool LaserOff();

        /// <summary>
        /// 设置激光器功率
        /// </summary>
        /// <param name="power">功率值(0-100%)</param>
        /// <returns>是否成功</returns>
        bool SetPower(double power);

        /// <summary>
        /// 获取激光器功率
        /// </summary>
        /// <param name="power">功率值</param>
        /// <returns>是否成功</returns>
        bool GetPower(ref double power);

        /// <summary>
        /// 设置激光器频率
        /// </summary>
        /// <param name="frequency">频率值(Hz)</param>
        /// <returns>是否成功</returns>
        bool SetFrequency(double frequency);

        /// <summary>
        /// 获取激光器频率
        /// </summary>
        /// <param name="frequency">频率值</param>
        /// <returns>是否成功</returns>
        bool GetFrequency(ref double frequency);

        /// <summary>
        /// 设置脉冲宽度
        /// </summary>
        /// <param name="pulseWidth">脉冲宽度(μs)</param>
        /// <returns>是否成功</returns>
        bool SetPulseWidth(double pulseWidth);

        /// <summary>
        /// 获取脉冲宽度
        /// </summary>
        /// <param name="pulseWidth">脉冲宽度</param>
        /// <returns>是否成功</returns>
        bool GetPulseWidth(ref double pulseWidth);

        /// <summary>
        /// 设置激光器模式
        /// </summary>
        /// <param name="mode">激光器模式</param>
        /// <returns>是否成功</returns>
        bool SetLaserMode(LaserMode mode);

        /// <summary>
        /// 获取激光器模式
        /// </summary>
        /// <param name="mode">激光器模式</param>
        /// <returns>是否成功</returns>
        bool GetLaserMode(ref LaserMode mode);

        /// <summary>
        /// 检查激光器是否准备就绪
        /// </summary>
        /// <returns>是否准备就绪</returns>
        bool IsReady();

        /// <summary>
        /// 检查激光器是否有错误
        /// </summary>
        /// <returns>是否有错误</returns>
        bool HasError();

        /// <summary>
        /// 获取激光器错误信息
        /// </summary>
        /// <returns>错误信息</returns>
        string GetErrorMessage();

        /// <summary>
        /// 清除激光器错误
        /// </summary>
        /// <returns>是否成功</returns>
        bool ClearError();

        /// <summary>
        /// 激光器自检
        /// </summary>
        /// <returns>自检结果</returns>
        bool SelfTest();

        /// <summary>
        /// 获取激光器温度
        /// </summary>
        /// <param name="temperature">温度值(℃)</param>
        /// <returns>是否成功</returns>
        bool GetTemperature(ref double temperature);

        /// <summary>
        /// 获取激光器工作时间
        /// </summary>
        /// <param name="workingHours">工作时间(小时)</param>
        /// <returns>是否成功</returns>
        bool GetWorkingHours(ref double workingHours);

        #endregion

        #region 事件

        /// <summary>
        /// 激光器状态变化事件
        /// </summary>
        event EventHandler<LaserStatusChangedEventArgs> LaserStatusChanged;

        /// <summary>
        /// 激光器错误事件
        /// </summary>
        event EventHandler<LaserErrorEventArgs> LaserError;

        /// <summary>
        /// 激光器功率变化事件
        /// </summary>
        event EventHandler<LaserPowerChangedEventArgs> PowerChanged;

        #endregion
    }

    /// <summary>
    /// 激光器类型枚举
    /// </summary>
    public enum LaserType
    {
        /// <summary>
        /// 未知类型
        /// </summary>
        Unknown,

        /// <summary>
        /// 连续激光器
        /// </summary>
        Continuous,

        /// <summary>
        /// 脉冲激光器
        /// </summary>
        Pulsed,

        /// <summary>
        /// 二极管激光器
        /// </summary>
        Diode,

        /// <summary>
        /// 光纤激光器
        /// </summary>
        Fiber,

        /// <summary>
        /// CO2激光器
        /// </summary>
        CO2,

        /// <summary>
        /// YAG激光器
        /// </summary>
        YAG
    }

    /// <summary>
    /// 激光器状态枚举
    /// </summary>
    public enum LaserStatus
    {
        /// <summary>
        /// 未知状态
        /// </summary>
        Unknown,

        /// <summary>
        /// 离线
        /// </summary>
        Offline,

        /// <summary>
        /// 初始化中
        /// </summary>
        Initializing,

        /// <summary>
        /// 准备就绪
        /// </summary>
        Ready,

        /// <summary>
        /// 激光开启
        /// </summary>
        LaserOn,

        /// <summary>
        /// 激光关闭
        /// </summary>
        LaserOff,

        /// <summary>
        /// 错误状态
        /// </summary>
        Error,

        /// <summary>
        /// 警告状态
        /// </summary>
        Warning,

        /// <summary>
        /// 维护模式
        /// </summary>
        Maintenance
    }

    /// <summary>
    /// 激光器模式枚举
    /// </summary>
    public enum LaserMode
    {
        /// <summary>
        /// 连续模式
        /// </summary>
        Continuous,

        /// <summary>
        /// 脉冲模式
        /// </summary>
        Pulsed,

        /// <summary>
        /// 调制模式
        /// </summary>
        Modulated,

        /// <summary>
        /// 外部触发模式
        /// </summary>
        ExternalTrigger
    }

    /// <summary>
    /// 激光器状态变化事件参数
    /// </summary>
    public class LaserStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 旧状态
        /// </summary>
        public LaserStatus OldStatus { get; set; }

        /// <summary>
        /// 新状态
        /// </summary>
        public LaserStatus NewStatus { get; set; }

        /// <summary>
        /// 变化时间
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="oldStatus">旧状态</param>
        /// <param name="newStatus">新状态</param>
        public LaserStatusChangedEventArgs(LaserStatus oldStatus, LaserStatus newStatus)
        {
            OldStatus = oldStatus;
            NewStatus = newStatus;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 激光器错误事件参数
    /// </summary>
    public class LaserErrorEventArgs : EventArgs
    {
        /// <summary>
        /// 错误代码
        /// </summary>
        public int ErrorCode { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 错误时间
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="errorCode">错误代码</param>
        /// <param name="errorMessage">错误信息</param>
        public LaserErrorEventArgs(int errorCode, string errorMessage)
        {
            ErrorCode = errorCode;
            ErrorMessage = errorMessage;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 激光器功率变化事件参数
    /// </summary>
    public class LaserPowerChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 旧功率值
        /// </summary>
        public double OldPower { get; set; }

        /// <summary>
        /// 新功率值
        /// </summary>
        public double NewPower { get; set; }

        /// <summary>
        /// 变化时间
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="oldPower">旧功率值</param>
        /// <param name="newPower">新功率值</param>
        public LaserPowerChangedEventArgs(double oldPower, double newPower)
        {
            OldPower = oldPower;
            NewPower = newPower;
            Timestamp = DateTime.Now;
        }
    }
}
