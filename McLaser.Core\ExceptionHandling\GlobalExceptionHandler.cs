using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace McLaser.Core.ExceptionHandling
{
    /// <summary>
    /// 全局异常处理器实现
    /// 负责全局异常的捕获和处理
    /// </summary>
    public class GlobalExceptionHandler : IGlobalExceptionHandler, IDisposable
    {
        #region 字段

        private readonly ConcurrentDictionary<string, IExceptionHandler> _handlers = new ConcurrentDictionary<string, IExceptionHandler>();
        private readonly ExceptionHandlingStatistics _statistics = new ExceptionHandlingStatistics();
        private readonly object _lockObject = new object();
        private bool _isEnabled = false;
        private bool _disposed = false;

        #endregion

        #region 属性

        /// <summary>
        /// 是否已启用
        /// </summary>
        public bool IsEnabled => _isEnabled;

        /// <summary>
        /// 异常处理策略
        /// </summary>
        public ExceptionHandlingPolicy Policy { get; set; } = new ExceptionHandlingPolicy();

        /// <summary>
        /// 已注册的处理器列表
        /// </summary>
        public IReadOnlyList<IExceptionHandler> RegisteredHandlers => _handlers.Values.ToList();

        #endregion

        #region 事件

        /// <summary>
        /// 异常处理事件
        /// </summary>
        public event EventHandler<ExceptionHandledEventArgs>? ExceptionHandled;

        /// <summary>
        /// 未处理异常事件
        /// </summary>
        public event EventHandler<UnhandledExceptionEventArgs>? UnhandledException;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化全局异常处理器
        /// </summary>
        public GlobalExceptionHandler()
        {
            RegisterDefaultHandlers();
        }

        /// <summary>
        /// 初始化全局异常处理器
        /// </summary>
        /// <param name="policy">异常处理策略</param>
        public GlobalExceptionHandler(ExceptionHandlingPolicy policy) : this()
        {
            Policy = policy ?? throw new ArgumentNullException(nameof(policy));
        }

        #endregion

        #region 启动和停止

        /// <summary>
        /// 启动全局异常处理
        /// </summary>
        public void Start()
        {
            if (_isEnabled)
                return;

            lock (_lockObject)
            {
                if (_isEnabled)
                    return;

                try
                {
                    // 注册应用程序域未处理异常事件
                    AppDomain.CurrentDomain.UnhandledException += OnAppDomainUnhandledException;

                    // 注册任务调度器未观察到的任务异常事件
                    TaskScheduler.UnobservedTaskException += OnUnobservedTaskException;

                    _isEnabled = true;
                    _statistics.StartTime = DateTime.Now;
                }
                catch (Exception ex)
                {
                    // 记录启动失败的异常
                    Console.WriteLine($"启动全局异常处理器失败: {ex.Message}");
                    throw;
                }
            }
        }

        /// <summary>
        /// 停止全局异常处理
        /// </summary>
        public void Stop()
        {
            if (!_isEnabled)
                return;

            lock (_lockObject)
            {
                if (!_isEnabled)
                    return;

                try
                {
                    // 注销事件处理器
                    AppDomain.CurrentDomain.UnhandledException -= OnAppDomainUnhandledException;
                    TaskScheduler.UnobservedTaskException -= OnUnobservedTaskException;

                    _isEnabled = false;
                }
                catch (Exception ex)
                {
                    // 记录停止失败的异常
                    Console.WriteLine($"停止全局异常处理器失败: {ex.Message}");
                }
            }
        }

        #endregion

        #region 处理器管理

        /// <summary>
        /// 注册异常处理器
        /// </summary>
        /// <param name="handler">异常处理器</param>
        public void RegisterHandler(IExceptionHandler handler)
        {
            if (handler == null)
                throw new ArgumentNullException(nameof(handler));

            var key = $"{handler.Name}_{handler.GetHashCode()}";
            _handlers.TryAdd(key, handler);
        }

        /// <summary>
        /// 注册异常处理器
        /// </summary>
        /// <typeparam name="T">异常类型</typeparam>
        /// <param name="handler">异常处理器</param>
        public void RegisterHandler<T>(IExceptionHandler<T> handler) where T : Exception
        {
            RegisterHandler((IExceptionHandler)handler);
        }

        /// <summary>
        /// 注册异常处理器
        /// </summary>
        /// <typeparam name="T">异常类型</typeparam>
        /// <param name="handleFunc">处理函数</param>
        /// <param name="name">处理器名称</param>
        /// <param name="priority">优先级</param>
        public void RegisterHandler<T>(Func<T, ExceptionContext, ExceptionHandlingResult> handleFunc, string name = "", int priority = 0) where T : Exception
        {
            if (handleFunc == null)
                throw new ArgumentNullException(nameof(handleFunc));

            var handler = new FuncExceptionHandler<T>(handleFunc, name.IsNullOrEmpty() ? typeof(T).Name : name, priority);
            RegisterHandler(handler);
        }

        /// <summary>
        /// 注册异步异常处理器
        /// </summary>
        /// <typeparam name="T">异常类型</typeparam>
        /// <param name="handleFunc">异步处理函数</param>
        /// <param name="name">处理器名称</param>
        /// <param name="priority">优先级</param>
        public void RegisterAsyncHandler<T>(Func<T, ExceptionContext, CancellationToken, Task<ExceptionHandlingResult>> handleFunc, string name = "", int priority = 0) where T : Exception
        {
            if (handleFunc == null)
                throw new ArgumentNullException(nameof(handleFunc));

            var handler = new AsyncFuncExceptionHandler<T>(handleFunc, name.IsNullOrEmpty() ? typeof(T).Name : name, priority);
            RegisterHandler(handler);
        }

        /// <summary>
        /// 移除异常处理器
        /// </summary>
        /// <param name="handler">异常处理器</param>
        /// <returns>是否成功移除</returns>
        public bool RemoveHandler(IExceptionHandler handler)
        {
            if (handler == null)
                return false;

            var key = _handlers.FirstOrDefault(kvp => kvp.Value == handler).Key;
            return !string.IsNullOrEmpty(key) && _handlers.TryRemove(key, out _);
        }

        /// <summary>
        /// 移除异常处理器
        /// </summary>
        /// <param name="name">处理器名称</param>
        /// <returns>是否成功移除</returns>
        public bool RemoveHandler(string name)
        {
            if (string.IsNullOrEmpty(name))
                return false;

            var keysToRemove = _handlers.Where(kvp => kvp.Value.Name == name).Select(kvp => kvp.Key).ToList();
            var removed = 0;

            foreach (var key in keysToRemove)
            {
                if (_handlers.TryRemove(key, out _))
                    removed++;
            }

            return removed > 0;
        }

        /// <summary>
        /// 移除指定类型的所有处理器
        /// </summary>
        /// <typeparam name="T">异常类型</typeparam>
        /// <returns>移除的处理器数量</returns>
        public int RemoveHandlers<T>() where T : Exception
        {
            var exceptionType = typeof(T);
            var keysToRemove = _handlers.Where(kvp => 
                kvp.Value.SupportedExceptionTypes.Contains(exceptionType) ||
                kvp.Value.SupportedExceptionTypes.Any(t => t.IsAssignableFrom(exceptionType))
            ).Select(kvp => kvp.Key).ToList();

            var removed = 0;
            foreach (var key in keysToRemove)
            {
                if (_handlers.TryRemove(key, out _))
                    removed++;
            }

            return removed;
        }

        /// <summary>
        /// 清空所有处理器
        /// </summary>
        public void ClearHandlers()
        {
            _handlers.Clear();
        }

        #endregion

        #region 异常处理

        /// <summary>
        /// 处理异常
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="context">异常上下文</param>
        /// <returns>处理结果</returns>
        public ExceptionHandlingResult HandleException(Exception exception, ExceptionContext? context = null)
        {
            if (exception == null)
                throw new ArgumentNullException(nameof(exception));

            context ??= ExceptionContext.Create();
            var stopwatch = Stopwatch.StartNew();

            try
            {
                // 更新统计信息
                _statistics.TotalExceptions++;
                UpdateExceptionStatistics(exception, context);

                // 检查是否应该忽略此异常
                if (Policy.ShouldIgnore(exception.GetType()))
                {
                    return ExceptionHandlingResult.NotHandled("异常类型在忽略列表中");
                }

                // 获取适用的处理器
                var handlers = GetHandlers(exception.GetType());
                if (handlers.Count == 0)
                {
                    _statistics.UnhandledException++;
                    OnUnhandledException(exception, context);
                    return ExceptionHandlingResult.NotHandled("未找到适用的处理器");
                }

                // 按优先级排序处理器
                var sortedHandlers = handlers.OrderByDescending(h => h.Priority).ToList();

                ExceptionHandlingResult? result = null;
                foreach (var handler in sortedHandlers)
                {
                    try
                    {
                        if (handler.CanHandle(exception))
                        {
                            result = handler.Handle(exception, context);
                            if (result.IsHandled)
                            {
                                result.HandlerName = handler.Name;
                                break;
                            }
                        }
                    }
                    catch (Exception handlerException)
                    {
                        // 处理器本身发生异常
                        Console.WriteLine($"异常处理器 {handler.Name} 发生异常: {handlerException.Message}");
                    }
                }

                result ??= ExceptionHandlingResult.NotHandled("所有处理器都无法处理此异常");

                // 更新处理结果
                stopwatch.Stop();
                result.ProcessingTime = stopwatch.ElapsedMilliseconds;

                // 更新统计信息
                if (result.IsHandled)
                {
                    _statistics.HandledException++;
                    UpdateHandlerStatistics(result.HandlerName);
                    UpdateProcessingTimeStatistics(result.ProcessingTime);
                }
                else
                {
                    _statistics.UnhandledException++;
                }

                // 触发事件
                if (result.IsHandled)
                {
                    OnExceptionHandled(exception, context, result);
                }
                else
                {
                    OnUnhandledException(exception, context);
                }

                return result;
            }
            catch (Exception processingException)
            {
                // 处理过程中发生异常
                stopwatch.Stop();
                _statistics.UnhandledException++;
                
                var errorResult = ExceptionHandlingResult.NotHandled($"处理过程中发生异常: {processingException.Message}");
                errorResult.ProcessingTime = stopwatch.ElapsedMilliseconds;
                
                OnUnhandledException(exception, context);
                return errorResult;
            }
        }

        /// <summary>
        /// 异步处理异常
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="context">异常上下文</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>处理任务</returns>
        public async Task<ExceptionHandlingResult> HandleExceptionAsync(Exception exception, ExceptionContext? context = null, CancellationToken cancellationToken = default)
        {
            if (exception == null)
                throw new ArgumentNullException(nameof(exception));

            context ??= ExceptionContext.Create();
            var stopwatch = Stopwatch.StartNew();

            try
            {
                // 更新统计信息
                _statistics.TotalExceptions++;
                UpdateExceptionStatistics(exception, context);

                // 检查是否应该忽略此异常
                if (Policy.ShouldIgnore(exception.GetType()))
                {
                    return ExceptionHandlingResult.NotHandled("异常类型在忽略列表中");
                }

                // 获取适用的处理器
                var handlers = GetHandlers(exception.GetType());
                if (handlers.Count == 0)
                {
                    _statistics.UnhandledException++;
                    OnUnhandledException(exception, context);
                    return ExceptionHandlingResult.NotHandled("未找到适用的处理器");
                }

                // 按优先级排序处理器
                var sortedHandlers = handlers.OrderByDescending(h => h.Priority).ToList();

                ExceptionHandlingResult? result = null;
                foreach (var handler in sortedHandlers)
                {
                    try
                    {
                        if (handler.CanHandle(exception))
                        {
                            result = await handler.HandleAsync(exception, context, cancellationToken);
                            if (result.IsHandled)
                            {
                                result.HandlerName = handler.Name;
                                break;
                            }
                        }
                    }
                    catch (Exception handlerException)
                    {
                        // 处理器本身发生异常
                        Console.WriteLine($"异常处理器 {handler.Name} 发生异常: {handlerException.Message}");
                    }
                }

                result ??= ExceptionHandlingResult.NotHandled("所有处理器都无法处理此异常");

                // 更新处理结果
                stopwatch.Stop();
                result.ProcessingTime = stopwatch.ElapsedMilliseconds;

                // 更新统计信息
                if (result.IsHandled)
                {
                    _statistics.HandledException++;
                    UpdateHandlerStatistics(result.HandlerName);
                    UpdateProcessingTimeStatistics(result.ProcessingTime);
                }
                else
                {
                    _statistics.UnhandledException++;
                }

                // 触发事件
                if (result.IsHandled)
                {
                    OnExceptionHandled(exception, context, result);
                }
                else
                {
                    OnUnhandledException(exception, context);
                }

                return result;
            }
            catch (Exception processingException)
            {
                // 处理过程中发生异常
                stopwatch.Stop();
                _statistics.UnhandledException++;
                
                var errorResult = ExceptionHandlingResult.NotHandled($"处理过程中发生异常: {processingException.Message}");
                errorResult.ProcessingTime = stopwatch.ElapsedMilliseconds;
                
                OnUnhandledException(exception, context);
                return errorResult;
            }
        }

        #endregion

        #region 查询方法

        /// <summary>
        /// 获取异常处理器
        /// </summary>
        /// <param name="exceptionType">异常类型</param>
        /// <returns>处理器列表</returns>
        public IList<IExceptionHandler> GetHandlers(Type exceptionType)
        {
            return _handlers.Values
                .Where(h => h.SupportedExceptionTypes.Contains(exceptionType) ||
                           h.SupportedExceptionTypes.Any(t => t.IsAssignableFrom(exceptionType)) ||
                           h.CanHandle(Activator.CreateInstance(exceptionType) as Exception ?? new Exception()))
                .ToList();
        }

        /// <summary>
        /// 获取异常处理器
        /// </summary>
        /// <typeparam name="T">异常类型</typeparam>
        /// <returns>处理器列表</returns>
        public IList<IExceptionHandler> GetHandlers<T>() where T : Exception
        {
            return GetHandlers(typeof(T));
        }

        #endregion

        #region 统计和日志

        /// <summary>
        /// 获取异常处理统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public ExceptionHandlingStatistics GetStatistics()
        {
            _statistics.LastUpdated = DateTime.Now;
            return _statistics;
        }

        /// <summary>
        /// 重置统计信息
        /// </summary>
        public void ResetStatistics()
        {
            _statistics.Reset();
        }

        /// <summary>
        /// 导出异常日志
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>导出任务</returns>
        public async Task ExportExceptionLogAsync(string filePath, DateTime? startTime = null, DateTime? endTime = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var logData = new
                {
                    ExportTime = DateTime.Now,
                    StartTime = startTime,
                    EndTime = endTime,
                    Statistics = _statistics,
                    Policy = Policy,
                    RegisteredHandlers = RegisteredHandlers.Select(h => new
                    {
                        h.Name,
                        h.Priority,
                        SupportedTypes = h.SupportedExceptionTypes.Select(t => t.FullName).ToArray()
                    }).ToArray()
                };

                var json = JsonConvert.SerializeObject(logData, Formatting.Indented);
                File.WriteAllText(filePath, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"导出异常日志失败: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region 私有方法

        private void RegisterDefaultHandlers()
        {
            // 注册默认的异常处理器
            RegisterHandler<ArgumentException>((ex, ctx) =>
                ExceptionHandlingResult.Handled($"参数错误: {ex.Message}", "DefaultArgumentExceptionHandler"));

            RegisterHandler<InvalidOperationException>((ex, ctx) =>
                ExceptionHandlingResult.Handled($"操作无效: {ex.Message}", "DefaultInvalidOperationExceptionHandler"));

            RegisterHandler<NotImplementedException>((ex, ctx) =>
                ExceptionHandlingResult.Handled("功能尚未实现", "DefaultNotImplementedExceptionHandler"));

            RegisterHandler<UnauthorizedAccessException>((ex, ctx) =>
                ExceptionHandlingResult.Handled("访问被拒绝", "DefaultUnauthorizedAccessExceptionHandler"));

            RegisterHandler<FileNotFoundException>((ex, ctx) =>
                ExceptionHandlingResult.Handled($"文件未找到: {ex.FileName}", "DefaultFileNotFoundExceptionHandler"));

            RegisterHandler<DirectoryNotFoundException>((ex, ctx) =>
                ExceptionHandlingResult.Handled("目录未找到", "DefaultDirectoryNotFoundExceptionHandler"));

            RegisterHandler<TimeoutException>((ex, ctx) =>
                ExceptionHandlingResult.Handled("操作超时", "DefaultTimeoutExceptionHandler"));

            RegisterHandler<OutOfMemoryException>((ex, ctx) =>
                ExceptionHandlingResult.Terminate("内存不足，应用程序将终止", "DefaultOutOfMemoryExceptionHandler"));
        }

        private void UpdateExceptionStatistics(Exception exception, ExceptionContext context)
        {
            var exceptionType = exception.GetType().FullName ?? exception.GetType().Name;
            _statistics.ExceptionTypeCount.TryGetValue(exceptionType, out var count);
            _statistics.ExceptionTypeCount[exceptionType] = count + 1;

            _statistics.SeverityCount.TryGetValue(context.Severity, out var severityCount);
            _statistics.SeverityCount[context.Severity] = severityCount + 1;

            _statistics.CategoryCount.TryGetValue(context.Category, out var categoryCount);
            _statistics.CategoryCount[context.Category] = categoryCount + 1;
        }

        private void UpdateHandlerStatistics(string? handlerName)
        {
            if (!string.IsNullOrEmpty(handlerName))
            {
                _statistics.HandlerCount.TryGetValue(handlerName, out var count);
                _statistics.HandlerCount[handlerName] = count + 1;
            }
        }

        private void UpdateProcessingTimeStatistics(long processingTime)
        {
            if (processingTime > _statistics.MaxHandlingTime)
                _statistics.MaxHandlingTime = processingTime;

            if (processingTime < _statistics.MinHandlingTime)
                _statistics.MinHandlingTime = processingTime;

            var totalHandled = _statistics.HandledException;
            _statistics.AverageHandlingTime = (_statistics.AverageHandlingTime * (totalHandled - 1) + processingTime) / totalHandled;
        }

        #endregion

        #region 事件处理

        private void OnAppDomainUnhandledException(object sender, System.UnhandledExceptionEventArgs e)
        {
            if (e.ExceptionObject is Exception exception)
            {
                var context = ExceptionContext.Create("AppDomain", "UnhandledException");
                context.Severity = ExceptionSeverity.Fatal;
                context.IsCriticalOperation = true;

                try
                {
                    HandleException(exception, context);
                }
                catch
                {
                    // 最后的异常处理失败，只能记录到控制台
                    Console.WriteLine($"致命异常: {exception}");
                }
            }
        }

        private void OnUnobservedTaskException(object? sender, UnobservedTaskExceptionEventArgs e)
        {
            var context = ExceptionContext.Create("TaskScheduler", "UnobservedTaskException");
            context.Severity = ExceptionSeverity.Error;

            try
            {
                var result = HandleException(e.Exception, context);
                if (result.IsHandled)
                {
                    e.SetObserved(); // 标记异常已被观察
                }
            }
            catch
            {
                // 异常处理失败
                Console.WriteLine($"未观察到的任务异常: {e.Exception}");
            }
        }

        private void OnExceptionHandled(Exception exception, ExceptionContext context, ExceptionHandlingResult result)
        {
            ExceptionHandled?.Invoke(this, new ExceptionHandledEventArgs(exception, context, result));
        }

        private void OnUnhandledException(Exception exception, ExceptionContext context)
        {
            UnhandledException?.Invoke(this, new UnhandledExceptionEventArgs(exception, context));
        }

        #endregion

        #region IDisposable

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否正在释放</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                try
                {
                    Stop();
                    ClearHandlers();
                }
                catch
                {
                    // 忽略释放时的异常
                }

                _disposed = true;
            }
        }

        /// <summary>
        /// 析构函数
        /// </summary>
        ~GlobalExceptionHandler()
        {
            Dispose(false);
        }

        #endregion
    }

    #region 辅助类

    /// <summary>
    /// 函数式异常处理器
    /// </summary>
    /// <typeparam name="T">异常类型</typeparam>
    internal class FuncExceptionHandler<T> : IExceptionHandler<T> where T : Exception
    {
        private readonly Func<T, ExceptionContext, ExceptionHandlingResult> _handleFunc;

        public string Name { get; }
        public int Priority { get; }
        public Type[] SupportedExceptionTypes { get; }

        public FuncExceptionHandler(Func<T, ExceptionContext, ExceptionHandlingResult> handleFunc, string name, int priority)
        {
            _handleFunc = handleFunc ?? throw new ArgumentNullException(nameof(handleFunc));
            Name = name;
            Priority = priority;
            SupportedExceptionTypes = new[] { typeof(T) };
        }

        public bool CanHandle(Exception exception)
        {
            return exception is T;
        }

        public ExceptionHandlingResult Handle(Exception exception, ExceptionContext context)
        {
            if (exception is T typedException)
            {
                return _handleFunc(typedException, context);
            }
            return ExceptionHandlingResult.NotHandled("异常类型不匹配");
        }

        public ExceptionHandlingResult Handle(T exception, ExceptionContext context)
        {
            return _handleFunc(exception, context);
        }

        public Task<ExceptionHandlingResult> HandleAsync(Exception exception, ExceptionContext context, CancellationToken cancellationToken = default)
        {
            return Task.FromResult(Handle(exception, context));
        }

        public Task<ExceptionHandlingResult> HandleAsync(T exception, ExceptionContext context, CancellationToken cancellationToken = default)
        {
            return Task.FromResult(Handle(exception, context));
        }
    }

    /// <summary>
    /// 异步函数式异常处理器
    /// </summary>
    /// <typeparam name="T">异常类型</typeparam>
    internal class AsyncFuncExceptionHandler<T> : IExceptionHandler<T> where T : Exception
    {
        private readonly Func<T, ExceptionContext, CancellationToken, Task<ExceptionHandlingResult>> _handleFunc;

        public string Name { get; }
        public int Priority { get; }
        public Type[] SupportedExceptionTypes { get; }

        public AsyncFuncExceptionHandler(Func<T, ExceptionContext, CancellationToken, Task<ExceptionHandlingResult>> handleFunc, string name, int priority)
        {
            _handleFunc = handleFunc ?? throw new ArgumentNullException(nameof(handleFunc));
            Name = name;
            Priority = priority;
            SupportedExceptionTypes = new[] { typeof(T) };
        }

        public bool CanHandle(Exception exception)
        {
            return exception is T;
        }

        public ExceptionHandlingResult Handle(Exception exception, ExceptionContext context)
        {
            if (exception is T typedException)
            {
                return HandleAsync(typedException, context).GetAwaiter().GetResult();
            }
            return ExceptionHandlingResult.NotHandled("异常类型不匹配");
        }

        public ExceptionHandlingResult Handle(T exception, ExceptionContext context)
        {
            return HandleAsync(exception, context).GetAwaiter().GetResult();
        }

        public async Task<ExceptionHandlingResult> HandleAsync(Exception exception, ExceptionContext context, CancellationToken cancellationToken = default)
        {
            if (exception is T typedException)
            {
                return await _handleFunc(typedException, context, cancellationToken);
            }
            return ExceptionHandlingResult.NotHandled("异常类型不匹配");
        }

        public async Task<ExceptionHandlingResult> HandleAsync(T exception, ExceptionContext context, CancellationToken cancellationToken = default)
        {
            return await _handleFunc(exception, context, cancellationToken);
        }
    }

    #endregion

    #region 扩展方法

    /// <summary>
    /// 字符串扩展方法
    /// </summary>
    internal static class StringExtensions
    {
        /// <summary>
        /// 检查字符串是否为空或null
        /// </summary>
        /// <param name="value">字符串值</param>
        /// <returns>是否为空</returns>
        public static bool IsNullOrEmpty(this string? value)
        {
            return string.IsNullOrEmpty(value);
        }
    }

    #endregion
}
