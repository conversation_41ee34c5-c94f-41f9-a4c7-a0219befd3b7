using System;
using System.Diagnostics;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using McLaser.Core.Framework.Logging;

namespace McLaser.Core.Framework.Serialization
{
    /// <summary>
    /// 默认JSON序列化服务实现
    /// 基于Newtonsoft.Json提供高性能JSON处理功能
    /// </summary>
    public class DefaultJsonService : IJsonService
    {
        #region 字段和属性

        private readonly ILogger? _logger;
        private JsonSerializerSettings _settings;
        private bool _formatOutput;
        private bool _includeNullValues;

        /// <summary>
        /// 是否格式化输出
        /// </summary>
        public bool FormatOutput
        {
            get => _formatOutput;
            set => _formatOutput = value;
        }

        /// <summary>
        /// 是否包含空值
        /// </summary>
        public bool IncludeNullValues
        {
            get => _includeNullValues;
            set => _includeNullValues = value;
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志服务（可选）</param>
        public DefaultJsonService(ILogger? logger = null)
        {
            _logger = logger;
            _formatOutput = true;
            _includeNullValues = true;

            // 初始化JSON序列化设置
            InitializeSettings();
        }

        /// <summary>
        /// 初始化JSON序列化设置
        /// </summary>
        private void InitializeSettings()
        {
            _settings = new JsonSerializerSettings
            {
                // 基础设置
                NullValueHandling = _includeNullValues ? NullValueHandling.Include : NullValueHandling.Ignore,
                Formatting = _formatOutput ? Formatting.Indented : Formatting.None,

                // 日期处理
                DateFormatHandling = DateFormatHandling.IsoDateFormat,
                DateTimeZoneHandling = DateTimeZoneHandling.Local,

                // 类型处理
                TypeNameHandling = TypeNameHandling.None,
                DefaultValueHandling = DefaultValueHandling.Include,

                // 错误处理
                MissingMemberHandling = MissingMemberHandling.Ignore,
                Error = HandleSerializationError,

                // 契约解析器
                ContractResolver = new CamelCasePropertyNamesContractResolver(),

                // 性能优化
                CheckAdditionalContent = false,
                ConstructorHandling = ConstructorHandling.AllowNonPublicDefaultConstructor
            };
        }

        /// <summary>
        /// 处理序列化错误
        /// </summary>
        private void HandleSerializationError(object? sender, Newtonsoft.Json.Serialization.ErrorEventArgs e)
        {
            _logger?.LogError($"JSON序列化错误: {e.ErrorContext.Error.Message}", e.ErrorContext.Error);

            // 触发序列化错误事件
            SerializationError?.Invoke(this, new JsonErrorEventArgs(
                "SerializationError",
                e.ErrorContext.Error,
                e.ErrorContext.OriginalObject));

            // 标记错误已处理，继续序列化
            e.ErrorContext.Handled = true;
        }

        #endregion

        #region 序列化配置

        /// <summary>
        /// 设置序列化选项
        /// </summary>
        /// <param name="formatOutput">是否格式化输出</param>
        /// <param name="includeNullValues">是否包含空值</param>
        public void ConfigureSettings(bool formatOutput = true, bool includeNullValues = true)
        {
            _formatOutput = formatOutput;
            _includeNullValues = includeNullValues;

            // 更新设置
            _settings.Formatting = _formatOutput ? Formatting.Indented : Formatting.None;
            _settings.NullValueHandling = _includeNullValues ? NullValueHandling.Include : NullValueHandling.Ignore;

            _logger?.LogInfo("JSON序列化设置已更新");
        }

        /// <summary>
        /// 重置为默认配置
        /// </summary>
        public void ResetToDefaultSettings()
        {
            _formatOutput = true;
            _includeNullValues = true;
            InitializeSettings();
            _logger?.LogInfo("JSON序列化设置已重置为默认值");
        }

        #endregion

        #region 基础序列化

        /// <summary>
        /// 将对象序列化为JSON字符串
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要序列化的对象</param>
        /// <returns>JSON字符串</returns>
        public string Serialize<T>(T obj)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                var json = JsonConvert.SerializeObject(obj, _settings);
                stopwatch.Stop();

                _logger?.LogInfo($"对象序列化完成，类型: {typeof(T).Name}, 耗时: {stopwatch.ElapsedMilliseconds}ms");

                // 触发序列化完成事件
                SerializationCompleted?.Invoke(this, new JsonSerializationEventArgs(typeof(T), json, stopwatch.ElapsedMilliseconds));

                return json;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger?.LogError($"对象序列化失败，类型: {typeof(T).Name}", ex);

                // 触发序列化错误事件
                SerializationError?.Invoke(this, new JsonErrorEventArgs("Serialize", ex, obj));

                throw new InvalidOperationException($"序列化对象失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 将对象序列化为格式化的JSON字符串
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要序列化的对象</param>
        /// <returns>格式化的JSON字符串</returns>
        public string SerializeFormatted<T>(T obj)
        {
            var json = Serialize(obj);
            return _formatOutput ? FormatJson(json) : json;
        }

        /// <summary>
        /// 将JSON字符串反序列化为对象
        /// </summary>
        /// <typeparam name="T">目标对象类型</typeparam>
        /// <param name="json">JSON字符串</param>
        /// <returns>反序列化的对象</returns>
        public T? Deserialize<T>(string json)
        {
            if (string.IsNullOrWhiteSpace(json))
                return default(T);

            var stopwatch = Stopwatch.StartNew();
            try
            {
                var result = JsonConvert.DeserializeObject<T>(json, _settings);
                stopwatch.Stop();

                _logger?.LogInfo($"JSON反序列化完成，类型: {typeof(T).Name}, 耗时: {stopwatch.ElapsedMilliseconds}ms");

                // 触发反序列化完成事件
                DeserializationCompleted?.Invoke(this, new JsonDeserializationEventArgs(typeof(T), json, result, stopwatch.ElapsedMilliseconds));

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger?.LogError($"JSON反序列化失败，类型: {typeof(T).Name}", ex);

                // 触发序列化错误事件
                SerializationError?.Invoke(this, new JsonErrorEventArgs("Deserialize", ex, json));

                throw new InvalidOperationException($"反序列化JSON失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 将JSON字符串反序列化为指定类型的对象
        /// </summary>
        /// <param name="json">JSON字符串</param>
        /// <param name="type">目标类型</param>
        /// <returns>反序列化的对象</returns>
        public object? Deserialize(string json, Type type)
        {
            if (string.IsNullOrWhiteSpace(json))
                return null;

            var stopwatch = Stopwatch.StartNew();
            try
            {
                var result = JsonConvert.DeserializeObject(json, type, _settings);
                stopwatch.Stop();

                _logger?.LogInfo($"JSON反序列化完成，类型: {type.Name}, 耗时: {stopwatch.ElapsedMilliseconds}ms");

                // 触发反序列化完成事件
                DeserializationCompleted?.Invoke(this, new JsonDeserializationEventArgs(type, json, result, stopwatch.ElapsedMilliseconds));

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger?.LogError($"JSON反序列化失败，类型: {type.Name}", ex);

                // 触发序列化错误事件
                SerializationError?.Invoke(this, new JsonErrorEventArgs("Deserialize", ex, json));

                throw new InvalidOperationException($"反序列化JSON失败: {ex.Message}", ex);
            }
        }

        #endregion

        #region 文件操作

        /// <summary>
        /// 将对象序列化并保存到文件
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要序列化的对象</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="formatted">是否格式化输出</param>
        public void SerializeToFile<T>(T obj, string filePath, bool formatted = true)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));

            try
            {
                // 确保目录存在
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var json = formatted ? SerializeFormatted(obj) : Serialize(obj);
                File.WriteAllText(filePath, json, Encoding.UTF8);
                
                _logger?.LogInfo($"对象已序列化并保存到文件: {filePath}");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"序列化到文件失败: {filePath}", ex);
                throw new InvalidOperationException($"序列化到文件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 从文件读取并反序列化为对象
        /// </summary>
        /// <typeparam name="T">目标对象类型</typeparam>
        /// <param name="filePath">文件路径</param>
        /// <returns>反序列化的对象</returns>
        public T? DeserializeFromFile<T>(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));

            if (!File.Exists(filePath))
                throw new FileNotFoundException($"文件不存在: {filePath}");

            try
            {
                var json = File.ReadAllText(filePath, Encoding.UTF8);
                var result = Deserialize<T>(json);
                
                _logger?.LogInfo($"已从文件反序列化对象: {filePath}");
                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"从文件反序列化失败: {filePath}", ex);
                throw new InvalidOperationException($"从文件反序列化失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 异步将对象序列化并保存到文件
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要序列化的对象</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="formatted">是否格式化输出</param>
        /// <returns>异步任务</returns>
        public async Task SerializeToFileAsync<T>(T obj, string filePath, bool formatted = true)
        {
            await Task.Run(() => SerializeToFile(obj, filePath, formatted));
        }

        /// <summary>
        /// 异步从文件读取并反序列化为对象
        /// </summary>
        /// <typeparam name="T">目标对象类型</typeparam>
        /// <param name="filePath">文件路径</param>
        /// <returns>反序列化的对象</returns>
        public async Task<T?> DeserializeFromFileAsync<T>(string filePath)
        {
            return await Task.Run(() => DeserializeFromFile<T>(filePath));
        }

        #endregion

        #region 验证和工具

        /// <summary>
        /// 验证JSON字符串是否有效
        /// </summary>
        /// <param name="json">JSON字符串</param>
        /// <returns>是否有效</returns>
        public bool IsValidJson(string json)
        {
            if (string.IsNullOrWhiteSpace(json))
                return false;

            try
            {
                JsonConvert.DeserializeObject(json);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 格式化JSON字符串
        /// </summary>
        /// <param name="json">原始JSON字符串</param>
        /// <returns>格式化后的JSON字符串</returns>
        public string FormatJson(string json)
        {
            if (string.IsNullOrWhiteSpace(json))
                return string.Empty;

            try
            {
                var obj = JsonConvert.DeserializeObject(json);
                return JsonConvert.SerializeObject(obj, Formatting.Indented);
            }
            catch (Exception ex)
            {
                _logger?.LogError("JSON格式化失败", ex);
                return json; // 返回原始字符串
            }
        }

        /// <summary>
        /// 压缩JSON字符串（移除空白字符）
        /// </summary>
        /// <param name="json">原始JSON字符串</param>
        /// <returns>压缩后的JSON字符串</returns>
        public string CompactJson(string json)
        {
            if (string.IsNullOrWhiteSpace(json))
                return string.Empty;

            try
            {
                var obj = JsonConvert.DeserializeObject(json);
                return JsonConvert.SerializeObject(obj, Formatting.None);
            }
            catch (Exception ex)
            {
                _logger?.LogError("JSON压缩失败", ex);
                return json; // 返回原始字符串
            }
        }

        /// <summary>
        /// 获取JSON字符串的大小（字节数）
        /// </summary>
        /// <param name="json">JSON字符串</param>
        /// <returns>字节数</returns>
        public long GetJsonSize(string json)
        {
            if (string.IsNullOrEmpty(json))
                return 0;

            return Encoding.UTF8.GetByteCount(json);
        }

        #endregion

        #region 事件

        /// <summary>
        /// 序列化完成事件
        /// </summary>
        public event EventHandler<JsonSerializationEventArgs>? SerializationCompleted;

        /// <summary>
        /// 反序列化完成事件
        /// </summary>
        public event EventHandler<JsonDeserializationEventArgs>? DeserializationCompleted;

        /// <summary>
        /// 序列化错误事件
        /// </summary>
        public event EventHandler<JsonErrorEventArgs>? SerializationError;

        #endregion
    }
}
