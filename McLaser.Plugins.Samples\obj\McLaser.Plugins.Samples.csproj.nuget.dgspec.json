{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\McLaser_V1\\McLaser.Plugins.Samples\\McLaser.Plugins.Samples.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\McLaser_V1\\McLaser.Core\\McLaser.Core.csproj": {"restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\McLaser_V1\\McLaser.Core\\McLaser.Core.csproj", "projectName": "McLaser.Core", "projectPath": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\McLaser_V1\\McLaser.Core\\McLaser.Core.csproj", "UsingMicrosoftNETSdk": false, "frameworks": {"net472": {"projectReferences": {}}}}, "frameworks": {"net472": {}}}, "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\McLaser_V1\\McLaser.Plugins.Samples\\McLaser.Plugins.Samples.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\McLaser_V1\\McLaser.Plugins.Samples\\McLaser.Plugins.Samples.csproj", "projectName": "McLaser.Plugins.Samples", "projectPath": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\McLaser_V1\\McLaser.Plugins.Samples\\McLaser.Plugins.Samples.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\McLaser_V1\\McLaser.Plugins.Samples\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\McLaser_V1\\McLaser.Core\\McLaser.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\柔性钙钛矿\\GTK\\McLaser_V1\\McLaser.Core\\McLaser.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}}}