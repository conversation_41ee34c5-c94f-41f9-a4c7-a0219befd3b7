using System.Windows;

namespace McLaser.App.Views
{
    /// <summary>
    /// 数据输入窗口
    /// 展示数据验证框架的使用
    /// </summary>
    public partial class DataInputWindow : Window
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public DataInputWindow()
        {
            // 临时解决方案：手动初始化窗口
            // 在XAML编译器修复后，应该恢复为 InitializeComponent();
            InitializeComponent();
        }

        /// <summary>
        /// 手动初始化窗口（临时解决方案）
        /// </summary>
        private void InitializeWindow()
        {
            // 基本窗口设置
            Title = "数据输入窗口";
            Width = 800;
            Height = 600;
            WindowStartupLocation = System.Windows.WindowStartupLocation.CenterScreen;

            // 设置基本内容
            Content = new System.Windows.Controls.TextBlock
            {
                Text = "数据输入窗口\n\n请等待XAML编译器修复后查看完整界面。",
                HorizontalAlignment = System.Windows.HorizontalAlignment.Center,
                VerticalAlignment = System.Windows.VerticalAlignment.Center,
                FontSize = 16,
                TextAlignment = System.Windows.TextAlignment.Center
            };
        }
    }
}
