using System;
using System.Collections.Generic;
using McLaser.Core.Framework.Configuration;
using McLaser.Core.Framework.Logging;
using McLaser.Core.Framework.Serialization;
using McLaser.Core.Framework.Styles;
using McLaser.Core.Framework.Security;
using McLaser.Core.Framework.UI;
using McLaser.Core.Framework.Services;

namespace McLaser.Core.Framework.Container
{
    /// <summary>
    /// 统一服务注册中心
    /// 集中管理所有服务的注册配置，替代MEF的自动发现机制
    /// </summary>
    public static class ServiceRegistration
    {
        /// <summary>
        /// 注册所有核心框架服务
        /// </summary>
        /// <param name="container">容器实例</param>
        public static void RegisterAllServices(IContainer container)
        {
            if (container == null)
                throw new ArgumentNullException(nameof(container));

            // 按依赖顺序注册服务
            RegisterCoreServices(container);
            RegisterFrameworkServices(container);
            RegisterUIServices(container);
            RegisterBusinessServices(container);
        }

        /// <summary>
        /// 注册核心基础服务（无依赖）
        /// </summary>
        /// <param name="container">容器实例</param>
        public static void RegisterCoreServices(IContainer container)
        {
            // 1. 配置服务（无依赖）
            //container.RegisterSingleton<IConfigurationService, DefaultConfigurationService>();

            // 2. 日志工厂（无依赖）
            container.RegisterSingleton<ILoggerFactory, DefaultLoggerFactory>();

            // 3. 日志服务（依赖ILoggerFactory）
            container.RegisterFactory<ILogger>(c =>
            {
                var factory = c.TryResolve<ILoggerFactory>();
                return factory?.CreateLogger("McLaser.Core") ?? new NullLogger();
            });
        }

        /// <summary>
        /// 注册框架服务
        /// </summary>
        /// <param name="container">容器实例</param>
        public static void RegisterFrameworkServices(IContainer container)
        {
            // JSON序列化服务
            container.RegisterFactory<IJsonService>(c =>
            {
                var logger = c.TryResolve<ILogger>();
                return new DefaultJsonService(logger);
            });

            // 样式服务
            container.RegisterFactory<IStyleService>(c =>
            {
                var logger = c.TryResolve<ILogger>();
                return new DefaultStyleService(logger);
            });

            // 用户服务
            container.RegisterFactory<IUserService>(c =>
            {
                var logger = c.TryResolve<ILogger>();
                return new DefaultUserService(logger);
            });

            // 角色服务
            container.RegisterFactory<IRoleService>(c =>
            {
                var logger = c.TryResolve<ILogger>();
                return new DefaultRoleService(logger);
            });

            // 权限服务
            container.RegisterFactory<IPermissionService>(c =>
            {
                var logger = c.TryResolve<ILogger>();
                return new DefaultPermissionService(logger);
            });
        }

        /// <summary>
        /// 注册UI服务
        /// </summary>
        /// <param name="container">容器实例</param>
        public static void RegisterUIServices(IContainer container)
        {
            // 主题服务
            container.RegisterSingleton<IThemeService, ThemeManager>();

            // 窗口管理服务
            container.RegisterSingleton<IWindowManager, WindowManager>();

            // 对话框服务
            container.RegisterSingleton<IDialogService, DefaultDialogService>();
        }

        /// <summary>
        /// 注册业务服务
        /// </summary>
        /// <param name="container">容器实例</param>
        public static void RegisterBusinessServices(IContainer container)
        {
            // 这里可以注册具体的业务服务
            // 例如：数据访问服务、业务逻辑服务等

            // 注意：数据验证服务等业务服务可以在应用程序层注册
        }

        /// <summary>
        /// 注册第三方服务
        /// </summary>
        /// <param name="container">容器实例</param>
        public static void RegisterThirdPartyServices(IContainer container)
        {
            // 这里可以注册第三方库的服务
            // 例如：HTTP客户端、缓存服务、消息队列等
        }

        /// <summary>
        /// 验证服务注册
        /// </summary>
        /// <param name="container">容器实例</param>
        /// <returns>验证结果</returns>
        public static ServiceRegistrationValidationResult ValidateRegistrations(IContainer container)
        {
            var result = new ServiceRegistrationValidationResult();

            try
            {
                // 验证核心服务
                ValidateCoreServices(container, result);
                
                // 验证框架服务
                ValidateFrameworkServices(container, result);
                
                // 验证UI服务
                ValidateUIServices(container, result);

                result.IsValid = result.Errors.Count == 0;
            }
            catch (Exception ex)
            {
                result.Errors.Add($"验证过程中发生异常: {ex.Message}");
                result.IsValid = false;
            }

            return result;
        }

        /// <summary>
        /// 验证核心服务
        /// </summary>
        private static void ValidateCoreServices(IContainer container, ServiceRegistrationValidationResult result)
        {
            // 验证配置服务
            if (!container.IsRegistered<IConfigurationService>())
            {
                result.Errors.Add("IConfigurationService 未注册");
            }

            // 验证日志服务
            if (!container.IsRegistered<ILoggerFactory>())
            {
                result.Errors.Add("ILoggerFactory 未注册");
            }

            if (!container.IsRegistered<ILogger>())
            {
                result.Errors.Add("ILogger 未注册");
            }
        }

        /// <summary>
        /// 验证框架服务
        /// </summary>
        private static void ValidateFrameworkServices(IContainer container, ServiceRegistrationValidationResult result)
        {
            // 验证JSON服务
            if (!container.IsRegistered<IJsonService>())
            {
                result.Errors.Add("IJsonService 未注册");
            }

            // 验证样式服务
            if (!container.IsRegistered<IStyleService>())
            {
                result.Errors.Add("IStyleService 未注册");
            }

            // 验证用户服务
            if (!container.IsRegistered<IUserService>())
            {
                result.Errors.Add("IUserService 未注册");
            }

            // 验证角色服务
            if (!container.IsRegistered<IRoleService>())
            {
                result.Errors.Add("IRoleService 未注册");
            }

            // 验证权限服务
            if (!container.IsRegistered<IPermissionService>())
            {
                result.Errors.Add("IPermissionService 未注册");
            }
        }

        /// <summary>
        /// 验证UI服务
        /// </summary>
        private static void ValidateUIServices(IContainer container, ServiceRegistrationValidationResult result)
        {
            // 验证主题服务
            if (!container.IsRegistered<IThemeService>())
            {
                result.Errors.Add("IThemeService 未注册");
            }

            // 验证窗口管理服务
            if (!container.IsRegistered<IWindowManager>())
            {
                result.Errors.Add("IWindowManager 未注册");
            }
        }
    }

    /// <summary>
    /// 服务注册验证结果
    /// </summary>
    public class ServiceRegistrationValidationResult
    {
        /// <summary>
        /// 是否验证通过
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 错误信息列表
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// 警告信息列表
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();
    }

    /// <summary>
    /// 空日志实现（用作后备）
    /// </summary>
    internal class NullLogger : ILogger
    {
        public void Debug(string message) { }
        public void Debug(string message, params object[] args) { }
        public void Info(string message) { }
        public void Info(string message, params object[] args) { }
        public void Warn(string message) { }
        public void Warn(string message, params object[] args) { }
        public void Error(string message) { }
        public void Error(string message, params object[] args) { }
        public void Error(Exception exception, string message = "") { }
        public void Fatal(string message) { }
        public void Fatal(string message, params object[] args) { }
        public void Fatal(Exception exception, string message = "") { }

        public bool IsDebugEnabled => false;
        public bool IsInfoEnabled => false;
        public bool IsWarnEnabled => false;
        public bool IsErrorEnabled => false;
        public bool IsFatalEnabled => false;
    }
}
