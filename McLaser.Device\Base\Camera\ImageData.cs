using System;

namespace McLaser.Devices
{
    /// <summary>
    /// 图像数据类
    /// 包含从相机采集的图像数据和相关参数
    /// </summary>
    [Serializable]
    public class ImageData
    {
        /// <summary>
        /// 图像数据指针（非托管内存）
        /// </summary>
        public IntPtr PixelDataPtr { get; set; } = IntPtr.Zero;

        /// <summary>
        /// 图像宽度（像素）
        /// </summary>
        public uint Width { get; set; } = 0;

        /// <summary>
        /// 图像高度（像素）
        /// </summary>
        public uint Height { get; set; } = 0;

        /// <summary>
        /// 图像数据大小（字节）
        /// </summary>
        public ulong ImageSize { get; set; } = 0;

        /// <summary>
        /// 增益值
        /// </summary>
        public float Gain { get; set; } = 0.0f;

        /// <summary>
        /// 曝光时间（微秒）
        /// </summary>
        public float ExposureTime { get; set; } = 0.0f;

        /// <summary>
        /// ROI区域水平偏移量
        /// </summary>
        public uint OffsetX { get; set; } = 0;

        /// <summary>
        /// ROI区域垂直偏移量
        /// </summary>
        public uint OffsetY { get; set; } = 0;

        /// <summary>
        /// 图像旋转角度
        /// </summary>
        public RotateImageAngle RotateImageAngle { get; set; } = RotateImageAngle.None;

        /// <summary>
        /// 图像镜像类型
        /// </summary>
        public MirrorImageType MirrorImageType { get; set; } = MirrorImageType.None;

        /// <summary>
        /// 构造函数
        /// </summary>
        public ImageData()
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="width">图像宽度</param>
        /// <param name="height">图像高度</param>
        /// <param name="pixelDataPtr">图像数据指针</param>
        /// <param name="imageSize">图像大小</param>
        public ImageData(uint width, uint height, IntPtr pixelDataPtr, ulong imageSize)
        {
            Width = width;
            Height = height;
            PixelDataPtr = pixelDataPtr;
            ImageSize = imageSize;
        }

        /// <summary>
        /// 检查图像数据是否有效
        /// </summary>
        /// <returns>数据是否有效</returns>
        public bool IsValid()
        {
            return Width > 0 && Height > 0 && PixelDataPtr != IntPtr.Zero && ImageSize > 0;
        }

        /// <summary>
        /// 重写ToString方法
        /// </summary>
        /// <returns>图像数据信息字符串</returns>
        public override string ToString()
        {
            return $"Image: {Width}x{Height}, Size: {ImageSize} bytes, Exposure: {ExposureTime}μs, Gain: {Gain}";
        }
    }
}
