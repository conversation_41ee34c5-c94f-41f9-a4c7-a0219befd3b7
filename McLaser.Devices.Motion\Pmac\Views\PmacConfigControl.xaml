<UserControl x:Class="McLaser.Devices.Motion.Pmac.Views.PmacConfigControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:McLaser.Devices.Motion.Pmac.Views"
             xmlns:devices="clr-namespace:McLaser.Devices"
             xmlns:pmac="clr-namespace:McLaser.Devices.Motion.Pmac"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">
    <Grid>
        <!-- PMAC特有的配置内容 -->
        <TabControl>
            <!-- 坐标系配置 -->
            <TabItem Header="坐标系配置">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- 工具栏 -->
                    <ToolBar Grid.Row="0">
                        <Button Command="{Binding AddCoordinateCommand}" Margin="2">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="&#xE710;" FontFamily="Segoe MDL2 Assets" Margin="0,0,5,0"/>
                                    <TextBlock Text="新增坐标系"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>
                        <Button Command="{Binding RemoveCoordinateCommand}" Margin="2">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="&#xE74D;" FontFamily="Segoe MDL2 Assets" Margin="0,0,5,0"/>
                                    <TextBlock Text="删除坐标系"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>
                    </ToolBar>
                    
                    <!-- 坐标系列表 -->
                    <ListView Grid.Row="1" ItemsSource="{Binding Coordinates}" 
                              SelectedItem="{Binding SelectedCoordinate}" Margin="5">
                        <ListView.View>
                            <GridView>
                                <GridViewColumn Header="编号" DisplayMemberBinding="{Binding ID}" Width="60"/>
                                <GridViewColumn Header="名称" DisplayMemberBinding="{Binding Name}" Width="120"/>
                                <GridViewColumn Header="包含轴" DisplayMemberBinding="{Binding AxisCount}" Width="80"/>
                                <GridViewColumn Header="状态" Width="100">
                                    <GridViewColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding IsActive, Converter={StaticResource BoolToTextConverter}, ConverterParameter=激活:未激活}"/>
                                        </DataTemplate>
                                    </GridViewColumn.CellTemplate>
                                </GridViewColumn>
                            </GridView>
                        </ListView.View>
                    </ListView>
                </Grid>
            </TabItem>
            
            <!-- PMAC特殊功能 -->
            <TabItem Header="特殊功能">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- 命令执行区 -->
                    <GroupBox Grid.Row="0" Header="命令执行" Margin="0,5">
                        <Grid Margin="5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBox Grid.Row="0" Grid.Column="0" Text="{Binding PmacCommand}" 
                                     Margin="0,0,5,0" Height="25"/>
                            <Button Grid.Row="0" Grid.Column="1" Content="执行" 
                                    Command="{Binding ExecutePmacCommandCommand}"/>
                            
                            <TextBlock Grid.Row="1" Grid.ColumnSpan="2" Text="响应:" Margin="0,5,0,2"/>
                            <TextBox Grid.Row="2" Grid.ColumnSpan="2" Text="{Binding PmacResponse}" 
                                     IsReadOnly="True" Height="80" TextWrapping="Wrap" 
                                     VerticalScrollBarVisibility="Auto"/>
                        </Grid>
                    </GroupBox>
                    
                    <!-- 控制卡特殊功能 -->
                    <GroupBox Grid.Row="1" Header="快捷操作" Margin="0,5">
                        <UniformGrid Columns="3" Rows="2" Margin="5">
                            <Button Content="重启控制卡" Command="{Binding RestartCardCommand}" Margin="3"/>
                            <Button Content="清除报警" Command="{Binding ClearAllAlarmsCommand}" Margin="3"/>
                            <Button Content="停止所有轴" Command="{Binding StopAllAxesCommand}" Margin="3"/>
                            <Button Content="使能所有轴" Command="{Binding EnableAllAxesCommand}" Margin="3"/>
                            <Button Content="禁用所有轴" Command="{Binding DisableAllAxesCommand}" Margin="3"/>
                            <Button Content="状态刷新" Command="{Binding RefreshAllStatusCommand}" Margin="3"/>
                        </UniformGrid>
                    </GroupBox>
                    
                    <!-- 卡状态信息 -->
                    <GroupBox Grid.Row="2" Header="控制卡状态" Margin="0,5">
                        <Grid Margin="5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="卡状态:" Margin="0,0,5,2"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding CardStatus}" Margin="0,0,15,2"/>
                            
                            <TextBlock Grid.Row="0" Grid.Column="2" Text="固件版本:" Margin="0,0,5,2"/>
                            <TextBlock Grid.Row="0" Grid.Column="3" Text="{Binding FirmwareVersion}" Margin="0,0,0,2"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="IP地址:" Margin="0,0,5,2"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding IpAddress}" Margin="0,0,15,2"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="2" Text="连接状态:" Margin="0,0,5,2"/>
                            <TextBlock Grid.Row="1" Grid.Column="3" Text="{Binding IsConnected, Converter={StaticResource BoolToTextConverter}, ConverterParameter=已连接:未连接}" Margin="0,0,0,2"/>
                            
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="错误代码:" Margin="0,0,5,2"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding ErrorCode}" Margin="0,0,15,2"/>
                            
                            <TextBlock Grid.Row="2" Grid.Column="2" Text="错误消息:" Margin="0,0,5,2"/>
                            <TextBlock Grid.Row="2" Grid.Column="3" Text="{Binding ErrorMessage}" Margin="0,0,0,2"/>
                            
                            <TextBlock Grid.Row="3" Grid.Column="0" Text="最后更新:" Margin="0,0,5,2"/>
                            <TextBlock Grid.Row="3" Grid.Column="1" Grid.ColumnSpan="3" Text="{Binding LastUpdateTime, StringFormat='{}{0:yyyy-MM-dd HH:mm:ss}'}" Margin="0,0,0,2"/>
                        </Grid>
                    </GroupBox>
                </Grid>
            </TabItem>
        </TabControl>
    </Grid>
</UserControl> 