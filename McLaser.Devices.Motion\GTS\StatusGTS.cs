using System;
using System.ComponentModel;
using McLaser.Device;

namespace McLaser.Devices.Motion.GTS
{
    /// <summary>
    /// 固高GTS运动控制卡状态类
    /// 用于记录和监控固高GTS控制卡的运行状态信息
    /// </summary>
    [Serializable]
    public class StatusGTS : StatusBase
    {
        #region 私有字段

        private bool _isConnected = false;
        private bool _isInitialized = false;
        private string _firmwareVersion = "未知";
        private string _errorMessage = string.Empty;
        private DateTime _lastUpdateTime = DateTime.MinValue;
        private int _connectedAxesCount = 0;
        private int _enabledAxesCount = 0;
        private string _connectionStatus = "未连接";
        private short _cardIndex = 0;

        #endregion

        #region 属性

        /// <summary>
        /// 设备是否已连接
        /// </summary>
        [Category("GTS状态"), DisplayName("连接状态")]
        public override bool IsConnected
        {
            get => _isConnected;
            set
            {
                if (_isConnected != value)
                {
                    _isConnected = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                    OnPropertyChanged(nameof(ConnectionStatus));
                }
            }
        }

        /// <summary>
        /// 是否已初始化
        /// </summary>
        [Category("GTS状态"), DisplayName("初始化状态")]
        public bool IsInitialized
        {
            get => _isInitialized;
            set
            {
                if (_isInitialized != value)
                {
                    _isInitialized = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// 固件版本
        /// </summary>
        [Category("GTS状态"), DisplayName("固件版本")]
        public string FirmwareVersion
        {
            get => _firmwareVersion;
            set
            {
                if (_firmwareVersion != value)
                {
                    _firmwareVersion = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 错误信息
        /// </summary>
        [Category("GTS状态"), DisplayName("错误信息")]
        public string ErrorMessage
        {
            get => _errorMessage;
            set
            {
                if (_errorMessage != value)
                {
                    _errorMessage = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        [Category("GTS状态"), DisplayName("最后更新时间")]
        public DateTime LastUpdateTime
        {
            get => _lastUpdateTime;
            set
            {
                if (_lastUpdateTime != value)
                {
                    _lastUpdateTime = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 已连接轴数量
        /// </summary>
        [Category("GTS状态"), DisplayName("已连接轴数")]
        public int ConnectedAxesCount
        {
            get => _connectedAxesCount;
            set
            {
                if (_connectedAxesCount != value)
                {
                    _connectedAxesCount = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 已使能轴数量
        /// </summary>
        [Category("GTS状态"), DisplayName("已使能轴数")]
        public int EnabledAxesCount
        {
            get => _enabledAxesCount;
            set
            {
                if (_enabledAxesCount != value)
                {
                    _enabledAxesCount = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 连接状态描述
        /// </summary>
        [Category("GTS状态"), DisplayName("连接状态描述")]
        public string ConnectionStatus
        {
            get => _connectionStatus;
            set
            {
                if (_connectionStatus != value)
                {
                    _connectionStatus = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 控制卡索引
        /// </summary>
        [Category("GTS状态"), DisplayName("控制卡索引")]
        public short CardIndex
        {
            get => _cardIndex;
            set
            {
                if (_cardIndex != value)
                {
                    _cardIndex = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 状态文本描述
        /// </summary>
        [Category("GTS状态"), DisplayName("状态描述")]
        public override string StatusText
        {
            get
            {
                if (!string.IsNullOrEmpty(_errorMessage))
                    return $"错误: {_errorMessage}";

                if (!_isConnected)
                    return "设备未连接";

                if (!_isInitialized)
                    return "设备已连接，未初始化";

                return $"运行正常 - 轴数: {_enabledAxesCount}/{_connectedAxesCount}";
            }
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public StatusGTS()
        {
            // 初始化状态
            Reset();
        }

        #endregion

        #region 方法

        /// <summary>
        /// 重置状态
        /// </summary>
        public override void Reset()
        {
            IsConnected = false;
            IsInitialized = false;
            FirmwareVersion = "未知";
            ErrorMessage = string.Empty;
            LastUpdateTime = DateTime.MinValue;
            ConnectedAxesCount = 0;
            EnabledAxesCount = 0;
            ConnectionStatus = "未连接";
            CardIndex = 0;
        }

        /// <summary>
        /// 设置错误信息
        /// </summary>
        /// <param name="error">错误信息</param>
        public void SetError(string error)
        {
            ErrorMessage = error;
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 清除错误信息
        /// </summary>
        public void ClearError()
        {
            ErrorMessage = string.Empty;
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 更新连接状态
        /// </summary>
        /// <param name="connected">是否连接</param>
        /// <param name="statusDescription">状态描述</param>
        public void UpdateConnectionStatus(bool connected, string statusDescription = null)
        {
            IsConnected = connected;
            ConnectionStatus = statusDescription ?? (connected ? "已连接" : "未连接");
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 更新轴状态统计
        /// </summary>
        /// <param name="connectedCount">已连接轴数</param>
        /// <param name="enabledCount">已使能轴数</param>
        public void UpdateAxesStatus(int connectedCount, int enabledCount)
        {
            ConnectedAxesCount = connectedCount;
            EnabledAxesCount = enabledCount;
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 获取状态摘要
        /// </summary>
        /// <returns>状态摘要字符串</returns>
        public override string GetStatusSummary()
        {
            return $"GTS控制卡 - {StatusText} | 卡号: {CardIndex} | 固件: {FirmwareVersion}";
        }

        /// <summary>
        /// 检查是否需要更新状态
        /// </summary>
        /// <param name="intervalSeconds">更新间隔(秒)</param>
        /// <returns>是否需要更新</returns>
        public bool ShouldUpdate(double intervalSeconds = 1.0)
        {
            if (LastUpdateTime == DateTime.MinValue)
                return true;

            return (DateTime.Now - LastUpdateTime).TotalSeconds >= intervalSeconds;
        }

        #endregion
    }

    /// <summary>
    /// GTS轴状态类
    /// </summary>
    [Serializable]
    public class StatusAxisGTS : StatusAxis
    {
        #region 私有字段

        private bool _isMoving = false;
        private bool _isInPosition = false;
        private bool _hasError = false;
        private bool _isEnabled = false;
        private bool _isHomed = false;
        private int _statusWord = 0;
        private double _commandPosition = 0;
        private double _actualPosition = 0;
        private double _followingError = 0;

        #endregion

        #region 属性

        /// <summary>
        /// 是否正在运动
        /// </summary>
        [Category("GTS轴状态"), DisplayName("正在运动")]
        public override bool IsMoving
        {
            get => _isMoving;
            set
            {
                if (_isMoving != value)
                {
                    _isMoving = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 是否到位
        /// </summary>
        [Category("GTS轴状态"), DisplayName("到位状态")]
        public bool IsInPosition
        {
            get => _isInPosition;
            set
            {
                if (_isInPosition != value)
                {
                    _isInPosition = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 是否有错误
        /// </summary>
        [Category("GTS轴状态"), DisplayName("错误状态")]
        public bool HasError
        {
            get => _hasError;
            set
            {
                if (_hasError != value)
                {
                    _hasError = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 是否使能
        /// </summary>
        [Category("GTS轴状态"), DisplayName("使能状态")]
        public bool IsEnabled
        {
            get => _isEnabled;
            set
            {
                if (_isEnabled != value)
                {
                    _isEnabled = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 是否已回零
        /// </summary>
        [Category("GTS轴状态"), DisplayName("回零状态")]
        public bool IsHomed
        {
            get => _isHomed;
            set
            {
                if (_isHomed != value)
                {
                    _isHomed = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 状态字
        /// </summary>
        [Category("GTS轴状态"), DisplayName("状态字")]
        public int StatusWord
        {
            get => _statusWord;
            set
            {
                if (_statusWord != value)
                {
                    _statusWord = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 指令位置
        /// </summary>
        [Category("GTS轴状态"), DisplayName("指令位置(mm)")]
        public double CommandPosition
        {
            get => _commandPosition;
            set
            {
                if (Math.Abs(_commandPosition - value) > 0.001)
                {
                    _commandPosition = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 实际位置
        /// </summary>
        [Category("GTS轴状态"), DisplayName("实际位置(mm)")]
        public double ActualPosition
        {
            get => _actualPosition;
            set
            {
                if (Math.Abs(_actualPosition - value) > 0.001)
                {
                    _actualPosition = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 跟随误差
        /// </summary>
        [Category("GTS轴状态"), DisplayName("跟随误差(mm)")]
        public double FollowingError
        {
            get => _followingError;
            set
            {
                if (Math.Abs(_followingError - value) > 0.001)
                {
                    _followingError = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region 方法

        /// <summary>
        /// 重置状态
        /// </summary>
        public override void Reset()
        {
            base.Reset();
            
            IsMoving = false;
            IsInPosition = false;
            HasError = false;
            IsEnabled = false;
            IsHomed = false;
            StatusWord = 0;
            CommandPosition = 0;
            ActualPosition = 0;
            FollowingError = 0;
        }

        /// <summary>
        /// 从状态字更新状态
        /// </summary>
        /// <param name="statusWord">状态字</param>
        public void UpdateFromStatusWord(int statusWord)
        {
            StatusWord = statusWord;
            
            // 解析状态字的各个位
            IsEnabled = (statusWord & 0x200) != 0;      // bit 9: 使能状态
            IsMoving = (statusWord & 0x400) == 0;       // bit 10: 到位状态(反向)
            IsInPosition = (statusWord & 0x400) != 0;   // bit 10: 到位状态
            IsHomed = (statusWord & 0x800) != 0;        // bit 11: 回零状态
            HasError = (statusWord & 0x002) != 0;       // bit 1: 错误状态
        }

        #endregion
    }
}
