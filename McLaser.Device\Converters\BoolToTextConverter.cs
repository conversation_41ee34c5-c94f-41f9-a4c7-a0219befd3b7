using System;
using System.Globalization;
using System.Windows.Data;

namespace McLaser.Devices
{
    /// <summary>
    /// 布尔值转文本转换器
    /// 用于将布尔值转换为易读文本
    /// </summary>
    [ValueConversion(typeof(bool), typeof(string))]
    public class BoolToTextConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool isTrue = false;
            
            // 处理null值
            if (value == null)
                return "未知";
                
            // 尝试获取布尔值
            if (value is bool boolValue)
                isTrue = boolValue;
                
            // 解析参数
            string[] texts = parameter != null ? parameter.ToString().Split(':') : new[] { "是", "否" };
            string trueText = texts.Length > 0 ? texts[0] : "是";
            string falseText = texts.Length > 1 ? texts[1] : "否";
            
            // 根据布尔值选择文本
            return isTrue ? trueText : falseText;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 