﻿using System;

namespace McLaser.Core.Framework.IO
{
    public static class AppPaths
    {
        private static IAppPathService _instance;
        public static IAppPathService Instance
        {
            get
            {
                if (_instance == null)
                    throw new Exception("AppPathServce is not registered.");
                return _instance;
            }
        }
        public static void Register(IAppPathService instance)
        {
            _instance = instance;
        }
    }


}
