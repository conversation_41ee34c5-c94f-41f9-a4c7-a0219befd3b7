using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Threading;
using System.Threading.Tasks;
using System.Xml.Serialization;
using McLaser.Device;
using GTN;

namespace McLaser.Devices.Motion.GTS
{
    /// <summary>
    /// 固高GTS运动控制卡驱动实现
    /// 支持固高GTS系列运动控制器的轴控制、IO操作等功能
    /// </summary>
    [Category("运动控制卡")]
    [DisplayName("固高GTS运动控制卡")]
    [Serializable]
    public class CardGTS : CardBase
    {
        #region 私有字段

        /// <summary>
        /// 控制卡索引
        /// </summary>
        private short cardIndex = 0;

        /// <summary>
        /// 是否已初始化
        /// </summary>
        private bool isInitialized = false;

        /// <summary>
        /// 状态监控任务取消令牌
        /// </summary>
        private CancellationTokenSource statusCancellationToken;

        /// <summary>
        /// 线程锁对象
        /// </summary>
        private static readonly object objLock = new object();

        #endregion

        #region 属性

        /// <summary>
        /// 轴列表
        /// </summary>
        [Category("GTS卡"), DisplayName("轴列表")]
        public override ObservableCollection<AxisBase> ListAxis { get; set; } = new ObservableCollection<AxisBase>();

        /// <summary>
        /// DI列表
        /// </summary>
        [Category("GTS卡"), DisplayName("DI列表")]
        public override List<IOBase> ListDi { get; set; } = new List<IOBase>();

        /// <summary>
        /// DO列表
        /// </summary>
        [Category("GTS卡"), DisplayName("DO列表")]
        public override List<IOBase> ListDo { get; set; } = new List<IOBase>();

        /// <summary>
        /// 控制卡索引号
        /// </summary>
        [Category("GTS连接"), DisplayName("控制卡索引")]
        public short CardIndex
        {
            get => cardIndex;
            set
            {
                if (cardIndex != value)
                {
                    cardIndex = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 配置文件路径
        /// </summary>
        [Category("GTS连接"), DisplayName("配置文件路径")]
        public string ConfigFilePath { get; set; } = "";

        /// <summary>
        /// 核心模式
        /// </summary>
        [Category("GTS连接"), DisplayName("核心模式")]
        public short CoreMode { get; set; } = mc.CORE_MODE_TIMER;

        /// <summary>
        /// 设备状态
        /// </summary>
        [XmlIgnore, Browsable(false)]
        public override StatusBase Status { get; set; } = new StatusGTS();

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public CardGTS()
        {
            Name = "固高GTS运动控制卡";
            DeviceType = DeviceType.MotionController;
        }

        #endregion

        #region 设备基本操作

        /// <summary>
        /// 打开设备连接
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool Open()
        {
            try
            {
                if (!IsEnabled) return false;
                if (Status.IsConnected) Close();

                // 设置卡号
                short result = mc.GT_SetCardNo(cardIndex);
                if (result != mc.CMD_SUCCESS)
                {
                    System.Diagnostics.Debug.WriteLine($"设置GTS卡号失败，错误码：{result}");
                    return false;
                }

                // 打开控制卡
                result = mc.GT_Open(mc.CHANNEL_PCIE, 0);
                if (result != mc.CMD_SUCCESS)
                {
                    System.Diagnostics.Debug.WriteLine($"打开GTS控制卡失败，错误码：{result}");
                    return false;
                }

                // 复位控制卡
                result = mc.GT_Reset();
                if (result != mc.CMD_SUCCESS)
                {
                    System.Diagnostics.Debug.WriteLine($"复位GTS控制卡失败，错误码：{result}");
                    mc.GT_Close();
                    return false;
                }

                // 加载配置文件
                if (!string.IsNullOrEmpty(ConfigFilePath))
                {
                    result = mc.GT_LoadConfig(ConfigFilePath);
                    if (result != mc.CMD_SUCCESS)
                    {
                        System.Diagnostics.Debug.WriteLine($"加载GTS配置文件失败，错误码：{result}");
                    }
                }

                // 使能所有轴
                for (int i = 0; i < ListAxis.Count; i++)
                {
                    AxisGTS axis = ListAxis[i] as AxisGTS;
                    if (axis != null)
                    {
                        // 清除轴状态
                        mc.GT_ClrSts((short)(axis.ID), 1);
                        
                        // 使能轴
                        mc.GT_AxisOn((short)(axis.ID));
                    }
                }

                Status.IsConnected = true;
                isInitialized = true;

                // 启动状态监控
                StartStatusMonitoring();

                return IsOpen();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"打开GTS控制卡异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查设备是否已打开
        /// </summary>
        /// <returns>是否已打开</returns>
        public override bool IsOpen()
        {
            return Status.IsConnected && isInitialized;
        }

        /// <summary>
        /// 关闭设备连接
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool Close()
        {
            try
            {
                // 停止状态监控
                StopStatusMonitoring();

                // 停止所有轴
                for (int i = 0; i < ListAxis.Count; i++)
                {
                    AxisGTS axis = ListAxis[i] as AxisGTS;
                    if (axis != null)
                    {
                        mc.GT_Stop(1 << (axis.ID - 1), 0);
                        mc.GT_AxisOff((short)(axis.ID));
                    }
                }

                // 关闭控制卡
                mc.GT_Close();

                Status.IsConnected = false;
                isInitialized = false;

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"关闭GTS控制卡异常：{ex.Message}");
                return false;
            }
        }

        #endregion

        #region 轴控制

        /// <summary>
        /// 使能轴
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <param name="isEnable">是否使能</param>
        /// <returns>是否成功</returns>
        public override bool Enable(int index, bool isEnable)
        {
            try
            {
                if (!CheckAxis(index)) return false;
                
                AxisGTS axis = ListAxis[index] as AxisGTS;
                short result;
                
                if (isEnable)
                {
                    result = mc.GT_AxisOn((short)(axis.ID));
                }
                else
                {
                    result = mc.GT_AxisOff((short)(axis.ID));
                }
                
                if (result == mc.CMD_SUCCESS)
                {
                    axis.IsEnable = isEnable;
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"GTS轴使能异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 轴回零
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <param name="vel">回零速度</param>
        /// <param name="timeout">超时时间</param>
        /// <returns>是否成功</returns>
        public override bool Home(int index, double vel = 100, int timeout = 10000)
        {
            try
            {
                if (!CheckAxis(index)) return false;
                
                AxisGTS axis = ListAxis[index] as AxisGTS;
                
                // 停止轴运动
                if (!Stop(index)) return false;
                
                // 设置回零速度
                short result = mc.GT_SetVel((short)(axis.ID), vel);
                if (result != mc.CMD_SUCCESS) return false;
                
                // 执行回零
                result = mc.GT_Home((short)(axis.ID), 0, vel);
                if (result != mc.CMD_SUCCESS) return false;
                
                // 等待回零完成
                return WaitForHome(index, timeout);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"GTS轴回零异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 轴移动
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <param name="pos">目标位置</param>
        /// <param name="isAbs">是否绝对位置</param>
        /// <param name="timeout">超时时间</param>
        /// <returns>是否成功</returns>
        public override bool Move(int index, double pos, bool isAbs = true, int timeout = 5000)
        {
            try
            {
                if (!CheckAxis(index)) return false;
                
                AxisGTS axis = ListAxis[index] as AxisGTS;
                
                // 停止轴运动
                if (!Stop(index)) return false;
                
                // 转换为脉冲
                int pulsePos = (int)(pos * axis.PluseScale);
                
                short result;
                if (isAbs)
                {
                    // 绝对位置移动
                    result = mc.GT_PrfPos((short)(axis.ID), pulsePos, axis.VelMax, axis.AccMax, axis.AccMax, 0);
                }
                else
                {
                    // 相对位置移动
                    double currentPos = 0;
                    if (!GetPos(index, ref currentPos)) return false;
                    int targetPos = (int)((currentPos + pos) * axis.PluseScale);
                    result = mc.GT_PrfPos((short)(axis.ID), targetPos, axis.VelMax, axis.AccMax, axis.AccMax, 0);
                }
                
                if (result != mc.CMD_SUCCESS) return false;
                
                // 启动运动
                result = mc.GT_Update(1 << (axis.ID - 1));
                if (result != mc.CMD_SUCCESS) return false;
                
                // 等待到位
                return WaitForInPos(index, pos, timeout);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"GTS轴移动异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 轴点动
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <param name="isPlus">是否正向</param>
        /// <returns>是否成功</returns>
        public override bool Jog(int index, bool isPlus)
        {
            try
            {
                if (!CheckAxis(index)) return false;
                
                AxisGTS axis = ListAxis[index] as AxisGTS;
                
                // 停止轴运动
                if (!Stop(index)) return false;
                
                // 设置点动速度
                double jogVel = isPlus ? axis.VelJog : -axis.VelJog;
                short result = mc.GT_PrfJog((short)(axis.ID), jogVel, axis.AccMax, axis.AccMax);
                
                if (result != mc.CMD_SUCCESS) return false;
                
                // 启动运动
                result = mc.GT_Update(1 << (axis.ID - 1));
                return result == mc.CMD_SUCCESS;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"GTS轴点动异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 停止轴运动
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <returns>是否成功</returns>
        public override bool Stop(int index)
        {
            try
            {
                if (!CheckAxis(index)) return false;
                
                AxisGTS axis = ListAxis[index] as AxisGTS;
                short result = mc.GT_Stop(1 << (axis.ID - 1), 0);
                
                return result == mc.CMD_SUCCESS;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"GTS轴停止异常：{ex.Message}");
                return false;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 检查轴索引是否有效
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <returns>是否有效</returns>
        private bool CheckAxis(int index)
        {
            return index >= 0 && index < ListAxis.Count;
        }

        /// <summary>
        /// 等待轴到位
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <param name="targetPos">目标位置</param>
        /// <param name="timeout">超时时间</param>
        /// <returns>是否成功</returns>
        private bool WaitForInPos(int index, double targetPos, int timeout)
        {
            try
            {
                var startTime = DateTime.Now;
                AxisGTS axis = ListAxis[index] as AxisGTS;
                
                while ((DateTime.Now - startTime).TotalMilliseconds < timeout)
                {
                    // 检查运动状态
                    int status;
                    short result = mc.GT_GetSts((short)(axis.ID), out status, 1, out uint clock);
                    if (result == mc.CMD_SUCCESS)
                    {
                        // 检查是否到位 (bit 10)
                        if ((status & 0x400) != 0)
                        {
                            return true;
                        }
                    }
                    
                    Thread.Sleep(10);
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"等待GTS轴到位异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 等待轴回零完成
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <param name="timeout">超时时间</param>
        /// <returns>是否成功</returns>
        private bool WaitForHome(int index, int timeout)
        {
            try
            {
                var startTime = DateTime.Now;
                
                while ((DateTime.Now - startTime).TotalMilliseconds < timeout)
                {
                    bool isHome = false;
                    if (IsHome(index, ref isHome) && isHome)
                    {
                        return true;
                    }
                    Thread.Sleep(100);
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"等待GTS轴回零异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 启动状态监控
        /// </summary>
        private void StartStatusMonitoring()
        {
            statusCancellationToken = new CancellationTokenSource();
            Task.Run(() => StatusMonitoringLoop(statusCancellationToken.Token));
        }

        /// <summary>
        /// 停止状态监控
        /// </summary>
        private void StopStatusMonitoring()
        {
            statusCancellationToken?.Cancel();
        }

        /// <summary>
        /// 状态监控循环
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        private void StatusMonitoringLoop(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested && IsOpen())
            {
                try
                {
                    // 更新轴位置和状态
                    for (int i = 0; i < ListAxis.Count; i++)
                    {
                        if (cancellationToken.IsCancellationRequested) break;
                        
                        double pos = 0;
                        GetPos(i, ref pos);
                        
                        // 更新轴状态
                        UpdateAxisStatus(i);
                    }
                    
                    Thread.Sleep(200);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"GTS状态监控异常：{ex.Message}");
                    break;
                }
            }
        }

        /// <summary>
        /// 更新轴状态
        /// </summary>
        /// <param name="index">轴索引</param>
        private void UpdateAxisStatus(int index)
        {
            try
            {
                if (!CheckAxis(index)) return;
                
                AxisGTS axis = ListAxis[index] as AxisGTS;
                int status;
                short result = mc.GT_GetSts((short)(axis.ID), out status, 1, out uint clock);
                
                if (result == mc.CMD_SUCCESS && axis.Status is StatusAxisGTS gtsStatus)
                {
                    gtsStatus.UpdateFromStatusWord(status);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新GTS轴状态异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取轴位置
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <param name="pos">位置值</param>
        /// <returns>是否成功</returns>
        public override bool GetPos(int index, ref double pos)
        {
            try
            {
                if (!CheckAxis(index)) return false;

                AxisGTS axis = ListAxis[index] as AxisGTS;
                double encPos;
                short result = mc.GT_GetAxisEncPos((short)(axis.ID), out encPos, 1, out uint clock);

                if (result == mc.CMD_SUCCESS)
                {
                    pos = encPos / axis.PluseScale;
                    axis.ActPos = pos;
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取GTS轴位置异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查轴是否回零
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <param name="bIsHome">是否回零</param>
        /// <returns>是否成功</returns>
        public override bool IsHome(int index, ref bool bIsHome)
        {
            try
            {
                if (!CheckAxis(index)) return false;

                AxisGTS axis = ListAxis[index] as AxisGTS;
                int status;
                short result = mc.GT_GetSts((short)(axis.ID), out status, 1, out uint clock);

                if (result == mc.CMD_SUCCESS)
                {
                    // 检查回零位 (bit 11)
                    bIsHome = (status & 0x800) != 0;
                    axis.IsHome = bIsHome;
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查GTS轴回零状态异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查轴是否正在运动
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <returns>是否正在运动</returns>
        public override bool IsMove(int index)
        {
            try
            {
                if (!CheckAxis(index)) return false;

                AxisGTS axis = ListAxis[index] as AxisGTS;
                int status;
                short result = mc.GT_GetSts((short)(axis.ID), out status, 1, out uint clock);

                if (result == mc.CMD_SUCCESS)
                {
                    // 检查运动状态 (bit 9)
                    return (status & 0x200) != 0;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查GTS轴运动状态异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置轴零位
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <returns>是否成功</returns>
        public override bool SetZero(int index)
        {
            try
            {
                if (!CheckAxis(index)) return false;

                AxisGTS axis = ListAxis[index] as AxisGTS;
                short result = mc.GT_ZeroPos((short)(axis.ID), 1);

                return result == mc.CMD_SUCCESS;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置GTS轴零位异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 清除轴错误
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <returns>是否成功</returns>
        public override bool Clear(int index)
        {
            try
            {
                if (!CheckAxis(index)) return false;

                AxisGTS axis = ListAxis[index] as AxisGTS;
                short result = mc.GT_ClrSts((short)(axis.ID), 1);

                return result == mc.CMD_SUCCESS;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清除GTS轴错误异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取DI状态
        /// </summary>
        /// <param name="index">DI索引</param>
        /// <param name="isValue">DI值</param>
        /// <returns>是否成功</returns>
        public override bool GetDi(int index, ref bool isValue)
        {
            try
            {
                if (index < 0 || index >= ListDi.Count) return false;

                IOGTS io = ListDi[index] as IOGTS;
                if (io == null) return false;

                int value;
                short result = mc.GT_GetDi(mc.MC_GPI, out value);

                if (result == mc.CMD_SUCCESS)
                {
                    isValue = (value & (1 << io.Bit)) != 0;
                    if (io.IsReverse) isValue = !isValue;
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取GTS DI状态异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置DO状态
        /// </summary>
        /// <param name="index">DO索引</param>
        /// <param name="isValue">DO值</param>
        /// <returns>是否成功</returns>
        public override bool SetDo(int index, bool isValue)
        {
            try
            {
                if (index < 0 || index >= ListDo.Count) return false;

                IOGTS io = ListDo[index] as IOGTS;
                if (io == null) return false;

                if (io.IsReverse) isValue = !isValue;

                short result = mc.GT_SetDoBit(mc.MC_GPO, (short)io.Bit, (short)(isValue ? 1 : 0));

                return result == mc.CMD_SUCCESS;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置GTS DO状态异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取DO状态
        /// </summary>
        /// <param name="index">DO索引</param>
        /// <param name="isValue">DO值</param>
        /// <returns>是否成功</returns>
        public override bool GetDo(int index, ref bool isValue)
        {
            try
            {
                if (index < 0 || index >= ListDo.Count) return false;

                IOGTS io = ListDo[index] as IOGTS;
                if (io == null) return false;

                int value;
                short result = mc.GT_GetDo(mc.MC_GPO, out value);

                if (result == mc.CMD_SUCCESS)
                {
                    isValue = (value & (1 << io.Bit)) != 0;
                    if (io.IsReverse) isValue = !isValue;
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取GTS DO状态异常：{ex.Message}");
                return false;
            }
        }

        #endregion
    }
}
