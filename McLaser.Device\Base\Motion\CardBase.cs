using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Windows.Controls;
using McLaser.Device;

namespace McLaser.Devices
{
    /// <summary>
    /// 控制卡基类
    /// 为所有运动控制卡提供基础功能实现
    /// 基类不能使用抽象类，否则会导致基类集合中无法实例化
    /// </summary>
    [Serializable]
    public class CardBase : DeviceBase, ICard
    {
        #region 属性
        /// <summary>
        /// 卡类型
        /// </summary>
        [Category("卡"), DisplayName("卡类型")]
        public virtual CardType Type { get; set; } = CardType.Virtual;

        /// <summary>
        /// 轴列表
        /// </summary>
        [Category("卡"), DisplayName("轴列表")]
        public virtual ObservableCollection<AxisBase> ListAxis { get; set; } = new ObservableCollection<AxisBase>();

        /// <summary>
        /// 数字输入列表
        /// </summary>
        [Category("卡"), DisplayName("Di列表")]
        public virtual List<IOBase> ListDi { get; set; } = new List<IOBase>();

        /// <summary>
        /// 数字输出列表
        /// </summary>
        [Category("卡"), DisplayName("Do列表")]
        public virtual List<IOBase> ListDo { get; set; } = new List<IOBase>();


        #endregion





        #region 轴操作
        /// <summary>
        /// 使能轴
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <param name="isEnable">是否使能</param>
        /// <returns>操作是否成功</returns>
        public virtual bool Enable(int index, bool isEnable)
        {
            return false;
        }

        /// <summary>
        /// 检查轴是否使能
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <param name="bIsEnable">输出使能状态</param>
        /// <returns>操作是否成功</returns>
        public virtual bool IsEnable(int index, ref bool bIsEnable)
        {
            return false;
        }

        /// <summary>
        /// 轴回零
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <param name="vel">回零速度</param>
        /// <param name="timeout">超时时间(毫秒)</param>
        /// <returns>操作是否成功</returns>
        public virtual bool Home(int index, double vel = 100, int timeout = 10000)
        {
            return false;
        }

        /// <summary>
        /// 检查轴是否已回零
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <param name="bIsHome">输出回零状态</param>
        /// <returns>操作是否成功</returns>
        public virtual bool IsHome(int index, ref bool bIsHome)
        {
            return false;
        }

        /// <summary>
        /// 轴运动
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <param name="pos">目标位置</param>
        /// <param name="isAbs">是否绝对运动</param>
        /// <param name="timeout">超时时间(毫秒)</param>
        /// <returns>操作是否成功</returns>
        public virtual bool Move(int index, double pos, bool isAbs = true, int timeout = 5000)
        {
            return false;
        }

        /// <summary>
        /// 检查轴是否正在运动
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <returns>是否正在运动</returns>
        public virtual bool IsMove(int index)
        {
            return false;
        }

        /// <summary>
        /// 多轴同时运动
        /// </summary>
        /// <param name="listIndex">轴索引列表</param>
        /// <param name="listPos">目标位置列表</param>
        /// <returns>操作是否成功</returns>
        public virtual bool MoveMult(List<int> listIndex, List<double> listPos)
        {
            return false;
        }

        /// <summary>
        /// Jog运动
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <param name="isPlus">是否正向</param>
        /// <returns>操作是否成功</returns>
        public virtual bool Jog(int index, bool isPlus)
        {
            return false;
        }

        /// <summary>
        /// 停止轴运动
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <returns>操作是否成功</returns>
        public virtual bool Stop(int index)
        {
            return false;
        }

        /// <summary>
        /// 检查轴是否到位
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <param name="pos">目标位置</param>
        /// <returns>是否到位</returns>
        public virtual bool IsPos(int index, double pos)
        {
            try
            {
                if (!CheckAxis(index)) return false;
                double currentPos = 0;
                if (!GetPos(index, ref currentPos)) return false;

                AxisBase axis = ListAxis[index];
                double tolerance = axis.FollowError;
                return Math.Abs(currentPos - pos) <= tolerance;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 等待轴到位
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <param name="pos">目标位置</param>
        /// <param name="timeout">超时时间(毫秒)</param>
        /// <returns>是否成功到位</returns>
        public virtual bool WaitForInPos(int index, double pos, int timeout = 30000)
        {
            try
            {
                if (!CheckAxis(index)) return false;
                AxisBase axis = ListAxis[index];
                Stopwatch sw = Stopwatch.StartNew();
                bool isOK = false;
                while (true)
                {
                    Thread.Sleep(100);
                    isOK = IsPos(index, pos);
                    if (isOK) break;
                    if (sw.ElapsedMilliseconds > timeout) break;
                }
                Thread.Sleep(30);
                if (!Stop(index)) return false;
                if (!isOK) return false;
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 获取轴当前位置
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <param name="pos">输出当前位置</param>
        /// <returns>操作是否成功</returns>
        public virtual bool GetPos(int index, ref double pos)
        {
            return false;
        }

        /// <summary>
        /// 获取轴状态
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <param name="status">输出轴状态</param>
        /// <returns>操作是否成功</returns>
        public virtual bool GetStatus(int index, ref AxisStatus status)
        {
            return false;
        }

        /// <summary>
        /// 设置轴零点
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <returns>操作是否成功</returns>
        public virtual bool SetZero(int index)
        {
            return false;
        }

        /// <summary>
        /// 清除轴报警
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <returns>操作是否成功</returns>
        public virtual bool Clear(int index)
        {
            return false;
        }

        /// <summary>
        /// 设置轴限位
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <returns>操作是否成功</returns>
        public virtual bool SetLimit(int index)
        {
            return false;
        }
        #endregion

        #region IO操作
        /// <summary>
        /// 获取数字输入
        /// </summary>
        /// <param name="index">输入索引</param>
        /// <param name="isValue">输出输入值</param>
        /// <returns>操作是否成功</returns>
        public virtual bool GetDi(int index, ref bool isValue)
        {
            return false;
        }

        /// <summary>
        /// 获取数字输出
        /// </summary>
        /// <param name="index">输出索引</param>
        /// <param name="isValue">输出输出值</param>
        /// <returns>操作是否成功</returns>
        public virtual bool GetDo(int index, ref bool isValue)
        {
            return false;
        }

        /// <summary>
        /// 设置数字输出
        /// </summary>
        /// <param name="index">输出索引</param>
        /// <param name="isValue">要设置的值</param>
        /// <returns>操作是否成功</returns>
        public virtual bool SetDo(int index, bool isValue)
        {
            return false;
        }
        #endregion

        #region 激光操作
        /// <summary>
        /// 激光开启
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <returns>操作是否成功</returns>
        public virtual bool LaserOn(int index)
        {
            return false;
        }

        /// <summary>
        /// 激光关闭
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <returns>操作是否成功</returns>
        public virtual bool LaserOff(int index)
        {
            return false;
        }

        /// <summary>
        /// 检查激光是否开启
        /// </summary>
        /// <param name="index">轴索引</param>
        /// <returns>激光是否开启</returns>
        public virtual bool IsLaserOn(int index)
        {
            return false;
        }
        #endregion

        #region 辅助方法
       
        protected virtual bool CheckAxis(int index)
        {
            return index >= 0 && index < ListAxis.Count && ListAxis[index] != null;
        }

       
        public virtual bool CheckMove(int index)
        {
            try
            {
                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }

 
        protected virtual double MMToPulse(AxisBase axis, double mm)
        {
            return mm * axis.PluseScale;
        }

        
        protected virtual double PulseToMM(AxisBase axis, double pulse)
        {
            return pulse / axis.PluseScale;
        }

       
        protected internal virtual void AddAxis()
        {

        }

       
        protected internal virtual void RemoveAxis(int axisId)
        {
            try
            {
                var axis = ListAxis.First(x => x.ID == axisId);
                if (axis != null)
                {
                    ListAxis.Remove(axis);
                }
            }
            catch (Exception ex)
            {

            }
        }

        CardView cardView;
        /// <summary>
        /// 获取设备配置界面
        /// </summary>
        /// <returns>配置界面控件</returns>
        public override UserControl GetConfigurationView()
        {
            if (cardView == null)
                cardView = new CardView(this);
            return cardView;
        }
        #endregion
    }
}
