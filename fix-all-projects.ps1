# 批量修复所有项目文件中的错误标签
Write-Host "=== 批量修复项目文件 ===" -ForegroundColor Cyan

# 定义需要修复的项目文件和对应的修复内容
$projectFixes = @{
    "McLaser.Devices.Sensor\McLaser.Devices.Sensor.csproj" = @{
        "search" = "<n>McLaser.Device</n>"
        "replace" = "<Name>McLaser.Device</Name>"
    }
    "McLaser.Devices.Camera\McLaser.Devices.Camera.csproj" = @{
        "search" = "<n>McLaser.Device</n>"
        "replace" = "<Name>McLaser.Device</Name>"
    }
    "McLaser.Devices.Motion\McLaser.Devices.Motion.csproj" = @{
        "search" = "<n>McLaser.Device</n>"
        "replace" = "<Name>McLaser.Device</Name>"
    }
    "McLaser.Devices.Laser\McLaser.Devices.Laser.csproj" = @{
        "search" = "<n>McLaser.Device</n>"
        "replace" = "<Name>McLaser.Device</Name>"
    }
    "McLaser.Core.Tests\McLaser.Core.Tests.csproj" = @{
        "search" = "<n>McLaser.Core</n>"
        "replace" = "<Name>McLaser.Core</Name>"
    }
}

foreach ($projectFile in $projectFixes.Keys) {
    if (Test-Path $projectFile) {
        Write-Host "修复文件: $projectFile" -ForegroundColor Yellow
        
        try {
            # 读取文件内容
            $content = Get-Content $projectFile -Raw -Encoding UTF8
            
            # 执行替换
            $fix = $projectFixes[$projectFile]
            $newContent = $content -replace [regex]::Escape($fix.search), $fix.replace
            
            if ($content -ne $newContent) {
                # 写回文件
                Set-Content -Path $projectFile -Value $newContent -Encoding UTF8
                Write-Host "  ✅ 修复完成" -ForegroundColor Green
            } else {
                Write-Host "  ✅ 无需修复" -ForegroundColor Green
            }
        }
        catch {
            Write-Host "  ❌ 修复失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "文件不存在: $projectFile" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "=== 修复完成 ===" -ForegroundColor Cyan
