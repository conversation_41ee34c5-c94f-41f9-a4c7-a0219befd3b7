<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{F87277CE-D77D-47E0-B602-335625F0DBA3}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>McLaser.Device</RootNamespace>
    <AssemblyName>McLaser.Device</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <LangVersion>11.0</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="halcondotnetxl, Version=22.5.0.0, Culture=neutral, PublicKeyToken=4973bed59ddbf2b8, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\bin\halcondotnetxl.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Xaml.Behaviors, Version=1.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\bin\Microsoft.Xaml.Behaviors.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\bin\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.Core" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xaml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="WindowsBase" />
    <Reference Include="WindowsFormsIntegration" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Base\Camera\BoolToInverseConverter.cs" />
    <Compile Include="Base\Camera\CameraBase.cs" />
    <Compile Include="Base\Camera\CameraEnums.cs" />
    <Compile Include="Base\Camera\CameraInfo.cs" />
    <Compile Include="Base\Camera\CameraView.xaml.cs">
      <DependentUpon>CameraView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Base\Camera\CameraViewModel.cs" />
    <Compile Include="Base\Camera\ICamera.cs" />
    <Compile Include="Base\Camera\ImageData.cs" />
    <Compile Include="Base\Camera\StatusCamera.cs" />
    <Compile Include="Base\Camera\TrigMode.cs" />
    <Compile Include="Base\Motion\AxisBase.cs" />
    <Compile Include="Base\Motion\CardBase.cs" />
    <Compile Include="Base\Motion\CardViewModel.cs" />
    <Compile Include="Base\Motion\IAxis.cs" />
    <Compile Include="Base\Motion\ICard.cs" />
    <Compile Include="Base\Motion\ICardConfigControl.cs" />
    <Compile Include="Base\Motion\IOBase.cs" />
    <Compile Include="Base\Motion\LimitBase.cs" />
    <Compile Include="Base\Motion\MotionEnums.cs" />
    <Compile Include="Base\Motion\StatusAxis.cs" />
    <Compile Include="Base\Motion\CardView.xaml.cs">
      <DependentUpon>CardView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Base\Motion\UnitStatus.cs" />
    <Compile Include="ConfigBase.cs" />
    <Compile Include="Converters\BoolToColorConverter.cs" />
    <Compile Include="Converters\BoolToForegroundConverter.cs" />
    <Compile Include="Converters\BoolToRotateTransformConverter.cs" />
    <Compile Include="Converters\BoolToTextConverter.cs" />
    <Compile Include="Converters\BoolToVisibilityConverter.cs" />
    <Compile Include="Converters\NullToVisibilityConverter.cs" />
    <Compile Include="DeviceBase.cs" />
    <Compile Include="DeviceManager\DeviceCategoryGroup.cs" />
    <Compile Include="DeviceManager\DeviceFactory.cs" />
    <Compile Include="DeviceManager\DeviceItemAttribute.cs" />
    <Compile Include="DeviceManager\DeviceManager.cs" />
    <Compile Include="DeviceManager\DeviceTypeMetadata.cs" />
    <Compile Include="DeviceManager\IDeviceManager.cs" />
    <Compile Include="DeviceManager\ViewModels\DeviceManagerViewModel.cs" />
    <Compile Include="DeviceManager\ViewModels\DeviceTypeCategoryViewModel.cs" />
    <Compile Include="DeviceManager\ViewModels\DeviceTypeItemViewModel.cs" />
    <Compile Include="DeviceManager\Views\DeviceManagerControl.xaml.cs">
      <DependentUpon>DeviceManagerControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="DeviceType.cs" />
    <Compile Include="IDevice.cs" />
    <Compile Include="IDeviceConfiguration.cs" />
    <Compile Include="ILaser.cs" />
    <Compile Include="ISensor.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="StatusBase.cs" />
  </ItemGroup>
  <ItemGroup>
    <Page Include="Base\Camera\CameraView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Base\Motion\CardView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="DeviceManager\Views\DeviceManagerControl.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\McLaser.Core\McLaser.Core.csproj">
      <Project>{B67B75F4-B65B-4FB4-9E9B-F86D2AA29B38}</Project>
      <Name>McLaser.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\McLaser.Modules.VisionLib\McLaser.Modules.Vision.csproj">
      <Project>{5F8A19FD-8E20-4C7D-916E-477D81850B3D}</Project>
      <Name>McLaser.Modules.Vision</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>