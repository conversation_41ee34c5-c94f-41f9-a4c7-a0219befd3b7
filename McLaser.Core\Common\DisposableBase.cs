using System;
using System.Threading;

namespace McLaser.Core.Common
{
    /// <summary>
    /// 可释放对象基类
    /// 提供标准的 IDisposable 实现模式
    /// </summary>
    public abstract class DisposableBase : IDisposable
    {
        private int _disposed = 0;

        /// <summary>
        /// 获取对象是否已被释放
        /// </summary>
        public bool IsDisposed => _disposed != 0;

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (Interlocked.CompareExchange(ref _disposed, 1, 0) == 0)
            {
                if (disposing)
                {
                    // 释放托管资源
                    DisposeManagedResources();
                }

                // 释放非托管资源
                DisposeUnmanagedResources();
            }
        }

        /// <summary>
        /// 释放托管资源
        /// 子类重写此方法来释放托管资源
        /// </summary>
        protected virtual void DisposeManagedResources()
        {
            // 子类实现
        }

        /// <summary>
        /// 释放非托管资源
        /// 子类重写此方法来释放非托管资源
        /// </summary>
        protected virtual void DisposeUnmanagedResources()
        {
            // 子类实现
        }

        /// <summary>
        /// 检查对象是否已被释放，如果已释放则抛出异常
        /// </summary>
        /// <exception cref="ObjectDisposedException">对象已被释放</exception>
        protected void ThrowIfDisposed()
        {
            if (IsDisposed)
            {
                throw new ObjectDisposedException(GetType().Name);
            }
        }

        /// <summary>
        /// 检查对象是否已被释放，如果已释放则抛出异常
        /// </summary>
        /// <param name="objectName">对象名称</param>
        /// <exception cref="ObjectDisposedException">对象已被释放</exception>
        protected void ThrowIfDisposed(string objectName)
        {
            if (IsDisposed)
            {
                throw new ObjectDisposedException(objectName);
            }
        }

        /// <summary>
        /// 析构函数
        /// </summary>
        ~DisposableBase()
        {
            Dispose(false);
        }
    }

    /// <summary>
    /// 可释放对象扩展方法
    /// </summary>
    public static class DisposableExtensions
    {
        /// <summary>
        /// 安全释放对象
        /// </summary>
        /// <param name="disposable">要释放的对象</param>
        public static void SafeDispose(this IDisposable? disposable)
        {
            try
            {
                disposable?.Dispose();
            }
            catch
            {
                // 忽略释放时的异常
            }
        }

        /// <summary>
        /// 使用对象并在使用完毕后自动释放
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="disposable">要使用的对象</param>
        /// <param name="action">使用对象的操作</param>
        public static void Using<T>(this T disposable, Action<T> action) where T : IDisposable
        {
            if (disposable == null)
                throw new ArgumentNullException(nameof(disposable));

            if (action == null)
                throw new ArgumentNullException(nameof(action));

            using (disposable)
            {
                action(disposable);
            }
        }

        /// <summary>
        /// 使用对象并在使用完毕后自动释放，返回操作结果
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <typeparam name="TResult">结果类型</typeparam>
        /// <param name="disposable">要使用的对象</param>
        /// <param name="func">使用对象的函数</param>
        /// <returns>操作结果</returns>
        public static TResult Using<T, TResult>(this T disposable, Func<T, TResult> func) where T : IDisposable
        {
            if (disposable == null)
                throw new ArgumentNullException(nameof(disposable));

            if (func == null)
                throw new ArgumentNullException(nameof(func));

            using (disposable)
            {
                return func(disposable);
            }
        }
    }

    /// <summary>
    /// 复合可释放对象
    /// 用于管理多个可释放对象的生命周期
    /// </summary>
    public sealed class CompositeDisposable : DisposableBase
    {
        private readonly object _lock = new object();
        private readonly System.Collections.Generic.List<IDisposable> _disposables = new System.Collections.Generic.List<IDisposable>();

        /// <summary>
        /// 添加可释放对象
        /// </summary>
        /// <param name="disposable">要添加的可释放对象</param>
        public void Add(IDisposable disposable)
        {
            if (disposable == null)
                throw new ArgumentNullException(nameof(disposable));

            lock (_lock)
            {
                ThrowIfDisposed();
                _disposables.Add(disposable);
            }
        }

        /// <summary>
        /// 移除可释放对象
        /// </summary>
        /// <param name="disposable">要移除的可释放对象</param>
        /// <returns>是否成功移除</returns>
        public bool Remove(IDisposable disposable)
        {
            if (disposable == null)
                return false;

            lock (_lock)
            {
                if (IsDisposed)
                    return false;

                return _disposables.Remove(disposable);
            }
        }

        /// <summary>
        /// 清空所有可释放对象
        /// </summary>
        public void Clear()
        {
            lock (_lock)
            {
                if (IsDisposed)
                    return;

                foreach (var disposable in _disposables)
                {
                    disposable.SafeDispose();
                }

                _disposables.Clear();
            }
        }

        /// <summary>
        /// 获取可释放对象数量
        /// </summary>
        public int Count
        {
            get
            {
                lock (_lock)
                {
                    return IsDisposed ? 0 : _disposables.Count;
                }
            }
        }

        /// <summary>
        /// 释放托管资源
        /// </summary>
        protected override void DisposeManagedResources()
        {
            Clear();
        }
    }
}
