using McLaser.Core.Container;
using McLaser.Core.Framework.Logging;
using McLaser.Core.Framework.Services;
using McLaser.Core.Framework.UI;
using McLaser.Core.Navigation;
using System.Windows.Controls;

namespace McLaser.Core.Modules.Home
{

    public partial class HomeView : UserControl, INavigationPage
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public HomeView()
        {
            var navigationService = IoC.Get<INavigationService>();
            var themeService = IoC.Get<IThemeService>();
            var dialogService = IoC.Get<IDialogService>();
            var loggerService = IoC.Get<ILogger>();
            InitializeComponent();
            this.DataContext = new HomeViewModel(navigationService, themeService, dialogService, loggerService);
        }
    }
}
