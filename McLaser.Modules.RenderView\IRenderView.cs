﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static System.Net.Mime.MediaTypeNames;
using System.Xml.Linq;
using HalconDotNet;
using McLaser.Modules.RenderView.Base;
using System.Windows.Forms;

namespace McLaser.Modules
{
    public interface IRenderView
    {
        //void DispObj(HObject obj);
        /// <summary>
        /// 显示互动控件，会调用repaint
        /// </summary>
        void DispROI(ROI roi);
        /// <summary>
        /// 隐藏互动控件，会调用repaint
        /// </summary>
        void HideROI(ROI roi);
        /// <summary>
        /// 更换窗口显示的图像，深拷贝方式
        /// </summary>
        /// <param name="hImage"></param>
        void DispImage(HImage hImage);
        void DispObj(HObject hObject);
        /// <summary>
        /// 清屏
        /// </summary>
        void ClearHWindow();
        /// <summary>
        /// 清除所有roi，并清屏
        /// </summary>
        void ClearWindow();
        /// <summary>
        /// 清除所有roi
        /// </summary>
        void ClearAllROI();
        /// <summary>
        /// 截屏
        /// </summary>
        /// <param name="filename">保存路径</param>
        void DumpWindow(string filename);
        //HRegion GetMaskRegion();
        //void HideMaskRegion(bool bHide);
        /// <summary>
        /// 结束涂抹
        /// </summary>
        HRegion EndDrawMask();
        /// <summary>
        /// 开始涂抹
        /// </summary>
        /// <param name="region">涂抹区域</param>
        void DrawMask(HRegion region);
        /// <summary>
        /// 设置涂抹
        /// </summary>
        /// <param name="shape">橡皮擦的形状</param>
        /// <param name="size">橡皮擦大小</param>
        void SetMaskShape(int shape, int size); // shape 0-->圆形 1-->矩形
        /// <summary>
        /// 使图像自适应显示
        /// </summary>
        bool SetFullImagePart();
        /// <summary>
        /// 设置窗口参数
        /// </summary>
        void SetViewParam(string param, bool param_value);
        /// <summary>
        /// 开启实时
        /// </summary>
        void StartRealTimeShow();
        /// <summary>
        /// 结束实时
        /// </summary>
        HImage EndRealTimeShow();
        /// <summary>
        /// 添加HROI
        /// </summary>
        void AddHObject(HRoi hObj, string name);
        /// <summary>
        /// 添加一组HROIs
        /// </summary>
        /// <param name="rois">添加的rois</param>
        /// <param name="isCopyMode">添加HROI的方式是否是深拷贝的</param>
        void AddHROIs(Dictionary<string, HRoi> rois, bool isCopyMode = true);
        /// <summary>
        /// 更新显示输入的图像和Hobjects，并重绘
        /// </summary>
        /// <param name="dispImage">更新的图像</param>
        /// <param name="hObjects">更新的rois</param>
        void Repaint(HImage dispImage = null, ConcurrentDictionary<string, HRoi> hObjects = null);


        void RegisterView(IRenderView renderView);


        event EventHandler RealTimeChanged;
        event MouseEventHandler OnRenderViewMouseDown;
        event MouseEventHandler OnRenderViewMouseUp;
    }


}
