<framework:ApplicationBase
    x:Class="McLaser.App.App"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:framework="clr-namespace:McLaser.Core.App;assembly=McLaser.Core"
    DispatcherUnhandledException="Application_DispatcherUnhandledException"
    Exit="Application_Exit"
    ShutdownMode="OnMainWindowClose">
    <framework:ApplicationBase.Resources>
        <!-- 应用程序级别的资源 -->
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- 默认样式 -->
                <ResourceDictionary>


                    <!-- 默认颜色资源 -->
                    <SolidColorBrush x:Key="WindowBackgroundBrush" Color="White" />
                    <SolidColorBrush x:Key="WindowForegroundBrush" Color="Black" />

                    <SolidColorBrush x:Key="ButtonBackgroundBrush" Color="#F0F0F0" />
                    <SolidColorBrush x:Key="ButtonForegroundBrush" Color="Black" />
                    <SolidColorBrush x:Key="ButtonBorderBrush" Color="#CCCCCC" />
                    <SolidColorBrush x:Key="ButtonHoverBackgroundBrush" Color="#E0E0E0" />
                    <SolidColorBrush x:Key="ButtonPressedBackgroundBrush" Color="#D0D0D0" />

                    <SolidColorBrush x:Key="TextBoxBackgroundBrush" Color="White" />
                    <SolidColorBrush x:Key="TextBoxForegroundBrush" Color="Black" />
                    <SolidColorBrush x:Key="TextBoxBorderBrush" Color="#CCCCCC" />

                    <SolidColorBrush x:Key="LabelForegroundBrush" Color="Black" />

                    <SolidColorBrush x:Key="MenuBackgroundBrush" Color="#F8F8F8" />
                    <SolidColorBrush x:Key="MenuForegroundBrush" Color="Black" />

                    <SolidColorBrush x:Key="ToolBarBackgroundBrush" Color="#F0F0F0" />
                    <SolidColorBrush x:Key="ToolBarForegroundBrush" Color="Black" />

                    <SolidColorBrush x:Key="StatusBarBackgroundBrush" Color="#F0F0F0" />
                    <SolidColorBrush x:Key="StatusBarForegroundBrush" Color="Black" />
                    
                    <!-- 默认窗口样式 -->
                    <Style TargetType="Window">
                        <Setter Property="FontFamily" Value="Microsoft YaHei UI" />
                        <Setter Property="FontSize" Value="12" />
                        <Setter Property="Background" Value="{DynamicResource WindowBackgroundBrush}" />
                        <Setter Property="Foreground" Value="{DynamicResource WindowForegroundBrush}" />
                    </Style>

                    <!-- 默认按钮样式 -->
                    <Style TargetType="Button">
                        <Setter Property="Padding" Value="8,4" />
                        <Setter Property="Margin" Value="4" />
                        <Setter Property="MinWidth" Value="75" />
                        <Setter Property="Background" Value="{DynamicResource ButtonBackgroundBrush}" />
                        <Setter Property="Foreground" Value="{DynamicResource ButtonForegroundBrush}" />
                        <Setter Property="BorderBrush" Value="{DynamicResource ButtonBorderBrush}" />
                        <Setter Property="BorderThickness" Value="1" />
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{DynamicResource ButtonHoverBackgroundBrush}" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="{DynamicResource ButtonPressedBackgroundBrush}" />
                            </Trigger>
                        </Style.Triggers>
                    </Style>

                    <!-- 默认文本框样式 -->
                    <Style TargetType="TextBox">
                        <Setter Property="Padding" Value="4" />
                        <Setter Property="Margin" Value="4" />
                        <Setter Property="Background" Value="{DynamicResource TextBoxBackgroundBrush}" />
                        <Setter Property="Foreground" Value="{DynamicResource TextBoxForegroundBrush}" />
                        <Setter Property="BorderBrush" Value="{DynamicResource TextBoxBorderBrush}" />
                        <Setter Property="BorderThickness" Value="1" />
                    </Style>

                    <!-- 默认标签样式 -->
                    <Style TargetType="Label">
                        <Setter Property="Foreground" Value="{DynamicResource LabelForegroundBrush}" />
                    </Style>

                    <!-- 默认菜单样式 -->
                    <Style TargetType="Menu">
                        <Setter Property="Background" Value="{DynamicResource MenuBackgroundBrush}" />
                        <Setter Property="Foreground" Value="{DynamicResource MenuForegroundBrush}" />
                    </Style>

                    <!-- 默认工具栏样式 -->
                    <Style TargetType="ToolBar">
                        <Setter Property="Background" Value="{DynamicResource ToolBarBackgroundBrush}" />
                        <Setter Property="Foreground" Value="{DynamicResource ToolBarForegroundBrush}" />
                    </Style>

                    <!-- 默认状态栏样式 -->
                    <Style TargetType="StatusBar">
                        <Setter Property="Background" Value="{DynamicResource StatusBarBackgroundBrush}" />
                        <Setter Property="Foreground" Value="{DynamicResource StatusBarForegroundBrush}" />
                    </Style>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>

        </ResourceDictionary>
    </framework:ApplicationBase.Resources>
</framework:ApplicationBase>