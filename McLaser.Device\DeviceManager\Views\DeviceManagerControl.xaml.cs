using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using McLaser.Core.Navigation;
using McLaser.Device.ViewModels;
using Newtonsoft.Json;

namespace McLaser.Device.Views
{
    /// <summary>
    /// UI状态配置类
    /// </summary>
    public class DeviceManagerUIConfig
    {
        /// <summary>
        /// 设备类型库是否展开
        /// </summary>
        public bool IsDeviceTypeExpanded { get; set; } = true;

        /// <summary>
        /// 设备类型库宽度
        /// </summary>
        public double DeviceTypeWidth { get; set; } = 280;
    }

    /// <summary>
    /// 设备管理器用户控件
    /// 可以嵌入到其他窗口或页面中使用
    /// </summary>
    public partial class DeviceManagerControl : UserControl, INavigationPage
    {
        #region 私有字段

        private DeviceItem? _deviceItem;
        private const string UIConfigFileName = "DeviceManagerUI.json";

        #endregion

        #region 属性

        /// <summary>
        /// 设备管理器视图模型
        /// </summary>
        public DeviceManagerViewModel ViewModel { get; private set; }

        #endregion

        #region 构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public DeviceManagerControl()
        {
            InitializeComponent();
            ViewModel = new DeviceManagerViewModel();
            DataContext = ViewModel;
        }

        /// <summary>
        /// 带设备管理器实例的构造函数
        /// </summary>
        /// <param name="deviceManager">设备管理器实例</param>
        public DeviceManagerControl(DeviceManager deviceManager)
        {
            InitializeComponent();
            ViewModel = new DeviceManagerViewModel(deviceManager);
            DataContext = ViewModel;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 设置设备管理器实例
        /// </summary>
        /// <param name="deviceManager">设备管理器实例</param>
        public void SetDeviceManager(DeviceManager deviceManager)
        {
            ViewModel = new DeviceManagerViewModel(deviceManager);
            DataContext = ViewModel;
        }

        /// <summary>
        /// 获取设备管理器实例
        /// </summary>
        /// <returns>设备管理器实例</returns>
        public DeviceManager GetDeviceManager()
        {
            return ViewModel?.DeviceManager;
        }

        #endregion

        #region 拖拽事件处理

        /// <summary>
        /// 设备类型鼠标左键按下事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">鼠标事件参数</param>
        private void DeviceType_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is FrameworkElement element)
            {
                var deviceTypeItem = element.DataContext as DeviceItem;
                if (deviceTypeItem != null)
                {
                    _deviceItem = deviceTypeItem;
                    ViewModel.AddOperationLog($"准备拖拽设备类型：{deviceTypeItem.DisplayName}");
                }
            }
        }

        /// <summary>
        /// 设备类型鼠标移动事件（开始拖拽）
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">鼠标事件参数</param>
        private void DeviceType_MouseMove(object sender, MouseEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed &&
                _deviceItem != null &&
                sender is FrameworkElement element)
            {
                try
                {
                    // 创建拖拽数据
                    var dragData = new DataObject("DeviceType", _deviceItem);

                    ViewModel.AddOperationLog($"开始拖拽设备类型：{_deviceItem.DisplayName}");

                    // 开始拖拽操作
                    var result = DragDrop.DoDragDrop(element, dragData, DragDropEffects.Copy);

                    ViewModel.AddOperationLog($"拖拽操作完成，结果：{result}");
                }
                catch (Exception ex)
                {
                    ViewModel.AddOperationLog($"拖拽操作异常：{ex.Message}");
                }
                finally
                {
                    _deviceItem = null;
                }
            }
        }

        /// <summary>
        /// 设备分组拖拽悬停事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">拖拽事件参数</param>
        private void DeviceGroup_DragOver(object sender, DragEventArgs e)
        {
            // 检查拖拽数据是否为设备类型
            if (e.Data.GetDataPresent("DeviceType"))
            {
                e.Effects = DragDropEffects.Copy;
                // 可以在这里添加视觉反馈
            }
            else
            {
                e.Effects = DragDropEffects.None;
            }

            e.Handled = true;
        }

        /// <summary>
        /// 设备分组放置事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">拖拽事件参数</param>
        private void DeviceGroup_Drop(object sender, DragEventArgs e)
        {
            try
            {
                ViewModel.AddOperationLog("检测到拖拽放置事件");

                // 检查拖拽数据
                if (e.Data.GetDataPresent("DeviceType"))
                {
                    ViewModel.AddOperationLog("拖拽数据包含设备类型");

                    var deviceTypeItem = e.Data.GetData("DeviceType") as DeviceItem;
                    if (deviceTypeItem != null)
                    {
                        ViewModel.AddOperationLog($"获取到设备类型：{deviceTypeItem.DisplayName}");

                        // 创建设备并添加到管理器
                        var typeName = deviceTypeItem.ItemType.ToString();
                        var deviceName = $"{deviceTypeItem.DisplayName}_{DateTime.Now:HHmmss}";

                        ViewModel.AddOperationLog($"尝试创建设备：{typeName}");

                        var device = DeviceFactory.CreateDevice(deviceTypeItem);
                        if (device != null)
                        {
                            ViewModel.AddOperationLog($"设备创建成功：{device.Name}");
                            ViewModel.DeviceManager.AddDevice(device);
                            ViewModel.AddOperationLog($"通过拖拽添加设备：{device.Name} ({typeName})");

                            // 自动展开对应的设备分组并选中新添加的设备
                            //ExpandAndSelectDevice(device);
                        }
                        else
                        {
                            ViewModel.AddOperationLog($"拖拽创建设备失败：{typeName}");
                        }
                    }
                    else
                    {
                        ViewModel.AddOperationLog("无法获取设备类型项");
                    }
                }
                else
                {
                    ViewModel.AddOperationLog("拖拽数据不包含设备类型");
                }
            }
            catch (Exception ex)
            {
                ViewModel.AddOperationLog($"拖拽操作异常：{ex.Message}");
                ViewModel.AddOperationLog($"异常详情：{ex.StackTrace}");
            }

            e.Handled = true;
        }

        #endregion

        #region TreeView选中事件处理

        /// <summary>
        /// 设备分组TreeView选中项变化事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">选中项变化事件参数</param>
        private void DeviceGroupsTreeView_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            try
            {
                if (e.NewValue is IDevice device)
                {
                    // 设置选中的设备
                    ViewModel.DeviceManager.SelectedDevice = device;
                    ViewModel.AddOperationLog($"选中设备：{device.Name}");

                    // 通知ViewModel属性变化
                    ViewModel.NotifyPropertyChanged(nameof(ViewModel.SelectedDevice));
                }
                else if (e.NewValue is DeviceCategoryGroup group)
                {
                    // 选中的是分组，清除设备选择
                    ViewModel.DeviceManager.SelectedDevice = null;
                    ViewModel.AddOperationLog($"选中设备分组：{group.CategoryName}");

                    // 通知ViewModel属性变化
                    ViewModel.NotifyPropertyChanged(nameof(ViewModel.SelectedDevice));
                }
                else
                {
                    // 清除选择
                    ViewModel.DeviceManager.SelectedDevice = null;
                    ViewModel.NotifyPropertyChanged(nameof(ViewModel.SelectedDevice));
                }
            }
            catch (Exception ex)
            {
                ViewModel.AddOperationLog($"选中项变化异常：{ex.Message}");
            }
        }

        #endregion

        #region 右击事件处理

        /// <summary>
        /// 设备项右击事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">鼠标事件参数</param>
        private void DeviceItem_RightClick(object sender, MouseButtonEventArgs e)
        {
            // 右击事件由ContextMenu自动处理，这里可以添加额外的逻辑
            if (sender is FrameworkElement element && element.DataContext is IDevice device)
            {
                // 设置选中的设备
                ViewModel.DeviceManager.SelectedDevice = device;
                ViewModel.AddOperationLog($"右击选中设备：{device.Name}");

                // 通知ViewModel属性变化
                ViewModel.NotifyPropertyChanged(nameof(ViewModel.SelectedDevice));
            }
        }

        #endregion

        #region 设备展开和选中

        /// <summary>
        /// 展开设备分组并选中指定设备
        /// </summary>
        /// <param name="device">要选中的设备</param>
        private void ExpandAndSelectDevice(IDevice device)
        {
            //try
            //{
            //    // 延迟执行，确保UI更新完成
            //    Dispatcher.BeginInvoke(new Action(() =>
            //    {
            //        // 查找设备对应的TreeViewItem
            //        var deviceGroupItem = FindTreeViewItemForDevice(device);
            //        if (deviceGroupItem != null)
            //        {
            //            // 展开父分组
            //            var parentGroupItem = FindParentTreeViewItem(deviceGroupItem);
            //            if (parentGroupItem != null)
            //            {
            //                parentGroupItem.IsExpanded = true;
            //                ViewModel.AddOperationLog($"自动展开设备分组：{device.Category}");
            //            }

            //            // 选中设备项
            //            deviceGroupItem.IsSelected = true;
            //            deviceGroupItem.BringIntoView();
            //            ViewModel.AddOperationLog($"自动选中设备：{device.Name}");
            //        }
            //    }), System.Windows.Threading.DispatcherPriority.Background);
            //}
            //catch (Exception ex)
            //{
            //    ViewModel.AddOperationLog($"展开和选中设备异常：{ex.Message}");
            //}
        }

        /// <summary>
        /// 查找设备对应的TreeViewItem
        /// </summary>
        /// <param name="device">设备对象</param>
        /// <returns>TreeViewItem或null</returns>
        private TreeViewItem FindTreeViewItemForDevice(IDevice device)
        {
            return null;
            //try
            //{
            //    return FindTreeViewItemRecursive(DeviceGroupsTreeView, device);
            //}
            //catch (Exception ex)
            //{
            //    ViewModel.AddOperationLog($"查找设备TreeViewItem异常：{ex.Message}");
            //    return null;
            //}
        }

        /// <summary>
        /// 递归查找TreeViewItem
        /// </summary>
        /// <param name="parent">父容器</param>
        /// <param name="device">目标设备</param>
        /// <returns>TreeViewItem或null</returns>
        private TreeViewItem FindTreeViewItemRecursive(ItemsControl parent, IDevice device)
        {
            for (int i = 0; i < parent.Items.Count; i++)
            {
                var container = parent.ItemContainerGenerator.ContainerFromIndex(i) as TreeViewItem;
                if (container != null)
                {
                    // 检查当前项是否是目标设备
                    if (container.DataContext is IDevice currentDevice && currentDevice.Id == device.Id)
                    {
                        return container;
                    }

                    // 递归查找子项
                    var childItem = FindTreeViewItemRecursive(container, device);
                    if (childItem != null)
                    {
                        return childItem;
                    }
                }
            }
            return null;
        }

        /// <summary>
        /// 查找父TreeViewItem
        /// </summary>
        /// <param name="item">子项</param>
        /// <returns>父TreeViewItem或null</returns>
        private TreeViewItem FindParentTreeViewItem(TreeViewItem item)
        {
            try
            {
                var parent = item.Parent as TreeViewItem;
                return parent;
            }
            catch (Exception ex)
            {
                ViewModel.AddOperationLog($"查找父TreeViewItem异常：{ex.Message}");
                return null;
            }
        }

        #endregion

        #region UI状态管理

        /// <summary>
        /// 控件加载事件
        /// </summary>
        private void DeviceManagerControl_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // 延迟执行，确保所有UI元素都已加载
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    // 加载UI配置
                    LoadUIConfiguration();

                    // 自动加载设备配置
                    if (ViewModel?.DeviceManager != null)
                    {
                        ViewModel.DeviceManager.LoadDeviceConfiguration();
                        ViewModel.AddOperationLog("设备管理器已加载，配置已自动恢复");
                    }
                }), System.Windows.Threading.DispatcherPriority.Loaded);
            }
            catch (Exception ex)
            {
                ViewModel?.AddOperationLog($"加载配置异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 控件卸载事件
        /// </summary>
        private void DeviceManagerControl_Unloaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // 保存UI配置
                SaveUIConfiguration();

                // 自动保存设备配置
                if (ViewModel?.DeviceManager != null)
                {
                    ViewModel.DeviceManager.SaveDeviceConfiguration();
                    ViewModel.AddOperationLog("设备管理器已卸载，配置已自动保存");
                }
            }
            catch (Exception ex)
            {
                ViewModel?.AddOperationLog($"保存配置异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 设备类型库展开事件
        /// </summary>
        private void DeviceTypeExpander_Expanded(object sender, RoutedEventArgs e)
        {
            try
            {
                // 延迟执行，确保UI元素可用
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    if (DeviceTypeColumn != null)
                    {
                        DeviceTypeColumn.Width = new GridLength(280);
                        ViewModel?.AddOperationLog("设备类型库已展开");
                    }
                }), System.Windows.Threading.DispatcherPriority.Background);
            }
            catch (Exception ex)
            {
                ViewModel?.AddOperationLog($"展开设备类型库异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 设备类型库折叠事件
        /// </summary>
        private void DeviceTypeExpander_Collapsed(object sender, RoutedEventArgs e)
        {
            try
            {
                // 延迟执行，确保UI元素可用
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    if (DeviceTypeColumn != null)
                    {
                        DeviceTypeColumn.Width = new GridLength(40);
                        ViewModel?.AddOperationLog("设备类型库已折叠");
                    }
                }), System.Windows.Threading.DispatcherPriority.Background);
            }
            catch (Exception ex)
            {
                ViewModel?.AddOperationLog($"折叠设备类型库异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 保存UI配置
        /// </summary>
        private void SaveUIConfiguration()
        {
            try
            {
                if (DeviceTypeExpander != null && DeviceTypeColumn != null)
                {
                    var config = new DeviceManagerUIConfig
                    {
                        IsDeviceTypeExpanded = DeviceTypeExpander.IsExpanded,
                        DeviceTypeWidth = DeviceTypeColumn.Width.Value
                    };

                    var json = JsonConvert.SerializeObject(config, Formatting.Indented);
                    File.WriteAllText(UIConfigFileName, json);
                }
            }
            catch (Exception ex)
            {
                ViewModel?.AddOperationLog($"保存UI配置异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 加载UI配置
        /// </summary>
        private void LoadUIConfiguration()
        {
            try
            {
                if (DeviceTypeExpander != null && DeviceTypeColumn != null && File.Exists(UIConfigFileName))
                {
                    var json = File.ReadAllText(UIConfigFileName);
                    var config = JsonConvert.DeserializeObject<DeviceManagerUIConfig>(json);

                    if (config != null)
                    {
                        DeviceTypeExpander.IsExpanded = config.IsDeviceTypeExpanded;
                        DeviceTypeColumn.Width = new GridLength(config.IsDeviceTypeExpanded ? config.DeviceTypeWidth : 40);
                    }
                }
            }
            catch (Exception ex)
            {
                ViewModel?.AddOperationLog($"加载UI配置异常：{ex.Message}");
            }
        }

        #endregion

        /// <summary>
        /// 设备列表选择变更事件处理程序
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void DevicesListView_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (sender is ListView listView && listView.SelectedItem is IDevice device)
                {
                    // 直接设置DeviceManager的SelectedDevice属性，确保所有绑定都能得到通知
                    ViewModel.DeviceManager.SelectedDevice = device;

                    // 同时更新ViewModel的SelectedDevice属性保持同步
                    ViewModel.SelectedDevice = device;

                    // 强制更新UI
                    ViewModel.NotifyPropertyChanged(nameof(ViewModel.SelectedDevice));

                    ViewModel.AddOperationLog($"已选择设备：{device.Name}");
                }
            }
            catch (Exception ex)
            {
                ViewModel.AddOperationLog($"设备选择发生异常：{ex.Message}");
            }
        }
    }
}
