using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace McLaser.Core.Communication
{
    /// <summary>
    /// 串口通信通道实现
    /// 支持串口通信
    /// </summary>
    public class SerialPortChannel : ICommunicationChannel, IConfigurableCommunicationChannel, IDisposable
    {
        #region 字段

        private SerialPort? _serialPort;
        private readonly ConcurrentQueue<byte> _receiveBuffer = new ConcurrentQueue<byte>();
        private readonly object _lockObject = new object();
        private CancellationTokenSource? _cancellationTokenSource;
        private Task? _receiveTask;
        private bool _disposed = false;
        private ConnectionState _connectionState = ConnectionState.Disconnected;
        private readonly CommunicationStatistics _statistics = new CommunicationStatistics();

        #endregion

        #region 属性

        /// <summary>
        /// 通道名称
        /// </summary>
        public string Name { get; set; } = "串口通信通道";

        /// <summary>
        /// 通道类型
        /// </summary>
        public CommunicationChannelType ChannelType => CommunicationChannelType.SerialPort;

        /// <summary>
        /// 连接状态
        /// </summary>
        public bool IsConnected => _connectionState == ConnectionState.Connected && _serialPort?.IsOpen == true;

        /// <summary>
        /// 是否正在连接
        /// </summary>
        public bool IsConnecting => _connectionState == ConnectionState.Connecting;

        /// <summary>
        /// 连接配置
        /// </summary>
        public CommunicationConfig Configuration { get; set; } = new SerialPortConfig();

        /// <summary>
        /// 串口配置
        /// </summary>
        public SerialPortConfig SerialConfiguration => Configuration as SerialPortConfig ?? new SerialPortConfig();

        #endregion

        #region 事件

        /// <summary>
        /// 连接状态变更事件
        /// </summary>
        public event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;

        /// <summary>
        /// 数据接收事件
        /// </summary>
        public event EventHandler<DataReceivedEventArgs>? DataReceived;

        /// <summary>
        /// 错误发生事件
        /// </summary>
        public event EventHandler<CommunicationErrorEventArgs>? ErrorOccurred;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化串口通信通道
        /// </summary>
        public SerialPortChannel()
        {
        }

        /// <summary>
        /// 初始化串口通信通道
        /// </summary>
        /// <param name="config">串口配置</param>
        public SerialPortChannel(SerialPortConfig config)
        {
            Configuration = config ?? throw new ArgumentNullException(nameof(config));
        }

        #endregion

        #region 连接管理

        /// <summary>
        /// 异步连接
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>连接任务</returns>
        public async Task<bool> ConnectAsync(CancellationToken cancellationToken = default)
        {
            if (IsConnected)
                return true;

            if (IsConnecting)
                return false;

            try
            {
                SetConnectionState(ConnectionState.Connecting);
                _cancellationTokenSource = new CancellationTokenSource();

                var config = SerialConfiguration;

                // 创建串口对象
                _serialPort = new SerialPort
                {
                    PortName = config.PortName,
                    BaudRate = config.BaudRate,
                    DataBits = config.DataBits,
                    StopBits = config.StopBits,
                    Parity = config.Parity,
                    Handshake = config.Handshake,
                    DtrEnable = config.DtrEnable,
                    RtsEnable = config.RtsEnable,
                    ReadTimeout = config.ReceiveTimeout,
                    WriteTimeout = config.SendTimeout,
                    ReadBufferSize = config.ReceiveBufferSize,
                    WriteBufferSize = config.SendBufferSize
                };

                // 注册事件处理器
                _serialPort.DataReceived += OnSerialPortDataReceived;
                _serialPort.ErrorReceived += OnSerialPortErrorReceived;

                // 打开串口
                await Task.Run(() => _serialPort.Open(), cancellationToken);

                SetConnectionState(ConnectionState.Connected);
                _statistics.ConnectedTime = DateTime.Now;

                // 启动接收任务
                StartReceiveTask();

                return true;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(CommunicationErrorType.ConnectionError, $"连接失败: {ex.Message}", ex);
                SetConnectionState(ConnectionState.Error);
                return false;
            }
        }

        /// <summary>
        /// 异步断开连接
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>断开任务</returns>
        public async Task DisconnectAsync(CancellationToken cancellationToken = default)
        {
            if (!IsConnected && !IsConnecting)
                return;

            try
            {
                SetConnectionState(ConnectionState.Disconnecting);

                // 取消所有任务
                _cancellationTokenSource?.Cancel();

                // 等待任务完成
                if (_receiveTask != null && !_receiveTask.IsCompleted)
                {
                    await _receiveTask.ConfigureAwait(false);
                }

                // 关闭串口
                if (_serialPort != null)
                {
                    if (_serialPort.IsOpen)
                    {
                        await Task.Run(() => _serialPort.Close(), cancellationToken);
                    }

                    // 注销事件处理器
                    _serialPort.DataReceived -= OnSerialPortDataReceived;
                    _serialPort.ErrorReceived -= OnSerialPortErrorReceived;

                    _serialPort.Dispose();
                    _serialPort = null;
                }

                SetConnectionState(ConnectionState.Disconnected);

                // 更新统计信息
                if (_statistics.ConnectedTime.HasValue)
                {
                    _statistics.TotalConnectedTime += DateTime.Now - _statistics.ConnectedTime.Value;
                    _statistics.ConnectedTime = null;
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred(CommunicationErrorType.ConnectionError, $"断开连接失败: {ex.Message}", ex);
            }
        }

        #endregion

        #region 数据传输

        /// <summary>
        /// 异步发送数据
        /// </summary>
        /// <param name="data">要发送的数据</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>发送任务，返回实际发送的字节数</returns>
        public async Task<int> SendAsync(byte[] data, CancellationToken cancellationToken = default)
        {
            if (data == null)
                throw new ArgumentNullException(nameof(data));

            if (!IsConnected || _serialPort == null)
                throw new InvalidOperationException("连接未建立");

            try
            {
                await Task.Run(() => _serialPort.Write(data, 0, data.Length), cancellationToken);

                // 更新统计信息
                _statistics.BytesSent += data.Length;
                _statistics.MessagesSent++;
                _statistics.LastActivity = DateTime.Now;

                return data.Length;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(CommunicationErrorType.SendError, $"发送数据失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 异步发送字符串数据
        /// </summary>
        /// <param name="message">要发送的消息</param>
        /// <param name="encoding">编码方式，默认UTF8</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>发送任务，返回实际发送的字节数</returns>
        public async Task<int> SendAsync(string message, Encoding? encoding = null, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(message))
                throw new ArgumentException("消息不能为空", nameof(message));

            encoding ??= Encoding.UTF8;
            var data = encoding.GetBytes(message);
            return await SendAsync(data, cancellationToken);
        }

        /// <summary>
        /// 异步接收数据
        /// </summary>
        /// <param name="buffer">接收缓冲区</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>接收任务，返回实际接收的字节数</returns>
        public async Task<int> ReceiveAsync(byte[] buffer, CancellationToken cancellationToken = default)
        {
            if (buffer == null)
                throw new ArgumentNullException(nameof(buffer));

            if (!IsConnected || _serialPort == null)
                throw new InvalidOperationException("连接未建立");

            try
            {
                var bytesRead = await Task.Run(() => _serialPort.Read(buffer, 0, buffer.Length), cancellationToken);

                // 更新统计信息
                if (bytesRead > 0)
                {
                    _statistics.BytesReceived += bytesRead;
                    _statistics.MessagesReceived++;
                    _statistics.LastActivity = DateTime.Now;
                }

                return bytesRead;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(CommunicationErrorType.ReceiveError, $"接收数据失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 异步接收数据直到指定分隔符
        /// </summary>
        /// <param name="delimiter">分隔符</param>
        /// <param name="maxLength">最大长度</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>接收任务，返回接收到的数据</returns>
        public async Task<byte[]> ReceiveUntilAsync(byte[] delimiter, int maxLength = 4096, CancellationToken cancellationToken = default)
        {
            if (delimiter == null || delimiter.Length == 0)
                throw new ArgumentException("分隔符不能为空", nameof(delimiter));

            if (!IsConnected || _serialPort == null)
                throw new InvalidOperationException("连接未建立");

            var result = new List<byte>();
            var delimiterIndex = 0;

            try
            {
                while (result.Count < maxLength && !cancellationToken.IsCancellationRequested)
                {
                    var receivedByte = await Task.Run(() => (byte)_serialPort.ReadByte(), cancellationToken);
                    result.Add(receivedByte);

                    // 检查分隔符
                    if (receivedByte == delimiter[delimiterIndex])
                    {
                        delimiterIndex++;
                        if (delimiterIndex == delimiter.Length)
                        {
                            // 找到完整分隔符，移除分隔符并返回
                            for (int i = 0; i < delimiter.Length; i++)
                            {
                                result.RemoveAt(result.Count - 1);
                            }
                            break;
                        }
                    }
                    else
                    {
                        delimiterIndex = 0;
                    }
                }

                // 更新统计信息
                if (result.Count > 0)
                {
                    _statistics.BytesReceived += result.Count;
                    _statistics.MessagesReceived++;
                    _statistics.LastActivity = DateTime.Now;
                }

                return result.ToArray();
            }
            catch (Exception ex)
            {
                OnErrorOccurred(CommunicationErrorType.ReceiveError, $"接收数据失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 清空接收缓冲区
        /// </summary>
        public void ClearReceiveBuffer()
        {
            _serialPort?.DiscardInBuffer();
            while (_receiveBuffer.TryDequeue(out _))
            {
                // 清空队列
            }
        }

        /// <summary>
        /// 清空发送缓冲区
        /// </summary>
        public void ClearSendBuffer()
        {
            _serialPort?.DiscardOutBuffer();
        }

        #endregion

        #region 统计和测试

        /// <summary>
        /// 获取通道统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public CommunicationStatistics GetStatistics()
        {
            return _statistics;
        }

        /// <summary>
        /// 重置统计信息
        /// </summary>
        public void ResetStatistics()
        {
            _statistics.Reset();
        }

        /// <summary>
        /// 测试连接
        /// </summary>
        /// <param name="timeout">超时时间</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>测试结果</returns>
        public async Task<bool> TestConnectionAsync(TimeSpan timeout, CancellationToken cancellationToken = default)
        {
            if (!IsConnected)
                return false;

            try
            {
                using (var timeoutCts = new CancellationTokenSource(timeout))
                using (var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token))
                {
                    // 发送测试数据
                    var testData = Encoding.UTF8.GetBytes("TEST");
                    await SendAsync(testData, combinedCts.Token);
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region 配置管理

        /// <summary>
        /// 应用配置
        /// </summary>
        /// <param name="config">配置信息</param>
        /// <returns>是否成功应用配置</returns>
        public bool ApplyConfiguration(CommunicationConfig config)
        {
            if (config == null)
                return false;

            try
            {
                Configuration = config;
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证配置
        /// </summary>
        /// <param name="config">配置信息</param>
        /// <returns>验证结果</returns>
        public ConfigurationValidationResult ValidateConfiguration(CommunicationConfig config)
        {
            var result = new ConfigurationValidationResult { IsValid = true };

            if (config == null)
            {
                result.Errors.Add("配置不能为空");
                result.IsValid = false;
                return result;
            }

            if (config is SerialPortConfig serialConfig)
            {
                // 验证端口名称
                if (string.IsNullOrEmpty(serialConfig.PortName))
                {
                    result.Errors.Add("端口名称不能为空");
                    result.IsValid = false;
                }

                // 验证波特率
                var validBaudRates = new[] { 110, 300, 600, 1200, 2400, 4800, 9600, 14400, 19200, 38400, 57600, 115200, 128000, 256000 };
                if (Array.IndexOf(validBaudRates, serialConfig.BaudRate) == -1)
                {
                    result.Warnings.Add($"波特率 {serialConfig.BaudRate} 可能不被支持");
                }

                // 验证数据位
                if (serialConfig.DataBits < 5 || serialConfig.DataBits > 8)
                {
                    result.Errors.Add("数据位必须在5-8范围内");
                    result.IsValid = false;
                }

                // 验证超时设置
                if (serialConfig.ReceiveTimeout <= 0)
                {
                    result.Warnings.Add("接收超时时间应该大于0");
                }

                if (serialConfig.SendTimeout <= 0)
                {
                    result.Warnings.Add("发送超时时间应该大于0");
                }

                // 检查端口是否可用
                var availablePorts = SerialPort.GetPortNames();
                if (Array.IndexOf(availablePorts, serialConfig.PortName) == -1)
                {
                    result.Warnings.Add($"端口 {serialConfig.PortName} 可能不可用");
                }
            }
            else
            {
                result.Errors.Add("配置类型不匹配，需要SerialPortConfig");
                result.IsValid = false;
            }

            return result;
        }

        /// <summary>
        /// 获取默认配置
        /// </summary>
        /// <returns>默认配置</returns>
        public CommunicationConfig GetDefaultConfiguration()
        {
            return new SerialPortConfig
            {
                Name = "默认串口配置",
                PortName = "COM1",
                BaudRate = 9600,
                DataBits = 8,
                StopBits = StopBits.One,
                Parity = Parity.None,
                Handshake = Handshake.None,
                DtrEnable = false,
                RtsEnable = false,
                ReceiveTimeout = 3000,
                SendTimeout = 3000,
                ReceiveBufferSize = 4096,
                SendBufferSize = 4096
            };
        }

        /// <summary>
        /// 获取支持的配置选项
        /// </summary>
        /// <returns>配置选项列表</returns>
        public CommunicationConfigOption[] GetSupportedOptions()
        {
            return new[]
            {
                new CommunicationConfigOption
                {
                    Name = "PortName",
                    OptionType = typeof(string),
                    DefaultValue = "COM1",
                    IsRequired = true,
                    Description = "串口名称",
                    PossibleValues = SerialPort.GetPortNames().Cast<object>().ToArray()
                },
                new CommunicationConfigOption
                {
                    Name = "BaudRate",
                    OptionType = typeof(int),
                    DefaultValue = 9600,
                    IsRequired = true,
                    Description = "波特率",
                    PossibleValues = new object[] { 110, 300, 600, 1200, 2400, 4800, 9600, 14400, 19200, 38400, 57600, 115200, 128000, 256000 }
                },
                new CommunicationConfigOption
                {
                    Name = "DataBits",
                    OptionType = typeof(int),
                    DefaultValue = 8,
                    IsRequired = true,
                    Description = "数据位",
                    MinValue = 5,
                    MaxValue = 8
                },
                new CommunicationConfigOption
                {
                    Name = "StopBits",
                    OptionType = typeof(StopBits),
                    DefaultValue = StopBits.One,
                    IsRequired = true,
                    Description = "停止位",
                    PossibleValues = Enum.GetValues(typeof(StopBits)).Cast<object>().ToArray()
                },
                new CommunicationConfigOption
                {
                    Name = "Parity",
                    OptionType = typeof(Parity),
                    DefaultValue = Parity.None,
                    IsRequired = true,
                    Description = "奇偶校验",
                    PossibleValues = Enum.GetValues(typeof(Parity)).Cast<object>().ToArray()
                },
                new CommunicationConfigOption
                {
                    Name = "Handshake",
                    OptionType = typeof(Handshake),
                    DefaultValue = Handshake.None,
                    IsRequired = false,
                    Description = "流控制",
                    PossibleValues = Enum.GetValues(typeof(Handshake)).Cast<object>().ToArray()
                }
            };
        }

        /// <summary>
        /// 获取可用的串口列表
        /// </summary>
        /// <returns>可用串口列表</returns>
        public static string[] GetAvailablePorts()
        {
            return SerialPort.GetPortNames();
        }

        #endregion

        #region 私有方法

        private void StartReceiveTask()
        {
            _receiveTask = Task.Run(async () =>
            {
                while (IsConnected && !_cancellationTokenSource?.Token.IsCancellationRequested == true)
                {
                    try
                    {
                        // 串口数据接收通过事件处理，这里只是保持任务运行
                        await Task.Delay(100, _cancellationTokenSource?.Token ?? CancellationToken.None);
                    }
                    catch (OperationCanceledException)
                    {
                        break;
                    }
                    catch (Exception ex)
                    {
                        OnErrorOccurred(CommunicationErrorType.ReceiveError, $"接收任务发生错误: {ex.Message}", ex);
                        break;
                    }
                }
            });
        }

        private void SetConnectionState(ConnectionState newState)
        {
            var oldState = _connectionState;
            _connectionState = newState;

            if (oldState != newState)
            {
                OnConnectionStateChanged(oldState, newState);
            }
        }

        #endregion

        #region 串口事件处理

        private void OnSerialPortDataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            try
            {
                if (_serialPort != null && _serialPort.IsOpen)
                {
                    var bytesToRead = _serialPort.BytesToRead;
                    if (bytesToRead > 0)
                    {
                        var buffer = new byte[bytesToRead];
                        var bytesRead = _serialPort.Read(buffer, 0, bytesToRead);

                        if (bytesRead > 0)
                        {
                            var data = new byte[bytesRead];
                            Array.Copy(buffer, 0, data, 0, bytesRead);

                            // 触发数据接收事件
                            OnDataReceived(data, bytesRead, null);

                            // 更新统计信息
                            _statistics.BytesReceived += bytesRead;
                            _statistics.MessagesReceived++;
                            _statistics.LastActivity = DateTime.Now;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                OnErrorOccurred(CommunicationErrorType.ReceiveError, $"串口数据接收错误: {ex.Message}", ex);
            }
        }

        private void OnSerialPortErrorReceived(object sender, SerialErrorReceivedEventArgs e)
        {
            var errorMessage = $"串口错误: {e.EventType}";
            OnErrorOccurred(CommunicationErrorType.ProtocolError, errorMessage);
        }

        #endregion

        #region 事件触发

        private void OnConnectionStateChanged(ConnectionState oldState, ConnectionState newState)
        {
            ConnectionStateChanged?.Invoke(this, new ConnectionStateChangedEventArgs(oldState, newState));
        }

        private void OnDataReceived(byte[] data, int length, EndPoint? remoteEndPoint)
        {
            DataReceived?.Invoke(this, new DataReceivedEventArgs(data, length, remoteEndPoint));
        }

        private void OnErrorOccurred(CommunicationErrorType errorType, string errorMessage, Exception? exception = null, bool isRecoverable = false)
        {
            _statistics.ErrorCount++;
            ErrorOccurred?.Invoke(this, new CommunicationErrorEventArgs(errorType, errorMessage, exception, isRecoverable));
        }

        #endregion

        #region IDisposable

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否正在释放</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                try
                {
                    DisconnectAsync().Wait(5000); // 等待最多5秒
                }
                catch
                {
                    // 忽略释放时的异常
                }

                _cancellationTokenSource?.Dispose();
                _disposed = true;
            }
        }

        /// <summary>
        /// 析构函数
        /// </summary>
        ~SerialPortChannel()
        {
            Dispose(false);
        }

        #endregion
    }
}
