using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using McLaser.Core.Plugins;

namespace McLaser.Plugins.Samples
{
    /// <summary>
    /// 插件基类
    /// 提供插件的基本实现
    /// </summary>
    public abstract class PluginBase : IPlugin
    {
        #region 私有字段

        private bool _disposed = false;
        private PluginStatus _status = PluginStatus.NotLoaded;
        private readonly Dictionary<string, object> _configuration = new Dictionary<string, object>();

        #endregion

        #region 属性

        /// <summary>
        /// 插件唯一标识符
        /// </summary>
        public virtual string Id => Metadata.Id;

        /// <summary>
        /// 插件名称
        /// </summary>
        public virtual string Name => Metadata.Name;

        /// <summary>
        /// 插件版本
        /// </summary>
        public virtual Version Version => Metadata.Version;

        /// <summary>
        /// 插件描述
        /// </summary>
        public virtual string Description => Metadata.Description;

        /// <summary>
        /// 插件作者
        /// </summary>
        public virtual string Author => Metadata.Author;

        /// <summary>
        /// 插件状态
        /// </summary>
        public virtual PluginStatus Status
        {
            get => _status;
            protected set
            {
                if (_status != value)
                {
                    var oldStatus = _status;
                    _status = value;
                    OnStatusChanged(oldStatus, value);
                }
            }
        }

        /// <summary>
        /// 插件元数据
        /// </summary>
        public virtual PluginMetadata Metadata { get; private set; }

        /// <summary>
        /// 插件依赖项
        /// </summary>
        public virtual IList<PluginDependency> Dependencies { get; protected set; } = new List<PluginDependency>();

        /// <summary>
        /// 插件配置
        /// </summary>
        public virtual Dictionary<string, object> Configuration
        {
            get => _configuration;
            set
            {
                _configuration.Clear();
                if (value != null)
                {
                    foreach (var kvp in value)
                    {
                        _configuration[kvp.Key] = kvp.Value;
                    }
                }
            }
        }

        /// <summary>
        /// 插件上下文
        /// </summary>
        protected IPluginContext Context { get; private set; }

        /// <summary>
        /// 启动时间
        /// </summary>
        protected DateTime StartTime { get; private set; } = DateTime.MinValue;

        #endregion

        #region 事件

        /// <summary>
        /// 插件状态变更事件
        /// </summary>
        public event EventHandler<PluginStatusChangedEventArgs> StatusChanged;

        /// <summary>
        /// 插件错误事件
        /// </summary>
        public event EventHandler<PluginErrorEventArgs> ErrorOccurred;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化插件基类
        /// </summary>
        protected PluginBase()
        {
            // 从特性中获取元数据
            var metadataAttribute = GetType().GetCustomAttributes(typeof(PluginMetadataAttribute), false);
            if (metadataAttribute.Length > 0 && metadataAttribute[0] is PluginMetadataAttribute attr)
            {
                Metadata = new PluginMetadata
                {
                    Id = attr.Id,
                    Name = attr.Name,
                    Version = Version.TryParse(attr.Version, out var version) ? version : new Version(1, 0, 0, 0),
                    Description = attr.Description,
                    Author = attr.Author,
                    Category = attr.Category,
                    SupportedPlatforms = attr.SupportedPlatforms,
                    MinFrameworkVersion = Version.TryParse(attr.MinFrameworkVersion, out var minVersion) ? minVersion : new Version(1, 0, 0, 0)
                };
            }
            else
            {
                // 默认元数据
                Metadata = new PluginMetadata
                {
                    Id = GetType().FullName ?? GetType().Name,
                    Name = GetType().Name,
                    Version = new Version(1, 0, 0, 0),
                    Description = "插件描述",
                    Author = "Unknown",
                    Category = "General"
                };
            }

            // 设置默认配置（延迟到初始化时设置，避免在构造函数中调用虚方法）
            Configuration = new Dictionary<string, object>();
        }

        #endregion

        #region 插件生命周期

        /// <summary>
        /// 初始化插件
        /// </summary>
        public virtual async Task InitializeAsync(IPluginContext context, CancellationToken cancellationToken = default)
        {
            Context = context;
            Status = PluginStatus.Initializing;

            // 设置默认配置（在这里调用虚方法是安全的，因为派生类构造函数已经完成）
            if (Configuration.Count == 0)
            {
                Configuration = GetDefaultConfiguration();
            }

            // 模拟初始化过程
            await Task.Delay(100, cancellationToken);

            Status = PluginStatus.Initialized;
        }

        /// <summary>
        /// 启动插件
        /// </summary>
        public virtual async Task StartAsync(CancellationToken cancellationToken = default)
        {
            if (Status != PluginStatus.Initialized && Status != PluginStatus.Stopped)
            {
                throw new InvalidOperationException($"插件状态不正确，无法启动。当前状态: {Status}");
            }

            Status = PluginStatus.Starting;
            StartTime = DateTime.Now;
            
            // 模拟启动过程
            await Task.Delay(50, cancellationToken);
            
            Status = PluginStatus.Running;
        }

        /// <summary>
        /// 停止插件
        /// </summary>
        public virtual async Task StopAsync(CancellationToken cancellationToken = default)
        {
            if (Status != PluginStatus.Running)
            {
                return;
            }

            Status = PluginStatus.Stopping;
            
            // 模拟停止过程
            await Task.Delay(50, cancellationToken);
            
            Status = PluginStatus.Stopped;
        }

        /// <summary>
        /// 暂停插件
        /// </summary>
        public virtual async Task PauseAsync(CancellationToken cancellationToken = default)
        {
            if (Status == PluginStatus.Running)
            {
                await StopAsync(cancellationToken);
            }
        }

        /// <summary>
        /// 恢复插件
        /// </summary>
        public virtual async Task ResumeAsync(CancellationToken cancellationToken = default)
        {
            if (Status == PluginStatus.Stopped)
            {
                await StartAsync(cancellationToken);
            }
        }

        /// <summary>
        /// 重启插件
        /// </summary>
        public virtual async Task RestartAsync(CancellationToken cancellationToken = default)
        {
            if (Status == PluginStatus.Running)
            {
                await StopAsync(cancellationToken);
            }
            await StartAsync(cancellationToken);
        }

        /// <summary>
        /// 验证插件
        /// </summary>
        public virtual PluginValidationResult Validate()
        {
            var result = new PluginValidationResult();

            // 基本验证
            if (string.IsNullOrEmpty(Id))
            {
                result.AddError("插件ID不能为空");
            }

            if (string.IsNullOrEmpty(Name))
            {
                result.AddError("插件名称不能为空");
            }

            if (Version == null)
            {
                result.AddError("插件版本不能为空");
            }

            return result;
        }

        /// <summary>
        /// 获取插件信息
        /// </summary>
        public virtual PluginInfo GetInfo()
        {
            return new PluginInfo
            {
                Metadata = Metadata,
                Dependencies = Dependencies,
                FilePath = "",
                AssemblyName = GetType().Assembly.GetName().Name ?? "",
                TypeName = GetType().FullName ?? GetType().Name
            };
        }

        #endregion

        #region 配置管理

        /// <summary>
        /// 获取默认配置
        /// </summary>
        public virtual Dictionary<string, object> GetDefaultConfiguration()
        {
            return new Dictionary<string, object>();
        }

        /// <summary>
        /// 验证配置
        /// </summary>
        public virtual bool ValidateConfiguration(Dictionary<string, object> configuration)
        {
            return true;
        }

        /// <summary>
        /// 应用配置
        /// </summary>
        public virtual void ApplyConfiguration(Dictionary<string, object> configuration)
        {
            if (ValidateConfiguration(configuration))
            {
                Configuration = configuration;
            }
            else
            {
                throw new ArgumentException("配置验证失败");
            }
        }

        /// <summary>
        /// 更新配置
        /// </summary>
        public virtual bool UpdateConfiguration(Dictionary<string, object> configuration)
        {
            try
            {
                ApplyConfiguration(configuration);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取配置值
        /// </summary>
        public virtual T GetConfigurationValue<T>(string key, T defaultValue = default(T))
        {
            if (Configuration.TryGetValue(key, out var value))
            {
                try
                {
                    if (value is T directValue)
                        return directValue;

                    return (T)Convert.ChangeType(value, typeof(T));
                }
                catch
                {
                    return defaultValue;
                }
            }
            return defaultValue;
        }

        /// <summary>
        /// 设置配置值
        /// </summary>
        public virtual void SetConfigurationValue(string key, object value)
        {
            Configuration[key] = value;
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 触发状态变更事件
        /// </summary>
        protected virtual void OnStatusChanged(PluginStatus newStatus)
        {
            OnStatusChanged(_status, newStatus);
        }

        /// <summary>
        /// 触发状态变更事件
        /// </summary>
        protected virtual void OnStatusChanged(PluginStatus oldStatus, PluginStatus newStatus)
        {
            try
            {
                StatusChanged?.Invoke(this, new PluginStatusChangedEventArgs(this, oldStatus, newStatus));
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"状态变更事件处理失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 触发错误事件
        /// </summary>
        protected virtual void OnErrorOccurred(string errorMessage, Exception exception = null)
        {
            try
            {
                ErrorOccurred?.Invoke(this, new PluginErrorEventArgs(this, errorMessage, exception));
            }
            catch
            {
                // 避免在错误处理中再次抛出异常
            }
        }

        #endregion

        #region IDisposable 实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    try
                    {
                        if (Status == PluginStatus.Running)
                        {
                            StopAsync().Wait(5000);
                        }
                    }
                    catch
                    {
                        // 忽略停止时的错误
                    }
                }
                _disposed = true;
            }
        }

        #endregion
    }
}
