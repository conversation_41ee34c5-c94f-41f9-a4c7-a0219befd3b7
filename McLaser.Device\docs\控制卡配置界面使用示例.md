# 控制卡配置界面使用示例

本文档提供了如何在应用程序中使用控制卡配置界面的示例代码。

## 1. 初始化

在应用程序启动时，需要初始化Motion模块，注册各种控制卡配置控件：

```csharp
// 在App.xaml.cs的OnStartup方法中
protected override void OnStartup(StartupEventArgs e)
{
    base.OnStartup(e);
    
    // 初始化Motion模块
    McLaser.Devices.Motion.MotionInitializer.Initialize();
    
    // 其他初始化代码...
}
```

## 2. 使用工厂创建控制卡配置控件

根据控制卡类型，使用工厂创建对应的配置控件：

```csharp
// 创建控制卡实例
var card = new CardBase();
card.Type = CardType.PMAC;  // 设置卡类型

// 使用工厂创建对应的配置控件
var configControl = CardConfigControlFactory.CreateConfigControl(card);

// 显示配置界面
ConfigContainer.Content = configControl.GetControl();
configControl.SetCard(card);
```

## 3. 创建配置窗口

可以创建一个独立的窗口来显示控制卡配置界面：

```csharp
public class CardConfigWindow : Window
{
    private ICardConfigControl _configControl;
    
    public CardConfigWindow(CardBase card)
    {
        Title = $"{card.Name} 配置";
        Width = 800;
        Height = 600;
        
        // 创建对应的配置控件
        _configControl = CardConfigControlFactory.CreateConfigControl(card);
        
        // 显示配置界面
        Content = _configControl.GetControl();
        _configControl.SetCard(card);
        
        // 窗口关闭时停止状态更新
        Closed += (s, e) => 
        {
            // 如果有需要释放的资源，在这里处理
        };
    }
}

// 使用示例
public void ShowCardConfig(CardBase card)
{
    var window = new CardConfigWindow(card);
    window.Show();
}
```

## 4. 添加到现有界面

也可以将配置控件添加到现有界面中：

```csharp
public void AddCardConfigToPanel(CardBase card, Panel container)
{
    // 创建对应的配置控件
    var configControl = CardConfigControlFactory.CreateConfigControl(card);
    
    // 设置关联的控制卡
    configControl.SetCard(card);
    
    // 添加到容器中
    container.Children.Add(configControl.GetControl());
}
```

## 5. 测试所有控制卡类型

可以使用测试窗口来测试不同类型的控制卡配置界面：

```csharp
// 创建测试窗口
var testWindow = new CardConfigTestWindow();
testWindow.ShowDialog();
```

## 6. 定制配置控件

如果需要为新的控制卡类型创建配置控件，可以参考以下步骤：

1. 在特定控制卡的项目中创建一个实现`ICardConfigControl`接口的控件
2. 在初始化时注册到工厂中

```csharp
// 1. 实现接口
public class MyCardConfigControl : UserControl, ICardConfigControl
{
    // 实现接口方法...
}

// 2. 注册到工厂
CardConfigControlFactory.RegisterCreator(CardType.MyCard, () => new MyCardConfigControl());
```

## 7. 注意事项

1. 确保在使用控制卡配置界面前已经初始化Motion模块
2. 控制卡类型需要正确设置，否则可能会创建错误的配置控件
3. 关闭窗口或卸载控件时，需要注意释放资源
4. 如果配置界面需要访问硬件，确保异常处理和线程安全 