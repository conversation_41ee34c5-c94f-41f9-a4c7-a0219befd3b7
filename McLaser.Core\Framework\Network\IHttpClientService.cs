using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace McLaser.Core.Framework.Network
{
    /// <summary>
    /// HTTP客户端服务接口
    /// 提供HTTP通信功能
    /// </summary>
    public interface IHttpClientService
    {
        /// <summary>
        /// 请求开始事件
        /// </summary>
        event EventHandler<HttpRequestEventArgs>? RequestStarted;

        /// <summary>
        /// 请求完成事件
        /// </summary>
        event EventHandler<HttpResponseEventArgs>? RequestCompleted;

        /// <summary>
        /// 请求失败事件
        /// </summary>
        event EventHandler<HttpRequestFailedEventArgs>? RequestFailed;

        /// <summary>
        /// 基础地址
        /// </summary>
        string? BaseAddress { get; set; }

        /// <summary>
        /// 默认请求头
        /// </summary>
        IDictionary<string, string> DefaultHeaders { get; }

        /// <summary>
        /// 超时时间
        /// </summary>
        TimeSpan Timeout { get; set; }

        /// <summary>
        /// 最大重试次数
        /// </summary>
        int MaxRetryCount { get; set; }

        /// <summary>
        /// 重试延迟
        /// </summary>
        TimeSpan RetryDelay { get; set; }

        // GET请求
        /// <summary>
        /// 发送GET请求
        /// </summary>
        /// <param name="requestUri">请求URI</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>HTTP响应</returns>
        Task<HttpResponseMessage> GetAsync(string requestUri, CancellationToken cancellationToken = default);

        /// <summary>
        /// 发送GET请求并返回字符串
        /// </summary>
        /// <param name="requestUri">请求URI</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>响应字符串</returns>
        Task<string> GetStringAsync(string requestUri, CancellationToken cancellationToken = default);

        /// <summary>
        /// 发送GET请求并返回字节数组
        /// </summary>
        /// <param name="requestUri">请求URI</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>响应字节数组</returns>
        Task<byte[]> GetByteArrayAsync(string requestUri, CancellationToken cancellationToken = default);

        /// <summary>
        /// 发送GET请求并返回流
        /// </summary>
        /// <param name="requestUri">请求URI</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>响应流</returns>
        Task<Stream> GetStreamAsync(string requestUri, CancellationToken cancellationToken = default);

        // POST请求
        /// <summary>
        /// 发送POST请求
        /// </summary>
        /// <param name="requestUri">请求URI</param>
        /// <param name="content">请求内容</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>HTTP响应</returns>
        Task<HttpResponseMessage> PostAsync(string requestUri, HttpContent? content, 
                                          CancellationToken cancellationToken = default);

        /// <summary>
        /// 发送POST请求（JSON内容）
        /// </summary>
        /// <param name="requestUri">请求URI</param>
        /// <param name="jsonContent">JSON内容</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>HTTP响应</returns>
        Task<HttpResponseMessage> PostJsonAsync(string requestUri, string jsonContent, 
                                               CancellationToken cancellationToken = default);

        /// <summary>
        /// 发送POST请求（表单内容）
        /// </summary>
        /// <param name="requestUri">请求URI</param>
        /// <param name="formData">表单数据</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>HTTP响应</returns>
        Task<HttpResponseMessage> PostFormAsync(string requestUri, IEnumerable<KeyValuePair<string, string>> formData, 
                                               CancellationToken cancellationToken = default);

        // PUT请求
        /// <summary>
        /// 发送PUT请求
        /// </summary>
        /// <param name="requestUri">请求URI</param>
        /// <param name="content">请求内容</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>HTTP响应</returns>
        Task<HttpResponseMessage> PutAsync(string requestUri, HttpContent? content, 
                                         CancellationToken cancellationToken = default);

        /// <summary>
        /// 发送PUT请求（JSON内容）
        /// </summary>
        /// <param name="requestUri">请求URI</param>
        /// <param name="jsonContent">JSON内容</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>HTTP响应</returns>
        Task<HttpResponseMessage> PutJsonAsync(string requestUri, string jsonContent, 
                                              CancellationToken cancellationToken = default);

        // DELETE请求
        /// <summary>
        /// 发送DELETE请求
        /// </summary>
        /// <param name="requestUri">请求URI</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>HTTP响应</returns>
        Task<HttpResponseMessage> DeleteAsync(string requestUri, CancellationToken cancellationToken = default);

        // PATCH请求
        /// <summary>
        /// 发送PATCH请求
        /// </summary>
        /// <param name="requestUri">请求URI</param>
        /// <param name="content">请求内容</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>HTTP响应</returns>
        Task<HttpResponseMessage> PatchAsync(string requestUri, HttpContent? content, 
                                           CancellationToken cancellationToken = default);

        // 文件操作
        /// <summary>
        /// 下载文件
        /// </summary>
        /// <param name="requestUri">请求URI</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="progress">进度报告</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>下载任务</returns>
        Task DownloadFileAsync(string requestUri, string filePath, 
                              IProgress<DownloadProgress>? progress = null, 
                              CancellationToken cancellationToken = default);

        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="requestUri">请求URI</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="fieldName">字段名称</param>
        /// <param name="progress">进度报告</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>HTTP响应</returns>
        Task<HttpResponseMessage> UploadFileAsync(string requestUri, string filePath, string fieldName = "file", 
                                                 IProgress<UploadProgress>? progress = null, 
                                                 CancellationToken cancellationToken = default);

        /// <summary>
        /// 上传多个文件
        /// </summary>
        /// <param name="requestUri">请求URI</param>
        /// <param name="files">文件列表</param>
        /// <param name="progress">进度报告</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>HTTP响应</returns>
        Task<HttpResponseMessage> UploadFilesAsync(string requestUri, IEnumerable<FileUploadInfo> files, 
                                                  IProgress<UploadProgress>? progress = null, 
                                                  CancellationToken cancellationToken = default);

        // 自定义请求
        /// <summary>
        /// 发送自定义请求
        /// </summary>
        /// <param name="request">HTTP请求消息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>HTTP响应</returns>
        Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken = default);

        // 配置方法
        /// <summary>
        /// 添加默认请求头
        /// </summary>
        /// <param name="name">头名称</param>
        /// <param name="value">头值</param>
        void AddDefaultHeader(string name, string value);

        /// <summary>
        /// 移除默认请求头
        /// </summary>
        /// <param name="name">头名称</param>
        /// <returns>是否成功移除</returns>
        bool RemoveDefaultHeader(string name);

        /// <summary>
        /// 清除所有默认请求头
        /// </summary>
        void ClearDefaultHeaders();

        /// <summary>
        /// 设置认证头
        /// </summary>
        /// <param name="scheme">认证方案</param>
        /// <param name="parameter">认证参数</param>
        void SetAuthenticationHeader(string scheme, string parameter);

        /// <summary>
        /// 设置Bearer令牌
        /// </summary>
        /// <param name="token">访问令牌</param>
        void SetBearerToken(string token);

        /// <summary>
        /// 清除认证头
        /// </summary>
        void ClearAuthenticationHeader();
    }

    /// <summary>
    /// 下载进度信息
    /// </summary>
    public class DownloadProgress
    {
        /// <summary>
        /// 已下载字节数
        /// </summary>
        public long BytesReceived { get; set; }

        /// <summary>
        /// 总字节数
        /// </summary>
        public long? TotalBytesToReceive { get; set; }

        /// <summary>
        /// 下载百分比
        /// </summary>
        public double? ProgressPercentage => TotalBytesToReceive.HasValue && TotalBytesToReceive > 0
            ? (double)BytesReceived / TotalBytesToReceive.Value * 100
            : null;

        /// <summary>
        /// 下载速度（字节/秒）
        /// </summary>
        public long BytesPerSecond { get; set; }

        /// <summary>
        /// 剩余时间
        /// </summary>
        public TimeSpan? EstimatedTimeRemaining { get; set; }
    }

    /// <summary>
    /// 上传进度信息
    /// </summary>
    public class UploadProgress
    {
        /// <summary>
        /// 已上传字节数
        /// </summary>
        public long BytesSent { get; set; }

        /// <summary>
        /// 总字节数
        /// </summary>
        public long TotalBytesToSend { get; set; }

        /// <summary>
        /// 上传百分比
        /// </summary>
        public double ProgressPercentage => TotalBytesToSend > 0
            ? (double)BytesSent / TotalBytesToSend * 100
            : 0;

        /// <summary>
        /// 上传速度（字节/秒）
        /// </summary>
        public long BytesPerSecond { get; set; }

        /// <summary>
        /// 剩余时间
        /// </summary>
        public TimeSpan? EstimatedTimeRemaining { get; set; }
    }

    /// <summary>
    /// 文件上传信息
    /// </summary>
    public class FileUploadInfo
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="fieldName">字段名称</param>
        /// <param name="fileName">文件名称</param>
        public FileUploadInfo(string filePath, string fieldName, string? fileName = null)
        {
            FilePath = filePath ?? throw new ArgumentNullException(nameof(filePath));
            FieldName = fieldName ?? throw new ArgumentNullException(nameof(fieldName));
            FileName = fileName ?? Path.GetFileName(filePath);
        }

        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath { get; }

        /// <summary>
        /// 字段名称
        /// </summary>
        public string FieldName { get; }

        /// <summary>
        /// 文件名称
        /// </summary>
        public string FileName { get; }

        /// <summary>
        /// 内容类型
        /// </summary>
        public string? ContentType { get; set; }
    }

    /// <summary>
    /// HTTP请求事件参数
    /// </summary>
    public class HttpRequestEventArgs : EventArgs
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="method">HTTP方法</param>
        /// <param name="requestUri">请求URI</param>
        public HttpRequestEventArgs(HttpMethod method, string requestUri)
        {
            Method = method;
            RequestUri = requestUri;
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// HTTP方法
        /// </summary>
        public HttpMethod Method { get; }

        /// <summary>
        /// 请求URI
        /// </summary>
        public string RequestUri { get; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// 请求ID
        /// </summary>
        public string RequestId { get; set; } = Guid.NewGuid().ToString();
    }

    /// <summary>
    /// HTTP响应事件参数
    /// </summary>
    public class HttpResponseEventArgs : EventArgs
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="request">请求事件参数</param>
        /// <param name="response">HTTP响应</param>
        /// <param name="duration">请求持续时间</param>
        public HttpResponseEventArgs(HttpRequestEventArgs request, HttpResponseMessage response, TimeSpan duration)
        {
            Request = request;
            Response = response;
            Duration = duration;
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// 请求信息
        /// </summary>
        public HttpRequestEventArgs Request { get; }

        /// <summary>
        /// HTTP响应
        /// </summary>
        public HttpResponseMessage Response { get; }

        /// <summary>
        /// 请求持续时间
        /// </summary>
        public TimeSpan Duration { get; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; }
    }

    /// <summary>
    /// HTTP请求失败事件参数
    /// </summary>
    public class HttpRequestFailedEventArgs : EventArgs
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="request">请求事件参数</param>
        /// <param name="exception">异常信息</param>
        /// <param name="duration">请求持续时间</param>
        public HttpRequestFailedEventArgs(HttpRequestEventArgs request, Exception exception, TimeSpan duration)
        {
            Request = request;
            Exception = exception;
            Duration = duration;
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// 请求信息
        /// </summary>
        public HttpRequestEventArgs Request { get; }

        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception Exception { get; }

        /// <summary>
        /// 请求持续时间
        /// </summary>
        public TimeSpan Duration { get; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; }
    }
}
