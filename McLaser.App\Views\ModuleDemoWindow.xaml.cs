using System.Windows;
using System.Windows.Controls;
using McLaser.App.ViewModels;

namespace McLaser.App.Views
{
    /// <summary>
    /// ModuleDemoWindow.xaml 的交互逻辑
    /// 展示McLaser.Core框架的三个核心模块功能
    /// </summary>
    public partial class ModuleDemoWindow : Window
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public ModuleDemoWindow()
        {
            InitializeComponent();
            
            // 订阅密码框变更事件
            PasswordBox.PasswordChanged += OnPasswordChanged;
        }

        /// <summary>
        /// 密码框变更事件处理
        /// </summary>
        private void OnPasswordChanged(object sender, RoutedEventArgs e)
        {
            if (DataContext is ModuleDemoViewModel viewModel && sender is PasswordBox passwordBox)
            {
                viewModel.LoginPassword = passwordBox.Password;
            }
        }

        /// <summary>
        /// 窗口关闭时清理资源
        /// </summary>
        protected override void OnClosed(System.EventArgs e)
        {
            // 取消事件订阅
            PasswordBox.PasswordChanged -= OnPasswordChanged;
            
            // 清理ViewModel资源
            if (DataContext is ModuleDemoViewModel viewModel)
            {
                viewModel.Dispose();
            }
            
            base.OnClosed(e);
        }
    }
}
