<Window x:Class="McLaser.App.Views.EventBusDemoWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="EventBus事件总线演示"
        Height="700" Width="1000"
        MinHeight="600" MinWidth="800"
        WindowStartupLocation="CenterScreen">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        
        <!-- 顶部工具栏 -->
        <ToolBar Grid.Row="0">
            <Button Content="初始化EventBus" Command="{Binding InitializeEventBusCommand}" />
            <Separator />
            <Button Content="发布设备事件" Command="{Binding PublishDeviceEventCommand}" />
            <Button Content="发布系统通知" Command="{Binding PublishSystemNotificationCommand}" />
            <Button Content="发布用户操作事件" Command="{Binding PublishUserActionEventCommand}" />
            <Separator />
            <Button Content="清空日志" Command="{Binding ClearLogCommand}" />
            <Button Content="导出日志" Command="{Binding ExportLogCommand}" />
        </ToolBar>
        
        <!-- 主内容区域 -->
        <Grid Grid.Row="1" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="5" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            
            <!-- 左侧：事件发布控制面板 -->
            <GroupBox Grid.Column="0" Header="事件发布控制面板" Padding="10">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!-- EventBus状态 -->
                        <GroupBox Header="EventBus状态" Margin="0,0,0,15">
                            <StackPanel>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    
                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="状态：" Margin="0,0,10,5" />
                                    <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding EventBusStatus}" FontWeight="Bold" Margin="0,0,0,5" />
                                    
                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="订阅者数量：" Margin="0,0,10,5" />
                                    <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding SubscriberCount}" Margin="0,0,0,5" />
                                    
                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="已发布事件：" Margin="0,0,10,5" />
                                    <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding PublishedEventCount}" Margin="0,0,0,5" />
                                    
                                    <TextBlock Grid.Row="3" Grid.Column="0" Text="处理中事件：" Margin="0,0,10,5" />
                                    <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding ProcessingEventCount}" Margin="0,0,0,5" />
                                    
                                    <TextBlock Grid.Row="4" Grid.Column="0" Text="异常次数：" Margin="0,0,10,5" />
                                    <TextBlock Grid.Row="4" Grid.Column="1" Text="{Binding ExceptionCount}" Foreground="Red" Margin="0,0,0,5" />
                                </Grid>
                            </StackPanel>
                        </GroupBox>
                        
                        <!-- 设备状态事件 -->
                        <GroupBox Header="设备状态事件" Margin="0,0,0,15">
                            <StackPanel>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    
                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="设备ID：" VerticalAlignment="Center" Margin="0,0,10,5" />
                                    <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding DeviceId}" Margin="0,0,0,5" />
                                    
                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="设备名称：" VerticalAlignment="Center" Margin="0,0,10,5" />
                                    <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding DeviceName}" Margin="0,0,0,5" />
                                    
                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="设备状态：" VerticalAlignment="Center" Margin="0,0,10,5" />
                                    <ComboBox Grid.Row="2" Grid.Column="1" SelectedItem="{Binding SelectedDeviceStatus}" 
                                              ItemsSource="{Binding DeviceStatusList}" Margin="0,0,0,10" />
                                    
                                    <Button Grid.Row="3" Grid.Column="1" Content="发布设备状态变更事件" 
                                            Command="{Binding PublishDeviceEventCommand}" Padding="10,5" />
                                </Grid>
                            </StackPanel>
                        </GroupBox>
                        
                        <!-- 系统通知事件 -->
                        <GroupBox Header="系统通知事件" Margin="0,0,0,15">
                            <StackPanel>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    
                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="通知类型：" VerticalAlignment="Center" Margin="0,0,10,5" />
                                    <ComboBox Grid.Row="0" Grid.Column="1" SelectedItem="{Binding SelectedNotificationType}" 
                                              ItemsSource="{Binding NotificationTypeList}" Margin="0,0,0,5" />
                                    
                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="标题：" VerticalAlignment="Center" Margin="0,0,10,5" />
                                    <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding NotificationTitle}" Margin="0,0,0,5" />
                                    
                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="消息：" VerticalAlignment="Top" Margin="0,5,10,5" />
                                    <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding NotificationMessage}" 
                                             TextWrapping="Wrap" Height="60" Margin="0,0,0,10" />
                                    
                                    <Button Grid.Row="3" Grid.Column="1" Content="发布系统通知事件" 
                                            Command="{Binding PublishSystemNotificationCommand}" Padding="10,5" />
                                </Grid>
                            </StackPanel>
                        </GroupBox>
                        
                        <!-- 用户操作事件 -->
                        <GroupBox Header="用户操作事件" Margin="0,0,0,15">
                            <StackPanel>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    
                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="用户名：" VerticalAlignment="Center" Margin="0,0,10,5" />
                                    <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding UserName}" Margin="0,0,0,5" />
                                    
                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="操作类型：" VerticalAlignment="Center" Margin="0,0,10,5" />
                                    <ComboBox Grid.Row="1" Grid.Column="1" SelectedItem="{Binding SelectedActionType}" 
                                              ItemsSource="{Binding ActionTypeList}" Margin="0,0,0,5" />
                                    
                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="操作描述：" VerticalAlignment="Top" Margin="0,5,10,5" />
                                    <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding ActionDescription}" 
                                             TextWrapping="Wrap" Height="40" Margin="0,0,0,10" />
                                    
                                    <Button Grid.Row="3" Grid.Column="1" Content="发布用户操作事件" 
                                            Command="{Binding PublishUserActionEventCommand}" Padding="10,5" />
                                </Grid>
                            </StackPanel>
                        </GroupBox>
                        
                        <!-- 事件处理选项 -->
                        <GroupBox Header="事件处理选项">
                            <StackPanel>
                                <CheckBox Content="启用异步处理" IsChecked="{Binding EnableAsyncProcessing}" Margin="0,0,0,5" />
                                <CheckBox Content="启用事件过滤" IsChecked="{Binding EnableEventFiltering}" Margin="0,0,0,5" />
                                <CheckBox Content="启用事件拦截" IsChecked="{Binding EnableEventInterception}" Margin="0,0,0,5" />
                                <CheckBox Content="启用事件持久化" IsChecked="{Binding EnableEventPersistence}" Margin="0,0,0,10" />
                                
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="处理超时(秒)：" VerticalAlignment="Center" Margin="0,0,10,0" />
                                    <TextBox Text="{Binding HandlingTimeoutSeconds}" Width="60" />
                                </StackPanel>
                            </StackPanel>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </GroupBox>
            
            <!-- 分隔符 -->
            <GridSplitter Grid.Column="1" HorizontalAlignment="Stretch" Background="LightGray" />
            
            <!-- 右侧：事件日志和统计 -->
            <GroupBox Grid.Column="2" Header="事件日志和统计" Padding="10">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="200" />
                    </Grid.RowDefinitions>
                    
                    <!-- 统计信息 -->
                    <GroupBox Grid.Row="0" Header="统计信息" Margin="0,0,0,10">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="{Binding TotalEventsPublished, StringFormat='总发布事件: {0}'}" Margin="0,0,0,3" />
                                <TextBlock Text="{Binding TotalEventsHandled, StringFormat='总处理事件: {0}'}" Margin="0,0,0,3" />
                                <TextBlock Text="{Binding AverageHandlingTime, StringFormat='平均处理时间: {0:F2}ms'}" Margin="0,0,0,3" />
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="{Binding ActiveSubscribers, StringFormat='活跃订阅者: {0}'}" Margin="0,0,0,3" />
                                <TextBlock Text="{Binding EventsPerSecond, StringFormat='事件/秒: {0:F1}'}" Margin="0,0,0,3" />
                                <TextBlock Text="{Binding LastEventTime, StringFormat='最后事件: {0:HH:mm:ss}'}" Margin="0,0,0,3" />
                            </StackPanel>
                        </Grid>
                    </GroupBox>
                    
                    <!-- 事件日志 -->
                    <GroupBox Grid.Row="1" Header="事件日志" Margin="0,0,0,10">
                        <ListBox ItemsSource="{Binding EventLogs}" 
                                 ScrollViewer.HorizontalScrollBarVisibility="Auto"
                                 ScrollViewer.VerticalScrollBarVisibility="Auto">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="80" />
                                            <ColumnDefinition Width="120" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        
                                        <TextBlock Grid.Column="0" Text="{Binding Timestamp, StringFormat='{}{0:HH:mm:ss}'}" 
                                                   FontFamily="Consolas" FontSize="10" />
                                        <TextBlock Grid.Column="1" Text="{Binding EventType}" 
                                                   FontWeight="Bold" FontSize="10" />
                                        <TextBlock Grid.Column="2" Text="{Binding Message}" 
                                                   TextWrapping="Wrap" FontSize="10" />
                                    </Grid>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                    </GroupBox>
                    
                    <!-- 订阅者列表 -->
                    <TextBlock Grid.Row="2" Text="活跃订阅者" FontWeight="Bold" Margin="0,0,0,5" />
                    <ListBox Grid.Row="3" ItemsSource="{Binding ActiveSubscribersList}">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    
                                    <TextBlock Grid.Column="0" Text="{Binding Name}" FontWeight="Bold" />
                                    <TextBlock Grid.Column="1" Text="{Binding EventType}" Margin="10,0,0,0" />
                                    <TextBlock Grid.Column="2" Text="{Binding HandledCount, StringFormat='处理: {0}'}" 
                                               Margin="10,0,0,0" FontSize="10" />
                                </Grid>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </Grid>
            </GroupBox>
        </Grid>
        
        <!-- 状态栏 -->
        <StatusBar Grid.Row="2">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}" />
            </StatusBarItem>
            <Separator />
            <StatusBarItem>
                <TextBlock Text="{Binding EventBusStatus, StringFormat='EventBus状态: {0}'}" />
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
