using System;
using System.Windows;
using McLaser.App.ViewModels;

namespace McLaser.App.Views
{
    /// <summary>
    /// 插件管理演示窗口
    /// 展示插件系统的完整功能
    /// </summary>
    public partial class PluginDemoWindow : Window
    {
        #region 属性

        /// <summary>
        /// 视图模型
        /// </summary>
        public PluginDemoViewModel ViewModel { get; }

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化插件管理演示窗口
        /// </summary>
        public PluginDemoWindow()
        {
            InitializeComponent();
            
            // 创建视图模型
            ViewModel = new PluginDemoViewModel();
            DataContext = ViewModel;

            // 订阅窗口事件
            Loaded += OnWindowLoaded;
            Closing += OnWindowClosing;
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 窗口加载事件处理
        /// </summary>
        private async void OnWindowLoaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // 自动初始化插件管理器
                if (ViewModel.InitializePluginManagerCommand.CanExecute(null))
                {
                    ViewModel.InitializePluginManagerCommand.Execute(null);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"窗口初始化失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 窗口关闭事件处理
        /// </summary>
        private void OnWindowClosing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                // 清理资源
                ViewModel?.Dispose();
            }
            catch (Exception ex)
            {
                // 记录错误但不阻止关闭
                System.Diagnostics.Debug.WriteLine($"窗口关闭时清理资源失败: {ex.Message}");
            }
        }

        #endregion
    }
}
