using System;
using System.ComponentModel;
using McLaser.Device;

namespace McLaser.Devices.Motion.GTS
{
    /// <summary>
    /// 固高GTS IO类
    /// 表示固高GTS运动控制器中的IO点，包含IO的配置参数和状态信息
    /// </summary>
    [Serializable]
    public class IOGTS : IOBase
    {
        #region 私有字段

        private int _bit = 0;
        private bool _isReverse = false;
        private string _description = string.Empty;
        private IOTypeGTS _ioType = IOTypeGTS.DI;

        #endregion

        #region 属性

        /// <summary>
        /// 位号
        /// </summary>
        [Category("GTS IO"), DisplayName("位号")]
        public override int Bit
        {
            get => _bit;
            set
            {
                if (_bit != value)
                {
                    _bit = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 是否反向
        /// </summary>
        [Category("GTS IO"), DisplayName("是否反向")]
        public override bool IsReverse
        {
            get => _isReverse;
            set
            {
                if (_isReverse != value)
                {
                    _isReverse = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// IO描述
        /// </summary>
        [Category("GTS IO"), DisplayName("IO描述")]
        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// IO类型
        /// </summary>
        [Category("GTS IO"), DisplayName("IO类型")]
        public IOTypeGTS IOType
        {
            get => _ioType;
            set
            {
                if (_ioType != value)
                {
                    _ioType = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// IO状态
        /// </summary>
        [XmlIgnore, Browsable(false)]
        public override StatusBase Status { get; set; } = new StatusIOGTS();

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public IOGTS()
        {
            Name = "GTS IO";
        }

        /// <summary>
        /// 带参数的构造函数
        /// </summary>
        /// <param name="bit">位号</param>
        /// <param name="ioType">IO类型</param>
        /// <param name="description">描述</param>
        public IOGTS(int bit, IOTypeGTS ioType, string description = "") : this()
        {
            Bit = bit;
            IOType = ioType;
            Description = description;
            Name = $"{ioType}_{bit}";
        }

        #endregion

        #region 方法

        /// <summary>
        /// 验证IO参数
        /// </summary>
        /// <returns>验证结果</returns>
        public bool ValidateParameters()
        {
            try
            {
                // 检查位号
                if (Bit < 0 || Bit > 31)
                {
                    System.Diagnostics.Debug.WriteLine($"IO位号 {Bit} 超出范围 (0-31)");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"验证GTS IO参数异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取IO配置摘要
        /// </summary>
        /// <returns>配置摘要字符串</returns>
        public string GetConfigurationSummary()
        {
            return $"GTS {IOType} - 位:{Bit} - {Description}";
        }

        #endregion
    }

    /// <summary>
    /// GTS IO状态类
    /// </summary>
    [Serializable]
    public class StatusIOGTS : StatusBase
    {
        #region 私有字段

        private bool _value = false;
        private DateTime _lastChangeTime = DateTime.MinValue;
        private int _changeCount = 0;
        private string _errorMessage = string.Empty;

        #endregion

        #region 属性

        /// <summary>
        /// IO值
        /// </summary>
        [Category("GTS IO状态"), DisplayName("IO值")]
        public bool Value
        {
            get => _value;
            set
            {
                if (_value != value)
                {
                    _value = value;
                    LastChangeTime = DateTime.Now;
                    ChangeCount++;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// 最后变化时间
        /// </summary>
        [Category("GTS IO状态"), DisplayName("最后变化时间")]
        public DateTime LastChangeTime
        {
            get => _lastChangeTime;
            set
            {
                if (_lastChangeTime != value)
                {
                    _lastChangeTime = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 变化次数
        /// </summary>
        [Category("GTS IO状态"), DisplayName("变化次数")]
        public int ChangeCount
        {
            get => _changeCount;
            set
            {
                if (_changeCount != value)
                {
                    _changeCount = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 错误信息
        /// </summary>
        [Category("GTS IO状态"), DisplayName("错误信息")]
        public string ErrorMessage
        {
            get => _errorMessage;
            set
            {
                if (_errorMessage != value)
                {
                    _errorMessage = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// 状态文本描述
        /// </summary>
        [Category("GTS IO状态"), DisplayName("状态描述")]
        public override string StatusText
        {
            get
            {
                if (!string.IsNullOrEmpty(_errorMessage))
                    return $"错误: {_errorMessage}";

                return $"值: {(_value ? "高" : "低")} - 变化: {_changeCount}次";
            }
        }

        #endregion

        #region 方法

        /// <summary>
        /// 重置状态
        /// </summary>
        public override void Reset()
        {
            Value = false;
            LastChangeTime = DateTime.MinValue;
            ChangeCount = 0;
            ErrorMessage = string.Empty;
        }

        /// <summary>
        /// 获取状态摘要
        /// </summary>
        /// <returns>状态摘要字符串</returns>
        public override string GetStatusSummary()
        {
            return $"GTS IO - {StatusText}";
        }

        #endregion
    }

    /// <summary>
    /// GTS IO类型枚举
    /// </summary>
    public enum IOTypeGTS
    {
        /// <summary>
        /// 数字输入
        /// </summary>
        DI,
        
        /// <summary>
        /// 数字输出
        /// </summary>
        DO
    }
}
