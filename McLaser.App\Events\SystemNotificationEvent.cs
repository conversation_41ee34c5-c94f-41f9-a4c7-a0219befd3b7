using System;

namespace McLaser.App.Events
{
    /// <summary>
    /// 系统通知事件
    /// 用于系统级别的通知消息传递
    /// </summary>
    public class SystemNotificationEvent : IEvent
    {
        /// <summary>
        /// 事件ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 事件时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 通知类型
        /// </summary>
        public string Type { get; set; } = "信息";

        /// <summary>
        /// 通知标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 通知消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 事件来源
        /// </summary>
        public string Source { get; set; } = "System";

        /// <summary>
        /// 事件优先级
        /// </summary>
        public EventPriority Priority { get; set; } = EventPriority.Normal;

        /// <summary>
        /// 是否需要持久化
        /// </summary>
        public bool RequiresPersistence { get; set; } = false;

        /// <summary>
        /// 扩展数据
        /// </summary>
        public object? Data { get; set; }

        /// <summary>
        /// 通知级别
        /// </summary>
        public NotificationLevel Level { get; set; } = NotificationLevel.Info;

        /// <summary>
        /// 是否需要用户确认
        /// </summary>
        public bool RequiresUserConfirmation { get; set; } = false;

        /// <summary>
        /// 自动关闭时间（秒）
        /// </summary>
        public int AutoCloseSeconds { get; set; } = 0;

        /// <summary>
        /// 构造函数
        /// </summary>
        public SystemNotificationEvent()
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="type">通知类型</param>
        /// <param name="title">标题</param>
        /// <param name="message">消息</param>
        public SystemNotificationEvent(string type, string title, string message)
        {
            Type = type;
            Title = title;
            Message = message;
            
            // 根据类型设置优先级和级别
            switch (type.ToLower())
            {
                case "错误":
                    Priority = EventPriority.High;
                    Level = NotificationLevel.Error;
                    RequiresUserConfirmation = true;
                    break;
                case "警告":
                    Priority = EventPriority.Normal;
                    Level = NotificationLevel.Warning;
                    AutoCloseSeconds = 10;
                    break;
                case "成功":
                    Priority = EventPriority.Low;
                    Level = NotificationLevel.Success;
                    AutoCloseSeconds = 5;
                    break;
                default:
                    Priority = EventPriority.Low;
                    Level = NotificationLevel.Info;
                    AutoCloseSeconds = 3;
                    break;
            }
        }

        /// <summary>
        /// 获取事件描述
        /// </summary>
        /// <returns>事件描述</returns>
        public override string ToString()
        {
            return $"SystemNotification: [{Type}] {Title} - {Message}";
        }
    }

    /// <summary>
    /// 通知级别枚举
    /// </summary>
    public enum NotificationLevel
    {
        /// <summary>
        /// 信息
        /// </summary>
        Info,

        /// <summary>
        /// 成功
        /// </summary>
        Success,

        /// <summary>
        /// 警告
        /// </summary>
        Warning,

        /// <summary>
        /// 错误
        /// </summary>
        Error
    }
}
