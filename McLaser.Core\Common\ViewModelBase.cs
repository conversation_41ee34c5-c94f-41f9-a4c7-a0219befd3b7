using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace McLaser.Core.Common
{
    /// <summary>
    /// ViewModel基类，实现INotifyPropertyChanged接口
    /// 为WPF数据绑定提供属性变更通知支持
    /// </summary>
    public abstract class ViewModelBase : INotifyPropertyChanged, IDataErrorInfo, IDisposable
    {
        private bool _isDisposed;
        private readonly Dictionary<string, List<string>> _errors = new Dictionary<string, List<string>>();

        /// <summary>
        /// 属性变更事件
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// 触发属性变更通知
        /// </summary>
        /// <param name="propertyName">属性名称，自动获取调用者名称</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 设置属性值并触发变更通知
        /// </summary>
        /// <typeparam name="T">属性类型</typeparam>
        /// <param name="field">字段引用</param>
        /// <param name="value">新值</param>
        /// <param name="propertyName">属性名称，自动获取调用者名称</param>
        /// <returns>如果值发生变化返回true，否则返回false</returns>
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// 设置属性值并触发变更通知（带回调）
        /// </summary>
        /// <typeparam name="T">属性类型</typeparam>
        /// <param name="field">字段引用</param>
        /// <param name="value">新值</param>
        /// <param name="onChanged">值变更后的回调</param>
        /// <param name="propertyName">属性名称，自动获取调用者名称</param>
        /// <returns>如果值发生变化返回true，否则返回false</returns>
        protected bool SetProperty<T>(ref T field, T value, Action onChanged, [CallerMemberName] string? propertyName = null)
        {
            if (SetProperty(ref field, value, propertyName))
            {
                onChanged?.Invoke();
                return true;
            }
            return false;
        }

        /// <summary>
        /// 验证属性名称是否有效
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        /// <returns>如果属性存在返回true，否则返回false</returns>
        protected bool IsValidPropertyName(string propertyName)
        {
            return TypeDescriptor.GetProperties(this)[propertyName] != null;
        }

        #region IDataErrorInfo 实现

        /// <summary>
        /// 获取对象级别的错误信息
        /// </summary>
        public string Error
        {
            get
            {
                var errors = new List<string>();
                foreach (var errorList in _errors.Values)
                {
                    errors.AddRange(errorList);
                }
                return string.Join(Environment.NewLine, errors);
            }
        }

        /// <summary>
        /// 获取指定属性的错误信息
        /// </summary>
        /// <param name="columnName">属性名称</param>
        /// <returns>错误信息</returns>
        public string this[string columnName]
        {
            get
            {
                if (_errors.ContainsKey(columnName))
                {
                    return string.Join(Environment.NewLine, _errors[columnName]);
                }
                return string.Empty;
            }
        }

        /// <summary>
        /// 是否存在验证错误
        /// </summary>
        public bool HasErrors => _errors.Count > 0;

        /// <summary>
        /// 添加验证错误
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        /// <param name="error">错误信息</param>
        protected void AddError(string propertyName, string error)
        {
            if (!_errors.ContainsKey(propertyName))
            {
                _errors[propertyName] = new List<string>();
            }

            if (!_errors[propertyName].Contains(error))
            {
                _errors[propertyName].Add(error);
                OnPropertyChanged(nameof(HasErrors));
                OnPropertyChanged(nameof(Error));
            }
        }

        /// <summary>
        /// 清除指定属性的验证错误
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        protected void ClearErrors(string propertyName)
        {
            if (_errors.ContainsKey(propertyName))
            {
                _errors.Remove(propertyName);
                OnPropertyChanged(nameof(HasErrors));
                OnPropertyChanged(nameof(Error));
            }
        }

        /// <summary>
        /// 清除所有验证错误
        /// </summary>
        protected void ClearAllErrors()
        {
            if (_errors.Count > 0)
            {
                _errors.Clear();
                OnPropertyChanged(nameof(HasErrors));
                OnPropertyChanged(nameof(Error));
            }
        }

        #endregion

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_isDisposed && disposing)
            {
                // 子类可以重写此方法来释放特定资源
                OnDisposing();
                _isDisposed = true;
            }
        }

        /// <summary>
        /// 释放资源时调用，子类可重写
        /// </summary>
        protected virtual void OnDisposing()
        {
            // 默认实现为空，子类可以重写
        }

        /// <summary>
        /// 析构函数
        /// </summary>
        ~ViewModelBase()
        {
            Dispose(false);
        }
    }
}
