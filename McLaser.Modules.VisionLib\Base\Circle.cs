﻿using McLaser.Core.Common;
using System;

namespace McLaser.Modules.Vision 
{
    [Serializable]
    public class Circle : ObservableObject
    {
        public bool Status;
        public double _CenterX;
        // 圆心坐标X
        public double CenterX
        {
            get { return _CenterX; }
            set { Set(ref _CenterX, value); }
        }
        public double _CenterY;
        // 圆心坐标Y
        public double CenterY
        {
            get { return _CenterY; }
            set { Set(ref _CenterY, value); }
        }
        public double _Radius;
        // 半径
        public double Radius
        {
            get { return _Radius; }
            set { Set(ref _Radius, value); }
        }
        public Circle()
        {
            Status = true;
        }
        public Circle(bool status, double centerX, double centerY, double radius)
        {
            Status = status;
            CenterX = centerX;
            CenterY = centerY;
            Radius = radius;
        }
    }
}
