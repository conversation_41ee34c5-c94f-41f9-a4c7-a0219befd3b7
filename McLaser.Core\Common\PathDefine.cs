﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;

namespace McLaser.Core.Common
{
    public partial class PathDefine
    {
        #region Folder
        /// <summary>
        /// exe文件夹
        /// </summary>
        public static readonly string FolderApp = AppDomain.CurrentDomain.BaseDirectory;
        /// <summary>
        /// 参数文件夹
        /// </summary>
        public static readonly string FolderConfig = Path.Combine(FolderApp, "MyConfig");
        /// <summary>
        /// Recipe文件夹
        /// </summary>
        public static readonly string FolderRecipe = Path.Combine(FolderApp, "MyRecipe");
        /// <summary>
        /// Log文件夹
        /// </summary>
        public static readonly string FolderLog = Path.Combine(FolderApp, "MyLog");
        /// <summary>
        /// Resources文件夹
        /// </summary>
        public static readonly string FolderResources = Path.Combine(FolderApp, "MyResources");
        /// <summary>
        /// Update文件夹
        /// </summary>
        public static readonly string FolderUpdate = Path.Combine(FolderApp, "MyUpdate");
        /// <summary>
        /// 切割文件夹
        /// </summary>
        public static readonly string FolderCut = Path.Combine(FolderApp, "MyCut");
        /// <summary>
        /// 打标文件夹
        /// </summary>
        public static readonly string FolderMark = Path.Combine(FolderApp, "MyMark");
        #endregion

        #region Path
        /// <summary>
        /// 默认Dock
        /// </summary>
        public static readonly string PathDock = Path.Combine(FolderConfig, "DockPanel.xml");
        /// <summary>
        /// 上次Dock
        /// </summary>
        public static readonly string PathDockLast = Path.Combine(FolderConfig, "DockPanelLast.xml");
        /// <summary>
        /// 参数
        /// </summary>
        public static readonly string PathConfig = Path.Combine(FolderConfig, "Config.xml");
        /// <summary>
        /// RecipeID
        /// </summary>
        public static readonly string PathRecipeID = Path.Combine(FolderRecipe, "RecipeID.xml");
        /// <summary>
        /// 加密狗
        /// </summary>
        public static readonly string PathDog = Path.Combine(FolderConfig, "Dog.xml");
        /// <summary>
        /// 用户
        /// </summary>
        public static readonly string PathUser = Path.Combine(FolderConfig, "User.xml");
        /// <summary>
        /// 切割
        /// </summary>
        public static readonly string PathCut = Path.Combine(FolderCut, "CutData.xml");
        #endregion

        #region File
        /// <summary>
        /// Recipe
        /// </summary>
        public static readonly string FileRecipe = "Recipe.xml";
        #endregion
    }
}
