﻿using McLaser.Devices;
using McLaser.Modules.Vision;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace McLaser.Device
{ 
    /// <summary>
    /// CameraView.xaml 的交互逻辑
    /// </summary>
    public partial class CameraView : UserControl
    {
        public CameraView(CameraBase cam)
        {
            InitializeComponent();
            var view = new RenderViewWpf();
            this.DataContext = new CameraViewModel(cam, view);
        }
    }
}
