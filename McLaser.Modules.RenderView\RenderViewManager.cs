﻿using McLaser.Core.Container;
using McLaser.Modules.Base;
using McLaser.Modules.RenderView.Views;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace McLaser.Modules.RenderView
{
    [Export(typeof(IRenderViewManager))]
    public class RenderViewManager : IRenderViewManager, IDisposable
    {
        private const int _viewCount = 9; //窗口数量

        private static ConcurrentQueue<System.Action> _queueActions = new ConcurrentQueue<System.Action>();
        private Thread _threadRender = null;

        public static Dictionary<int, RenderViewWpf> mViewDic = new Dictionary<int, RenderViewWpf>();
        private static ManualResetEvent _eventRender = new ManualResetEvent(false);
        public static List<RenderViewWpf> ViewList { get; private set; } = new List<RenderViewWpf>();

        private bool _isDisposing = false;

        public RenderViewManager()
        {
            ViewList.Clear();
            for (int i = 0; i < _viewCount; i++)
            {
                var view = new RenderViewWpf();
                ViewList.Add(view);
            }

            _threadRender = new Thread(OnRender);
            _threadRender.IsBackground = true;
            _threadRender.Start();
        }


        public static void RegisterFrame(System.Action action)
        {
            _queueActions.Enqueue(action);
            _eventRender.Set();
        }

        private void OnRender()
        {
            while (true)
            {
                if (_eventRender.WaitOne(-1) || _isDisposing)   //等待到Set信号
                {
                    if (_isDisposing) return;

                    _eventRender.Reset();
                    while (_queueActions.Count > 0)
                    {
                        try
                        {
                            _queueActions.TryDequeue(out var action);
                            //Application.Current.Dispatcher.Invoke(action);
                            action();
                        }
                        catch
                        {
                        }
                    }
                }
            }
        }

        public IRenderView GenRenderView(bool hideBtn = false)
        {
            return (IRenderView)new RenderViewWpf();
        }

        public IRenderView GetView(int index)
        {
            return ViewList[index];
        }

        public void SetVieMode(ViewMode mode)
        {
            VisionView view = IoC.Get<VisionView>() as VisionView;
            if (view != null)
            {
                view.ViewMode = mode;
            }
        }

        public void Dispose()
        {
            _eventRender.Set();
            _isDisposing = true;
        }
    }
}
