using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using McLaser.Core.Plugins;

namespace McLaser.Plugins.Samples
{
    /// <summary>
    /// 示例计算器插件
    /// 演示基本的插件功能实现
    /// </summary>
    [PluginMetadata(
        Id = "McLaser.Plugins.Calculator",
        Name = "示例计算器",
        Version = "1.0.0",
        Description = "提供基本数学计算功能的示例插件",
        Author = "McLaser Team",
        Category = "工具"
    )]
    public class CalculatorPlugin : PluginBase
    {
        #region 私有字段

        private readonly Dictionary<string, Func<double, double, double>> _operations;
        private int _calculationCount = 0;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化计算器插件
        /// </summary>
        public CalculatorPlugin()
        {
            // 初始化支持的运算
            _operations = new Dictionary<string, Func<double, double, double>>
            {
                { "add", (a, b) => a + b },
                { "subtract", (a, b) => a - b },
                { "multiply", (a, b) => a * b },
                { "divide", (a, b) => b != 0 ? a / b : throw new DivideByZeroException("除数不能为零") },
                { "power", (a, b) => Math.Pow(a, b) },
                { "mod", (a, b) => a % b }
            };
        }

        #endregion

        #region 插件生命周期

        /// <summary>
        /// 初始化插件
        /// </summary>
        public override async Task InitializeAsync(IPluginContext context, CancellationToken cancellationToken = default)
        {
            await base.InitializeAsync(context, cancellationToken);
            
            OnStatusChanged(PluginStatus.Initializing);
            
            // 模拟初始化过程
            await Task.Delay(500, cancellationToken);
            
            // 注册插件服务
            if (context.ServiceProvider != null)
            {
                // 这里可以注册插件提供的服务
                LogMessage("计算器服务已注册", LogLevel.Info);
            }
            
            OnStatusChanged(PluginStatus.Initialized);
            LogMessage("计算器插件初始化完成", LogLevel.Info);
        }

        /// <summary>
        /// 启动插件
        /// </summary>
        public override async Task StartAsync(CancellationToken cancellationToken = default)
        {
            await base.StartAsync(cancellationToken);
            
            OnStatusChanged(PluginStatus.Starting);
            
            // 模拟启动过程
            await Task.Delay(300, cancellationToken);
            
            OnStatusChanged(PluginStatus.Running);
            LogMessage("计算器插件已启动，准备接受计算请求", LogLevel.Info);
        }

        /// <summary>
        /// 停止插件
        /// </summary>
        public override async Task StopAsync(CancellationToken cancellationToken = default)
        {
            OnStatusChanged(PluginStatus.Stopping);
            
            // 模拟停止过程
            await Task.Delay(200, cancellationToken);
            
            await base.StopAsync(cancellationToken);
            
            OnStatusChanged(PluginStatus.Stopped);
            LogMessage($"计算器插件已停止，共执行了 {_calculationCount} 次计算", LogLevel.Info);
        }

        #endregion

        #region 计算功能

        /// <summary>
        /// 执行计算
        /// </summary>
        /// <param name="operation">运算类型</param>
        /// <param name="operand1">操作数1</param>
        /// <param name="operand2">操作数2</param>
        /// <returns>计算结果</returns>
        public double Calculate(string operation, double operand1, double operand2)
        {
            if (Status != PluginStatus.Running)
            {
                throw new InvalidOperationException("计算器插件未运行");
            }

            if (!_operations.TryGetValue(operation.ToLower(), out var func))
            {
                throw new ArgumentException($"不支持的运算类型: {operation}");
            }

            try
            {
                var result = func(operand1, operand2);
                _calculationCount++;
                
                LogMessage($"计算: {operand1} {operation} {operand2} = {result}", LogLevel.Debug);
                
                return result;
            }
            catch (Exception ex)
            {
                LogMessage($"计算错误: {ex.Message}", LogLevel.Error);
                throw;
            }
        }

        /// <summary>
        /// 获取支持的运算类型
        /// </summary>
        /// <returns>运算类型列表</returns>
        public IEnumerable<string> GetSupportedOperations()
        {
            return _operations.Keys;
        }

        /// <summary>
        /// 获取计算统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public Dictionary<string, object> GetStatistics()
        {
            return new Dictionary<string, object>
            {
                { "CalculationCount", _calculationCount },
                { "SupportedOperations", _operations.Count },
                { "Status", Status.ToString() },
                { "StartTime", StartTime },
                { "RunTime", Status == PluginStatus.Running ? DateTime.Now - StartTime : TimeSpan.Zero }
            };
        }

        /// <summary>
        /// 重置计算器
        /// </summary>
        public void Reset()
        {
            _calculationCount = 0;
            LogMessage("计算器已重置", LogLevel.Info);
        }

        #endregion

        #region 配置管理

        /// <summary>
        /// 获取默认配置
        /// </summary>
        public override Dictionary<string, object> GetDefaultConfiguration()
        {
            return new Dictionary<string, object>
            {
                { "Precision", 10 },
                { "EnableLogging", true },
                { "MaxCalculationsPerSecond", 1000 },
                { "SupportedOperations", string.Join(",", _operations.Keys) }
            };
        }

        /// <summary>
        /// 验证配置
        /// </summary>
        public override bool ValidateConfiguration(Dictionary<string, object> configuration)
        {
            try
            {
                if (configuration.TryGetValue("Precision", out var precision))
                {
                    var precisionValue = Convert.ToInt32(precision);
                    if (precisionValue < 0 || precisionValue > 15)
                    {
                        LogMessage("精度值必须在0-15之间", LogLevel.Error);
                        return false;
                    }
                }

                if (configuration.TryGetValue("MaxCalculationsPerSecond", out var maxCalc))
                {
                    var maxCalcValue = Convert.ToInt32(maxCalc);
                    if (maxCalcValue <= 0)
                    {
                        LogMessage("最大计算频率必须大于0", LogLevel.Error);
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                LogMessage($"配置验证失败: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 记录日志消息
        /// </summary>
        private void LogMessage(string message, LogLevel level)
        {
            try
            {
                // 这里可以使用插件上下文中的日志服务
                System.Diagnostics.Debug.WriteLine($"[CalculatorPlugin] [{level}] {message}");
            }
            catch
            {
                // 忽略日志错误
            }
        }

        #endregion

        #region 资源清理

        /// <summary>
        /// 释放资源
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                LogMessage("计算器插件资源已释放", LogLevel.Info);
            }
            base.Dispose(disposing);
        }

        #endregion
    }

    /// <summary>
    /// 日志级别枚举（临时定义）
    /// </summary>
    public enum LogLevel
    {
        Debug,
        Info,
        Warn,
        Error
    }
}
