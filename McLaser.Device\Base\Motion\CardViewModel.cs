using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows.Input;
using McLaser.Core.Common;
using McLaser.Device;

namespace McLaser.Devices
{
 
    public class CardViewModel : ObservableObject
    {
        #region 属性

        /// <summary>
        /// 关联的控制卡
        /// </summary>
        public CardBase Card { get; private set; }

        /// <summary>
        /// 当前选中的轴
        /// </summary>
        private AxisBase _selectedAxis;
        public AxisBase SelectedAxis
        {
            get => _selectedAxis;
            set 
            { 
                _selectedAxis = value;
                OnPropertyChanged();
                UpdateAxisCommands();
            }
        }

        /// <summary>
        /// 轴状态信息
        /// </summary>
        private ObservableCollection<AxisStatusInfo> _axisStatusCollection = new ObservableCollection<AxisStatusInfo>();
        public ObservableCollection<AxisStatusInfo> AxisStatusCollection
        {
            get => _axisStatusCollection;
            set
            {
                _axisStatusCollection = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 当前选中的轴状态
        /// </summary>
        private AxisStatusInfo _selectedAxisStatus;
        public AxisStatusInfo SelectedAxisStatus
        {
            get => _selectedAxisStatus;
            set
            {
                // 如果当前有选中项，先清除其选中状态
                if (_selectedAxisStatus != null)
                {
                    _selectedAxisStatus.Status.IsSelected = false;
                }
                
                _selectedAxisStatus = value;
                
                // 设置新选中项的状态
                if (_selectedAxisStatus != null)
                {
                    _selectedAxisStatus.Status.IsSelected = true;
                }
                
                SelectedAxis = _selectedAxisStatus?.Axis;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 当前选中的IO项
        /// </summary>
        private IOBase _selectedIO;
        public IOBase SelectedIO
        {
            get => _selectedIO;
            set
            {
                _selectedIO = value;
                OnPropertyChanged();
                UpdateIOCommands();
            }
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="card">关联的控制卡</param>
        public CardViewModel(CardBase card)
        {
            Card = card ?? throw new ArgumentNullException(nameof(card));
            InitializeCommands();
            InitializeAxisStatus();
        }

        #endregion

        #region 命令

        // 轴相关命令
        public ICommand AddAxisCommand { get; private set; }
        public ICommand RemoveAxisCommand { get; private set; }
        public ICommand EnableAxisCommand { get; private set; }
        public ICommand DisableAxisCommand { get; private set; }
        public ICommand HomeAxisCommand { get; private set; }
        public ICommand StopAxisCommand { get; private set; }
        public ICommand ClearAxisCommand { get; private set; }
        public ICommand SetZeroCommand { get; private set; }
        public ICommand MovePositiveCommand { get; private set; }
        public ICommand MoveNegativeCommand { get; private set; }
        public ICommand UpdateAllAxisCommand { get; set; }
        
        // IO相关命令
        public ICommand SetOutputCommand { get; private set; }
        public ICommand ClearOutputCommand { get; private set; }

        /// <summary>
        /// 初始化命令
        /// </summary>
        private void InitializeCommands()
        {
            // 轴相关命令
            AddAxisCommand = new RelayCommand(ExecuteAddAxis);
            RemoveAxisCommand = new RelayCommand(ExecuteRemoveAxis, CanExecuteAxisOperation);
            EnableAxisCommand = new RelayCommand(ExecuteEnableAxis, CanExecuteAxisOperation);
            DisableAxisCommand = new RelayCommand(ExecuteDisableAxis, CanExecuteAxisOperation);
            HomeAxisCommand = new RelayCommand(ExecuteHomeAxis, CanExecuteAxisOperation);
            StopAxisCommand = new RelayCommand(ExecuteStopAxis, CanExecuteAxisOperation);
            ClearAxisCommand = new RelayCommand(ExecuteClearAxis, CanExecuteAxisOperation);
            SetZeroCommand = new RelayCommand(ExecuteSetZero, CanExecuteAxisOperation);
            MovePositiveCommand = new RelayCommand(ExecuteMovePositive, CanExecuteAxisOperation);
            MoveNegativeCommand = new RelayCommand(ExecuteMoveNegative, CanExecuteAxisOperation);
            UpdateAllAxisCommand = new RelayCommand(() => UpdateAllAxisStatus());
            
            // IO相关命令
            SetOutputCommand = new RelayCommand(ExecuteSetOutput, CanExecuteIOOperation);
            ClearOutputCommand = new RelayCommand(ExecuteClearOutput, CanExecuteIOOperation);
        }

        /// <summary>
        /// 更新轴命令状态
        /// </summary>
        protected virtual void UpdateAxisCommands()
        {
            (AddAxisCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (RemoveAxisCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (EnableAxisCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (DisableAxisCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (HomeAxisCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (StopAxisCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (ClearAxisCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (SetZeroCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (MovePositiveCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (MoveNegativeCommand as RelayCommand)?.RaiseCanExecuteChanged();
        }

        /// <summary>
        /// 更新IO命令状态
        /// </summary>
        protected virtual void UpdateIOCommands()
        {
            (SetOutputCommand as RelayCommand)?.RaiseCanExecuteChanged();
            (ClearOutputCommand as RelayCommand)?.RaiseCanExecuteChanged();
        }

        #endregion

        #region 轴操作相关方法

        /// <summary>
        /// 初始化轴状态信息
        /// </summary>
        protected virtual void InitializeAxisStatus()
        {
            AxisStatusCollection.Clear();
            foreach (var axis in Card.ListAxis)
            {
                AxisStatusCollection.Add(new AxisStatusInfo { Axis = axis });
            }
        }

        /// <summary>
        /// 添加轴
        /// </summary>
        protected virtual void ExecuteAddAxis()
        {
            int axisId = Card.ListAxis.Count;
            var axis = new AxisBase($"Axis{axisId + 1}", axisId);
            axis.Card = Card;
            Card.ListAxis.Add(axis);
            
            // 添加到状态集合
            AxisStatusCollection.Add(new AxisStatusInfo { Axis = axis });
        }

        /// <summary>
        /// 删除轴
        /// </summary>
        protected virtual void ExecuteRemoveAxis()
        {
            if (SelectedAxis == null) return;
            
            int index = Card.ListAxis.IndexOf(SelectedAxis);
            if (index >= 0)
            {
                Card.RemoveAxis(index);
                
                // 从状态集合中移除
                for (int i = 0; i < AxisStatusCollection.Count; i++)
                {
                    if (AxisStatusCollection[i].Axis == SelectedAxis)
                    {
                        AxisStatusCollection.RemoveAt(i);
                        break;
                    }
                }
                
                SelectedAxis = null;
            }
        }

        /// <summary>
        /// 使能轴
        /// </summary>
        protected virtual void ExecuteEnableAxis()
        {
            if (SelectedAxis == null) return;
            int index = Card.ListAxis.IndexOf(SelectedAxis);
            if (index >= 0)
            {
                Card.Enable(index, true);
                UpdateAxisStatus(index);
            }
        }

        /// <summary>
        /// 禁用轴
        /// </summary>
        protected virtual void ExecuteDisableAxis()
        {
            if (SelectedAxis == null) return;
            int index = Card.ListAxis.IndexOf(SelectedAxis);
            if (index >= 0)
            {
                Card.Enable(index, false);
                UpdateAxisStatus(index);
            }
        }

        /// <summary>
        /// 轴回零
        /// </summary>
        protected virtual void ExecuteHomeAxis()
        {
            if (SelectedAxis == null) return;
            int index = Card.ListAxis.IndexOf(SelectedAxis);
            if (index >= 0)
            {
                Card.Home(index, SelectedAxis.VelHome, (int)SelectedAxis.TimeoutHome);
                UpdateAxisStatus(index);
            }
        }

        /// <summary>
        /// 停止轴
        /// </summary>
        protected virtual void ExecuteStopAxis()
        {
            if (SelectedAxis == null) return;
            int index = Card.ListAxis.IndexOf(SelectedAxis);
            if (index >= 0)
            {
                Card.Stop(index);
                UpdateAxisStatus(index);
            }
        }

        /// <summary>
        /// 清除报警
        /// </summary>
        protected virtual void ExecuteClearAxis()
        {
            if (SelectedAxis == null) return;
            int index = Card.ListAxis.IndexOf(SelectedAxis);
            if (index >= 0)
            {
                Card.Clear(index);
                UpdateAxisStatus(index);
            }
        }

        /// <summary>
        /// 设置零点
        /// </summary>
        protected virtual void ExecuteSetZero()
        {
            if (SelectedAxis == null) return;
            int index = Card.ListAxis.IndexOf(SelectedAxis);
            if (index >= 0)
            {
                Card.SetZero(index);
                UpdateAxisStatus(index);
            }
        }

        /// <summary>
        /// 正向点动
        /// </summary>
        protected virtual void ExecuteMovePositive()
        {
            if (SelectedAxis == null) return;
            int index = Card.ListAxis.IndexOf(SelectedAxis);
            if (index >= 0)
            {
                Card.Jog(index, true);
            }
        }

        /// <summary>
        /// 负向点动
        /// </summary>
        protected virtual void ExecuteMoveNegative()
        {
            if (SelectedAxis == null) return;
            int index = Card.ListAxis.IndexOf(SelectedAxis);
            if (index >= 0)
            {
                Card.Jog(index, false);
            }
        }

        /// <summary>
        /// 设置输出
        /// </summary>
        protected virtual void ExecuteSetOutput()
        {
            if (SelectedIO == null) return;
            int index = Card.ListDo.IndexOf(SelectedIO);
            if (index >= 0)
            {
                Card.SetDo(index, true);
            }
        }

        /// <summary>
        /// 清除输出
        /// </summary>
        protected virtual void ExecuteClearOutput()
        {
            if (SelectedIO == null) return;
            int index = Card.ListDo.IndexOf(SelectedIO);
            if (index >= 0)
            {
                Card.SetDo(index, false);
            }
        }

        /// <summary>
        /// 是否可执行轴操作
        /// </summary>
        protected virtual bool CanExecuteAxisOperation()
        {
            return SelectedAxis != null;
        }

        /// <summary>
        /// 是否可执行IO操作
        /// </summary>
        protected virtual bool CanExecuteIOOperation()
        {
            return SelectedIO != null && Card.ListDo.Contains(SelectedIO);
        }

        /// <summary>
        /// 更新轴状态
        /// </summary>
        /// <param name="index">轴索引</param>
        protected virtual void UpdateAxisStatus(int index)
        {
            if (index < 0 || index >= Card.ListAxis.Count) return;
            
            AxisStatus status = new AxisStatus();
            Card.GetStatus(index, ref status);
            
            double pos = 0;
            Card.GetPos(index, ref pos);
            
            // 更新集合中的状态
            foreach (var axisStatus in AxisStatusCollection)
            {
                if (axisStatus.Axis == Card.ListAxis[index])
                {
                    // 保存选中状态
                    bool isSelected = axisStatus.Status.IsSelected;
                    
                    // 更新状态
                    axisStatus.Status = status;
                    axisStatus.Position = pos;
                    
                    // 恢复选中状态
                    axisStatus.Status.IsSelected = isSelected;
                    
                    // 确保轴对象引用正确
                    axisStatus.Status.Axis = axisStatus.Axis;
                    
                    // 更新位置信息
                    axisStatus.Status.Position = pos;
                    break;
                }
            }
        }

        /// <summary>
        /// 更新所有轴状态
        /// </summary>
        public virtual void UpdateAllAxisStatus()
        {
            for (int i = 0; i < Card.ListAxis.Count; i++)
            {
                UpdateAxisStatus(i);
            }
        }

        #endregion
    }

    /// <summary>
    /// 轴状态信息类
    /// </summary>
    public class AxisStatusInfo : ObservableObject
    {
        private AxisBase _axis;
        public AxisBase Axis
        {
            get => _axis;
            set
            {
                _axis = value;
                
                // 同时更新Status中的Axis引用
                if (_status != null && _axis != null)
                {
                    _status.Axis = _axis;
                }
                
                OnPropertyChanged();
            }
        }

        private AxisStatus _status = new AxisStatus();
        public AxisStatus Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged();
                
                // 更新相关属性
                OnPropertyChanged(nameof(IsEnabled));
                OnPropertyChanged(nameof(IsHomed));
                OnPropertyChanged(nameof(IsHoming));
                OnPropertyChanged(nameof(IsMoving));
                OnPropertyChanged(nameof(IsError));
                OnPropertyChanged(nameof(IsPlusLimit));
                OnPropertyChanged(nameof(IsMinusLimit));
                OnPropertyChanged(nameof(IsOrigin));
                OnPropertyChanged(nameof(IsInPosition));
            }
        }

        private double _position;
        public double Position
        {
            get => _position;
            set
            {
                _position = value;
                OnPropertyChanged();
            }
        }

        // 轴状态的快捷属性
        public bool IsEnabled => Status.IsEnabled;
        public bool IsHomed => Status.IsHome;
        public bool IsHoming => Status.IsHoming;
        public bool IsMoving => Status.IsMoving;
        public bool IsError => Status.IsError;
        public bool IsPlusLimit => Status.IsPlusLimit;
        public bool IsMinusLimit => Status.IsMinusLimit;
        public bool IsOrigin => Status.IsOrigin;
        public bool IsInPosition => Status.IsInPos;
    }
} 