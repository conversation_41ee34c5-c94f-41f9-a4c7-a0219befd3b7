using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Threading;
using System.Threading.Tasks;
using System.Xml.Serialization;
using McLaser.Device;
using Basler.Pylon;
using HalconDotNet;
using GxIAPINET;

namespace McLaser.Devices.Camera
{

    [DeviceItem("工业相机", "Basler相机", "兼容网口/USB3系列", "📷")]
    public class CameraBasler : CameraBase
    {
        #region 私有字段
        
        /// <summary>
        /// 巴斯勒相机对象
        /// </summary>
        private Basler.Pylon.Camera Camera = null;
        
        /// <summary>
        /// 图像转换器
        /// </summary>
        private PixelDataConverter converter = new PixelDataConverter();

        #endregion

        #region 属性

        /// <summary>
        /// 设备状态
        /// </summary>
        [XmlIgnore, Browsable(false)]
        public override StatusDevice Status { get; set; } = new StatusBasler();

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public CameraBasler()
        {
            Name = "巴斯勒相机";
            DeviceType = DevicesType.Camera;
        }

        #endregion

        #region 设备基本操作

        /// <summary>
        /// 搜索相机设备
        /// </summary>
        /// <returns>相机信息列表</returns>
        public override List<CameraInfo> SearchCameras()
        {
            List<CameraInfo> cameraInfos = new List<CameraInfo>();
            try
            {
                // 枚举设备
                List<ICameraInfo> listcameraInfo = CameraFinder.Enumerate();

                // 添加设备信息到列表
                for (int i = 0; i < listcameraInfo.Count; i++)
                {
                    ICameraInfo cameraInfo = listcameraInfo[i];
                    string displayName = !string.IsNullOrEmpty(cameraInfo[CameraInfoKey.UserDefinedName]) 
                        ? cameraInfo[CameraInfoKey.UserDefinedName]
                        : cameraInfo[CameraInfoKey.SerialNumber];
                    
                    cameraInfos.Add(new CameraInfo() 
                    { 
                        CamName = displayName, 
                        SerialNO = cameraInfo[CameraInfoKey.SerialNumber], 
                        ExtInfo = cameraInfo 
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"搜索巴斯勒相机异常：{ex.Message}");
            }
            return cameraInfos;
        }

        /// <summary>
        /// 打开相机设备
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool Open()
        {
            try
            {
                if (!IsEnabled) return false;
                if (Status.IsConnected) Close();

                var cameraInfo = (ICameraInfo)CameraInfo.ExtInfo;
                
                // 创建相机对象
                Camera = new Basler.Pylon.Camera(cameraInfo);
                
                // 打开设备
                Camera.Open();
                
                // 设置缓存区数量
                Camera.Parameters[PLCameraInstance.MaxNumBuffer].SetValue(5);
                
                // 设置采集模式
                Camera.Parameters[PLCamera.AcquisitionMode].SetValue(PLCamera.AcquisitionMode.Continuous);

                // 设置触发模式
                if (TrigMode == TrigMode.软触发)
                {
                    Camera.Parameters[PLCamera.TriggerMode].SetValue(PLCamera.TriggerMode.On);
                    Camera.Parameters[PLCamera.TriggerSource].SetValue(PLCamera.TriggerSource.Software);
                }
                else
                {
                    Camera.Parameters[PLCamera.TriggerMode].SetValue(PLCamera.TriggerMode.Off);
                }

                Status.IsConnected = true;
                
                // 启动采集
                if (!Start()) return false;
                
                // 获取图像尺寸
                int width = 0, height = 0;
                if (!GetWidth(ref width)) return false;
                if (!GetHeight(ref height)) return false;

                return IsOpen();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"打开巴斯勒相机异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查设备是否已打开
        /// </summary>
        /// <returns>是否已打开</returns>
        public override bool IsOpen()
        {
            return Status.IsConnected && Camera != null && Camera.IsOpen;
        }

        /// <summary>
        /// 关闭相机设备
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool Close()
        {
            try
            {
                Stop();
                
                // 关闭设备
                if (Camera != null)
                {
                    Camera.Close();
                    Camera.Dispose();
                    Camera = null;
                }

                Status.IsConnected = false;
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"关闭巴斯勒相机异常：{ex.Message}");
                return false;
            }
        }

        #endregion

        #region 采集控制

        /// <summary>
        /// 开始采集
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool Start()
        {
            try
            {
                if (!IsOpen()) return false;
                if (Camera == null) return false;
                
                Camera.StreamGrabber.Start();
                
                (Status as StatusCamera).IsStart = true;
                (Status as StatusCamera).IsGrapOnce = false;
                (Status as StatusCamera).IsGrapContinue = false;
                
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"启动巴斯勒相机采集异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查是否正在采集
        /// </summary>
        /// <returns>是否正在采集</returns>
        public override bool IsStart()
        {
            return (Status as StatusCamera).IsStart;
        }

        /// <summary>
        /// 停止采集
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool Stop()
        {
            try
            {
                if (Camera != null && Camera.StreamGrabber.IsGrabbing)
                {
                    Camera.StreamGrabber.Stop();
                }
                
                (Status as StatusCamera).IsStart = false;
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"停止巴斯勒相机采集异常：{ex.Message}");
                return false;
            }
        }

        #endregion

        #region 图像采集

        /// <summary>
        /// 采集单帧图像
        /// </summary>
        /// <param name="source">输出图像数据</param>
        /// <returns>是否成功</returns>
        public override bool GrabImageData(out ImageData source)
        {
            source = new ImageData();
            try
            {
                if (!IsOpen()) return false;
                if (!IsStart()) return false;
                if (Camera == null) return false;

                // 软触发
                if (TrigMode == TrigMode.软触发)
                {
                    Camera.Parameters[PLCamera.TriggerSoftware].Execute();
                }

                // 获取图像
                IGrabResult grabResult = Camera.StreamGrabber.RetrieveResult(TimeoutOnce, TimeoutHandling.ThrowException);
                
                if (grabResult.GrabSucceeded)
                {
                    // 拷贝帧数据
                    CopyFrameData(grabResult, ref source);
                    (Status as StatusCamera).IsGrapOnce = true;
                    
                    grabResult.Dispose();
                    return true;
                }
                else
                {
                    grabResult.Dispose();
                    return false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"巴斯勒相机采集图像异常：{ex.Message}");
                return false;
            }
        }

        #endregion

        #region 参数设置

        /// <summary>
        /// 获取曝光时间
        /// </summary>
        /// <param name="value">曝光时间值</param>
        /// <returns>是否成功</returns>
        public override bool GetExposure(ref double value)
        {
            try
            {
                if (!IsOpen()) return false;
                if (Camera == null) return false;
                
                value = Camera.Parameters[PLCamera.ExposureTime].GetValue();
                (Status as StatusCamera).Exposure = value;
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取巴斯勒相机曝光时间异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置曝光时间
        /// </summary>
        /// <param name="value">曝光时间值</param>
        /// <returns>是否成功</returns>
        public override bool SetExposure(double value)
        {
            try
            {
                if (!IsOpen()) return false;
                if (Camera == null) return false;
                
                if (!Camera.Parameters[PLCamera.ExposureTime].TrySetValue(value)) return false;
                Exposure = value;
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置巴斯勒相机曝光时间异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取增益值
        /// </summary>
        /// <param name="value">增益值</param>
        /// <returns>是否成功</returns>
        public override bool GetGain(ref double value)
        {
            try
            {
                if (!IsOpen()) return false;
                if (Camera == null) return false;
                
                value = Camera.Parameters[PLCamera.Gain].GetValue();
                (Status as StatusCamera).Gain = value;
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取巴斯勒相机增益异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置增益值
        /// </summary>
        /// <param name="value">增益值</param>
        /// <returns>是否成功</returns>
        public override bool SetGain(double value)
        {
            try
            {
                if (!IsOpen()) return false;
                if (Camera == null) return false;
                
                if (!Camera.Parameters[PLCamera.Gain].TrySetValue(value)) return false;
                Gain = value;
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置巴斯勒相机增益异常：{ex.Message}");
                return false;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 拷贝帧数据
        /// </summary>
        /// <param name="grabResult">抓取结果</param>
        /// <param name="source">目标图像数据</param>
        private void CopyFrameData(IGrabResult grabResult, ref ImageData imageData)
        {
            //if (frameData.GetBuffer() != null)
            //{
            //    imageData.PixelDataPtr = frameData.GetBuffer();
            //    imageData.Width = (uint)frameData.GetWidth();
            //    imageData.Height = (uint)frameData.GetHeight();
            //    Height = (int)imageData.Height;
            //    Width = (int)imageData.Width;
            //    imageData.ImageSize = frameData.GetPayloadSize();
            //}
        }

        #endregion
    }
}
