using McLaser.Modules.Vision;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Windows;

namespace McLaser.Device
{
    /// <summary>
    /// 设备工厂类
    /// 负责创建各种类型的设备实例
    /// </summary>
    public static class DeviceFactory
    {
        #region 私有字段

        /// <summary>
        /// 设备类型注册表
        /// </summary>
        private static readonly Dictionary<string, Type> _deviceTypes = new Dictionary<string, Type>();

        /// <summary>
        /// 设备类型元数据字典
        /// </summary>
        private static readonly Dictionary<string, DeviceTypeMetadata> _deviceMetadata = new Dictionary<string, DeviceTypeMetadata>();

        private static List<DeviceItem> DeviceItems { get; } = new List<DeviceItem>();

        /// <summary>
        /// 是否已初始化
        /// </summary>
        private static bool _isInitialized = false;

        #endregion

        #region 静态构造函数

        /// <summary>
        /// 静态构造函数
        /// </summary>
        static DeviceFactory()
        {
            Initialize();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 初始化设备工厂
        /// </summary>
        public static void Initialize()
        {
            if (_isInitialized) return;

            try
            {
                System.Diagnostics.Debug.WriteLine("开始初始化设备工厂...");

                // 注册内置设备类型
                RegisterBuiltInDeviceTypes();

                _isInitialized = true;
                System.Diagnostics.Debug.WriteLine($"设备工厂初始化完成，共注册 {_deviceTypes.Count} 个设备类型");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设备工厂初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建设备实例
        /// </summary>
        /// <param name="deviceTypeName">设备类型名称</param>
        /// <param name="deviceName">设备名称</param>
        /// <returns>创建的设备实例</returns>
        public static IDevice CreateDevice(DeviceItem deviceItem)
        {
            if (string.IsNullOrEmpty(deviceItem.DisplayName))
                throw new ArgumentException("设备类型名称不能为空", nameof(deviceItem.DisplayName));
            try
            {
                // 使用无参构造函数创建
                var device = Activator.CreateInstance(deviceItem.ItemType) as IDevice;
                if (device != null)
                {
                    device.Name = deviceItem.DisplayName;
             
                }

                return device;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"创建设备实例失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 创建设备实例
        /// </summary>
        /// <typeparam name="T">设备类型</typeparam>
        /// <param name="deviceName">设备名称</param>
        /// <returns>创建的设备实例</returns>
        public static T CreateDevice<T>(string deviceName = null) where T : class, IDevice, new()
        {
            try
            {
                var device = new T();

                if (!string.IsNullOrEmpty(deviceName))
                {
                    device.Name = deviceName;
                }

                return device;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"创建设备实例失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 注册设备类型
        /// </summary>
        /// <param name="typeName">类型名称</param>
        /// <param name="deviceType">设备类型</param>
        public static void RegisterDeviceType(string typeName, Type deviceType)
        {
            if (string.IsNullOrEmpty(typeName))
                throw new ArgumentException("类型名称不能为空", nameof(typeName));

            if (deviceType == null)
                throw new ArgumentNullException(nameof(deviceType));

            if (!typeof(IDevice).IsAssignableFrom(deviceType))
                throw new ArgumentException("设备类型必须实现IDevice接口", nameof(deviceType));

            _deviceTypes[typeName] = deviceType;

            // 创建并注册元数据
            var metadata = DeviceTypeMetadata.FromType(deviceType);
            metadata.TypeName = typeName; // 确保使用注册的名称
            _deviceMetadata[typeName] = metadata;
        }

        /// <summary>
        /// 注册设备类型（带元数据）
        /// </summary>
        /// <param name="typeName">类型名称</param>
        /// <param name="deviceType">设备类型</param>
        /// <param name="metadata">元数据</param>
        public static void RegisterDeviceType(string typeName, Type deviceType, DeviceTypeMetadata metadata)
        {
            if (string.IsNullOrEmpty(typeName))
                throw new ArgumentException("类型名称不能为空", nameof(typeName));

            if (deviceType == null)
                throw new ArgumentNullException(nameof(deviceType));

            if (metadata == null)
                throw new ArgumentNullException(nameof(metadata));

            if (!typeof(IDevice).IsAssignableFrom(deviceType))
                throw new ArgumentException("设备类型必须实现IDevice接口", nameof(deviceType));

            _deviceTypes[typeName] = deviceType;
            metadata.TypeName = typeName;
            metadata.ActualType = deviceType;
            _deviceMetadata[typeName] = metadata;
        }



        /// <summary>
        /// 获取设备类型的显示名称
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <returns>显示名称</returns>
        public static string GetDeviceTypeDisplayName(DevicesType deviceType)
        {
            return deviceType switch
            {
                DevicesType.Camera => "相机设备",
                DevicesType.MotionCard => "运动控制卡",
                DevicesType.Laser => "激光器设备",
                DevicesType.Sensor => "传感器设备",
                DevicesType.BarcodeReader => "条码读取器",
                DevicesType.NetworkDevice => "网络设备",
                DevicesType.SerialDevice => "串口设备",
                _ => "未知设备"
            };
        }

        /// <summary>
        /// 创建测试设备
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <param name="deviceName">设备名称</param>
        /// <returns>测试设备实例</returns>
        public static IDevice CreateTestDevice(DevicesType deviceType, string deviceName = null)
        {
            var name = deviceName ?? $"测试{GetDeviceTypeDisplayName(deviceType)}";

            // 创建一个基础的测试设备
            var testDevice = new DeviceBase(name, deviceType);

            // 设置一些测试配置
            testDevice.Configuration["TestMode"] = true;
            testDevice.Configuration["CreatedTime"] = DateTime.Now;

            return testDevice;
        }

        /// <summary>
        /// 获取支持的设备类型列表
        /// </summary>
        /// <returns>设备类型名称列表</returns>
        public static List<string> GetSupportedDeviceTypes()
        {
            if (!_isInitialized)
            {
                Initialize();
            }

            return _deviceTypes.Keys.ToList();
        }

        /// <summary>
        /// 获取设备类型元数据列表
        /// </summary>
        /// <returns>设备类型元数据列表</returns>
        public static List<DeviceTypeMetadata> GetDeviceTypeMetadata()
        {
            if (!_isInitialized)
            {
                Initialize();
            }

            return _deviceMetadata.Values.ToList();
        }

        public static List<DeviceItem> GetDeviceItems()
        {
            return DeviceItems;
        }

        /// <summary>
        /// 根据类别获取设备类型元数据
        /// </summary>
        /// <param name="category">设备类别</param>
        /// <returns>设备类型元数据列表</returns>
        public static List<DeviceTypeMetadata> GetDeviceTypeMetadataByCategory(DeviceCategory category)
        {
            if (!_isInitialized)
            {
                Initialize();
            }

            return _deviceMetadata.Values
                .Where(m => m.Category == category)
                .ToList();
        }

        /// <summary>
        /// 获取设备类型元数据
        /// </summary>
        /// <param name="typeName">类型名称</param>
        /// <returns>设备类型元数据</returns>
        public static DeviceTypeMetadata? GetDeviceTypeMetadata(string typeName)
        {
            if (!_isInitialized)
            {
                Initialize();
            }

            return _deviceMetadata.TryGetValue(typeName, out var metadata) ? metadata : null;
        }



        #endregion

        #region 私有方法

        /// <summary>
        /// 注册内置设备类型
        /// </summary>
        private static void RegisterBuiltInDeviceTypes()
        {
            // 注册基础设备类型
            RegisterDeviceType("DeviceBase", typeof(DeviceBase), new DeviceTypeMetadata
            {
                TypeName = "DeviceBase",
                DisplayName = "基础设备",
                Category = DeviceCategory.None,
                Description = "所有设备的基础类",
                IsBuiltIn = true,
                Manufacturer = "McLaser"
            });

            // 注册相机设备类型
            RegisterCameraDeviceTypes();

            // 注册运动控制卡设备类型
            RegisterMotionControllerDeviceTypes();

            // 注册激光器设备类型
            RegisterLaserDeviceTypes();

            // 注册传感器设备类型
            RegisterSensorDeviceTypes();

            System.Diagnostics.Debug.WriteLine($"已注册 {_deviceTypes.Count} 个内置设备类型");

            string appStartupPath = System.IO.Path.GetDirectoryName(Process.GetCurrentProcess().MainModule.FileName);
            string[] assembliesDllFile = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory, "*.dll");
            List<string> selectFiles = assembliesDllFile.Where(item => new FileInfo(item).Name.StartsWith("McLaser.Devices")).ToList();

            //加载
            var assemblies = selectFiles.Select(x => Assembly.Load(AssemblyName.GetAssemblyName(x)));

            var devices = assemblies.SelectMany(x => x.GetTypes().Where(y => y.GetCustomAttributes(typeof(DeviceItemAttribute), false).Any()))
                .Select(x =>
                {
                    var attribute = (DeviceItemAttribute)x.GetCustomAttributes(typeof(DeviceItemAttribute), false).First();
                    return new DeviceItem
                    {
                        DisplayName = attribute.DisplayName,
                        Category = attribute.Category,
                        IconSource = attribute.IconSource,//(attribute.IconSource != null) ? new Uri(attribute.IconSource) : null,
                        Description = attribute.Description,
                        ItemType = x
                    };
                });
            //.GroupBy(x => x.Category);
            //.ToDictionary(x => x.Key, x => x.AsEnumerable());

            // 添加到可观察集合
            foreach (var device in devices)
            {
                DeviceItems.Add(device);
            }

        }

        /// <summary>
        /// 注册相机设备类型
        /// </summary>
        private static void RegisterCameraDeviceTypes()
        {
            RegisterDeviceType("HIKCamera", typeof(DeviceBase), new DeviceTypeMetadata
            {
                TypeName = "HIKCamera",
                DisplayName = "海康威视相机",
                Category = DeviceCategory.Camera,
                DeviceType = DevicesType.Camera,
                Description = "海康威视工业相机设备",
                Manufacturer = "海康威视",
                Icon = "📷",
                IsBuiltIn = true,
                SupportsAutoDiscovery = true,
                RequiresDriver = true,
                SupportedConnections = new List<string> { "GigE", "USB3.0", "Camera Link" }
            });

            RegisterDeviceType("BaslerCamera", typeof(DeviceBase), new DeviceTypeMetadata
            {
                TypeName = "BaslerCamera",
                DisplayName = "巴斯勒相机",
                Category = DeviceCategory.Camera,
                DeviceType = DevicesType.Camera,
                Description = "巴斯勒工业相机设备",
                Manufacturer = "Basler",
                Icon = "📷",
                IsBuiltIn = true,
                SupportsAutoDiscovery = true,
                RequiresDriver = true,
                SupportedConnections = new List<string> { "GigE", "USB3.0", "Camera Link", "CoaXPress" }
            });

            RegisterDeviceType("DahengCamera", typeof(DeviceBase), new DeviceTypeMetadata
            {
                TypeName = "DahengCamera",
                DisplayName = "大恒图像相机",
                Category = DeviceCategory.Camera,
                DeviceType = DevicesType.Camera,
                Description = "大恒图像工业相机设备",
                Manufacturer = "大恒图像",
                Icon = "📷",
                IsBuiltIn = true,
                SupportsAutoDiscovery = true,
                RequiresDriver = true,
                SupportedConnections = new List<string> { "GigE", "USB3.0" }
            });
        }

        /// <summary>
        /// 注册运动控制卡设备类型
        /// </summary>
        private static void RegisterMotionControllerDeviceTypes()
        {
            RegisterDeviceType("PMACCard", typeof(DeviceBase), new DeviceTypeMetadata
            {
                TypeName = "PMACCard",
                DisplayName = "PMAC运动控制卡",
                Category = DeviceCategory.MotionController,
                DeviceType = DevicesType.MotionCard,
                Description = "Delta Tau PMAC运动控制卡",
                Manufacturer = "Delta Tau",
                Icon = "🎛️",
                IsBuiltIn = true,
                RequiresDriver = true,
                SupportedConnections = new List<string> { "PCI", "PCIe", "Ethernet" }
            });

            RegisterDeviceType("GTSCard", typeof(DeviceBase), new DeviceTypeMetadata
            {
                TypeName = "GTSCard",
                DisplayName = "固高GTS运动控制卡",
                Category = DeviceCategory.MotionController,
                DeviceType = DevicesType.MotionCard,
                Description = "固高科技GTS系列运动控制卡",
                Manufacturer = "固高科技",
                Icon = "🎛️",
                IsBuiltIn = true,
                RequiresDriver = true,
                SupportedConnections = new List<string> { "PCI", "PCIe", "USB", "Ethernet" }
            });
        }

        /// <summary>
        /// 注册激光器设备类型
        /// </summary>
        private static void RegisterLaserDeviceTypes()
        {
            RegisterDeviceType("IPGLaser", typeof(DeviceBase), new DeviceTypeMetadata
            {
                TypeName = "IPGLaser",
                DisplayName = "IPG激光器",
                Category = DeviceCategory.Laser,
                DeviceType = DevicesType.Laser,
                Description = "IPG光纤激光器",
                Manufacturer = "IPG Photonics",
                Icon = "🔴",
                IsBuiltIn = true,
                RequiresDriver = false,
                SupportedConnections = new List<string> { "Serial", "Ethernet" }
            });

            RegisterDeviceType("CoherentLaser", typeof(DeviceBase), new DeviceTypeMetadata
            {
                TypeName = "CoherentLaser",
                DisplayName = "Coherent激光器",
                Category = DeviceCategory.Laser,
                DeviceType = DevicesType.Laser,
                Description = "Coherent激光器设备",
                Manufacturer = "Coherent",
                Icon = "🔴",
                IsBuiltIn = true,
                RequiresDriver = false,
                SupportedConnections = new List<string> { "Serial", "USB" }
            });
        }

        /// <summary>
        /// 注册传感器设备类型
        /// </summary>
        private static void RegisterSensorDeviceTypes()
        {
            RegisterDeviceType("TemperatureSensor", typeof(DeviceBase), new DeviceTypeMetadata
            {
                TypeName = "TemperatureSensor",
                DisplayName = "温度传感器",
                Category = DeviceCategory.Sensor,
                DeviceType = DevicesType.Sensor,
                Description = "温度检测传感器",
                Manufacturer = "通用",
                Icon = "🌡️",
                IsBuiltIn = true,
                SupportedConnections = new List<string> { "Analog", "Digital", "I2C", "Serial" }
            });

            RegisterDeviceType("DisplacementSensor", typeof(DeviceBase), new DeviceTypeMetadata
            {
                TypeName = "DisplacementSensor",
                DisplayName = "位移传感器",
                Category = DeviceCategory.Sensor,
                DeviceType = DevicesType.Sensor,
                Description = "位移检测传感器",
                Manufacturer = "通用",
                Icon = "📏",
                IsBuiltIn = true,
                SupportedConnections = new List<string> { "Analog", "Digital", "Serial" }
            });
        }


        #endregion
    }
}
