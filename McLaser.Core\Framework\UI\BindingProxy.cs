using System;
using System.Windows;

namespace McLaser.Core.Framework.UI
{
    /// <summary>
    /// 绑定代理类
    /// 用于在XAML中绑定到不在可视化树中的对象
    /// </summary>
    public class BindingProxy : Freezable
    {
        /// <summary>
        /// 数据依赖属性
        /// </summary>
        public static readonly DependencyProperty DataProperty =
            DependencyProperty.Register(nameof(Data), typeof(object), typeof(BindingProxy), 
                new PropertyMetadata(null));

        /// <summary>
        /// 数据对象
        /// </summary>
        public object Data
        {
            get => GetValue(DataProperty);
            set => SetValue(DataProperty, value);
        }

        /// <summary>
        /// 创建实例副本
        /// </summary>
        /// <returns>新的实例</returns>
        protected override Freezable CreateInstanceCore()
        {
            return new BindingProxy();
        }
    }

    /// <summary>
    /// 泛型绑定代理类
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    public class BindingProxy<T> : Freezable
    {
        /// <summary>
        /// 数据依赖属性
        /// </summary>
        public static readonly DependencyProperty DataProperty =
            DependencyProperty.Register(nameof(Data), typeof(T), typeof(BindingProxy<T>), 
                new PropertyMetadata(default(T)));

        /// <summary>
        /// 数据对象
        /// </summary>
        public T Data
        {
            get => (T)GetValue(DataProperty);
            set => SetValue(DataProperty, value);
        }

        /// <summary>
        /// 创建实例副本
        /// </summary>
        /// <returns>新的实例</returns>
        protected override Freezable CreateInstanceCore()
        {
            return new BindingProxy<T>();
        }
    }

    /// <summary>
    /// 多重绑定助手
    /// </summary>
    public static class MultiBindingHelper
    {
        /// <summary>
        /// 创建多重绑定
        /// </summary>
        /// <param name="converter">值转换器</param>
        /// <param name="bindings">绑定列表</param>
        /// <returns>多重绑定对象</returns>
        public static System.Windows.Data.MultiBinding CreateMultiBinding(
            System.Windows.Data.IMultiValueConverter converter,
            params System.Windows.Data.BindingBase[] bindings)
        {
            var multiBinding = new System.Windows.Data.MultiBinding
            {
                Converter = converter
            };

            foreach (var binding in bindings)
            {
                multiBinding.Bindings.Add(binding);
            }

            return multiBinding;
        }

        /// <summary>
        /// 创建多重绑定
        /// </summary>
        /// <param name="converter">值转换器</param>
        /// <param name="converterParameter">转换器参数</param>
        /// <param name="bindings">绑定列表</param>
        /// <returns>多重绑定对象</returns>
        public static System.Windows.Data.MultiBinding CreateMultiBinding(
            System.Windows.Data.IMultiValueConverter converter,
            object converterParameter,
            params System.Windows.Data.BindingBase[] bindings)
        {
            var multiBinding = CreateMultiBinding(converter, bindings);
            multiBinding.ConverterParameter = converterParameter;
            return multiBinding;
        }
    }

    /// <summary>
    /// 绑定助手类
    /// </summary>
    public static class BindingHelper
    {
        /// <summary>
        /// 创建绑定
        /// </summary>
        /// <param name="path">绑定路径</param>
        /// <param name="source">绑定源</param>
        /// <param name="mode">绑定模式</param>
        /// <param name="converter">值转换器</param>
        /// <param name="converterParameter">转换器参数</param>
        /// <returns>绑定对象</returns>
        public static System.Windows.Data.Binding CreateBinding(
            string path,
            object? source = null,
            System.Windows.Data.BindingMode mode = System.Windows.Data.BindingMode.Default,
            System.Windows.Data.IValueConverter? converter = null,
            object? converterParameter = null)
        {
            var binding = new System.Windows.Data.Binding(path)
            {
                Mode = mode
            };

            if (source != null)
                binding.Source = source;

            if (converter != null)
                binding.Converter = converter;

            if (converterParameter != null)
                binding.ConverterParameter = converterParameter;

            return binding;
        }

        /// <summary>
        /// 创建相对源绑定
        /// </summary>
        /// <param name="path">绑定路径</param>
        /// <param name="relativeSourceMode">相对源模式</param>
        /// <param name="ancestorType">祖先类型</param>
        /// <param name="ancestorLevel">祖先级别</param>
        /// <param name="mode">绑定模式</param>
        /// <returns>绑定对象</returns>
        public static System.Windows.Data.Binding CreateRelativeSourceBinding(
            string path,
            System.Windows.Data.RelativeSourceMode relativeSourceMode,
            Type? ancestorType = null,
            int ancestorLevel = 1,
            System.Windows.Data.BindingMode mode = System.Windows.Data.BindingMode.Default)
        {
            var binding = new System.Windows.Data.Binding(path)
            {
                Mode = mode,
                RelativeSource = new System.Windows.Data.RelativeSource(relativeSourceMode)
            };

            if (ancestorType != null)
            {
                binding.RelativeSource.AncestorType = ancestorType;
                binding.RelativeSource.AncestorLevel = ancestorLevel;
            }

            return binding;
        }

        /// <summary>
        /// 创建元素名称绑定
        /// </summary>
        /// <param name="path">绑定路径</param>
        /// <param name="elementName">元素名称</param>
        /// <param name="mode">绑定模式</param>
        /// <returns>绑定对象</returns>
        public static System.Windows.Data.Binding CreateElementNameBinding(
            string path,
            string elementName,
            System.Windows.Data.BindingMode mode = System.Windows.Data.BindingMode.Default)
        {
            return new System.Windows.Data.Binding(path)
            {
                ElementName = elementName,
                Mode = mode
            };
        }

        /// <summary>
        /// 设置绑定
        /// </summary>
        /// <param name="target">目标对象</param>
        /// <param name="targetProperty">目标属性</param>
        /// <param name="binding">绑定对象</param>
        public static void SetBinding(DependencyObject target, DependencyProperty targetProperty, 
                                    System.Windows.Data.BindingBase binding)
        {
            System.Windows.Data.BindingOperations.SetBinding(target, targetProperty, binding);
        }

        /// <summary>
        /// 清除绑定
        /// </summary>
        /// <param name="target">目标对象</param>
        /// <param name="targetProperty">目标属性</param>
        public static void ClearBinding(DependencyObject target, DependencyProperty targetProperty)
        {
            System.Windows.Data.BindingOperations.ClearBinding(target, targetProperty);
        }

        /// <summary>
        /// 清除所有绑定
        /// </summary>
        /// <param name="target">目标对象</param>
        public static void ClearAllBindings(DependencyObject target)
        {
            System.Windows.Data.BindingOperations.ClearAllBindings(target);
        }

        /// <summary>
        /// 获取绑定表达式
        /// </summary>
        /// <param name="target">目标对象</param>
        /// <param name="targetProperty">目标属性</param>
        /// <returns>绑定表达式</returns>
        public static System.Windows.Data.BindingExpression? GetBindingExpression(
            DependencyObject target, DependencyProperty targetProperty)
        {
            return System.Windows.Data.BindingOperations.GetBindingExpression(target, targetProperty);
        }

        /// <summary>
        /// 获取多重绑定表达式
        /// </summary>
        /// <param name="target">目标对象</param>
        /// <param name="targetProperty">目标属性</param>
        /// <returns>多重绑定表达式</returns>
        public static System.Windows.Data.MultiBindingExpression? GetMultiBindingExpression(
            DependencyObject target, DependencyProperty targetProperty)
        {
            return System.Windows.Data.BindingOperations.GetMultiBindingExpression(target, targetProperty);
        }

        /// <summary>
        /// 检查是否有绑定
        /// </summary>
        /// <param name="target">目标对象</param>
        /// <param name="targetProperty">目标属性</param>
        /// <returns>是否有绑定</returns>
        public static bool IsDataBound(DependencyObject target, DependencyProperty targetProperty)
        {
            return System.Windows.Data.BindingOperations.IsDataBound(target, targetProperty);
        }
    }

    /// <summary>
    /// 绑定表达式验证器
    /// </summary>
    public static class BindingExpressionValidator
    {
        /// <summary>
        /// 验证绑定表达式
        /// </summary>
        /// <param name="bindingExpression">绑定表达式</param>
        /// <returns>验证结果</returns>
        public static BindingValidationResult ValidateBinding(System.Windows.Data.BindingExpression? bindingExpression)
        {
            if (bindingExpression == null)
                return new BindingValidationResult(false, "绑定表达式为空");

            try
            {
                // 更新绑定源
                bindingExpression.UpdateSource();

                // 检查是否有验证错误
                if (bindingExpression.HasError)
                {
                    var error = bindingExpression.ValidationError;
                    return new BindingValidationResult(false, error?.ErrorContent?.ToString() ?? "绑定验证失败");
                }

                return new BindingValidationResult(true, "绑定验证成功");
            }
            catch (Exception ex)
            {
                return new BindingValidationResult(false, $"绑定验证异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证多重绑定表达式
        /// </summary>
        /// <param name="multiBindingExpression">多重绑定表达式</param>
        /// <returns>验证结果</returns>
        public static BindingValidationResult ValidateMultiBinding(
            System.Windows.Data.MultiBindingExpression? multiBindingExpression)
        {
            if (multiBindingExpression == null)
                return new BindingValidationResult(false, "多重绑定表达式为空");

            try
            {
                // 更新绑定源
                multiBindingExpression.UpdateSource();

                // 检查是否有验证错误
                if (multiBindingExpression.HasError)
                {
                    var error = multiBindingExpression.ValidationError;
                    return new BindingValidationResult(false, error?.ErrorContent?.ToString() ?? "多重绑定验证失败");
                }

                return new BindingValidationResult(true, "多重绑定验证成功");
            }
            catch (Exception ex)
            {
                return new BindingValidationResult(false, $"多重绑定验证异常: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 绑定验证结果
    /// </summary>
    public class BindingValidationResult
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="isValid">是否有效</param>
        /// <param name="message">消息</param>
        public BindingValidationResult(bool isValid, string message)
        {
            IsValid = isValid;
            Message = message ?? string.Empty;
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid { get; }

        /// <summary>
        /// 验证消息
        /// </summary>
        public string Message { get; }

        /// <summary>
        /// 验证时间戳
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// 转换为字符串
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"[{(IsValid ? "成功" : "失败")}] {Message}";
        }
    }
}
