﻿using System;
using System.Collections.Generic;
using System.ComponentModel.Composition.Hosting;
using System.ComponentModel.Composition;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using McLaser.Core.Framework.Container;
using System.ComponentModel.Composition.Registration;
using McLaser.Core.Modules.RecipeManager;

namespace McLaser.Core.Container
{
    public static class IoC
    {
        private static CompositionContainer Container;

        internal static void PreInitialize()
        {
            string[] files = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory, "*.dll");
            List<string> source = files.Where((string item) => new FileInfo(item).Name.StartsWith("McLaser")).ToList();
   
            IEnumerable<Assembly> collection = source.Select((string x) => Assembly.Load(AssemblyName.GetAssemblyName(x)));
            List<Assembly> list = new List<Assembly>();
            list.AddRange(collection);
            list.Add(Assembly.GetEntryAssembly());
            // 1. 创建动态注册规则
            var registrationBuilder = new RegistrationBuilder();
            registrationBuilder.ForTypesMatching(type =>
                    typeof(IRecipeItem).IsAssignableFrom(type) &&
                    !type.IsAbstract && !type.IsInterface)
                .Export(export => export.AsContractType<IRecipeItem>())
                .SetCreationPolicy(CreationPolicy.NonShared); // 关键设置


            AggregateCatalog catalog = new AggregateCatalog(list.Select((Assembly x) => new AssemblyCatalog(x)));
            Container = new CompositionContainer(catalog);
        }


        public static T Get<T>(string key = "")
        {
            string contract = (string.IsNullOrEmpty(key) ? AttributedModelServices.GetContractName(typeof(T)) : key);
            return GetWapper<T>(contract);
        }

        public static object Get(Type serviceType, string key = "")
        {
            string contract = (string.IsNullOrEmpty(key) ? AttributedModelServices.GetContractName(serviceType) : key);
            return GetWapper(contract);
        }

        public static IEnumerable<T> GetAll<T>(Type t)
        {
            return Container.GetExportedValues<T>(AttributedModelServices.GetContractName(t));
        }

        public static IEnumerable<object> GetAll(Type serviceType)
        {
            return Container.GetExportedValues<object>(AttributedModelServices.GetContractName(serviceType));
        }

        public static IEnumerable<T> GetAll<T>()
        {
            return Container.GetExportedValues<T>(AttributedModelServices.GetContractName(typeof(T)));
        }

        private static T GetWapper<T>(string contract)
        {
            try
            {
                IEnumerable<Lazy<T>> exports = Container.GetExports<T>(contract);
                if (exports.Any())
                {
                    return exports.First().Value;
                }
            }
            catch (ReflectionTypeLoadException ex)
            {
                StringBuilder stringBuilder = new StringBuilder();
                Exception[] loaderExceptions = ex.LoaderExceptions;
                foreach (Exception ex2 in loaderExceptions)
                {
                    stringBuilder.AppendLine(ex2.Message);
                    if (ex2 is FileNotFoundException ex3 && !string.IsNullOrEmpty(ex3.FusionLog))
                    {
                        stringBuilder.AppendLine("Fusion Log:");
                        stringBuilder.AppendLine(ex3.FusionLog);
                    }

                    stringBuilder.AppendLine();
                }

                Console.WriteLine(stringBuilder);
            }

            throw new Exception($"Could not locate any instances of contract {contract}.");
        }

        private static object GetWapper(string contract)
        {
            try
            {
                IEnumerable<Lazy<object>> exports = Container.GetExports<object>(contract);
                if (exports.Any())
                {
                    return exports.First().Value;
                }
            }
            catch (ReflectionTypeLoadException ex)
            {
                StringBuilder stringBuilder = new StringBuilder();
                Exception[] loaderExceptions = ex.LoaderExceptions;
                foreach (Exception ex2 in loaderExceptions)
                {
                    stringBuilder.AppendLine(ex2.Message);
                    if (ex2 is FileNotFoundException ex3 && !string.IsNullOrEmpty(ex3.FusionLog))
                    {
                        stringBuilder.AppendLine("Fusion Log:");
                        stringBuilder.AppendLine(ex3.FusionLog);
                    }

                    stringBuilder.AppendLine();
                }

                Console.WriteLine(stringBuilder);
            }

            throw new Exception($"Could not locate any instances of contract {contract}.");
        }
    }
}
