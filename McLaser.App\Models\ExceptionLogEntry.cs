using System;
using McLaser.App.Events;

namespace McLaser.App.Models
{
    /// <summary>
    /// 异常日志条目
    /// 用于异常处理演示界面的日志显示
    /// </summary>
    public class ExceptionLogEntry
    {
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 异常级别
        /// </summary>
        public ExceptionLevel Level { get; set; } = ExceptionLevel.Error;

        /// <summary>
        /// 异常消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 异常来源
        /// </summary>
        public string Source { get; set; } = string.Empty;

        /// <summary>
        /// 异常类型
        /// </summary>
        public string ExceptionType { get; set; } = string.Empty;

        /// <summary>
        /// 堆栈跟踪
        /// </summary>
        public string StackTrace { get; set; } = string.Empty;

        /// <summary>
        /// 是否已处理
        /// </summary>
        public bool IsHandled { get; set; } = false;

        /// <summary>
        /// 处理器名称
        /// </summary>
        public string HandlerName { get; set; } = string.Empty;

        /// <summary>
        /// 处理时间（毫秒）
        /// </summary>
        public long ProcessingTime { get; set; } = 0;

        /// <summary>
        /// 扩展数据
        /// </summary>
        public object? ExtendedData { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ExceptionLogEntry()
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="level">异常级别</param>
        /// <param name="message">异常消息</param>
        /// <param name="source">异常来源</param>
        public ExceptionLogEntry(ExceptionLevel level, string message, string source = "System")
        {
            Level = level;
            Message = message;
            Source = source;
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// 从异常对象创建日志条目
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <param name="level">异常级别</param>
        /// <param name="source">异常来源</param>
        /// <returns>异常日志条目</returns>
        public static ExceptionLogEntry FromException(Exception exception, ExceptionLevel level = ExceptionLevel.Error, string source = "System")
        {
            return new ExceptionLogEntry
            {
                Level = level,
                Message = exception.Message,
                Source = source,
                ExceptionType = exception.GetType().Name,
                StackTrace = exception.StackTrace ?? string.Empty,
                Timestamp = DateTime.Now
            };
        }

        /// <summary>
        /// 获取级别显示文本
        /// </summary>
        /// <returns>级别显示文本</returns>
        public string GetLevelDisplayText()
        {
            return Level switch
            {
                ExceptionLevel.Info => "信息",
                ExceptionLevel.Warning => "警告",
                ExceptionLevel.Error => "错误",
                ExceptionLevel.Critical => "严重",
                _ => "未知"
            };
        }

        /// <summary>
        /// 获取级别颜色
        /// </summary>
        /// <returns>级别颜色</returns>
        public string GetLevelColor()
        {
            return Level switch
            {
                ExceptionLevel.Info => "#FF17A2B8",      // 蓝色
                ExceptionLevel.Warning => "#FFFFC107",   // 黄色
                ExceptionLevel.Error => "#FFDC3545",     // 红色
                ExceptionLevel.Critical => "#FF6F42C1",  // 紫色
                _ => "#FF6C757D"                         // 灰色
            };
        }

        /// <summary>
        /// 转换为字符串
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"[{Timestamp:yyyy-MM-dd HH:mm:ss}] [{GetLevelDisplayText()}] {Message} (来源: {Source})";
        }
    }
}
