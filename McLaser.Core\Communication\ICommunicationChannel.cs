using System;
using System.Threading;
using System.Threading.Tasks;

namespace McLaser.Core.Communication
{
    /// <summary>
    /// 通信通道接口
    /// 定义了统一的通信通道操作规范
    /// </summary>
    public interface ICommunicationChannel : IDisposable
    {
        /// <summary>
        /// 通道名称
        /// </summary>
        string Name { get; }

        /// <summary>
        /// 通道类型
        /// </summary>
        CommunicationChannelType ChannelType { get; }

        /// <summary>
        /// 连接状态
        /// </summary>
        bool IsConnected { get; }

        /// <summary>
        /// 是否正在连接
        /// </summary>
        bool IsConnecting { get; }

        /// <summary>
        /// 连接配置
        /// </summary>
        CommunicationConfig Configuration { get; set; }

        /// <summary>
        /// 连接状态变更事件
        /// </summary>
        event EventHandler<ConnectionStateChangedEventArgs> ConnectionStateChanged;

        /// <summary>
        /// 数据接收事件
        /// </summary>
        event EventHandler<DataReceivedEventArgs> DataReceived;

        /// <summary>
        /// 错误发生事件
        /// </summary>
        event EventHandler<CommunicationErrorEventArgs> ErrorOccurred;

        /// <summary>
        /// 异步连接
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>连接任务</returns>
        Task<bool> ConnectAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 异步断开连接
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>断开任务</returns>
        Task DisconnectAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 异步发送数据
        /// </summary>
        /// <param name="data">要发送的数据</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>发送任务，返回实际发送的字节数</returns>
        Task<int> SendAsync(byte[] data, CancellationToken cancellationToken = default);

        /// <summary>
        /// 异步发送字符串数据
        /// </summary>
        /// <param name="message">要发送的消息</param>
        /// <param name="encoding">编码方式，默认UTF8</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>发送任务，返回实际发送的字节数</returns>
        Task<int> SendAsync(string message, System.Text.Encoding? encoding = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 异步接收数据
        /// </summary>
        /// <param name="buffer">接收缓冲区</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>接收任务，返回实际接收的字节数</returns>
        Task<int> ReceiveAsync(byte[] buffer, CancellationToken cancellationToken = default);

        /// <summary>
        /// 异步接收数据直到指定分隔符
        /// </summary>
        /// <param name="delimiter">分隔符</param>
        /// <param name="maxLength">最大长度</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>接收任务，返回接收到的数据</returns>
        Task<byte[]> ReceiveUntilAsync(byte[] delimiter, int maxLength = 4096, CancellationToken cancellationToken = default);

        /// <summary>
        /// 清空接收缓冲区
        /// </summary>
        void ClearReceiveBuffer();

        /// <summary>
        /// 清空发送缓冲区
        /// </summary>
        void ClearSendBuffer();

        /// <summary>
        /// 获取通道统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        CommunicationStatistics GetStatistics();

        /// <summary>
        /// 重置统计信息
        /// </summary>
        void ResetStatistics();

        /// <summary>
        /// 测试连接
        /// </summary>
        /// <param name="timeout">超时时间</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>测试结果</returns>
        Task<bool> TestConnectionAsync(TimeSpan timeout, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// 可配置的通信通道接口
    /// </summary>
    public interface IConfigurableCommunicationChannel : ICommunicationChannel
    {
        /// <summary>
        /// 应用配置
        /// </summary>
        /// <param name="config">配置信息</param>
        /// <returns>是否成功应用配置</returns>
        bool ApplyConfiguration(CommunicationConfig config);

        /// <summary>
        /// 验证配置
        /// </summary>
        /// <param name="config">配置信息</param>
        /// <returns>验证结果</returns>
        ConfigurationValidationResult ValidateConfiguration(CommunicationConfig config);

        /// <summary>
        /// 获取默认配置
        /// </summary>
        /// <returns>默认配置</returns>
        CommunicationConfig GetDefaultConfiguration();

        /// <summary>
        /// 获取支持的配置选项
        /// </summary>
        /// <returns>配置选项列表</returns>
        CommunicationConfigOption[] GetSupportedOptions();
    }

    /// <summary>
    /// 支持心跳检测的通信通道接口
    /// </summary>
    public interface IHeartbeatCommunicationChannel : ICommunicationChannel
    {
        /// <summary>
        /// 心跳间隔
        /// </summary>
        TimeSpan HeartbeatInterval { get; set; }

        /// <summary>
        /// 心跳超时时间
        /// </summary>
        TimeSpan HeartbeatTimeout { get; set; }

        /// <summary>
        /// 是否启用心跳检测
        /// </summary>
        bool HeartbeatEnabled { get; set; }

        /// <summary>
        /// 心跳数据
        /// </summary>
        byte[] HeartbeatData { get; set; }

        /// <summary>
        /// 心跳状态变更事件
        /// </summary>
        event EventHandler<HeartbeatStateChangedEventArgs> HeartbeatStateChanged;

        /// <summary>
        /// 启动心跳检测
        /// </summary>
        void StartHeartbeat();

        /// <summary>
        /// 停止心跳检测
        /// </summary>
        void StopHeartbeat();

        /// <summary>
        /// 发送心跳
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>发送任务</returns>
        Task SendHeartbeatAsync(CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// 支持重连的通信通道接口
    /// </summary>
    public interface IReconnectableCommunicationChannel : ICommunicationChannel
    {
        /// <summary>
        /// 是否启用自动重连
        /// </summary>
        bool AutoReconnectEnabled { get; set; }

        /// <summary>
        /// 重连间隔
        /// </summary>
        TimeSpan ReconnectInterval { get; set; }

        /// <summary>
        /// 最大重连次数
        /// </summary>
        int MaxReconnectAttempts { get; set; }

        /// <summary>
        /// 当前重连次数
        /// </summary>
        int CurrentReconnectAttempts { get; }

        /// <summary>
        /// 重连状态变更事件
        /// </summary>
        event EventHandler<ReconnectStateChangedEventArgs> ReconnectStateChanged;

        /// <summary>
        /// 手动重连
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>重连任务</returns>
        Task<bool> ReconnectAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 停止重连
        /// </summary>
        void StopReconnect();
    }
}
