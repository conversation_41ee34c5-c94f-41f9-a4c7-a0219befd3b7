using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Threading.Tasks;
using System.Windows;

namespace McLaser.Core.Framework.UI
{
    /// <summary>
    /// 窗口管理器接口
    /// 提供窗口生命周期管理功能
    /// </summary>
    public interface IWindowManager
    {
        /// <summary>
        /// 活动窗口列表
        /// </summary>
        IReadOnlyList<Window> ActiveWindows { get; }

        /// <summary>
        /// 主窗口
        /// </summary>
        Window? MainWindow { get; set; }

        /// <summary>
        /// 设置主窗口
        /// </summary>
        /// <param name="window">窗口实例</param>
        void SetMainWindow(Window window);

        /// <summary>
        /// 窗口打开事件
        /// </summary>
        event EventHandler<WindowEventArgs>? WindowOpened;

        /// <summary>
        /// 窗口关闭事件
        /// </summary>
        event EventHandler<WindowEventArgs>? WindowClosed;

        /// <summary>
        /// 窗口激活事件
        /// </summary>
        event EventHandler<WindowEventArgs>? WindowActivated;

        /// <summary>
        /// 显示窗口
        /// </summary>
        /// <param name="window">窗口实例</param>
        /// <param name="owner">父窗口</param>
        /// <returns>窗口ID</returns>
        string ShowWindow(Window window, Window? owner = null);

        /// <summary>
        /// 显示模态对话框
        /// </summary>
        /// <param name="window">窗口实例</param>
        /// <param name="owner">父窗口</param>
        /// <returns>对话框结果</returns>
        bool? ShowDialog(Window window, Window? owner = null);

        /// <summary>
        /// 异步显示模态对话框
        /// </summary>
        /// <param name="window">窗口实例</param>
        /// <param name="owner">父窗口</param>
        /// <returns>对话框结果</returns>
        Task<bool?> ShowDialogAsync(Window window, Window? owner = null);

        /// <summary>
        /// 关闭窗口
        /// </summary>
        /// <param name="windowId">窗口ID</param>
        /// <returns>是否成功关闭</returns>
        bool CloseWindow(string windowId);

        /// <summary>
        /// 关闭窗口
        /// </summary>
        /// <param name="window">窗口实例</param>
        /// <returns>是否成功关闭</returns>
        bool CloseWindow(Window window);

        /// <summary>
        /// 关闭所有窗口
        /// </summary>
        /// <param name="excludeMain">是否排除主窗口</param>
        void CloseAllWindows(bool excludeMain = true);

        /// <summary>
        /// 激活窗口
        /// </summary>
        /// <param name="windowId">窗口ID</param>
        /// <returns>是否成功激活</returns>
        bool ActivateWindow(string windowId);

        /// <summary>
        /// 激活窗口
        /// </summary>
        /// <param name="window">窗口实例</param>
        /// <returns>是否成功激活</returns>
        bool ActivateWindow(Window window);

        /// <summary>
        /// 查找窗口
        /// </summary>
        /// <param name="windowId">窗口ID</param>
        /// <returns>窗口实例</returns>
        Window? FindWindow(string windowId);

        /// <summary>
        /// 查找窗口
        /// </summary>
        /// <typeparam name="T">窗口类型</typeparam>
        /// <returns>窗口实例</returns>
        T? FindWindow<T>() where T : Window;

        /// <summary>
        /// 保存窗口状态
        /// </summary>
        /// <param name="window">窗口实例</param>
        void SaveWindowState(Window window);

        /// <summary>
        /// 恢复窗口状态
        /// </summary>
        /// <param name="window">窗口实例</param>
        void RestoreWindowState(Window window);

        /// <summary>
        /// 保存所有窗口状态
        /// </summary>
        void SaveAllWindowStates();

        /// <summary>
        /// 获取窗口布局信息
        /// </summary>
        /// <param name="window">窗口实例</param>
        /// <returns>布局信息</returns>
        WindowLayout GetWindowLayout(Window window);

        /// <summary>
        /// 应用窗口布局
        /// </summary>
        /// <param name="window">窗口实例</param>
        /// <param name="layout">布局信息</param>
        void ApplyWindowLayout(Window window, WindowLayout layout);

        /// <summary>
        /// 检查窗口是否在屏幕范围内
        /// </summary>
        /// <param name="window">窗口实例</param>
        /// <returns>是否在屏幕范围内</returns>
        bool IsWindowOnScreen(Window window);

        /// <summary>
        /// 将窗口移动到屏幕范围内
        /// </summary>
        /// <param name="window">窗口实例</param>
        void EnsureWindowOnScreen(Window window);

        /// <summary>
        /// 获取最佳显示器
        /// </summary>
        /// <param name="window">窗口实例</param>
        /// <returns>显示器信息</returns>
        ScreenInfo GetBestScreen(Window window);
    }

    /// <summary>
    /// 窗口事件参数
    /// </summary>
    public class WindowEventArgs : EventArgs
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="window">窗口实例</param>
        /// <param name="windowId">窗口ID</param>
        public WindowEventArgs(Window window, string windowId)
        {
            Window = window;
            WindowId = windowId;
        }

        /// <summary>
        /// 窗口实例
        /// </summary>
        public Window Window { get; }

        /// <summary>
        /// 窗口ID
        /// </summary>
        public string WindowId { get; }
    }

    /// <summary>
    /// 窗口布局信息
    /// </summary>
    public class WindowLayout
    {
        /// <summary>
        /// 窗口左边距
        /// </summary>
        public double Left { get; set; }

        /// <summary>
        /// 窗口上边距
        /// </summary>
        public double Top { get; set; }

        /// <summary>
        /// 窗口宽度
        /// </summary>
        public double Width { get; set; }

        /// <summary>
        /// 窗口高度
        /// </summary>
        public double Height { get; set; }

        /// <summary>
        /// 窗口状态
        /// </summary>
        public WindowState WindowState { get; set; }

        /// <summary>
        /// 是否置顶
        /// </summary>
        public bool Topmost { get; set; }

        /// <summary>
        /// 显示器索引
        /// </summary>
        public int ScreenIndex { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 显示器信息
    /// </summary>
    public class ScreenInfo
    {
        /// <summary>
        /// 显示器索引
        /// </summary>
        public int Index { get; set; }

        /// <summary>
        /// 显示器名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 工作区域
        /// </summary>
        public Rect WorkingArea { get; set; }

        /// <summary>
        /// 显示区域
        /// </summary>
        public Rect Bounds { get; set; }

        /// <summary>
        /// 是否主显示器
        /// </summary>
        public bool IsPrimary { get; set; }

        /// <summary>
        /// DPI缩放比例
        /// </summary>
        public double DpiScale { get; set; } = 1.0;
    }

    /// <summary>
    /// 窗口显示选项
    /// </summary>
    public class WindowShowOptions
    {
        /// <summary>
        /// 是否模态显示
        /// </summary>
        public bool IsModal { get; set; }

        /// <summary>
        /// 父窗口
        /// </summary>
        public Window? Owner { get; set; }

        /// <summary>
        /// 初始位置
        /// </summary>
        public WindowStartupLocation StartupLocation { get; set; } = WindowStartupLocation.CenterOwner;

        /// <summary>
        /// 是否显示在任务栏
        /// </summary>
        public bool ShowInTaskbar { get; set; } = true;

        /// <summary>
        /// 是否置顶
        /// </summary>
        public bool Topmost { get; set; }

        /// <summary>
        /// 是否可调整大小
        /// </summary>
        public bool CanResize { get; set; } = true;

        /// <summary>
        /// 是否自动保存状态
        /// </summary>
        public bool AutoSaveState { get; set; } = true;

        /// <summary>
        /// 是否自动恢复状态
        /// </summary>
        public bool AutoRestoreState { get; set; } = true;
    }
}
