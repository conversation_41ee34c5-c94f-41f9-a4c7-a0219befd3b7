using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace McLaser.Device.Converters
{
    /// <summary>
    /// 布尔值到旋转变换转换器
    /// 用于Expander标题的旋转效果
    /// </summary>
    public class BoolToRotateTransformConverter : IValueConverter
    {
        /// <summary>
        /// 将布尔值转换为旋转变换
        /// </summary>
        /// <param name="value">布尔值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>旋转变换</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isExpanded)
            {
                // 展开时不旋转，折叠时旋转-90度
                return new RotateTransform(isExpanded ? 0 : -90);
            }
            return new RotateTransform(0);
        }

        /// <summary>
        /// 反向转换（不支持）
        /// </summary>
        /// <param name="value">值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>不支持</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotSupportedException("BoolToRotateTransformConverter does not support ConvertBack");
        }
    }


    public class NullToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value ==null)
            {
                return false;
            }
            return true;
        }

   
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotSupportedException("NullToVisibilityConverter does not support ConvertBack");
        }
    }
}
