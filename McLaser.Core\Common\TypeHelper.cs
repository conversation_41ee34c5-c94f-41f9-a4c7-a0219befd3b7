using System;
using System.Linq;
using System.Reflection;

namespace McLaser.Core.Common
{
    /// <summary>
    /// 类型帮助类
    /// 提供类型相关的实用方法
    /// </summary>
    public static class TypeHelper
    {
        /// <summary>
        /// 检查类型是否为可空类型
        /// </summary>
        /// <param name="type">要检查的类型</param>
        /// <returns>是否为可空类型</returns>
        public static bool IsNullableType(Type type)
        {
            if (type == null)
                throw new ArgumentNullException(nameof(type));

            return type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>);
        }

        /// <summary>
        /// 获取可空类型的基础类型
        /// </summary>
        /// <param name="type">可空类型</param>
        /// <returns>基础类型</returns>
        public static Type GetUnderlyingType(Type type)
        {
            if (type == null)
                throw new ArgumentNullException(nameof(type));

            return IsNullableType(type) ? type.GetGenericArguments()[0] : type;
        }

        /// <summary>
        /// 检查类型是否为数值类型
        /// </summary>
        /// <param name="type">要检查的类型</param>
        /// <returns>是否为数值类型</returns>
        public static bool IsNumericType(Type type)
        {
            if (type == null)
                throw new ArgumentNullException(nameof(type));

            type = GetUnderlyingType(type);

            return type == typeof(byte) ||
                   type == typeof(sbyte) ||
                   type == typeof(short) ||
                   type == typeof(ushort) ||
                   type == typeof(int) ||
                   type == typeof(uint) ||
                   type == typeof(long) ||
                   type == typeof(ulong) ||
                   type == typeof(float) ||
                   type == typeof(double) ||
                   type == typeof(decimal);
        }

        /// <summary>
        /// 检查类型是否为整数类型
        /// </summary>
        /// <param name="type">要检查的类型</param>
        /// <returns>是否为整数类型</returns>
        public static bool IsIntegerType(Type type)
        {
            if (type == null)
                throw new ArgumentNullException(nameof(type));

            type = GetUnderlyingType(type);

            return type == typeof(byte) ||
                   type == typeof(sbyte) ||
                   type == typeof(short) ||
                   type == typeof(ushort) ||
                   type == typeof(int) ||
                   type == typeof(uint) ||
                   type == typeof(long) ||
                   type == typeof(ulong);
        }

        /// <summary>
        /// 检查类型是否为浮点数类型
        /// </summary>
        /// <param name="type">要检查的类型</param>
        /// <returns>是否为浮点数类型</returns>
        public static bool IsFloatingPointType(Type type)
        {
            if (type == null)
                throw new ArgumentNullException(nameof(type));

            type = GetUnderlyingType(type);

            return type == typeof(float) ||
                   type == typeof(double) ||
                   type == typeof(decimal);
        }

        /// <summary>
        /// 检查类型是否为简单类型（基元类型、字符串、DateTime等）
        /// </summary>
        /// <param name="type">要检查的类型</param>
        /// <returns>是否为简单类型</returns>
        public static bool IsSimpleType(Type type)
        {
            if (type == null)
                throw new ArgumentNullException(nameof(type));

            type = GetUnderlyingType(type);

            return type.IsPrimitive ||
                   type == typeof(string) ||
                   type == typeof(DateTime) ||
                   type == typeof(DateTimeOffset) ||
                   type == typeof(TimeSpan) ||
                   type == typeof(Guid) ||
                   type.IsEnum;
        }

        /// <summary>
        /// 获取类型的默认值
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>默认值</returns>
        public static object? GetDefaultValue(Type type)
        {
            if (type == null)
                throw new ArgumentNullException(nameof(type));

            return type.IsValueType ? Activator.CreateInstance(type) : null;
        }

        /// <summary>
        /// 检查对象是否可以转换为指定类型
        /// </summary>
        /// <param name="value">要检查的对象</param>
        /// <param name="targetType">目标类型</param>
        /// <returns>是否可以转换</returns>
        public static bool CanConvertTo(object? value, Type targetType)
        {
            if (targetType == null)
                throw new ArgumentNullException(nameof(targetType));

            if (value == null)
                return !targetType.IsValueType || IsNullableType(targetType);

            var sourceType = value.GetType();

            // 相同类型
            if (sourceType == targetType)
                return true;

            // 可赋值
            if (targetType.IsAssignableFrom(sourceType))
                return true;

            // 可空类型转换
            if (IsNullableType(targetType))
            {
                var underlyingType = GetUnderlyingType(targetType);
                return CanConvertTo(value, underlyingType);
            }

            // 数值类型转换
            if (IsNumericType(sourceType) && IsNumericType(targetType))
                return true;

            // 字符串转换
            if (sourceType == typeof(string))
            {
                try
                {
                    Convert.ChangeType(value, GetUnderlyingType(targetType));
                    return true;
                }
                catch
                {
                    return false;
                }
            }

            // IConvertible 接口
            if (value is IConvertible)
            {
                try
                {
                    Convert.ChangeType(value, GetUnderlyingType(targetType));
                    return true;
                }
                catch
                {
                    return false;
                }
            }

            return false;
        }

        /// <summary>
        /// 将对象转换为指定类型
        /// </summary>
        /// <param name="value">要转换的对象</param>
        /// <param name="targetType">目标类型</param>
        /// <returns>转换后的对象</returns>
        public static object? ConvertTo(object? value, Type targetType)
        {
            if (targetType == null)
                throw new ArgumentNullException(nameof(targetType));

            if (value == null)
            {
                if (!targetType.IsValueType || IsNullableType(targetType))
                    return null;

                throw new InvalidCastException($"无法将 null 转换为 {targetType.Name}");
            }

            var sourceType = value.GetType();

            // 相同类型
            if (sourceType == targetType)
                return value;

            // 可赋值
            if (targetType.IsAssignableFrom(sourceType))
                return value;

            // 可空类型转换
            if (IsNullableType(targetType))
            {
                var underlyingType = GetUnderlyingType(targetType);
                var convertedValue = ConvertTo(value, underlyingType);
                return Activator.CreateInstance(targetType, convertedValue);
            }

            // 枚举转换
            if (targetType.IsEnum)
            {
                if (sourceType == typeof(string))
                    return Enum.Parse(targetType, (string)value);

                return Enum.ToObject(targetType, value);
            }

            // 使用 Convert.ChangeType
            try
            {
                return Convert.ChangeType(value, GetUnderlyingType(targetType));
            }
            catch (Exception ex)
            {
                throw new InvalidCastException($"无法将 {sourceType.Name} 转换为 {targetType.Name}", ex);
            }
        }

        /// <summary>
        /// 获取类型的所有属性
        /// </summary>
        /// <param name="type">类型</param>
        /// <param name="bindingFlags">绑定标志</param>
        /// <returns>属性列表</returns>
        public static PropertyInfo[] GetProperties(Type type, BindingFlags bindingFlags = BindingFlags.Public | BindingFlags.Instance)
        {
            if (type == null)
                throw new ArgumentNullException(nameof(type));

            return type.GetProperties(bindingFlags);
        }

        /// <summary>
        /// 获取类型的所有字段
        /// </summary>
        /// <param name="type">类型</param>
        /// <param name="bindingFlags">绑定标志</param>
        /// <returns>字段列表</returns>
        public static FieldInfo[] GetFields(Type type, BindingFlags bindingFlags = BindingFlags.Public | BindingFlags.Instance)
        {
            if (type == null)
                throw new ArgumentNullException(nameof(type));

            return type.GetFields(bindingFlags);
        }

        /// <summary>
        /// 获取类型的所有方法
        /// </summary>
        /// <param name="type">类型</param>
        /// <param name="bindingFlags">绑定标志</param>
        /// <returns>方法列表</returns>
        public static MethodInfo[] GetMethods(Type type, BindingFlags bindingFlags = BindingFlags.Public | BindingFlags.Instance)
        {
            if (type == null)
                throw new ArgumentNullException(nameof(type));

            return type.GetMethods(bindingFlags);
        }

        /// <summary>
        /// 检查类型是否实现了指定接口
        /// </summary>
        /// <param name="type">要检查的类型</param>
        /// <param name="interfaceType">接口类型</param>
        /// <returns>是否实现了接口</returns>
        public static bool ImplementsInterface(Type type, Type interfaceType)
        {
            if (type == null)
                throw new ArgumentNullException(nameof(type));

            if (interfaceType == null)
                throw new ArgumentNullException(nameof(interfaceType));

            if (!interfaceType.IsInterface)
                throw new ArgumentException("指定的类型不是接口", nameof(interfaceType));

            return interfaceType.IsAssignableFrom(type);
        }

        /// <summary>
        /// 获取类型实现的所有接口
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>接口类型列表</returns>
        public static Type[] GetInterfaces(Type type)
        {
            if (type == null)
                throw new ArgumentNullException(nameof(type));

            return type.GetInterfaces();
        }
    }
}
