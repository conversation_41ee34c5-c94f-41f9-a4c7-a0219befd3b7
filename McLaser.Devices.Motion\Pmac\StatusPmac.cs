using System;
using System.ComponentModel;
using McLaser.Device;

namespace McLaser.Devices.Motion
{

    [Serializable]
    public class StatusPmac : StatusBase
    {
        #region 私有字段

        private bool _isConnected = false;
        private bool _isInitialized = false;
        private string _firmwareVersion = "未知";
        private string _errorMessage = string.Empty;
        private DateTime _lastUpdateTime = DateTime.MinValue;
        private int _connectedAxesCount = 0;
        private int _enabledAxesCount = 0;
        private double _cpuUsage = 0;
        private double _temperature = 0;
        private string _connectionStatus = "未连接";

        #endregion

        #region 属性

        /// <summary>
        /// 设备是否已连接
        /// </summary>
        [Category("PMAC状态"), DisplayName("连接状态")]
        public  bool IsConnected
        {
            get => _isConnected;
            set
            {
                if (_isConnected != value)
                {
                    _isConnected = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                    OnPropertyChanged(nameof(ConnectionStatus));
                }
            }
        }

        /// <summary>
        /// 是否已初始化
        /// </summary>
        [Category("PMAC状态"), DisplayName("初始化状态")]
        public bool IsInitialized
        {
            get => _isInitialized;
            set
            {
                if (_isInitialized != value)
                {
                    _isInitialized = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// 固件版本
        /// </summary>
        [Category("PMAC状态"), DisplayName("固件版本")]
        public string FirmwareVersion
        {
            get => _firmwareVersion;
            set
            {
                if (_firmwareVersion != value)
                {
                    _firmwareVersion = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 错误信息
        /// </summary>
        [Category("PMAC状态"), DisplayName("错误信息")]
        public string ErrorMessage
        {
            get => _errorMessage;
            set
            {
                if (_errorMessage != value)
                {
                    _errorMessage = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        [Category("PMAC状态"), DisplayName("最后更新时间")]
        public DateTime LastUpdateTime
        {
            get => _lastUpdateTime;
            set
            {
                if (_lastUpdateTime != value)
                {
                    _lastUpdateTime = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 已连接轴数量
        /// </summary>
        [Category("PMAC状态"), DisplayName("已连接轴数")]
        public int ConnectedAxesCount
        {
            get => _connectedAxesCount;
            set
            {
                if (_connectedAxesCount != value)
                {
                    _connectedAxesCount = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 已使能轴数量
        /// </summary>
        [Category("PMAC状态"), DisplayName("已使能轴数")]
        public int EnabledAxesCount
        {
            get => _enabledAxesCount;
            set
            {
                if (_enabledAxesCount != value)
                {
                    _enabledAxesCount = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// CPU使用率(%)
        /// </summary>
        [Category("PMAC状态"), DisplayName("CPU使用率(%)")]
        public double CpuUsage
        {
            get => _cpuUsage;
            set
            {
                if (Math.Abs(_cpuUsage - value) > 0.1)
                {
                    _cpuUsage = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 控制器温度(℃)
        /// </summary>
        [Category("PMAC状态"), DisplayName("控制器温度(℃)")]
        public double Temperature
        {
            get => _temperature;
            set
            {
                if (Math.Abs(_temperature - value) > 0.1)
                {
                    _temperature = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 连接状态描述
        /// </summary>
        [Category("PMAC状态"), DisplayName("连接状态描述")]
        public string ConnectionStatus
        {
            get => _connectionStatus;
            set
            {
                if (_connectionStatus != value)
                {
                    _connectionStatus = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 状态文本描述
        /// </summary>
        [Category("PMAC状态"), DisplayName("状态描述")]
        public  string StatusText
        {
            get
            {
                if (!string.IsNullOrEmpty(_errorMessage))
                    return $"错误: {_errorMessage}";

                if (!_isConnected)
                    return "设备未连接";

                if (!_isInitialized)
                    return "设备已连接，未初始化";

                return $"运行正常 - 轴数: {_enabledAxesCount}/{_connectedAxesCount} - CPU: {_cpuUsage:F1}%";
            }
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public StatusPmac()
        {
            // 初始化状态
            Reset();
        }

        #endregion

        #region 方法

        /// <summary>
        /// 重置状态
        /// </summary>
        public  void Reset()
        {
            IsConnected = false;
            IsInitialized = false;
            FirmwareVersion = "未知";
            ErrorMessage = string.Empty;
            LastUpdateTime = DateTime.MinValue;
            ConnectedAxesCount = 0;
            EnabledAxesCount = 0;
            CpuUsage = 0;
            Temperature = 0;
            ConnectionStatus = "未连接";
        }

        /// <summary>
        /// 设置错误信息
        /// </summary>
        /// <param name="error">错误信息</param>
        public void SetError(string error)
        {
            ErrorMessage = error;
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 清除错误信息
        /// </summary>
        public void ClearError()
        {
            ErrorMessage = string.Empty;
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 更新连接状态
        /// </summary>
        /// <param name="connected">是否连接</param>
        /// <param name="statusDescription">状态描述</param>
        public void UpdateConnectionStatus(bool connected, string statusDescription = null)
        {
            IsConnected = connected;
            ConnectionStatus = statusDescription ?? (connected ? "已连接" : "未连接");
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 更新轴状态统计
        /// </summary>
        /// <param name="connectedCount">已连接轴数</param>
        /// <param name="enabledCount">已使能轴数</param>
        public void UpdateAxesStatus(int connectedCount, int enabledCount)
        {
            ConnectedAxesCount = connectedCount;
            EnabledAxesCount = enabledCount;
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 更新系统性能信息
        /// </summary>
        /// <param name="cpuUsage">CPU使用率</param>
        /// <param name="temperature">温度</param>
        public void UpdatePerformanceInfo(double cpuUsage, double temperature)
        {
            CpuUsage = cpuUsage;
            Temperature = temperature;
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 更新固件版本
        /// </summary>
        /// <param name="version">版本信息</param>
        public void UpdateFirmwareVersion(string version)
        {
            FirmwareVersion = version ?? "未知";
            LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 获取状态摘要
        /// </summary>
        /// <returns>状态摘要字符串</returns>
        public  string GetStatusSummary()
        {
            return $"PMAC控制卡 - {StatusText} | 固件: {FirmwareVersion} | 温度: {Temperature:F1}℃";
        }

        /// <summary>
        /// 检查是否需要更新状态
        /// </summary>
        /// <param name="intervalSeconds">更新间隔(秒)</param>
        /// <returns>是否需要更新</returns>
        public bool ShouldUpdate(double intervalSeconds = 1.0)
        {
            if (LastUpdateTime == DateTime.MinValue)
                return true;

            return (DateTime.Now - LastUpdateTime).TotalSeconds >= intervalSeconds;
        }

        #endregion
    }


}
