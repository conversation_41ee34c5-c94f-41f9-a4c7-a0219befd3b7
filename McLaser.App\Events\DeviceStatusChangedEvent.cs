using System;

namespace McLaser.App.Events
{
    /// <summary>
    /// 设备状态变更事件
    /// 当设备状态发生变化时触发此事件
    /// </summary>
    public class DeviceStatusChangedEvent : IEvent
    {
        /// <summary>
        /// 事件ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 事件时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceId { get; set; } = string.Empty;

        /// <summary>
        /// 设备名称
        /// </summary>
        public string DeviceName { get; set; } = string.Empty;

        /// <summary>
        /// 旧状态
        /// </summary>
        public string OldStatus { get; set; } = string.Empty;

        /// <summary>
        /// 新状态
        /// </summary>
        public string NewStatus { get; set; } = string.Empty;

        /// <summary>
        /// 状态变更消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 事件来源
        /// </summary>
        public string Source { get; set; } = "DeviceManager";

        /// <summary>
        /// 事件优先级
        /// </summary>
        public EventPriority Priority { get; set; } = EventPriority.Normal;

        /// <summary>
        /// 是否需要持久化
        /// </summary>
        public bool RequiresPersistence { get; set; } = true;

        /// <summary>
        /// 扩展数据
        /// </summary>
        public object? Data { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public DeviceStatusChangedEvent()
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="deviceName">设备名称</param>
        /// <param name="oldStatus">旧状态</param>
        /// <param name="newStatus">新状态</param>
        public DeviceStatusChangedEvent(string deviceId, string deviceName, string oldStatus, string newStatus)
        {
            DeviceId = deviceId;
            DeviceName = deviceName;
            OldStatus = oldStatus;
            NewStatus = newStatus;
            Message = $"设备 {deviceName} 状态从 {oldStatus} 变更为 {newStatus}";
        }

        /// <summary>
        /// 获取事件描述
        /// </summary>
        /// <returns>事件描述</returns>
        public override string ToString()
        {
            return $"DeviceStatusChanged: {DeviceName} ({DeviceId}) {OldStatus} -> {NewStatus}";
        }
    }
}
