# McLaser.App 使用指南

## 📖 概述

本指南将详细介绍如何使用McLaser.App示例应用程序，以及如何通过该应用程序学习和掌握McLaser.Core框架的各项功能。

## 🚀 启动应用程序

### 方法一：Visual Studio
1. 打开`McLaser_V1.sln`解决方案
2. 右键点击`McLaser.App`项目，选择"设为启动项目"
3. 按`F5`或点击"开始调试"按钮

### 方法二：命令行
```bash
cd McLaser_V1
dotnet run --project McLaser.App
```

### 方法三：直接运行
```bash
cd McLaser.App\bin\Debug
McLaser.App.exe
```

## 🏠 主窗口介绍

### 窗口布局
主窗口采用经典的桌面应用程序布局：
- **菜单栏**: 顶部包含文件、编辑、视图、工具、帮助菜单
- **工具栏**: 常用功能的快速访问按钮
- **主内容区**: 分为左右两个面板
- **状态栏**: 底部显示当前状态和主题信息

### 左侧功能面板
展示McLaser.Core框架的核心特性：
- 统一DI容器架构
- 主题管理系统
- 窗口管理器
- 数据验证框架
- 配置管理服务
- MVVM模式支持
- 日志记录系统
- 对话框服务
- 导航服务
- 异常处理服务

### 右侧操作面板
提供各种功能的演示按钮：
- **打开设置窗口**: 演示窗口管理和配置服务
- **打开数据输入窗口**: 演示数据验证框架
- **测试对话框**: 演示对话框服务
- **测试配置**: 演示配置管理服务
- **刷新状态**: 更新应用程序状态信息

## 🎨 主题管理功能

### 切换主题
1. **方法一**: 点击状态栏右侧的主题切换按钮
2. **方法二**: 使用菜单栏"视图" → "主题" → 选择主题
3. **方法三**: 在设置窗口中选择主题

### 可用主题
- **Light (浅色主题)**: 适合日间使用的明亮主题
- **Dark (深色主题)**: 适合夜间使用的深色主题

### 主题特性
- **即时切换**: 无需重启应用程序
- **全局应用**: 所有窗口和控件都会应用新主题
- **设置保存**: 主题选择会自动保存到配置文件

## ⚙️ 设置窗口功能

### 打开设置窗口
- 点击主窗口的"打开设置窗口"按钮
- 或使用菜单栏"工具" → "设置"

### 设置选项
1. **主题设置**
   - 选择应用程序主题
   - 实时预览主题效果

2. **窗口设置**
   - 记住窗口位置和大小
   - 启动时恢复窗口状态

3. **日志设置**
   - 设置日志级别
   - 配置日志输出选项

4. **性能设置**
   - 启用缓存功能
   - 性能监控选项

### 设置保存
- 点击"保存设置"按钮保存当前配置
- 点击"重置设置"按钮恢复默认配置
- 设置会自动保存到`App.config`文件

## 📝 数据验证功能

### 打开数据输入窗口
- 点击主窗口的"打开数据输入窗口"按钮
- 或使用菜单栏"工具" → "数据验证演示"

### 验证字段
1. **姓名** (必填)
   - 长度：2-50个字符
   - 不能为空

2. **邮箱** (必填)
   - 必须是有效的邮箱格式
   - 例如：<EMAIL>

3. **年龄** (必填)
   - 范围：1-120
   - 必须是数字

4. **电话** (可选)
   - 必须是有效的电话号码格式
   - 例如：13812345678

5. **网站** (可选)
   - 必须是有效的URL格式
   - 例如：https://www.example.com

6. **描述** (可选)
   - 最大长度：500个字符

### 验证特性
- **实时验证**: 输入时即时显示验证结果
- **错误提示**: 清晰的错误信息显示
- **状态指示**: 验证通过/失败状态显示
- **按钮状态**: 保存按钮根据验证结果启用/禁用

### 操作按钮
- **验证数据**: 手动触发完整验证
- **保存数据**: 保存验证通过的数据
- **清空数据**: 清除所有输入内容
- **生成测试数据**: 自动填充测试数据

## 💬 对话框服务演示

### 测试对话框功能
点击主窗口的"测试对话框"按钮，将依次显示：

1. **信息对话框**: 显示一般信息
2. **确认对话框**: 询问用户确认操作
3. **结果对话框**: 根据用户选择显示结果

### 对话框类型
- **ShowInformation**: 信息提示对话框
- **ShowWarning**: 警告对话框
- **ShowError**: 错误对话框
- **ShowConfirmation**: 确认对话框

## 🔧 配置服务演示

### 测试配置功能
点击主窗口的"测试配置"按钮，将演示：

1. **设置配置值**: 保存当前时间戳到配置
2. **读取配置值**: 从配置中读取保存的值
3. **显示结果**: 对比设置和读取的值

### 配置特性
- **类型安全**: 支持强类型配置读取
- **自动保存**: 配置变更自动持久化
- **默认值**: 支持配置项的默认值

## 📊 状态和监控

### 状态栏信息
- **时间戳**: 显示最后操作的时间
- **状态消息**: 当前操作的状态信息
- **主题信息**: 当前使用的主题名称
- **活动窗口**: 当前打开的窗口数量

### 刷新状态
点击"刷新状态"按钮可以：
- 更新主题信息
- 刷新窗口列表
- 重新加载配置信息
- 更新状态显示

## 🔍 日志和调试

### 日志查看
应用程序的所有操作都会记录到日志中：
- 应用程序启动和关闭
- 窗口打开和关闭
- 主题切换操作
- 配置变更
- 验证结果
- 错误和异常

### 调试信息
在Visual Studio中运行时，可以在输出窗口查看详细的调试信息。

## ❗ 常见问题

### Q: 应用程序无法启动
A: 检查是否安装了.NET Framework 4.7.2或更高版本

### Q: 主题切换不生效
A: 确保主题文件存在且格式正确，重启应用程序

### Q: 数据验证不工作
A: 检查输入格式是否符合验证规则要求

### Q: 设置无法保存
A: 确保应用程序有写入配置文件的权限

## 📞 技术支持

如果遇到问题或需要技术支持，请：
1. 查看日志文件获取详细错误信息
2. 检查配置文件是否正确
3. 确认运行环境满足要求
4. 提交Issue到项目仓库

## 🎓 学习建议

1. **从简单开始**: 先熟悉基本的窗口操作和主题切换
2. **逐步深入**: 然后学习数据验证和配置管理
3. **查看代码**: 结合源代码理解MVVM模式的实现
4. **动手实践**: 尝试修改和扩展现有功能
5. **参考文档**: 查阅McLaser.Core框架的详细文档
