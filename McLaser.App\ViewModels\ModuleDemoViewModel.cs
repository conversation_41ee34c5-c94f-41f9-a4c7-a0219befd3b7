using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows.Input;
using McLaser.Core.Common;
using McLaser.Core.Framework.Logging;
using McLaser.Core.Framework.Serialization;
using McLaser.Core.Framework.Styles;
using McLaser.Core.Framework.Security;

namespace McLaser.App.ViewModels
{
    /// <summary>
    /// 模块演示ViewModel
    /// 展示JSON序列化、样式系统和用户权限管理模块的功能
    /// </summary>
    public class ModuleDemoViewModel : ViewModelBase
    {
        #region 字段和属性

        private readonly ILogger? _logger;
        private readonly IJsonService _jsonService;
        private readonly IStyleService _styleService;
        private readonly IUserService _userService;

        private string _jsonInput = string.Empty;
        private string _jsonOutput = string.Empty;
        private string _currentStyleSet = string.Empty;
        private string _loginUsername = "admin";
        private string _loginPassword = "admin123";
        private string _loginStatus = "未登录";
        private bool _isLoggedIn = false;

        /// <summary>
        /// JSON输入
        /// </summary>
        public string JsonInput
        {
            get => _jsonInput;
            set => SetProperty(ref _jsonInput, value);
        }

        /// <summary>
        /// JSON输出
        /// </summary>
        public string JsonOutput
        {
            get => _jsonOutput;
            set => SetProperty(ref _jsonOutput, value);
        }

        /// <summary>
        /// 当前样式集合
        /// </summary>
        public string CurrentStyleSet
        {
            get => _currentStyleSet;
            set => SetProperty(ref _currentStyleSet, value);
        }

        /// <summary>
        /// 登录用户名
        /// </summary>
        public string LoginUsername
        {
            get => _loginUsername;
            set => SetProperty(ref _loginUsername, value);
        }

        /// <summary>
        /// 登录密码
        /// </summary>
        public string LoginPassword
        {
            get => _loginPassword;
            set => SetProperty(ref _loginPassword, value);
        }

        /// <summary>
        /// 登录状态
        /// </summary>
        public string LoginStatus
        {
            get => _loginStatus;
            set => SetProperty(ref _loginStatus, value);
        }

        /// <summary>
        /// 是否已登录
        /// </summary>
        public bool IsLoggedIn
        {
            get => _isLoggedIn;
            set => SetProperty(ref _isLoggedIn, value);
        }

        /// <summary>
        /// 可用样式集合
        /// </summary>
        public ObservableCollection<string> AvailableStyleSets { get; }

        /// <summary>
        /// 用户列表
        /// </summary>
        public ObservableCollection<User> Users { get; }

        /// <summary>
        /// 日志消息
        /// </summary>
        public ObservableCollection<string> LogMessages { get; }

        #endregion

        #region 命令

        /// <summary>
        /// 序列化JSON命令
        /// </summary>
        public ICommand SerializeJsonCommand { get; }

        /// <summary>
        /// 反序列化JSON命令
        /// </summary>
        public ICommand DeserializeJsonCommand { get; }

        /// <summary>
        /// 格式化JSON命令
        /// </summary>
        public ICommand FormatJsonCommand { get; }

        /// <summary>
        /// 应用样式命令
        /// </summary>
        public ICommand ApplyStyleCommand { get; }

        /// <summary>
        /// 清除样式缓存命令
        /// </summary>
        public ICommand ClearStyleCacheCommand { get; }

        /// <summary>
        /// 用户登录命令
        /// </summary>
        public ICommand LoginCommand { get; }

        /// <summary>
        /// 用户登出命令
        /// </summary>
        public ICommand LogoutCommand { get; }

        /// <summary>
        /// 刷新用户列表命令
        /// </summary>
        public ICommand RefreshUsersCommand { get; }

        /// <summary>
        /// 清除日志命令
        /// </summary>
        public ICommand ClearLogCommand { get; }

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public ModuleDemoViewModel(
            ILogger? logger,
            IJsonService jsonService,
            IStyleService styleService,
            IUserService userService)
        {
            _logger = logger;
            _jsonService = jsonService;
            _styleService = styleService;
            _userService = userService;

            // 初始化集合
            AvailableStyleSets = new ObservableCollection<string>();
            Users = new ObservableCollection<User>();
            LogMessages = new ObservableCollection<string>();

            // 初始化命令
            SerializeJsonCommand = new RelayCommand(SerializeJson);
            DeserializeJsonCommand = new RelayCommand(DeserializeJson);
            FormatJsonCommand = new RelayCommand(FormatJson);
            ApplyStyleCommand = new RelayCommand<string>(ApplyStyle);
            ClearStyleCacheCommand = new RelayCommand(ClearStyleCache);
            LoginCommand = new AsyncRelayCommand(LoginAsync);
            LogoutCommand = new AsyncRelayCommand(LogoutAsync);
            RefreshUsersCommand = new AsyncRelayCommand(RefreshUsersAsync);
            ClearLogCommand = new RelayCommand(ClearLog);

            // 初始化数据
            InitializeData();

            // 订阅事件
            SubscribeToEvents();

            AddLogMessage("模块演示ViewModel已初始化");
        }

        #endregion

        #region 初始化

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            try
            {
                // 初始化样式集合
                foreach (var styleSet in _styleService.AvailableStyleSets)
                {
                    AvailableStyleSets.Add(styleSet);
                }
                CurrentStyleSet = _styleService.CurrentStyleSet;

                // 初始化JSON示例
                JsonInput = @"{
  ""name"": ""McLaser Framework"",
  ""version"": ""1.0.0"",
  ""features"": [
    ""JSON序列化"",
    ""样式管理"",
    ""用户权限""
  ]
}";

                // 更新登录状态
                UpdateLoginStatus();

                // 刷新用户列表
                _ = RefreshUsersAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError("初始化数据失败", ex);
                AddLogMessage($"初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 订阅事件
        /// </summary>
        private void SubscribeToEvents()
        {
            try
            {
                // 订阅JSON服务事件
                _jsonService.SerializationCompleted += OnJsonSerializationCompleted;
                _jsonService.DeserializationCompleted += OnJsonDeserializationCompleted;
                _jsonService.SerializationError += OnJsonSerializationError;

                // 订阅样式服务事件
                _styleService.StyleSetChanged += OnStyleSetChanged;

                // 订阅用户服务事件
                _userService.UserLoggedIn += OnUserLoggedIn;
                _userService.UserLoggedOut += OnUserLoggedOut;
            }
            catch (Exception ex)
            {
                _logger?.LogError("订阅事件失败", ex);
                AddLogMessage($"事件订阅失败: {ex.Message}");
            }
        }

        #endregion

        #region JSON序列化功能

        /// <summary>
        /// 序列化JSON
        /// </summary>
        private void SerializeJson()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(JsonInput))
                {
                    AddLogMessage("请输入要序列化的JSON内容");
                    return;
                }

                // 先反序列化为对象，再序列化（验证格式）
                var obj = _jsonService.Deserialize<object>(JsonInput);
                if (obj != null)
                {
                    JsonOutput = _jsonService.SerializeFormatted(obj);
                    AddLogMessage("JSON序列化成功");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("JSON序列化失败", ex);
                AddLogMessage($"序列化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 反序列化JSON
        /// </summary>
        private void DeserializeJson()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(JsonInput))
                {
                    AddLogMessage("请输入要反序列化的JSON内容");
                    return;
                }

                var obj = _jsonService.Deserialize<object>(JsonInput);
                JsonOutput = obj?.ToString() ?? "null";
                AddLogMessage("JSON反序列化成功");
            }
            catch (Exception ex)
            {
                _logger?.LogError("JSON反序列化失败", ex);
                AddLogMessage($"反序列化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 格式化JSON
        /// </summary>
        private void FormatJson()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(JsonInput))
                {
                    AddLogMessage("请输入要格式化的JSON内容");
                    return;
                }

                JsonOutput = _jsonService.FormatJson(JsonInput);
                AddLogMessage("JSON格式化成功");
            }
            catch (Exception ex)
            {
                _logger?.LogError("JSON格式化失败", ex);
                AddLogMessage($"格式化失败: {ex.Message}");
            }
        }

        #endregion

        #region 样式系统功能

        /// <summary>
        /// 应用样式
        /// </summary>
        private void ApplyStyle(string? styleSetName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(styleSetName))
                {
                    AddLogMessage("请选择要应用的样式集合");
                    return;
                }

                var success = _styleService.ApplyStyleSet(styleSetName);
                if (success)
                {
                    CurrentStyleSet = styleSetName;
                    AddLogMessage($"样式集合已应用: {styleSetName}");
                }
                else
                {
                    AddLogMessage($"应用样式集合失败: {styleSetName}");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"应用样式失败: {styleSetName}", ex);
                AddLogMessage($"应用样式失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清除样式缓存
        /// </summary>
        private void ClearStyleCache()
        {
            try
            {
                _styleService.ClearStyleCache();
                var stats = _styleService.GetCacheStatistics();
                AddLogMessage($"样式缓存已清除，统计信息: 缓存数量={stats.CachedStylesCount}, 命中率={stats.HitRatio:P2}");
            }
            catch (Exception ex)
            {
                _logger?.LogError("清除样式缓存失败", ex);
                AddLogMessage($"清除缓存失败: {ex.Message}");
            }
        }

        #endregion

        #region 用户权限功能

        /// <summary>
        /// 用户登录
        /// </summary>
        private async Task LoginAsync()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(LoginUsername) || string.IsNullOrWhiteSpace(LoginPassword))
                {
                    AddLogMessage("请输入用户名和密码");
                    return;
                }

                var result = await _userService.LoginAsync(LoginUsername, LoginPassword);
                if (result.Success)
                {
                    AddLogMessage($"登录成功: {result.User?.DisplayNameOrUsername}");
                    UpdateLoginStatus();
                    await RefreshUsersAsync();
                }
                else
                {
                    AddLogMessage($"登录失败: {result.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("用户登录失败", ex);
                AddLogMessage($"登录异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 用户登出
        /// </summary>
        private async Task LogoutAsync()
        {
            try
            {
                var success = await _userService.LogoutAsync();
                if (success)
                {
                    AddLogMessage("登出成功");
                    UpdateLoginStatus();
                }
                else
                {
                    AddLogMessage("登出失败");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("用户登出失败", ex);
                AddLogMessage($"登出异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 刷新用户列表
        /// </summary>
        private async Task RefreshUsersAsync()
        {
            try
            {
                Users.Clear();
                var users = await _userService.GetAllUsersAsync();
                foreach (var user in users)
                {
                    Users.Add(user);
                }
                AddLogMessage($"用户列表已刷新，共 {Users.Count} 个用户");
            }
            catch (Exception ex)
            {
                _logger?.LogError("刷新用户列表失败", ex);
                AddLogMessage($"刷新用户列表失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新登录状态
        /// </summary>
        private void UpdateLoginStatus()
        {
            try
            {
                IsLoggedIn = _userService.IsAuthenticated;
                if (IsLoggedIn)
                {
                    var currentUser = _userService.CurrentUser;
                    var session = _userService.GetCurrentSession();
                    LoginStatus = $"已登录: {currentUser?.DisplayNameOrUsername} (会话剩余: {session?.RemainingTime:hh\\:mm\\:ss})";
                }
                else
                {
                    LoginStatus = "未登录";
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("更新登录状态失败", ex);
                LoginStatus = "状态未知";
            }
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// JSON序列化完成事件
        /// </summary>
        private void OnJsonSerializationCompleted(object? sender, JsonSerializationEventArgs e)
        {
            AddLogMessage($"JSON序列化完成: {e.SourceType.Name}, 耗时: {e.ElapsedMilliseconds}ms");
        }

        /// <summary>
        /// JSON反序列化完成事件
        /// </summary>
        private void OnJsonDeserializationCompleted(object? sender, JsonDeserializationEventArgs e)
        {
            AddLogMessage($"JSON反序列化完成: {e.TargetType.Name}, 耗时: {e.ElapsedMilliseconds}ms");
        }

        /// <summary>
        /// JSON序列化错误事件
        /// </summary>
        private void OnJsonSerializationError(object? sender, JsonErrorEventArgs e)
        {
            AddLogMessage($"JSON操作错误: {e.Operation} - {e.Exception.Message}");
        }

        /// <summary>
        /// 样式集合变更事件
        /// </summary>
        private void OnStyleSetChanged(object? sender, StyleSetChangedEventArgs e)
        {
            CurrentStyleSet = e.NewStyleSet;
            AddLogMessage($"样式集合已变更: {e.OldStyleSet} -> {e.NewStyleSet}");
        }

        /// <summary>
        /// 用户登录事件
        /// </summary>
        private void OnUserLoggedIn(object? sender, UserLoginEventArgs e)
        {
            AddLogMessage($"用户登录事件: {e.User.DisplayNameOrUsername} 于 {e.LoginTime:HH:mm:ss} 登录");
            UpdateLoginStatus();
        }

        /// <summary>
        /// 用户登出事件
        /// </summary>
        private void OnUserLoggedOut(object? sender, UserLogoutEventArgs e)
        {
            AddLogMessage($"用户登出事件: {e.User.DisplayNameOrUsername} 于 {e.LogoutTime:HH:mm:ss} 登出");
            UpdateLoginStatus();
        }

        #endregion

        #region 工具方法

        /// <summary>
        /// 添加日志消息
        /// </summary>
        private void AddLogMessage(string message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            LogMessages.Add($"[{timestamp}] {message}");

            // 限制日志数量
            while (LogMessages.Count > 100)
            {
                LogMessages.RemoveAt(0);
            }
        }

        /// <summary>
        /// 清除日志
        /// </summary>
        private void ClearLog()
        {
            LogMessages.Clear();
            AddLogMessage("日志已清除");
        }

        #endregion

        #region 资源清理

        /// <summary>
        /// 释放资源
        /// </summary>
        protected override void OnDisposing()
        {
            try
            {
                // 取消事件订阅
                _jsonService.SerializationCompleted -= OnJsonSerializationCompleted;
                _jsonService.DeserializationCompleted -= OnJsonDeserializationCompleted;
                _jsonService.SerializationError -= OnJsonSerializationError;
                _styleService.StyleSetChanged -= OnStyleSetChanged;
                _userService.UserLoggedIn -= OnUserLoggedIn;
                _userService.UserLoggedOut -= OnUserLoggedOut;
            }
            catch (Exception ex)
            {
                _logger?.LogError("释放资源失败", ex);
            }

            base.OnDisposing();
        }

        #endregion
    }
}
