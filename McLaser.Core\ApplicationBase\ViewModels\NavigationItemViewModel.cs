#nullable enable
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows.Input;
using McLaser.Core.Common;
using McLaser.Core.Navigation;

namespace McLaser.Core.ApplicationBase
{
    /// <summary>
    /// 导航项ViewModel
    /// 表示单个导航按钮的视图模型
    /// </summary>
    public class NavigationItemViewModel : ViewModelBase
    {
        private readonly NavigationViewModel _parentViewModel;
        private bool _isSelected;
        private bool _isPopupOpen;
        private string _currentSubPageTitle = string.Empty;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="navigationItem">导航项模型</param>
        /// <param name="parentViewModel">父ViewModel</param>
        public NavigationItemViewModel(NavigationItem navigationItem, NavigationViewModel parentViewModel)
        {
            NavigationItem = navigationItem ?? throw new ArgumentNullException(nameof(navigationItem));
            _parentViewModel = parentViewModel ?? throw new ArgumentNullException(nameof(parentViewModel));

            InitializeCommands();
            InitializeSubPages();
        }

        #region 属性

        /// <summary>
        /// 导航项模型
        /// </summary>
        public NavigationItem NavigationItem { get; }

        /// <summary>
        /// 导航项ID
        /// </summary>
        public string Id => NavigationItem.Id;

        /// <summary>
        /// 标题
        /// </summary>
        public string Title => NavigationItem.Title;

        /// <summary>
        /// 图标
        /// </summary>
        public string Icon => NavigationItem.Icon;

        /// <summary>
        /// 是否为分类按钮
        /// </summary>
        public bool IsCategory => NavigationItem.IsCategory;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled => NavigationItem.IsEnabled;

        /// <summary>
        /// 工具提示
        /// </summary>
        public string ToolTip => NavigationItem.ToolTip;

        /// <summary>
        /// 是否选中
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set => SetProperty(ref _isSelected, value);
        }

        /// <summary>
        /// 弹出框是否打开
        /// </summary>
        public bool IsPopupOpen
        {
            get => _isPopupOpen;
            set => SetProperty(ref _isPopupOpen, value);
        }

        /// <summary>
        /// 当前选中的子页面标题
        /// </summary>
        public string CurrentSubPageTitle
        {
            get => _currentSubPageTitle;
            set => SetProperty(ref _currentSubPageTitle, value);
        }

        /// <summary>
        /// 子页面集合
        /// </summary>
        public ObservableCollection<PageInfo> SubPages { get; } = new ObservableCollection<PageInfo>();

        /// <summary>
        /// 命令
        /// </summary>
        public ICommand Command { get; private set; } = null!;

        /// <summary>
        /// 命令参数
        /// </summary>
        public object? CommandParameter => NavigationItem.CommandParameter;

        /// <summary>
        /// 导航到页面命令
        /// </summary>
        public ICommand NavigateToPageCommand => _parentViewModel.NavigateToPageCommand;

        #endregion

        #region 公共方法

        /// <summary>
        /// 更新选中状态
        /// </summary>
        /// <param name="currentPageId">当前页面ID</param>
        public void UpdateSelection(string? currentPageId)
        {
            if (IsCategory)
            {
                // 分类按钮：检查子页面是否有选中的
                var selectedSubPage = SubPages.FirstOrDefault(p => p.Id == currentPageId);
                IsSelected = selectedSubPage != null;

                // 更新当前子页面标题
                CurrentSubPageTitle = selectedSubPage?.Title ?? string.Empty;

                // 更新所有子页面的当前状态
                foreach (var subPage in SubPages)
                {
                    subPage.IsCurrentPage = subPage.Id == currentPageId;
                }
            }
            else
            {
                // 普通按钮：检查自己的页面是否选中
                IsSelected = NavigationItem.PageInfo?.Id == currentPageId;
                CurrentSubPageTitle = string.Empty;

                // 更新页面的当前状态
                if (NavigationItem.PageInfo != null)
                {
                    NavigationItem.PageInfo.IsCurrentPage = NavigationItem.PageInfo.Id == currentPageId;
                }
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化命令
        /// </summary>
        private void InitializeCommands()
        {
            if (IsCategory)
            {
                // 分类按钮：切换弹出框显示
                Command = new RelayCommand(ExecuteTogglePopup);
            }
            else
            {
                // 普通按钮：直接导航
                Command = new RelayCommand(ExecuteNavigate, CanExecuteNavigate);
            }
        }

        /// <summary>
        /// 初始化子页面
        /// </summary>
        private void InitializeSubPages()
        {
            if (IsCategory && NavigationItem.SubPages != null)
            {
                foreach (var page in NavigationItem.SubPages.OrderBy(p => p.Order))
                {
                    SubPages.Add(page);
                }
            }
        }

        /// <summary>
        /// 执行切换弹出框命令
        /// </summary>
        private void ExecuteTogglePopup()
        {
            IsPopupOpen = !IsPopupOpen;
        }

        /// <summary>
        /// 执行导航命令
        /// </summary>
        private void ExecuteNavigate()
        {
            if (NavigationItem.PageInfo != null)
            {
                _parentViewModel.NavigateToPageCommand.Execute(NavigationItem.PageInfo);
                // 导航后关闭当前弹出框（如果有的话）
                IsPopupOpen = false;
            }
        }

        /// <summary>
        /// 判断是否可以执行导航命令
        /// </summary>
        /// <returns>是否可以执行</returns>
        private bool CanExecuteNavigate()
        {
            return NavigationItem.PageInfo != null && IsEnabled;
        }

        #endregion
    }
}
