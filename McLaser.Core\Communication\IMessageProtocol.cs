using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace McLaser.Core.Communication
{
    /// <summary>
    /// 消息协议接口
    /// 定义了消息的编码、解码和处理规范
    /// </summary>
    public interface IMessageProtocol
    {
        /// <summary>
        /// 协议名称
        /// </summary>
        string Name { get; }

        /// <summary>
        /// 协议版本
        /// </summary>
        Version Version { get; }

        /// <summary>
        /// 协议描述
        /// </summary>
        string Description { get; }

        /// <summary>
        /// 最大消息长度
        /// </summary>
        int MaxMessageLength { get; }

        /// <summary>
        /// 消息头长度
        /// </summary>
        int HeaderLength { get; }

        /// <summary>
        /// 是否支持分片传输
        /// </summary>
        bool SupportsFragmentation { get; }

        /// <summary>
        /// 编码消息
        /// </summary>
        /// <param name="message">要编码的消息</param>
        /// <returns>编码后的字节数组</returns>
        byte[] EncodeMessage(IMessage message);

        /// <summary>
        /// 解码消息
        /// </summary>
        /// <param name="data">要解码的数据</param>
        /// <returns>解码后的消息</returns>
        IMessage DecodeMessage(byte[] data);

        /// <summary>
        /// 尝试解码消息
        /// </summary>
        /// <param name="data">要解码的数据</param>
        /// <param name="message">解码后的消息</param>
        /// <returns>是否成功解码</returns>
        bool TryDecodeMessage(byte[] data, out IMessage? message);

        /// <summary>
        /// 验证消息格式
        /// </summary>
        /// <param name="data">要验证的数据</param>
        /// <returns>验证结果</returns>
        MessageValidationResult ValidateMessage(byte[] data);

        /// <summary>
        /// 检查数据是否包含完整消息
        /// </summary>
        /// <param name="buffer">数据缓冲区</param>
        /// <param name="messageLength">消息长度</param>
        /// <returns>是否包含完整消息</returns>
        bool HasCompleteMessage(byte[] buffer, out int messageLength);

        /// <summary>
        /// 创建心跳消息
        /// </summary>
        /// <returns>心跳消息</returns>
        IMessage CreateHeartbeatMessage();

        /// <summary>
        /// 创建确认消息
        /// </summary>
        /// <param name="originalMessage">原始消息</param>
        /// <returns>确认消息</returns>
        IMessage CreateAcknowledgmentMessage(IMessage originalMessage);

        /// <summary>
        /// 创建错误消息
        /// </summary>
        /// <param name="errorCode">错误代码</param>
        /// <param name="errorMessage">错误消息</param>
        /// <returns>错误消息</returns>
        IMessage CreateErrorMessage(int errorCode, string errorMessage);
    }

    /// <summary>
    /// 异步消息协议接口
    /// </summary>
    public interface IAsyncMessageProtocol : IMessageProtocol
    {
        /// <summary>
        /// 异步编码消息
        /// </summary>
        /// <param name="message">要编码的消息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>编码任务</returns>
        Task<byte[]> EncodeMessageAsync(IMessage message, CancellationToken cancellationToken = default);

        /// <summary>
        /// 异步解码消息
        /// </summary>
        /// <param name="data">要解码的数据</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>解码任务</returns>
        Task<IMessage> DecodeMessageAsync(byte[] data, CancellationToken cancellationToken = default);

        /// <summary>
        /// 异步验证消息
        /// </summary>
        /// <param name="data">要验证的数据</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>验证任务</returns>
        Task<MessageValidationResult> ValidateMessageAsync(byte[] data, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// 支持压缩的消息协议接口
    /// </summary>
    public interface ICompressibleMessageProtocol : IMessageProtocol
    {
        /// <summary>
        /// 是否启用压缩
        /// </summary>
        bool CompressionEnabled { get; set; }

        /// <summary>
        /// 压缩算法
        /// </summary>
        CompressionAlgorithm CompressionAlgorithm { get; set; }

        /// <summary>
        /// 压缩级别
        /// </summary>
        CompressionLevel CompressionLevel { get; set; }

        /// <summary>
        /// 压缩阈值（字节）
        /// </summary>
        int CompressionThreshold { get; set; }

        /// <summary>
        /// 压缩数据
        /// </summary>
        /// <param name="data">原始数据</param>
        /// <returns>压缩后的数据</returns>
        byte[] Compress(byte[] data);

        /// <summary>
        /// 解压数据
        /// </summary>
        /// <param name="compressedData">压缩数据</param>
        /// <returns>解压后的数据</returns>
        byte[] Decompress(byte[] compressedData);
    }

    /// <summary>
    /// 支持加密的消息协议接口
    /// </summary>
    public interface IEncryptableMessageProtocol : IMessageProtocol
    {
        /// <summary>
        /// 是否启用加密
        /// </summary>
        bool EncryptionEnabled { get; set; }

        /// <summary>
        /// 加密算法
        /// </summary>
        EncryptionAlgorithm EncryptionAlgorithm { get; set; }

        /// <summary>
        /// 加密密钥
        /// </summary>
        byte[] EncryptionKey { get; set; }

        /// <summary>
        /// 初始化向量
        /// </summary>
        byte[] InitializationVector { get; set; }

        /// <summary>
        /// 加密数据
        /// </summary>
        /// <param name="data">原始数据</param>
        /// <returns>加密后的数据</returns>
        byte[] Encrypt(byte[] data);

        /// <summary>
        /// 解密数据
        /// </summary>
        /// <param name="encryptedData">加密数据</param>
        /// <returns>解密后的数据</returns>
        byte[] Decrypt(byte[] encryptedData);

        /// <summary>
        /// 生成密钥
        /// </summary>
        /// <returns>生成的密钥</returns>
        byte[] GenerateKey();

        /// <summary>
        /// 生成初始化向量
        /// </summary>
        /// <returns>生成的初始化向量</returns>
        byte[] GenerateIV();
    }

    /// <summary>
    /// 支持分片的消息协议接口
    /// </summary>
    public interface IFragmentableMessageProtocol : IMessageProtocol
    {
        /// <summary>
        /// 最大分片大小
        /// </summary>
        int MaxFragmentSize { get; set; }

        /// <summary>
        /// 分片消息
        /// </summary>
        /// <param name="message">要分片的消息</param>
        /// <returns>分片列表</returns>
        IList<MessageFragment> FragmentMessage(IMessage message);

        /// <summary>
        /// 重组消息
        /// </summary>
        /// <param name="fragments">分片列表</param>
        /// <returns>重组后的消息</returns>
        IMessage ReassembleMessage(IList<MessageFragment> fragments);

        /// <summary>
        /// 检查分片是否完整
        /// </summary>
        /// <param name="fragments">分片列表</param>
        /// <returns>是否完整</returns>
        bool IsFragmentationComplete(IList<MessageFragment> fragments);

        /// <summary>
        /// 获取缺失的分片索引
        /// </summary>
        /// <param name="fragments">分片列表</param>
        /// <param name="totalFragments">总分片数</param>
        /// <returns>缺失的分片索引</returns>
        IList<int> GetMissingFragmentIndices(IList<MessageFragment> fragments, int totalFragments);
    }

    /// <summary>
    /// 消息协议工厂接口
    /// </summary>
    public interface IMessageProtocolFactory
    {
        /// <summary>
        /// 创建协议实例
        /// </summary>
        /// <param name="protocolName">协议名称</param>
        /// <returns>协议实例</returns>
        IMessageProtocol CreateProtocol(string protocolName);

        /// <summary>
        /// 创建协议实例
        /// </summary>
        /// <typeparam name="T">协议类型</typeparam>
        /// <returns>协议实例</returns>
        T CreateProtocol<T>() where T : class, IMessageProtocol;

        /// <summary>
        /// 注册协议
        /// </summary>
        /// <param name="protocolName">协议名称</param>
        /// <param name="protocolType">协议类型</param>
        void RegisterProtocol(string protocolName, Type protocolType);

        /// <summary>
        /// 注册协议
        /// </summary>
        /// <typeparam name="T">协议类型</typeparam>
        /// <param name="protocolName">协议名称</param>
        void RegisterProtocol<T>(string protocolName) where T : class, IMessageProtocol;

        /// <summary>
        /// 获取已注册的协议列表
        /// </summary>
        /// <returns>协议名称列表</returns>
        IList<string> GetRegisteredProtocols();

        /// <summary>
        /// 检查协议是否已注册
        /// </summary>
        /// <param name="protocolName">协议名称</param>
        /// <returns>是否已注册</returns>
        bool IsProtocolRegistered(string protocolName);
    }
}
