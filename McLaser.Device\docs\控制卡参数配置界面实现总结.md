# 控制卡参数配置界面实现总结

## 1. 设计目标

开发一个通用的控制卡配置界面架构，具有以下特点：

- 提供基础的轴管理、IO管理等功能
- 支持不同控制卡类型扩展特定功能
- 符合MVVM设计模式
- 遵循架构分层原则，基类库不依赖具体实现

## 2. 架构设计

### 2.1 接口设计

为了实现基类库与具体实现的解耦，设计了`ICardConfigControl`接口：

```csharp
public interface ICardConfigControl
{
    void SetCard(CardBase card);
    UserControl GetControl();
    CardType GetSupportCardType();
    void UpdateAllAxisStatus();
}
```

### 2.2 基类实现

`CardConfigControlBase`类提供了基础UI和功能实现：

- 轴列表管理
- IO管理
- 状态监控与更新
- Tab页扩展机制

### 2.3 工厂模式

采用工厂模式创建配置控件实例，实现了以下功能：

- 根据控制卡类型动态创建对应配置控件
- 支持注册自定义控制卡配置控件创建器
- 提供默认实现确保系统稳定性

## 3. 关键类说明

### 3.1 ICardConfigControl

控制卡配置界面的基础接口，定义了所有配置控件必须实现的功能。

### 3.2 CardConfigControlBase

基础配置控件实现，提供了Tab页式界面布局，包含以下页面：

- 轴管理
- IO管理
- 参数设置

### 3.3 CardConfigViewModel

配置界面的视图模型，负责处理：

- 轴状态监控
- 命令绑定
- 属性通知

### 3.4 CardConfigControlFactory

配置控件工厂类，用于创建和管理不同类型控制卡的配置界面实例。

## 4. 扩展机制

### 4.1 如何扩展新的控制卡类型

1. 创建新的卡类型配置控件类，实现`ICardConfigControl`接口
2. 重写必要的方法，添加特定的功能
3. 在应用启动时，向工厂注册控制卡类型与配置控件的映射：

```csharp
// 在应用初始化时注册
CardConfigControlFactory.RegisterCreator(CardType.PMAC, () => new PmacCardConfigControl());
CardConfigControlFactory.RegisterCreator(CardType.GTS, () => new GTSCardConfigControl());
```

### 4.2 扩展点

控制卡配置控件提供了以下扩展点：

- 添加自定义Tab页
- 替换基础功能Tab页
- 扩展轴管理页面的功能
- 自定义视图模型

## 5. 使用示例

### 5.1 基础使用

```csharp
// 创建控制卡实例
var card = new CardBase();

// 使用工厂创建配置控件
var configControl = CardConfigControlFactory.CreateConfigControl(card);

// 显示配置界面
ConfigContainer.Content = configControl.GetControl();
configControl.SetCard(card);
```

### 5.2 测试窗口

提供了`CardConfigTestWindow`测试窗口，用于测试和展示控制卡配置界面功能。

## 6. 注意事项

1. 基类库不依赖具体实现，所有扩展实现应放在对应的设备类库中
2. 工厂类注册应在应用初始化时完成
3. 状态更新使用定时器机制，确保UI实时性
4. 配置界面遵循MVVM模式，确保UI和逻辑分离

## 7. 未来扩展

1. 支持多语言切换
2. 增加权限控制机制
3. 添加配置导入导出功能
4. 实现更丰富的状态可视化 