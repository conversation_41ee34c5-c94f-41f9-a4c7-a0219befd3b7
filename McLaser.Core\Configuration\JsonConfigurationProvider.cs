using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Newtonsoft.Json;
using System.Diagnostics;
using System.Linq;

namespace McLaser.Core.Configuration
{
    /// <summary>
    /// JSON配置提供者
    /// 支持从JSON文件读取和写入配置数据
    /// </summary>
    public class JsonConfigurationProvider : IConfigurationProvider
    {
        #region 私有字段

        private readonly string _filePath;
        private readonly bool _optional;
        private readonly bool _reloadOnChange;
        private Dictionary<string, object> _data;
        private FileSystemWatcher _fileWatcher;
        private DateTime _lastWriteTime;
        private readonly object _lock = new object();
        private readonly ConfigurationProviderStatistics _statistics;
        private bool _disposed;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="filePath">JSON文件路径</param>
        /// <param name="optional">文件是否可选</param>
        /// <param name="reloadOnChange">文件变更时是否自动重新加载</param>
        public JsonConfigurationProvider(string filePath, bool optional = false, bool reloadOnChange = true)
        {
            _filePath = filePath ?? throw new ArgumentNullException(nameof(filePath));
            _optional = optional;
            _reloadOnChange = reloadOnChange;
            _data = new Dictionary<string, object>();
            _statistics = new ConfigurationProviderStatistics();
        }

        #endregion

        #region 基本属性

        /// <summary>
        /// 提供者名称
        /// </summary>
        public string Name => $"JsonConfigurationProvider({Path.GetFileName(_filePath)})";

        /// <summary>
        /// 提供者描述
        /// </summary>
        public string Description => $"JSON配置提供者，文件路径: {_filePath}";

        /// <summary>
        /// 是否支持写入
        /// </summary>
        public bool SupportsWrite => true;

        /// <summary>
        /// 是否支持监控变更
        /// </summary>
        public bool SupportsWatch => _reloadOnChange;

        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized { get; private set; }

        /// <summary>
        /// 优先级（数值越大优先级越高）
        /// </summary>
        public int Priority { get; set; }

        #endregion

        #region 事件

        /// <summary>
        /// 配置变更事件
        /// </summary>
        public event EventHandler<ConfigurationChangedEventArgs> ConfigurationChanged;

        #endregion

        #region 初始化和生命周期

        /// <summary>
        /// 初始化配置提供者
        /// </summary>
        /// <param name="parameters">初始化参数</param>
        /// <returns>异步任务</returns>
        public async Task InitializeAsync(IDictionary<string, object> parameters = null)
        {
            if (IsInitialized)
                return;

            try
            {
                await LoadDataAsync();
                
                if (_reloadOnChange && File.Exists(_filePath))
                {
                    SetupFileWatcher();
                }

                IsInitialized = true;
                Debug.WriteLine($"JSON配置提供者初始化成功: {_filePath}");
            }
            catch (Exception ex)
            {
                _statistics.ErrorCount++;
                if (!_optional)
                {
                    throw new InvalidOperationException($"初始化JSON配置提供者失败: {ex.Message}", ex);
                }
                
                Debug.WriteLine($"JSON配置提供者初始化失败（可选文件）: {ex.Message}");
                IsInitialized = true; // 可选文件初始化失败仍然标记为已初始化
            }
        }

        /// <summary>
        /// 关闭配置提供者
        /// </summary>
        /// <returns>异步任务</returns>
        public async Task ShutdownAsync()
        {
            if (!IsInitialized)
                return;

            try
            {
                await SaveAsync();
                _fileWatcher?.Dispose();
                _fileWatcher = null;
                IsInitialized = false;
                
                Debug.WriteLine($"JSON配置提供者已关闭: {_filePath}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"关闭JSON配置提供者失败: {ex.Message}");
            }
        }

        #endregion

        #region 配置数据操作

        /// <summary>
        /// 加载所有配置数据
        /// </summary>
        /// <returns>配置数据字典</returns>
        public async Task<IDictionary<string, object>> LoadAsync()
        {
            await LoadDataAsync();
            return new Dictionary<string, object>(_data);
        }

        /// <summary>
        /// 获取配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>配置值</returns>
        public Task<object> GetValueAsync(string key)
        {
            if (string.IsNullOrEmpty(key))
                return Task.FromResult<object>(null);

            var stopwatch = Stopwatch.StartNew();
            try
            {
                lock (_lock)
                {
                    _statistics.ReadCount++;
                    _statistics.LastAccessTime = DateTime.UtcNow;
                    
                    return Task.FromResult(_data.TryGetValue(key, out var value) ? value : null);
                }
            }
            finally
            {
                stopwatch.Stop();
                UpdateAverageResponseTime(stopwatch.ElapsedMilliseconds);
            }
        }

        /// <summary>
        /// 设置配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        /// <returns>异步任务</returns>
        public Task SetValueAsync(string key, object value)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("配置键不能为空", nameof(key));

            lock (_lock)
            {
                var oldValue = _data.TryGetValue(key, out var existing) ? existing : null;
                _data[key] = value;
                _statistics.WriteCount++;
                _statistics.LastAccessTime = DateTime.UtcNow;

                // 触发配置变更事件
                var changeType = oldValue == null ? ConfigurationChangeType.Added : ConfigurationChangeType.Modified;
                OnConfigurationChanged(new ConfigurationChangedEventArgs(key, oldValue, value, changeType));
            }

            return Task.CompletedTask;
        }

        /// <summary>
        /// 删除配置项
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>是否删除成功</returns>
        public Task<bool> RemoveValueAsync(string key)
        {
            if (string.IsNullOrEmpty(key))
                return Task.FromResult(false);

            lock (_lock)
            {
                if (_data.TryGetValue(key, out var oldValue))
                {
                    _data.Remove(key);
                    _statistics.LastAccessTime = DateTime.UtcNow;

                    // 触发配置变更事件
                    OnConfigurationChanged(new ConfigurationChangedEventArgs(key, oldValue, null, ConfigurationChangeType.Removed));
                    return Task.FromResult(true);
                }

                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// 检查配置键是否存在
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>是否存在</returns>
        public Task<bool> ContainsKeyAsync(string key)
        {
            if (string.IsNullOrEmpty(key))
                return Task.FromResult(false);

            lock (_lock)
            {
                _statistics.ReadCount++;
                _statistics.LastAccessTime = DateTime.UtcNow;
                return Task.FromResult(_data.ContainsKey(key));
            }
        }

        /// <summary>
        /// 获取所有配置键
        /// </summary>
        /// <returns>配置键集合</returns>
        public Task<IEnumerable<string>> GetKeysAsync()
        {
            lock (_lock)
            {
                _statistics.ReadCount++;
                _statistics.LastAccessTime = DateTime.UtcNow;
                return Task.FromResult<IEnumerable<string>>(_data.Keys);
            }
        }

        #endregion

        #region 批量操作

        /// <summary>
        /// 批量设置配置值
        /// </summary>
        /// <param name="values">配置值字典</param>
        /// <returns>异步任务</returns>
        public Task SetValuesAsync(IDictionary<string, object> values)
        {
            if (values == null)
                return Task.CompletedTask;

            lock (_lock)
            {
                foreach (var kvp in values)
                {
                    var oldValue = _data.TryGetValue(kvp.Key, out var existing) ? existing : null;
                    _data[kvp.Key] = kvp.Value;

                    // 触发配置变更事件
                    var changeType = oldValue == null ? ConfigurationChangeType.Added : ConfigurationChangeType.Modified;
                    OnConfigurationChanged(new ConfigurationChangedEventArgs(kvp.Key, oldValue, kvp.Value, changeType));
                }

                _statistics.WriteCount += values.Count;
                _statistics.LastAccessTime = DateTime.UtcNow;
            }

            return Task.CompletedTask;
        }

        /// <summary>
        /// 批量删除配置项
        /// </summary>
        /// <param name="keys">配置键集合</param>
        /// <returns>删除成功的键集合</returns>
        public Task<IEnumerable<string>> RemoveValuesAsync(IEnumerable<string> keys)
        {
            if (keys == null)
                return Task.FromResult<IEnumerable<string>>(new List<string>());

            var removedKeys = new List<string>();

            lock (_lock)
            {
                foreach (var key in keys)
                {
                    if (_data.TryGetValue(key, out var oldValue))
                    {
                        _data.Remove(key);
                        removedKeys.Add(key);

                        // 触发配置变更事件
                        OnConfigurationChanged(new ConfigurationChangedEventArgs(key, oldValue, null, ConfigurationChangeType.Removed));
                    }
                }

                _statistics.LastAccessTime = DateTime.UtcNow;
            }

            return Task.FromResult<IEnumerable<string>>(removedKeys);
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 加载数据
        /// </summary>
        /// <returns>异步任务</returns>
        private async Task LoadDataAsync()
        {
            if (!File.Exists(_filePath))
            {
                if (!_optional)
                {
                    throw new FileNotFoundException($"配置文件不存在: {_filePath}");
                }
                return;
            }

            try
            {
                var json = File.ReadAllText(_filePath);
                var data = JsonConvert.DeserializeObject<Dictionary<string, object>>(json) ?? new Dictionary<string, object>();

                lock (_lock)
                {
                    _data = data;
                    _statistics.ItemCount = _data.Count;
                    _lastWriteTime = File.GetLastWriteTime(_filePath);
                }

                Debug.WriteLine($"成功加载JSON配置文件: {_filePath}，包含 {data.Count} 个配置项");
            }
            catch (Exception ex)
            {
                _statistics.ErrorCount++;
                throw new InvalidOperationException($"加载JSON配置文件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 设置文件监控
        /// </summary>
        private void SetupFileWatcher()
        {
            try
            {
                var directory = Path.GetDirectoryName(_filePath);
                var fileName = Path.GetFileName(_filePath);

                _fileWatcher = new FileSystemWatcher(directory, fileName)
                {
                    NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.Size,
                    EnableRaisingEvents = true
                };

                _fileWatcher.Changed += OnFileChanged;
                Debug.WriteLine($"已设置文件监控: {_filePath}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"设置文件监控失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 文件变更事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private async void OnFileChanged(object sender, FileSystemEventArgs e)
        {
            try
            {
                // 防止重复触发
                var currentWriteTime = File.GetLastWriteTime(_filePath);
                if (currentWriteTime <= _lastWriteTime)
                    return;

                // 延迟一下，确保文件写入完成
                await Task.Delay(100);

                await LoadDataAsync();

                // 触发重新加载事件
                OnConfigurationChanged(new ConfigurationChangedEventArgs(string.Empty, null, null, ConfigurationChangeType.Reloaded));
                
                Debug.WriteLine($"检测到配置文件变更，已重新加载: {_filePath}");
            }
            catch (Exception ex)
            {
                _statistics.ErrorCount++;
                Debug.WriteLine($"处理文件变更失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 触发配置变更事件
        /// </summary>
        /// <param name="args">事件参数</param>
        private void OnConfigurationChanged(ConfigurationChangedEventArgs args)
        {
            try
            {
                ConfigurationChanged?.Invoke(this, args);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"触发配置变更事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新平均响应时间
        /// </summary>
        /// <param name="elapsedMilliseconds">耗时毫秒数</param>
        private void UpdateAverageResponseTime(long elapsedMilliseconds)
        {
            lock (_lock)
            {
                if (_statistics.ReadCount == 1)
                {
                    _statistics.AverageResponseTime = elapsedMilliseconds;
                }
                else
                {
                    _statistics.AverageResponseTime = (_statistics.AverageResponseTime * (_statistics.ReadCount - 1) + elapsedMilliseconds) / _statistics.ReadCount;
                }
            }
        }

        #endregion

        #region 配置持久化

        /// <summary>
        /// 保存配置到持久化存储
        /// </summary>
        /// <returns>异步任务</returns>
        public async Task SaveAsync()
        {
            try
            {
                Dictionary<string, object> dataToSave;
                lock (_lock)
                {
                    dataToSave = new Dictionary<string, object>(_data);
                }

                var json = JsonConvert.SerializeObject(dataToSave, Formatting.Indented);

                // 确保目录存在
                var directory = Path.GetDirectoryName(_filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                File.WriteAllText(_filePath, json);
                _lastWriteTime = File.GetLastWriteTime(_filePath);

                Debug.WriteLine($"成功保存JSON配置文件: {_filePath}");
            }
            catch (Exception ex)
            {
                _statistics.ErrorCount++;
                throw new InvalidOperationException($"保存JSON配置文件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 重新加载配置
        /// </summary>
        /// <returns>异步任务</returns>
        public async Task ReloadAsync()
        {
            await LoadDataAsync();
            OnConfigurationChanged(new ConfigurationChangedEventArgs(string.Empty, null, null, ConfigurationChangeType.Reloaded));
        }

        #endregion

        #region 配置监控

        /// <summary>
        /// 开始监控配置变更
        /// </summary>
        public void StartWatching()
        {
            if (_fileWatcher != null && !_fileWatcher.EnableRaisingEvents)
            {
                _fileWatcher.EnableRaisingEvents = true;
                Debug.WriteLine($"开始监控配置文件: {_filePath}");
            }
        }

        /// <summary>
        /// 停止监控配置变更
        /// </summary>
        public void StopWatching()
        {
            if (_fileWatcher != null && _fileWatcher.EnableRaisingEvents)
            {
                _fileWatcher.EnableRaisingEvents = false;
                Debug.WriteLine($"停止监控配置文件: {_filePath}");
            }
        }

        /// <summary>
        /// 是否正在监控
        /// </summary>
        public bool IsWatching => _fileWatcher?.EnableRaisingEvents ?? false;

        #endregion

        #region 配置验证

        /// <summary>
        /// 验证配置数据
        /// </summary>
        /// <param name="data">配置数据</param>
        /// <returns>验证结果</returns>
        public Task<ConfigurationValidationResult> ValidateAsync(IDictionary<string, object> data)
        {
            var result = new ConfigurationValidationResult();

            if (data == null)
            {
                result.AddError("Data", "配置数据不能为空");
                return Task.FromResult(result);
            }

            // 验证JSON序列化是否正常
            try
            {
                JsonConvert.SerializeObject(data);
            }
            catch (Exception ex)
            {
                result.AddError("Serialization", $"配置数据无法序列化为JSON: {ex.Message}");
            }

            return Task.FromResult(result);
        }

        #endregion

        #region 配置备份和恢复

        /// <summary>
        /// 创建配置备份
        /// </summary>
        /// <param name="backupName">备份名称</param>
        /// <returns>备份标识</returns>
        public async Task<string> CreateBackupAsync(string backupName = null)
        {
            if (!File.Exists(_filePath))
                throw new FileNotFoundException($"配置文件不存在: {_filePath}");

            var backupId = Guid.NewGuid().ToString("N");
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var fileName = Path.GetFileNameWithoutExtension(_filePath);
            var extension = Path.GetExtension(_filePath);
            var directory = Path.GetDirectoryName(_filePath);

            var backupFileName = string.IsNullOrEmpty(backupName)
                ? $"{fileName}_backup_{timestamp}_{backupId}{extension}"
                : $"{fileName}_backup_{backupName}_{timestamp}_{backupId}{extension}";

            var backupPath = Path.Combine(directory, "backups", backupFileName);

            // 确保备份目录存在
            var backupDirectory = Path.GetDirectoryName(backupPath);
            if (!Directory.Exists(backupDirectory))
            {
                Directory.CreateDirectory(backupDirectory);
            }

            File.Copy(_filePath, backupPath);

            Debug.WriteLine($"创建配置备份: {backupPath}");
            return backupId;
        }

        /// <summary>
        /// 恢复配置备份
        /// </summary>
        /// <param name="backupId">备份标识</param>
        /// <returns>异步任务</returns>
        public async Task RestoreBackupAsync(string backupId)
        {
            if (string.IsNullOrEmpty(backupId))
                throw new ArgumentException("备份标识不能为空", nameof(backupId));

            var directory = Path.GetDirectoryName(_filePath);
            var backupDirectory = Path.Combine(directory, "backups");

            if (!Directory.Exists(backupDirectory))
                throw new DirectoryNotFoundException($"备份目录不存在: {backupDirectory}");

            var backupFiles = Directory.GetFiles(backupDirectory, $"*_{backupId}.*");
            if (backupFiles.Length == 0)
                throw new FileNotFoundException($"找不到备份文件: {backupId}");

            var backupPath = backupFiles[0];
            File.Copy(backupPath, _filePath, true);
            await ReloadAsync();

            Debug.WriteLine($"恢复配置备份: {backupPath}");
        }

        /// <summary>
        /// 获取所有备份
        /// </summary>
        /// <returns>备份信息集合</returns>
        public Task<IEnumerable<ConfigurationBackupInfo>> GetBackupsAsync()
        {
            var backups = new List<ConfigurationBackupInfo>();
            var directory = Path.GetDirectoryName(_filePath);
            var backupDirectory = Path.Combine(directory, "backups");

            if (!Directory.Exists(backupDirectory))
                return Task.FromResult<IEnumerable<ConfigurationBackupInfo>>(backups);

            var fileName = Path.GetFileNameWithoutExtension(_filePath);
            var backupFiles = Directory.GetFiles(backupDirectory, $"{fileName}_backup_*.*");

            foreach (var backupFile in backupFiles)
            {
                try
                {
                    var fileInfo = new FileInfo(backupFile);
                    var parts = Path.GetFileNameWithoutExtension(backupFile).Split('_');

                    if (parts.Length >= 4)
                    {
                        var backupInfo = new ConfigurationBackupInfo
                        {
                            Id = parts[parts.Length - 1],
                            Name = parts.Length > 4 ? parts[2] : "自动备份",
                            CreatedAt = fileInfo.CreationTime,
                            Size = fileInfo.Length,
                            Description = $"配置文件备份 - {fileInfo.CreationTime:yyyy-MM-dd HH:mm:ss}"
                        };

                        backups.Add(backupInfo);
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"解析备份文件信息失败: {ex.Message}");
                }
            }

            return Task.FromResult<IEnumerable<ConfigurationBackupInfo>>(backups.OrderByDescending(b => b.CreatedAt));
        }

        /// <summary>
        /// 删除配置备份
        /// </summary>
        /// <param name="backupId">备份标识</param>
        /// <returns>是否删除成功</returns>
        public Task<bool> DeleteBackupAsync(string backupId)
        {
            if (string.IsNullOrEmpty(backupId))
                return Task.FromResult(false);

            try
            {
                var directory = Path.GetDirectoryName(_filePath);
                var backupDirectory = Path.Combine(directory, "backups");

                if (!Directory.Exists(backupDirectory))
                    return Task.FromResult(false);

                var backupFiles = Directory.GetFiles(backupDirectory, $"*_{backupId}.*");
                if (backupFiles.Length == 0)
                    return Task.FromResult(false);

                File.Delete(backupFiles[0]);
                Debug.WriteLine($"删除配置备份: {backupFiles[0]}");
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"删除配置备份失败: {ex.Message}");
                return Task.FromResult(false);
            }
        }

        #endregion

        #region 配置统计

        /// <summary>
        /// 获取提供者统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public ConfigurationProviderStatistics GetStatistics()
        {
            lock (_lock)
            {
                _statistics.ItemCount = _data.Count;
                return _statistics;
            }
        }

        #endregion

        #region IDisposable实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否正在释放</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                try
                {
                    ShutdownAsync().Wait();
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"关闭JSON配置提供者失败: {ex.Message}");
                }

                _disposed = true;
            }
        }

        #endregion
    }

    /// <summary>
    /// 文件复制扩展方法
    /// </summary>
    internal static class FileExtensions
    {
        /// <summary>
        /// 异步复制文件
        /// </summary>
        /// <param name="sourceFile">源文件路径</param>
        /// <param name="destinationFile">目标文件路径</param>
        /// <param name="overwrite">是否覆盖</param>
        /// <returns>异步任务</returns>
        public static async Task CopyAsync(string sourceFile, string destinationFile, bool overwrite = false)
        {
            if (File.Exists(destinationFile) && !overwrite)
                throw new IOException($"目标文件已存在: {destinationFile}");

            using (var sourceStream = new FileStream(sourceFile, FileMode.Open, FileAccess.Read))
            using (var destinationStream = new FileStream(destinationFile, FileMode.Create, FileAccess.Write))
            {
                await sourceStream.CopyToAsync(destinationStream);
            }
        }
    }
}
