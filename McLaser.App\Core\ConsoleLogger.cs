using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using McLaser.Core.Framework.Logging;

namespace McLaser.App.Core
{
    /// <summary>
    /// 简单的控制台日志器
    /// 用作ILogger的后备实现
    /// </summary>
    public class ConsoleLogger : ILogger
    {
        private readonly string _categoryName;
        private LogLevel _level = LogLevel.Debug;

        /// <summary>
        /// 初始化控制台日志器
        /// </summary>
        /// <param name="categoryName">日志类别名称</param>
        public ConsoleLogger(string categoryName)
        {
            _categoryName = categoryName ?? throw new ArgumentNullException(nameof(categoryName));
        }

        /// <summary>
        /// 检查是否启用调试级别
        /// </summary>
        public bool IsDebugEnabled => _level <= LogLevel.Debug;

        /// <summary>
        /// 检查是否启用信息级别
        /// </summary>
        public bool IsInfoEnabled => _level <= LogLevel.Info;

        /// <summary>
        /// 检查是否启用警告级别
        /// </summary>
        public bool IsWarnEnabled => _level <= LogLevel.Warn;

        /// <summary>
        /// 检查是否启用错误级别
        /// </summary>
        public bool IsErrorEnabled => _level <= LogLevel.Error;

        /// <summary>
        /// 检查是否启用致命错误级别
        /// </summary>
        public bool IsFatalEnabled => _level <= LogLevel.Fatal;

        #region 基本日志记录

        /// <summary>
        /// 记录调试信息
        /// </summary>
        /// <param name="message">消息</param>
        public void Debug(string message)
        {
            if (IsDebugEnabled)
                WriteLog(LogLevel.Debug, message);
        }

        /// <summary>
        /// 记录调试信息
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="args">格式化参数</param>
        public void Debug(string message, params object[] args)
        {
            if (IsDebugEnabled)
                WriteLog(LogLevel.Debug, message, args);
        }

        /// <summary>
        /// 记录信息
        /// </summary>
        /// <param name="message">消息</param>
        public void Info(string message)
        {
            if (IsInfoEnabled)
                WriteLog(LogLevel.Info, message);
        }

        /// <summary>
        /// 记录信息
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="args">格式化参数</param>
        public void Info(string message, params object[] args)
        {
            if (IsInfoEnabled)
                WriteLog(LogLevel.Info, message, args);
        }

        /// <summary>
        /// 记录警告
        /// </summary>
        /// <param name="message">消息</param>
        public void Warn(string message)
        {
            if (IsWarnEnabled)
                WriteLog(LogLevel.Warn, message);
        }

        /// <summary>
        /// 记录警告
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="args">格式化参数</param>
        public void Warn(string message, params object[] args)
        {
            if (IsWarnEnabled)
                WriteLog(LogLevel.Warn, message, args);
        }

        /// <summary>
        /// 记录错误
        /// </summary>
        /// <param name="message">消息</param>
        public void Error(string message)
        {
            if (IsErrorEnabled)
                WriteLog(LogLevel.Error, message);
        }

        /// <summary>
        /// 记录错误
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="args">格式化参数</param>
        public void Error(string message, params object[] args)
        {
            if (IsErrorEnabled)
                WriteLog(LogLevel.Error, message, args);
        }

        /// <summary>
        /// 记录错误和异常
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="message">消息</param>
        public void Error(Exception exception, string message = "")
        {
            if (IsErrorEnabled)
                WriteLog(LogLevel.Error, exception, message);
        }

        /// <summary>
        /// 记录致命错误
        /// </summary>
        /// <param name="message">消息</param>
        public void Fatal(string message)
        {
            if (IsFatalEnabled)
                WriteLog(LogLevel.Fatal, message);
        }

        /// <summary>
        /// 记录致命错误
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="args">格式化参数</param>
        public void Fatal(string message, params object[] args)
        {
            if (IsFatalEnabled)
                WriteLog(LogLevel.Fatal, message, args);
        }

        /// <summary>
        /// 记录致命错误和异常
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="message">消息</param>
        public void Fatal(Exception exception, string message = "")
        {
            if (IsFatalEnabled)
                WriteLog(LogLevel.Fatal, exception, message);
        }

        #endregion



        #region 私有方法

        /// <summary>
        /// 写入日志到控制台
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        /// <param name="args">格式化参数</param>
        private void WriteLog(LogLevel level, string message, params object[] args)
        {
            WriteLog(level, null, message, args);
        }

        /// <summary>
        /// 写入日志到控制台
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="exception">异常对象</param>
        /// <param name="message">日志消息</param>
        /// <param name="args">格式化参数</param>
        private void WriteLog(LogLevel level, Exception? exception, string message, params object[]? args)
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var formattedMessage = args != null && args.Length > 0 ? string.Format(message, args) : message;

            var logMessage = $"[{timestamp}] [{level.ToString().ToUpper()}] [{_categoryName}] {formattedMessage}";

            if (exception != null)
            {
                logMessage += $"\n异常: {exception}";
            }

            // 根据日志级别设置控制台颜色
            var originalColor = Console.ForegroundColor;
            try
            {
                Console.ForegroundColor = level switch
                {
                    LogLevel.Debug => ConsoleColor.Gray,
                    LogLevel.Info => ConsoleColor.White,
                    LogLevel.Warn => ConsoleColor.Yellow,
                    LogLevel.Error => ConsoleColor.Red,
                    LogLevel.Fatal => ConsoleColor.DarkRed,
                    _ => ConsoleColor.White
                };

                Console.WriteLine(logMessage);

                // 同时输出到调试窗口
                System.Diagnostics.Debug.WriteLine(logMessage);
            }
            finally
            {
                Console.ForegroundColor = originalColor;
            }
        }

        #endregion
    }
}
