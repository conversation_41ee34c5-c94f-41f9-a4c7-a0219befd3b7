# PropertyGridView使用指南

## 概述

PropertyGridView是一个用于显示和编辑对象属性的控件，类似于Windows Forms中的PropertyGrid。它支持按类别分组显示属性，并根据属性类型提供不同的编辑器。

## 常见问题解决方案

### PropertyGridView不显示问题解决

如果PropertyGridView控件没有正常显示，可能有以下几个原因：

1. **确保正确初始化PropertyGridView**

   ```csharp
   // 创建PropertyGridView实例
   var propertyGrid = new PropertyGridView();
   
   // 设置到容器中
   PropertyGridContainer.Content = propertyGrid;
   
   // 确保容器可见
   PropertyGridContainer.Visibility = Visibility.Visible;
   ```

2. **确保设置了SelectedObject属性**

   ```csharp
   // 设置要显示的对象
   propertyGrid.SelectedObject = myObject;
   ```

3. **确保对象有可显示的属性**

   属性需要添加`PropertyCategory`和`PropertyDisplayName`特性才能显示：

   ```csharp
   public class MyClass
   {
       [PropertyCategory("基本信息")]
       [PropertyDisplayName("名称")]
       public string Name { get; set; }
   }
   ```

4. **检查BoolToVisibilityConverter是否正确注册**

   在XAML中确保转换器已正确注册：

   ```xml
   <UserControl.Resources>
       <local:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
   </UserControl.Resources>
   ```

5. **使用测试工具验证控件功能**

   可以使用提供的测试工具来验证PropertyGridView的功能：

   ```csharp
   // 启动PropertyGridView测试窗口
   PropertyGridTestLauncher.Launch();
   ```

## 使用方法

### 1. 在XAML中添加PropertyGridView

```xml
<local:PropertyGridView x:Name="myPropertyGrid" />
```

### 2. 在代码中设置要显示的对象

```csharp
myPropertyGrid.SelectedObject = myObject;
```

### 3. 在对象类中添加属性特性

```csharp
public class MyClass
{
    [PropertyCategory("基本信息")]
    [PropertyDisplayName("名称")]
    public string Name { get; set; }
    
    [PropertyCategory("参数")]
    [PropertyDisplayName("值")]
    public double Value { get; set; }
    
    [PropertyCategory("设置")]
    [PropertyDisplayName("启用")]
    public bool IsEnabled { get; set; }
}
```

### 4. 支持的属性类型

PropertyGridView支持以下属性类型：

- 字符串 (String)
- 数值 (int, double, float等)
- 布尔值 (bool)
- 枚举 (enum)

## 在CardView中的使用

CardView中已经集成了PropertyGridView，用于显示轴参数和卡参数：

```csharp
// 设置轴参数
if (_viewModel.SelectedAxis != null && _axisPropertyGrid != null)
{
    _axisPropertyGrid.SelectedObject = _viewModel.SelectedAxis;
}

// 设置卡参数
if (_cardPropertyGrid != null)
{
    _cardPropertyGrid.SelectedObject = card;
}
```

## 测试工具

项目中提供了以下测试工具：

1. **PropertyGridTestWindow**: 用于测试PropertyGridView的独立窗口
2. **PropertyGridTestLauncher**: 用于启动测试窗口的工具类
3. **TestCommands**: 提供测试命令，可以在应用程序中使用

使用示例：

```csharp
// 测试PropertyGridView
PropertyGridTestLauncher.Launch();

// 或者使用命令
TestCommands.TestPropertyGridCommand.Execute(null);

// 测试CardView
TestCommands.TestCardViewCommand.Execute(null);
``` 