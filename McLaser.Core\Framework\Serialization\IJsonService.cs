using System;
using System.Threading.Tasks;

namespace McLaser.Core.Framework.Serialization
{
    /// <summary>
    /// JSON序列化服务接口
    /// 提供统一的JSON处理功能，包括序列化、反序列化、文件操作等
    /// </summary>
    public interface IJsonService
    {
        #region 序列化配置

        /// <summary>
        /// 是否格式化输出
        /// </summary>
        bool FormatOutput { get; set; }

        /// <summary>
        /// 是否包含空值
        /// </summary>
        bool IncludeNullValues { get; set; }

        /// <summary>
        /// 设置序列化选项
        /// </summary>
        /// <param name="formatOutput">是否格式化输出</param>
        /// <param name="includeNullValues">是否包含空值</param>
        void ConfigureSettings(bool formatOutput = true, bool includeNullValues = true);

        /// <summary>
        /// 重置为默认配置
        /// </summary>
        void ResetToDefaultSettings();

        #endregion

        #region 基础序列化

        /// <summary>
        /// 将对象序列化为JSON字符串
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要序列化的对象</param>
        /// <returns>JSON字符串</returns>
        string Serialize<T>(T obj);

        /// <summary>
        /// 将对象序列化为格式化的JSON字符串
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要序列化的对象</param>
        /// <returns>格式化的JSON字符串</returns>
        string SerializeFormatted<T>(T obj);

        /// <summary>
        /// 将JSON字符串反序列化为对象
        /// </summary>
        /// <typeparam name="T">目标对象类型</typeparam>
        /// <param name="json">JSON字符串</param>
        /// <returns>反序列化的对象</returns>
        T? Deserialize<T>(string json);

        /// <summary>
        /// 将JSON字符串反序列化为指定类型的对象
        /// </summary>
        /// <param name="json">JSON字符串</param>
        /// <param name="type">目标类型</param>
        /// <returns>反序列化的对象</returns>
        object? Deserialize(string json, Type type);

        #endregion

        #region 文件操作

        /// <summary>
        /// 将对象序列化并保存到文件
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要序列化的对象</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="formatted">是否格式化输出</param>
        void SerializeToFile<T>(T obj, string filePath, bool formatted = true);

        /// <summary>
        /// 从文件读取并反序列化为对象
        /// </summary>
        /// <typeparam name="T">目标对象类型</typeparam>
        /// <param name="filePath">文件路径</param>
        /// <returns>反序列化的对象</returns>
        T? DeserializeFromFile<T>(string filePath);

        /// <summary>
        /// 异步将对象序列化并保存到文件
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要序列化的对象</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="formatted">是否格式化输出</param>
        /// <returns>异步任务</returns>
        Task SerializeToFileAsync<T>(T obj, string filePath, bool formatted = true);

        /// <summary>
        /// 异步从文件读取并反序列化为对象
        /// </summary>
        /// <typeparam name="T">目标对象类型</typeparam>
        /// <param name="filePath">文件路径</param>
        /// <returns>反序列化的对象</returns>
        Task<T?> DeserializeFromFileAsync<T>(string filePath);

        #endregion

        #region 验证和工具

        /// <summary>
        /// 验证JSON字符串是否有效
        /// </summary>
        /// <param name="json">JSON字符串</param>
        /// <returns>是否有效</returns>
        bool IsValidJson(string json);

        /// <summary>
        /// 格式化JSON字符串
        /// </summary>
        /// <param name="json">原始JSON字符串</param>
        /// <returns>格式化后的JSON字符串</returns>
        string FormatJson(string json);

        /// <summary>
        /// 压缩JSON字符串（移除空白字符）
        /// </summary>
        /// <param name="json">原始JSON字符串</param>
        /// <returns>压缩后的JSON字符串</returns>
        string CompactJson(string json);

        /// <summary>
        /// 获取JSON字符串的大小（字节数）
        /// </summary>
        /// <param name="json">JSON字符串</param>
        /// <returns>字节数</returns>
        long GetJsonSize(string json);

        #endregion

        #region 事件

        /// <summary>
        /// 序列化完成事件
        /// </summary>
        event EventHandler<JsonSerializationEventArgs>? SerializationCompleted;

        /// <summary>
        /// 反序列化完成事件
        /// </summary>
        event EventHandler<JsonDeserializationEventArgs>? DeserializationCompleted;

        /// <summary>
        /// 序列化错误事件
        /// </summary>
        event EventHandler<JsonErrorEventArgs>? SerializationError;

        #endregion
    }

    #region 事件参数类

    /// <summary>
    /// JSON序列化事件参数
    /// </summary>
    public class JsonSerializationEventArgs : EventArgs
    {
        /// <summary>
        /// 源对象类型
        /// </summary>
        public Type SourceType { get; }

        /// <summary>
        /// 生成的JSON字符串
        /// </summary>
        public string Json { get; }

        /// <summary>
        /// 序列化耗时（毫秒）
        /// </summary>
        public long ElapsedMilliseconds { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public JsonSerializationEventArgs(Type sourceType, string json, long elapsedMilliseconds)
        {
            SourceType = sourceType;
            Json = json;
            ElapsedMilliseconds = elapsedMilliseconds;
        }
    }

    /// <summary>
    /// JSON反序列化事件参数
    /// </summary>
    public class JsonDeserializationEventArgs : EventArgs
    {
        /// <summary>
        /// 目标对象类型
        /// </summary>
        public Type TargetType { get; }

        /// <summary>
        /// 源JSON字符串
        /// </summary>
        public string Json { get; }

        /// <summary>
        /// 反序列化结果
        /// </summary>
        public object? Result { get; }

        /// <summary>
        /// 反序列化耗时（毫秒）
        /// </summary>
        public long ElapsedMilliseconds { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public JsonDeserializationEventArgs(Type targetType, string json, object? result, long elapsedMilliseconds)
        {
            TargetType = targetType;
            Json = json;
            Result = result;
            ElapsedMilliseconds = elapsedMilliseconds;
        }
    }

    /// <summary>
    /// JSON错误事件参数
    /// </summary>
    public class JsonErrorEventArgs : EventArgs
    {
        /// <summary>
        /// 操作类型
        /// </summary>
        public string Operation { get; }

        /// <summary>
        /// 错误异常
        /// </summary>
        public Exception Exception { get; }

        /// <summary>
        /// 相关数据
        /// </summary>
        public object? Data { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public JsonErrorEventArgs(string operation, Exception exception, object? data = null)
        {
            Operation = operation;
            Exception = exception;
            Data = data;
        }
    }

    #endregion
}
