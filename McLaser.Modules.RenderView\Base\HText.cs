﻿namespace McLaser.Modules.RenderView.Base
{
    public class HText : HRoi
    {
        //新增数据

        /// <summary>
        /// 文字
        /// </summary>
        public string Text { get; set; }

        /// <summary>
        /// 字体
        /// </summary>
        public string Font = "宋体";

        /// <summary>
        /// 显示的位置-X
        /// </summary>
        public int Row { get; set; } = 0;

        /// <summary>
        /// 显示的位置-Y
        /// </summary>
        public int Col { get; set; } = 0;

        /// <summary>
        /// 大小
        /// </summary>
        public int Size { get; set; } = 18;

        /// <summary>
        /// 是否显示边框
        /// </summary>
        public bool IsShowBox { get; set; } = true;

        /// <summary>
        /// 角度
        /// </summary>
        public int Phi { get; set; }

        public HText()
        {

        }

        public HText(string text, int row, int col, string color = "red", bool isShowBox = true, int size = 18, string font = "宋体")
        {
            Text = text;
            Row = row;
            Col = col;
            DrawColor = color;
            Size = size;
            Font = font;
            IsShowBox = isShowBox;
        }
    }
}
