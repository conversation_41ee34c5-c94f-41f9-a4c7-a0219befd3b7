<UserControl x:Class="McLaser.Core.ApplicationBase.CategoryPopup"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d" 
             d:DesignHeight="300" d:DesignWidth="200">
    
    <UserControl.Resources>
        <!-- 弹出框样式 -->
        <Style x:Key="PopupBorderStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource {x:Static SystemColors.WindowBrushKey}}"/>
            <Setter Property="BorderBrush" Value="{DynamicResource {x:Static SystemColors.ActiveBorderBrushKey}}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" 
                                    Direction="270" 
                                    ShadowDepth="4" 
                                    BlurRadius="8" 
                                    Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 页面按钮样式 -->
        <Style x:Key="PageButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="Margin" Value="0,1"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background"
                                        Value="{DynamicResource {x:Static SystemColors.ControlLightBrushKey}}"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background"
                                        Value="{DynamicResource {x:Static SystemColors.ControlDarkBrushKey}}"/>
                            </Trigger>
                            <!-- 当前页面高亮显示 -->
                            <DataTrigger Binding="{Binding IsCurrentPage}" Value="True">
                                <Setter TargetName="border" Property="Background"
                                        Value="{DynamicResource {x:Static SystemColors.HighlightBrushKey}}"/>
                                <Setter Property="Foreground"
                                        Value="{DynamicResource {x:Static SystemColors.HighlightTextBrushKey}}"/>
                                <Setter TargetName="border" Property="BorderBrush"
                                        Value="{DynamicResource {x:Static SystemColors.HighlightBrushKey}}"/>
                                <Setter TargetName="border" Property="BorderThickness" Value="2"/>
                            </DataTrigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 页面项数据模板 -->
        <DataTemplate x:Key="PageItemTemplate">
            <Button Style="{StaticResource PageButtonStyle}"
                    Command="{Binding DataContext.NavigateToPageCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                    CommandParameter="{Binding}">
                <StackPanel Orientation="Horizontal">
                    <!-- 图标 -->
                    <TextBlock Text="{Binding Icon}" 
                               FontFamily="Segoe MDL2 Assets"
                               FontSize="16"
                               VerticalAlignment="Center"
                               Margin="0,0,8,0"/>
                    
                    <!-- 内容 -->
                    <StackPanel>
                        <TextBlock Text="{Binding Title}" 
                                   FontWeight="Medium"
                                   FontSize="13"/>
                        <TextBlock Text="{Binding Description}"
                                   FontSize="11"
                                   Foreground="{DynamicResource {x:Static SystemColors.GrayTextBrushKey}}"
                                   TextWrapping="Wrap">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Visibility" Value="Visible"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding Description}" Value="">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Description}" Value="{x:Null}">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </StackPanel>
                </StackPanel>
            </Button>
        </DataTemplate>
    </UserControl.Resources>

    <Border Style="{StaticResource PopupBorderStyle}">
        <StackPanel>
            <!-- 标题 -->
            <TextBlock Text="{Binding Title}" 
                       FontWeight="Bold"
                       FontSize="14"
                       Margin="0,0,0,8"
                       HorizontalAlignment="Center"/>
            
            <!-- 分隔线 -->
            <Separator Margin="0,0,0,8"/>
            
            <!-- 页面列表 -->
            <ItemsControl ItemsSource="{Binding SubPages}"
                          ItemTemplate="{StaticResource PageItemTemplate}">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <StackPanel Orientation="Vertical"/>
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
            </ItemsControl>
        </StackPanel>
    </Border>
</UserControl>
