using System;
using System.ComponentModel;
using McLaser.Device;

namespace McLaser.Devices.Motion
{
  
    [Serializable]
    public class LimitPmac : LimitBase
    {
        #region 私有字段

        private bool _isUse = true;
        private double _posMax = 10000.0;
        private double _posMin = -10000.0;
        private bool _enableSoftLimit = true;
        private bool _enableHardLimit = true;
        private bool _posLimitReverse = false;
        private bool _negLimitReverse = false;
        private LimitAction _limitAction = LimitAction.Stop;
        private double _limitDeceleration = 1000.0;

        #endregion

        #region 属性

        /// <summary>
        /// 是否启用限位
        /// </summary>
        [Category("PMAC限位基本"), DisplayName("启用限位")]
        public override bool IsUse
        {
            get => _isUse;
            set
            {
                if (_isUse != value)
                {
                    _isUse = value;
                    OnPropertyChanged();
                }
            }
        }

      
 

        /// <summary>
        /// 启用软限位
        /// </summary>
        [Category("PMAC限位高级"), DisplayName("启用软限位")]
        public bool EnableSoftLimit
        {
            get => _enableSoftLimit;
            set
            {
                if (_enableSoftLimit != value)
                {
                    _enableSoftLimit = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 启用硬限位
        /// </summary>
        [Category("PMAC限位高级"), DisplayName("启用硬限位")]
        public bool EnableHardLimit
        {
            get => _enableHardLimit;
            set
            {
                if (_enableHardLimit != value)
                {
                    _enableHardLimit = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 正向限位信号反向
        /// </summary>
        [Category("PMAC限位高级"), DisplayName("正向限位信号反向")]
        public bool PosLimitReverse
        {
            get => _posLimitReverse;
            set
            {
                if (_posLimitReverse != value)
                {
                    _posLimitReverse = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 负向限位信号反向
        /// </summary>
        [Category("PMAC限位高级"), DisplayName("负向限位信号反向")]
        public bool NegLimitReverse
        {
            get => _negLimitReverse;
            set
            {
                if (_negLimitReverse != value)
                {
                    _negLimitReverse = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 限位动作
        /// </summary>
        [Category("PMAC限位高级"), DisplayName("限位动作")]
        public LimitAction LimitAction
        {
            get => _limitAction;
            set
            {
                if (_limitAction != value)
                {
                    _limitAction = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 限位减速度(毫米/秒²)
        /// </summary>
        [Category("PMAC限位高级"), DisplayName("限位减速度(mm/s²)")]
        public double LimitDeceleration
        {
            get => _limitDeceleration;
            set
            {
                if (Math.Abs(_limitDeceleration - value) > 0.001)
                {
                    _limitDeceleration = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public LimitPmac()
        {
            // 初始化默认值
            InitializeDefaults();
        }

        #endregion

        #region 方法

        /// <summary>
        /// 初始化默认值
        /// </summary>
        private void InitializeDefaults()
        {
            IsUse = true;
            PosMax = 10000.0;      // 正向限位 10000mm
            PosMin = -10000.0;     // 负向限位 -10000mm
            EnableSoftLimit = true;
            EnableHardLimit = true;
            PosLimitReverse = false;
            NegLimitReverse = false;
            LimitAction = LimitAction.Stop;
            LimitDeceleration = 1000.0; // 限位减速度 1000mm/s²
        }

        /// <summary>
        /// 验证限位参数
        /// </summary>
        /// <returns>验证结果</returns>
        public bool ValidateParameters()
        {
            try
            {
                // 检查限位范围
                if (PosMax <= PosMin)
                {
                    System.Diagnostics.Debug.WriteLine("正向限位必须大于负向限位");
                    return false;
                }

                // 检查限位范围是否合理
                double range = PosMax - PosMin;
                if (range <= 0)
                {
                    System.Diagnostics.Debug.WriteLine("限位范围必须大于0");
                    return false;
                }

                // 检查减速度
                if (LimitDeceleration <= 0)
                {
                    System.Diagnostics.Debug.WriteLine("限位减速度必须大于0");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"验证PMAC限位参数异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查位置是否在限位范围内
        /// </summary>
        /// <param name="position">位置值</param>
        /// <returns>是否在范围内</returns>
        public bool IsPositionInRange(double position)
        {
            if (!IsUse) return true;
            
            return position >= PosMin && position <= PosMax;
        }

        /// <summary>
        /// 检查位置是否超出正向限位
        /// </summary>
        /// <param name="position">位置值</param>
        /// <returns>是否超出正向限位</returns>
        public bool IsPositiveLimitExceeded(double position)
        {
            if (!IsUse || !EnableSoftLimit) return false;
            
            return position > PosMax;
        }

        /// <summary>
        /// 检查位置是否超出负向限位
        /// </summary>
        /// <param name="position">位置值</param>
        /// <returns>是否超出负向限位</returns>
        public bool IsNegativeLimitExceeded(double position)
        {
            if (!IsUse || !EnableSoftLimit) return false;
            
            return position < PosMin;
        }

        /// <summary>
        /// 获取到限位的距离
        /// </summary>
        /// <param name="position">当前位置</param>
        /// <param name="direction">运动方向 (true为正向, false为负向)</param>
        /// <returns>到限位的距离</returns>
        public double GetDistanceToLimit(double position, bool direction)
        {
            if (!IsUse) return double.MaxValue;
            
            if (direction)
            {
                // 正向运动，检查正向限位
                return Math.Max(0, PosMax - position);
            }
            else
            {
                // 负向运动，检查负向限位
                return Math.Max(0, position - PosMin);
            }
        }

        /// <summary>
        /// 获取限位配置摘要
        /// </summary>
        /// <returns>配置摘要字符串</returns>
        public string GetConfigurationSummary()
        {
            if (!IsUse)
                return "PMAC限位 - 已禁用";
            
            return $"PMAC限位 - 范围:[{PosMin:F1}, {PosMax:F1}]mm - 软限位:{(EnableSoftLimit ? "启用" : "禁用")} - 硬限位:{(EnableHardLimit ? "启用" : "禁用")}";
        }

        /// <summary>
        /// 克隆限位配置
        /// </summary>
        /// <returns>克隆的限位对象</returns>
        public LimitPmac Clone()
        {
            return new LimitPmac
            {
                IsUse = this.IsUse,
                PosMax = this.PosMax,
                PosMin = this.PosMin,
                EnableSoftLimit = this.EnableSoftLimit,
                EnableHardLimit = this.EnableHardLimit,
                PosLimitReverse = this.PosLimitReverse,
                NegLimitReverse = this.NegLimitReverse,
                LimitAction = this.LimitAction,
                LimitDeceleration = this.LimitDeceleration
            };
        }

        #endregion
    }

    /// <summary>
    /// 限位动作枚举
    /// </summary>
    public enum LimitAction
    {
        /// <summary>
        /// 停止
        /// </summary>
        Stop,
        
        /// <summary>
        /// 减速停止
        /// </summary>
        DecelerateStop,
        
        /// <summary>
        /// 急停
        /// </summary>
        EmergencyStop,
        
        /// <summary>
        /// 忽略
        /// </summary>
        Ignore
    }
}
