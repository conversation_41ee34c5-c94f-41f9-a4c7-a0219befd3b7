﻿using McLaser.Devices;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows;
using McLaser.Core.Common;
using System.Timers;
using McLaser.Core.Container;
using HalconDotNet;
using McLaser.Modules.Vision;

namespace McLaser.Device
{
    public class CameraViewModel : ObservableObject
    {


        #region Prop
        [NonSerialized]
        public Timer Timer_ContinuousAcq;


        private IRenderView renderView;

        // 相机列表
        public ObservableCollection<CameraBase> CameraModels { get; set; } = new ObservableCollection<CameraBase>();

        private int _selectedIndex;
        public int SelectedIndex
        {
            get { return _selectedIndex; }
            set { Set(ref _selectedIndex, value); }
        }

        private CameraBase _selectedCamera;
        public CameraBase SelectedCamera
        {
            get { return _selectedCamera; }
            set { Set(ref _selectedCamera, value); }
        }

        private ObservableCollection<CameraInfo> _cameraNos = new ObservableCollection<CameraInfo>();
        public ObservableCollection<CameraInfo> CameraNos
        {
            get { return _cameraNos; }
            set { Set(ref _cameraNos, value); }
        }

        private int _cameraNoIndex = -1;
        public int CameraNoIndex
        {
            get { return _cameraNoIndex; }
            set { Set(ref _cameraNoIndex, value); }
        }

        private FrameworkElement _DesignControl;
        public FrameworkElement DesignControl => renderView as FrameworkElement;


        public ICommand CamOpenCommand => new RelayCommand(OnCamOpen);
        public ICommand CamCloseCommand => new RelayCommand(OnCamClose);
        public ICommand CamGrabOnceCommand => new RelayCommand(OnCamGrabOnce);
        public ICommand CamGrabContinueCommand => new RelayCommand(OnCamGrabContinue);
        public ICommand ComboBoxOpenedCommand => new RelayCommand(OnComboBoxOpened);

        #endregion

        private void OnCamOpen()
        {
            if (SelectedCamera == null) return;
            SelectedCamera.Open();
        }

        private void OnCamClose()
        {
            if (SelectedCamera == null) return;
            SelectedCamera.Close();
        }

        private void OnCamGrabOnce()
        {
            if (SelectedCamera == null) return;
            try
            {
                var hImage = new HImage();
                SelectedCamera.GrabImageData(out ImageData imageData);
                if (imageData.PixelDataPtr != null && imageData.PixelDataPtr != (IntPtr)0)
                {
                    // 获取图像
                    hImage = new HImage("byte", (HTuple)imageData.Width, (HTuple)imageData.Height, imageData.PixelDataPtr);
   
                }
                if (hImage != null && hImage.IsInitialized())
                {
                    renderView.DispImage(hImage);
                    renderView.Repaint();
                }
                hImage?.Dispose();
                hImage = null;

            }
            catch (Exception ex)
            {
               
            }
        }

        private void OnCamGrabContinue()
        {
            SelectedCamera.GrabContinue();
        }

        private void OnComboBoxOpened()
        {
            Task.Run(() =>
            {
                CameraNos = new ObservableCollection<CameraInfo>(SelectedCamera.SearchCameras());
            });
        }

        #region Command


        #endregion

        #region Method

        public CameraViewModel(CameraBase cam, IRenderView viewer)
        {
            //Timer_ContinuousAcq = new Timer();
            //Timer_ContinuousAcq.Interval = 100;
            //Timer_ContinuousAcq.Elapsed += ContinuousAcqMethod;
            renderView = viewer;

            //DesignControl = new WindowsFormsHost() { Child = (System.Windows.Forms.Control)renderView };
            SelectedCamera = cam;

            if (CameraNos.Count == 0)
            {
                Task.Run(() =>
                {
                    CameraNos = new ObservableCollection<CameraInfo>(SelectedCamera.SearchCameras());
                    if (CameraNos.Count <= 0) return;
                    CameraNoIndex = CameraNos.Count - 1;
                    var cameraInfo = CameraNos[CameraNoIndex];
                    if (cameraInfo != null)
                    {
                        SelectedCamera.CameraInfo = cameraInfo;
                    }
                });
            }
        }

        public Window GetView()
        {
            return null;
        }

        public ObservableCollection<CameraBase> GetCameras()
        {
            return CameraModels;
        }

        private void ContinuousAcqMethod(object sender, EventArgs e)
        {
            //try
            //{
            //    if (SelectedCamera == null) return;
            //    if (!SelectedCamera.IsConnected) return;
            //    VisionLib.MV_ImageGrab(SelectedCamera, out HImage hImage);
            //    if (hImage != null && hImage.IsInitialized())
            //    {
            //        renderView.DispImage(hImage);
            //        renderView.Repaint();
            //    }
            //}
            //catch (Exception ex)
            //{
            //    Logger.AddLog(ex.Message, MsgType.Error);
            //}

        }

        [OnDeserialized()]
        internal void OnDeserializedMethod(StreamingContext context)
        {
            Timer_ContinuousAcq = new Timer();
            Timer_ContinuousAcq.Interval = 100;
            Timer_ContinuousAcq.Elapsed += ContinuousAcqMethod;
            if (renderView == null)
            {
                renderView = IoC.Get<IRenderViewManager>().GenRenderView();
            }
            //DesignControl = renderView as FrameworkElement;
        }
        #endregion
    }
}
