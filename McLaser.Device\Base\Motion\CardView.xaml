<UserControl
    x:Class="McLaser.Devices.CardView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:devices="clr-namespace:McLaser.Devices"
    xmlns:local="clr-namespace:McLaser.Devices"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Background="#F0F2F5"
    d:DesignHeight="700"
    d:DesignWidth="1000">

    <UserControl.Resources>
        <!-- 颜色资源 -->
        <SolidColorBrush x:Key="PrimaryColor" Color="#1976D2" />
        <SolidColorBrush x:Key="SecondaryColor" Color="#2196F3" />
        <SolidColorBrush x:Key="AccentColor" Color="#FF4081" />
        <SolidColorBrush x:Key="BackgroundColor" Color="#F0F2F5" />
        <SolidColorBrush x:Key="SurfaceColor" Color="#FFFFFF" />
        <SolidColorBrush x:Key="TextPrimaryColor" Color="#212121" />
        <SolidColorBrush x:Key="TextSecondaryColor" Color="#757575" />
        <SolidColorBrush x:Key="BorderColor" Color="#E0E0E0" />
        <SolidColorBrush x:Key="SuccessColor" Color="#4CAF50" />
        <SolidColorBrush x:Key="WarningColor" Color="#FFC107" />
        <SolidColorBrush x:Key="ErrorColor" Color="#F44336" />
        <SolidColorBrush x:Key="DisabledColor" Color="#BDBDBD" />

        <!-- 转换器 -->
        <local:NullToVisibilityConverter x:Key="NullToVisibilityConverter" />
        <local:BoolToForegroundConverter x:Key="BoolToForegroundConverter" />
        <local:BoolToColorConverter x:Key="BoolToColorConverter" />
        <local:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter" />

        <!-- 通用样式 -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource SurfaceColor}" />
            <Setter Property="BorderBrush" Value="{StaticResource BorderColor}" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="CornerRadius" Value="4" />
            <Setter Property="Padding" Value="16" />
            <Setter Property="Margin" Value="8" />
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect
                        Color="#000000"
                        BlurRadius="10"
                        Direction="270"
                        Opacity="0.2"
                        ShadowDepth="1" />
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16" />
            <Setter Property="FontWeight" Value="SemiBold" />
            <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}" />
            <Setter Property="Margin" Value="0,0,0,8" />
        </Style>

        <Style x:Key="SubHeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14" />
            <Setter Property="FontWeight" Value="Medium" />
            <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}" />
            <Setter Property="Margin" Value="0,0,0,4" />
        </Style>

        <Style x:Key="BodyTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12" />
            <Setter Property="Foreground" Value="{StaticResource TextSecondaryColor}" />
            <Setter Property="TextWrapping" Value="Wrap" />
        </Style>

        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryColor}" />
            <Setter Property="Foreground" Value="White" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Padding" Value="12,6" />
            <Setter Property="Margin" Value="4" />
            <Setter Property="MinWidth" Value="80" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4">
                            <ContentPresenter
                                Margin="{TemplateBinding Padding}"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource SecondaryColor}" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryColor}" />
                                <Setter Property="Opacity" Value="0.8" />
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="{StaticResource DisabledColor}" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style
            x:Key="SecondaryButtonStyle"
            BasedOn="{StaticResource ButtonStyle}"
            TargetType="Button">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="Foreground" Value="{StaticResource PrimaryColor}" />
            <Setter Property="BorderBrush" Value="{StaticResource PrimaryColor}" />
            <Setter Property="BorderThickness" Value="1" />
        </Style>

        <Style x:Key="TabControlStyle" TargetType="TabControl">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Padding" Value="0" />
        </Style>

        <Style x:Key="TabItemStyle" TargetType="TabItem">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TabItem">
                        <Border
                            Margin="0,0,4,0"
                            BorderBrush="Transparent"
                            BorderThickness="0,0,0,2"
                            Name="Border"
                            Padding="12,8">
                            <ContentPresenter
                                x:Name="ContentSite"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                ContentSource="Header" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource PrimaryColor}" />
                                <Setter Property="Foreground" Value="{StaticResource PrimaryColor}" />
                                <Setter Property="FontWeight" Value="SemiBold" />
                            </Trigger>
                            <Trigger Property="IsSelected" Value="False">
                                <Setter Property="Foreground" Value="{StaticResource TextSecondaryColor}" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 状态指示器样式 -->
        <Style x:Key="StatusIndicatorStyle" TargetType="Ellipse">
            <Setter Property="Width" Value="14" />
            <Setter Property="Height" Value="14" />
            <Setter Property="Stroke" Value="#AAA" />
            <Setter Property="StrokeThickness" Value="1" />
            <Setter Property="Margin" Value="2" />
        </Style>

        <!-- 轴状态模板 -->
        <DataTemplate x:Key="AxisStatusTemplate">
            <Border
                x:Name="AxisItemBorder"
                Margin="0,4"
                Background="{StaticResource SurfaceColor}"
                BorderBrush="{Binding IsSelected, Converter={StaticResource BoolToColorConverter}, ConverterParameter=Blue:LightGray}"
                BorderThickness="{Binding IsSelected, Converter={StaticResource BoolToColorConverter}, ConverterParameter=2:1}"
                CornerRadius="4"
                Padding="12,8">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <!-- 轴名称和位置 -->
                    <StackPanel
                        Grid.Row="0"
                        Grid.Column="0"
                        Orientation="Horizontal">
                        <TextBlock
                            VerticalAlignment="Center"
                            FontSize="14"
                            FontWeight="SemiBold"
                            Foreground="{StaticResource TextPrimaryColor}"
                            Text="{Binding Axis.Name}" />
                        <TextBlock
                            VerticalAlignment="Center"
                            Foreground="{StaticResource TextSecondaryColor}"
                            Text=" - " />
                        <TextBlock
                            VerticalAlignment="Center"
                            Foreground="{StaticResource TextSecondaryColor}"
                            Text="{Binding Position, StringFormat='{}{0:F3} mm'}" />
                    </StackPanel>

                    <!-- 状态指示器行 -->
                    <WrapPanel
                        Grid.Row="1"
                        Grid.Column="0"
                        Margin="0,6,0,0"
                        Orientation="Horizontal">
                        <!-- 使能状态 -->
                        <Border
                            Margin="0,0,4,0"
                            Background="#F5F5F5"
                            BorderBrush="{Binding IsEnabled, Converter={StaticResource BoolToColorConverter}, ConverterParameter=Green:Gray}"
                            BorderThickness="1"
                            CornerRadius="2"
                            Padding="4,2">
                            <StackPanel Orientation="Horizontal">
                                <Ellipse
                                    Width="8"
                                    Height="8"
                                    Margin="0,0,4,0">
                                    <Ellipse.Fill>
                                        <SolidColorBrush Color="{Binding IsEnabled, Converter={StaticResource BoolToColorConverter}, ConverterParameter=Green:Gray}" />
                                    </Ellipse.Fill>
                                </Ellipse>
                                <TextBlock FontSize="11" Text="使能" />
                            </StackPanel>
                        </Border>

                        <!-- 回零状态 -->
                        <Border
                            Margin="0,0,4,0"
                            Background="#F5F5F5"
                            BorderBrush="{Binding IsHomed, Converter={StaticResource BoolToColorConverter}, ConverterParameter=Green:Gray}"
                            BorderThickness="1"
                            CornerRadius="2"
                            Padding="4,2">
                            <StackPanel Orientation="Horizontal">
                                <Ellipse
                                    Width="8"
                                    Height="8"
                                    Margin="0,0,4,0">
                                    <Ellipse.Fill>
                                        <SolidColorBrush Color="{Binding IsHomed, Converter={StaticResource BoolToColorConverter}, ConverterParameter=Green:Gray}" />
                                    </Ellipse.Fill>
                                </Ellipse>
                                <TextBlock FontSize="11" Text="已回零" />
                            </StackPanel>
                        </Border>

                        <!-- 运动中状态 -->
                        <Border
                            Margin="0,0,4,0"
                            Background="#F5F5F5"
                            BorderBrush="{Binding IsMoving, Converter={StaticResource BoolToColorConverter}, ConverterParameter=Blue:Gray}"
                            BorderThickness="1"
                            CornerRadius="2"
                            Padding="4,2"
                            Visibility="{Binding IsMoving, Converter={StaticResource BoolToVisibilityConverter}}">
                            <StackPanel Orientation="Horizontal">
                                <Ellipse
                                    Width="8"
                                    Height="8"
                                    Margin="0,0,4,0">
                                    <Ellipse.Fill>
                                        <SolidColorBrush Color="{Binding IsMoving, Converter={StaticResource BoolToColorConverter}, ConverterParameter=Blue:Gray}" />
                                    </Ellipse.Fill>
                                </Ellipse>
                                <TextBlock FontSize="11" Text="运动中" />
                            </StackPanel>
                        </Border>

                        <!-- 限位状态 -->
                        <Border
                            Margin="0,0,4,0"
                            Background="#F5F5F5"
                            BorderBrush="#FFC107"
                            BorderThickness="1"
                            CornerRadius="2"
                            Padding="4,2"
                            Visibility="{Binding IsPlusLimit, Converter={StaticResource BoolToVisibilityConverter}}">
                            <StackPanel Orientation="Horizontal">
                                <Ellipse
                                    Width="8"
                                    Height="8"
                                    Margin="0,0,4,0">
                                    <Ellipse.Fill>
                                        <SolidColorBrush Color="#FFC107" />
                                    </Ellipse.Fill>
                                </Ellipse>
                                <TextBlock FontSize="11" Text="正限位" />
                            </StackPanel>
                        </Border>

                        <Border
                            Margin="0,0,4,0"
                            Background="#F5F5F5"
                            BorderBrush="#FFC107"
                            BorderThickness="1"
                            CornerRadius="2"
                            Padding="4,2"
                            Visibility="{Binding IsMinusLimit, Converter={StaticResource BoolToVisibilityConverter}}">
                            <StackPanel Orientation="Horizontal">
                                <Ellipse
                                    Width="8"
                                    Height="8"
                                    Margin="0,0,4,0">
                                    <Ellipse.Fill>
                                        <SolidColorBrush Color="#FFC107" />
                                    </Ellipse.Fill>
                                </Ellipse>
                                <TextBlock FontSize="11" Text="负限位" />
                            </StackPanel>
                        </Border>

                        <!-- 报警状态 -->
                        <Border
                            Margin="0,0,4,0"
                            Background="#F5F5F5"
                            BorderBrush="#F44336"
                            BorderThickness="1"
                            CornerRadius="2"
                            Padding="4,2"
                            Visibility="{Binding IsError, Converter={StaticResource BoolToVisibilityConverter}}">
                            <StackPanel Orientation="Horizontal">
                                <Ellipse
                                    Width="8"
                                    Height="8"
                                    Margin="0,0,4,0">
                                    <Ellipse.Fill>
                                        <SolidColorBrush Color="#F44336" />
                                    </Ellipse.Fill>
                                </Ellipse>
                                <TextBlock FontSize="11" Text="报警" />
                            </StackPanel>
                        </Border>
                    </WrapPanel>

                    <!-- 快速操作按钮 -->
                    <StackPanel
                        Grid.Row="0"
                        Grid.RowSpan="2"
                        Grid.Column="1"
                        VerticalAlignment="Center"
                        Orientation="Horizontal">
                        <Button
                            MinWidth="60"
                            Margin="2,0"
                            Command="{Binding DataContext.EnableAxisCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                            Content="使能"
                            Padding="8,3"
                            Style="{StaticResource SecondaryButtonStyle}" />
                        <Button
                            MinWidth="60"
                            Margin="2,0"
                            Command="{Binding DataContext.HomeAxisCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                            Content="回零"
                            Padding="8,3"
                            Style="{StaticResource SecondaryButtonStyle}" />
                        <Button
                            MinWidth="60"
                            Margin="2,0"
                            Command="{Binding DataContext.StopAxisCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                            Content="停止"
                            Padding="8,3"
                            Style="{StaticResource SecondaryButtonStyle}" />
                    </StackPanel>
                </Grid>
            </Border>
        </DataTemplate>

        <!-- IO模板 -->
        <DataTemplate x:Key="StatusColumnTemplate">
            <StackPanel Orientation="Horizontal" Background="{Binding StatusColor}">
                <TextBlock Text="{Binding StatusDisplay}" Margin="5" VerticalAlignment="Center" />
            </StackPanel>
        </DataTemplate>

        <DataTemplate x:Key="MotifyColumnTemplate">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition/>
                    <ColumnDefinition/>
                </Grid.ColumnDefinitions>

                <Button Grid.Column="0" Content="真" Click="btnTrue_Click" CommandParameter="{Binding ElementName=dgIOOutput}"/>
                <Button Grid.Column="1" Content="假" Click="btnFalse_Click" CommandParameter="{Binding ElementName=dgIOOutput}"/>
            </Grid>

        </DataTemplate>
        
        
    </UserControl.Resources>

    <Grid>
        <TabControl x:Name="MainTabControl" Style="{StaticResource TabControlStyle}">
            <!-- 轴管理页面 -->
            <TabItem Header="轴管理" Style="{StaticResource TabItemStyle}">
                <Grid Margin="8">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <!-- 轴管理卡片 -->
                    <Border Grid.Row="0" Style="{StaticResource CardStyle}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <TextBlock Style="{StaticResource HeaderTextStyle}" Text="轴管理" />

                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <Button
                                    Command="{Binding AddAxisCommand}"
                                    Style="{StaticResource ButtonStyle}"
                                    ToolTip="添加新的轴">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock
                                            Margin="0,0,4,0"
                                            FontFamily="Segoe MDL2 Assets"
                                            Text="&#xE710;" />
                                        <TextBlock Text="添加轴" />
                                    </StackPanel>
                                </Button>
                                <Button
                                    Command="{Binding UpdateAllAxisCommand}"
                                    Style="{StaticResource SecondaryButtonStyle}"
                                    ToolTip="刷新所有轴状态">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock
                                            Margin="0,0,4,0"
                                            FontFamily="Segoe MDL2 Assets"
                                            Text="&#xE72C;" />
                                        <TextBlock Text="刷新状态" />
                                    </StackPanel>
                                </Button>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- 轴列表与控制区域 -->
                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                            <ColumnDefinition Width="1*" />
                        </Grid.ColumnDefinitions>

                        <!-- 轴列表 -->
                        <Border Grid.Column="0" Style="{StaticResource CardStyle}">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="*" />
                                </Grid.RowDefinitions>

                                <TextBlock Style="{StaticResource SubHeaderTextStyle}" Text="轴状态" />

                                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                                    <ItemsControl
                                        x:Name="AxisItemsControl"
                                        Margin="0,8,0,0"
                                        ItemsSource="{Binding AxisStatusCollection}"
                                        ItemTemplate="{StaticResource AxisStatusTemplate}">
                                        <ItemsControl.ItemsPanel>
                                            <ItemsPanelTemplate>
                                                <StackPanel />
                                            </ItemsPanelTemplate>
                                        </ItemsControl.ItemsPanel>
                                        <ItemsControl.ItemContainerStyle>
                                            <Style TargetType="ContentPresenter">
                                                <EventSetter Event="MouseLeftButtonDown" Handler="AxisItem_MouseLeftButtonDown" />
                                                <Setter Property="Cursor" Value="Hand" />
                                            </Style>
                                        </ItemsControl.ItemContainerStyle>
                                    </ItemsControl>
                                </ScrollViewer>
                            </Grid>
                        </Border>


                        <!-- 轴控制面板 -->
                        <Border Grid.Column="1" Style="{StaticResource CardStyle}">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>

                                <TextBlock Style="{StaticResource SubHeaderTextStyle}" Text="轴控制" />

                                <!-- 基本控制 -->
                                <UniformGrid
                                    Grid.Row="1"
                                    Margin="0,8,0,0"
                                    Columns="3"
                                    Rows="2">
                                    <Button
                                        Margin="4"
                                        Command="{Binding EnableAxisCommand}"
                                        Content="使能"
                                        Style="{StaticResource ButtonStyle}" />
                                    <Button
                                        Margin="4"
                                        Command="{Binding DisableAxisCommand}"
                                        Content="禁用"
                                        Style="{StaticResource ButtonStyle}" />
                                    <Button
                                        Margin="4"
                                        Command="{Binding HomeAxisCommand}"
                                        Content="回零"
                                        Style="{StaticResource ButtonStyle}" />
                                    <Button
                                        Margin="4"
                                        Command="{Binding StopAxisCommand}"
                                        Content="停止"
                                        Style="{StaticResource ButtonStyle}" />
                                    <Button
                                        Margin="4"
                                        Command="{Binding ClearAxisCommand}"
                                        Content="清除报警"
                                        Style="{StaticResource ButtonStyle}" />
                                    <Button
                                        Margin="4"
                                        Command="{Binding SetZeroCommand}"
                                        Content="设零点"
                                        Style="{StaticResource ButtonStyle}" />
                                </UniformGrid>

                                <!-- 点动控制 -->
                                <Grid Grid.Row="2" Margin="0,16,0,0">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>

                                    <TextBlock Style="{StaticResource BodyTextStyle}" Text="点动控制" />

                                    <Grid Grid.Row="1" Margin="0,8,0,0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*" />
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>

                                        <Button
                                            Grid.Column="0"
                                            Command="{Binding MoveNegativeCommand}"
                                            Content="← 负向"
                                            Style="{StaticResource ButtonStyle}" />

                                        <TextBlock
                                            Grid.Column="1"
                                            Margin="12,0"
                                            VerticalAlignment="Center"
                                            Text="点动" />

                                        <Button
                                            Grid.Column="2"
                                            Command="{Binding MovePositiveCommand}"
                                            Content="正向 →"
                                            Style="{StaticResource ButtonStyle}" />
                                    </Grid>
                                </Grid>
                            </Grid>
                        </Border>
                        <!-- 轴详细控制面板 -->
                        <Grid Grid.Column="2">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <!-- 轴参数编辑 -->
                            <Border Grid.Row="0" Style="{StaticResource CardStyle}">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="*" />
                                    </Grid.RowDefinitions>

                                    <TextBlock Style="{StaticResource SubHeaderTextStyle}" Text="轴参数" />

                                    <!-- PropertyGrid将在这里添加 -->
                                    <Border
                                        Grid.Row="1"
                                        Margin="0,8,0,0"
                                        BorderBrush="{StaticResource BorderColor}"
                                        BorderThickness="1">
                                        <WindowsFormsHost x:Name="propertyGrid" />
                                    </Border>
                                </Grid>
                            </Border>


                        </Grid>
                    </Grid>


                </Grid>
            </TabItem>

            <!-- IO管理页面 -->
            <TabItem Header="IO管理" Style="{StaticResource TabItemStyle}">
                <Grid Margin="8">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <!-- IO管理卡片 -->
                    <Border Grid.Row="0" Style="{StaticResource CardStyle}">
                        <TextBlock Style="{StaticResource HeaderTextStyle}" Text="IO管理" />
                    </Border>

                    <!-- IO列表与控制区域 -->
                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <!-- 输入IO列表 -->
                        <Border Grid.Column="0" Style="{StaticResource CardStyle}">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="*" />
                                </Grid.RowDefinitions>

                                <TextBlock Style="{StaticResource SubHeaderTextStyle}" Text="数字输入" />


                                <DataGrid
                                    x:Name="dgIOInput"
                                    Grid.Row="1"
                                    Margin="10"
                                    HorizontalAlignment="Stretch"
                                    VerticalAlignment="Stretch"
                                    AutoGenerateColumns="False">
                                    <DataGrid.Columns>
                                        <DataGridTextColumn
                                            Width="2*"
                                            Binding="{Binding Name}"
                                            Header="输入" />
                                        <DataGridTemplateColumn
                                            Width="*"
                                            CellTemplate="{StaticResource StatusColumnTemplate}"
                                            Header="数值" />
                                    </DataGrid.Columns>
                                </DataGrid>
                            </Grid>
                        </Border>

                        <!-- 输出IO列表 -->
                        <Border Grid.Column="1" Style="{StaticResource CardStyle}">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="*" />
                                </Grid.RowDefinitions>

                                <TextBlock Style="{StaticResource SubHeaderTextStyle}" Text="数字输出" />
                                <DataGrid
                                    x:Name="dgIOOutput"
                                    Grid.Row="1"
                                    Margin="10"
                                    HorizontalAlignment="Stretch"
                                    VerticalAlignment="Stretch"
                                    AutoGenerateColumns="False">
                                    <DataGrid.Columns>
                                        <DataGridTextColumn
                                            Width="2*"
                                            Binding="{Binding Name}"
                                            Header="输出" />
                                        <DataGridTemplateColumn
                                            Width="2*"
                                            CellTemplate="{StaticResource StatusColumnTemplate}"
                                            Header="数值" />
                                        <DataGridTemplateColumn
                                            Width="2*"
                                            CellTemplate="{StaticResource MotifyColumnTemplate}"
                                            Header="修改" />
                                    </DataGrid.Columns>
                                </DataGrid>
                            </Grid>
                        </Border>
                    </Grid>
                </Grid>
            </TabItem>

            <!-- 卡参数配置页面 -->
            <TabItem Header="卡参数" Style="{StaticResource TabItemStyle}">
                <Border Margin="8" Style="{StaticResource CardStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>

                        <TextBlock Style="{StaticResource HeaderTextStyle}" Text="控制卡参数配置" />

                        <!-- 卡参数PropertyGrid将在这里添加 -->
                        <Border
                            Grid.Row="1"
                            Margin="0,8,0,0"
                            BorderBrush="{StaticResource BorderColor}"
                            BorderThickness="1">
                            <ContentControl x:Name="CardPropertyGridContainer" />
                        </Border>
                    </Grid>
                </Border>
            </TabItem>

            <!-- 扩展内容占位符 -->
            <TabItem Header="扩展功能" Style="{StaticResource TabItemStyle}">
                <ContentControl x:Name="AxisExtensionContent" Margin="8" />
            </TabItem>
        </TabControl>
    </Grid>
</UserControl>