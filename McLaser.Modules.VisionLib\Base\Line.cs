﻿using McLaser.Core.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace McLaser.Modules.Vision
{
    [Serializable]
    public class Line : ObservableObject
    {
        public bool Status;
        // 起点行坐标
        public double _StartY;
        public double StartY
        {
            get { return _StartY; }
            set { Set(ref _StartY, value); }
        }
        // 起点列坐标
        public double _StartX;
        public double StartX
        {
            get { return _StartX; }
            set { Set(ref _StartX, value); }
        }
        // 终点行坐标
        public double _EndY;
        public double EndY
        {
            get { return _EndY; }
            set { Set(ref _EndY, value); }
        }
        // 终点列坐标
        public double _EndX;
        public double EndX
        {
            get { return _EndX; }
            set { Set(ref _EndX, value); }
        }
        // 中点行坐标
        public double _MidX;
        public double MidX
        {
            get { return _MidX; }
            set { Set(ref _MidX, value); }
        }
        // 中点列坐标
        public double _MidY;
        public double MidY
        {
            get { return _MidY; }
            set { Set(ref _MidY, value); }
        }
        // 直线角度
        public double _Phi;
        public double Phi
        {
            get { return _Phi; }
            set { Set(ref _Phi, value); }
        }
        // 直线长度
        public double Dist;
        // 行向量
        public double Nx;
        // 列向量
        public double Ny;
        // X点集合
        public double[] X;
        // Y点集合
        public double[] Y;
        public Line()
        {
            Status = true;
        }

        public Line(bool status, double startY, double startX, double endY, double endX)
        {
            Status = status;
            StartY = startY;
            StartX = startX;
            EndY = endY;
            EndX = endX;
        }
    }
}
