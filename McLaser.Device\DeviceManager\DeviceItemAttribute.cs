﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace McLaser.Device
{
    [AttributeUsage(AttributeTargets.Class, AllowMultiple = false)]
    public class DeviceItemAttribute : Attribute
    {
        public string? DisplayName { get; set; }

        public string? Category { get; set; }

        public string? IconSource { get; set; }

        public string? Description { get; set; }

        public DeviceItemAttribute( string category, string dispName, string description , string iconSource)
        {
            Category = category;
            DisplayName = dispName;
            Description = description;
            IconSource = iconSource;
        }
    }


    public class DeviceItem
    {
        public string? DisplayName { get; set; }
        public string? Category { get; set; }
        public string? IconSource { get; set; }
        public string? Description { get; set; }

        public Type ItemType { get; set; }
    }
}
