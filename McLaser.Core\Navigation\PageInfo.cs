#nullable enable
using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;

namespace McLaser.Core.Navigation
{
    /// <summary>
    /// 页面信息模型
    /// 包含页面的基本信息和创建方法
    /// </summary>
    public class PageInfo : INotifyPropertyChanged
    {
        private bool _isCurrentPage;
        /// <summary>
        /// 页面唯一标识
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 页面标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 页面描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 页面图标
        /// </summary>
        public string Icon { get; set; } = string.Empty;

        /// <summary>
        /// 页面类型
        /// </summary>
        public Type? PageType { get; set; }

        /// <summary>
        /// ViewModel类型
        /// </summary>
        public Type? ViewModelType { get; set; }

        /// <summary>
        /// 页面创建工厂方法
        /// </summary>
        public Func<FrameworkElement>? PageFactory { get; set; }

        /// <summary>
        /// ViewModel创建工厂方法
        /// </summary>
        public Func<object>? ViewModelFactory { get; set; }

        /// <summary>
        /// 是否为单例页面（只创建一次）
        /// </summary>
        public bool IsSingleton { get; set; } = true;

        /// <summary>
        /// 页面分组
        /// </summary>
        public string Group { get; set; } = string.Empty;

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// 是否需要权限验证
        /// </summary>
        public bool RequireAuth { get; set; }

        /// <summary>
        /// 所需权限
        /// </summary>
        public string RequiredPermission { get; set; } = string.Empty;

        /// <summary>
        /// 是否为当前页面
        /// </summary>
        public bool IsCurrentPage
        {
            get => _isCurrentPage;
            set => SetProperty(ref _isCurrentPage, value);
        }

        #region INotifyPropertyChanged

        /// <summary>
        /// 属性变更事件
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// 设置属性值并触发变更通知
        /// </summary>
        /// <typeparam name="T">属性类型</typeparam>
        /// <param name="field">属性字段</param>
        /// <param name="value">新值</param>
        /// <param name="propertyName">属性名称</param>
        /// <returns>是否发生了变更</returns>
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// 触发属性变更事件
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
