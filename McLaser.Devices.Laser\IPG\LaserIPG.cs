using System;
using System.ComponentModel;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Xml.Serialization;
using McLaser.Device;

namespace McLaser.Devices.Laser.IPG
{
    /// <summary>
    /// IPG激光器驱动实现
    /// 支持IPG光纤激光器的控制和监控功能
    /// </summary>
    [Category("激光器")]
    [DisplayName("IPG激光器")]
    [Serializable]
    public class LaserIPG : LaserBase
    {
        #region 私有字段

        /// <summary>
        /// TCP客户端
        /// </summary>
        private TcpClient tcpClient;

        /// <summary>
        /// 网络流
        /// </summary>
        private NetworkStream networkStream;

        /// <summary>
        /// 状态监控任务取消令牌
        /// </summary>
        private CancellationTokenSource statusCancellationToken;

        /// <summary>
        /// 线程锁对象
        /// </summary>
        private static readonly object objLock = new object();

        #endregion

        #region 属性

        /// <summary>
        /// IP地址
        /// </summary>
        [Category("IPG连接"), DisplayName("IP地址")]
        public string IPAddress { get; set; } = "*************";

        /// <summary>
        /// 端口号
        /// </summary>
        [Category("IPG连接"), DisplayName("端口")]
        public int Port { get; set; } = 10001;

        /// <summary>
        /// 连接超时时间(毫秒)
        /// </summary>
        [Category("IPG连接"), DisplayName("连接超时(ms)")]
        public int ConnectionTimeout { get; set; } = 5000;

        /// <summary>
        /// 激光器类型
        /// </summary>
        [Category("IPG信息"), DisplayName("激光器类型")]
        public override LaserType LaserType => LaserType.Fiber;

        /// <summary>
        /// 最大功率(W)
        /// </summary>
        [Category("IPG信息"), DisplayName("最大功率(W)")]
        public override double MaxPower { get; set; } = 1000;

        /// <summary>
        /// 最小功率(W)
        /// </summary>
        [Category("IPG信息"), DisplayName("最小功率(W)")]
        public override double MinPower => 0;

        /// <summary>
        /// 最大频率(Hz)
        /// </summary>
        [Category("IPG信息"), DisplayName("最大频率(Hz)")]
        public override double MaxFrequency => 100000;

        /// <summary>
        /// 最小频率(Hz)
        /// </summary>
        [Category("IPG信息"), DisplayName("最小频率(Hz)")]
        public override double MinFrequency => 1;

        /// <summary>
        /// 激光器状态
        /// </summary>
        [Category("IPG状态"), DisplayName("激光器状态")]
        public override LaserStatus LaserStatus
        {
            get
            {
                if (Status is StatusIPG ipgStatus)
                    return ipgStatus.LaserStatus;
                return LaserStatus.Unknown;
            }
        }

        /// <summary>
        /// 设备状态
        /// </summary>
        [XmlIgnore, Browsable(false)]
        public override StatusDevice Status { get; set; } = new StatusIPG();

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public LaserIPG()
        {
            Name = "IPG激光器";
            DeviceType = DeviceType.Laser;
        }

        #endregion

        #region 设备基本操作

        /// <summary>
        /// 打开设备连接
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool Open()
        {
            try
            {
                if (!IsEnabled) return false;
                if (Status.IsConnected) Close();

                // 创建TCP连接
                tcpClient = new TcpClient();
                var connectTask = tcpClient.ConnectAsync(IPAddress, Port);
                
                if (!connectTask.Wait(ConnectionTimeout))
                {
                    tcpClient?.Close();
                    return false;
                }

                networkStream = tcpClient.GetStream();
                Status.IsConnected = true;

                // 启动状态监控
                StartStatusMonitoring();

                // 初始化激光器
                if (!InitializeLaser())
                {
                    Close();
                    return false;
                }

                return IsOpen();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"打开IPG激光器异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查设备是否已打开
        /// </summary>
        /// <returns>是否已打开</returns>
        public override bool IsOpen()
        {
            return Status.IsConnected && tcpClient != null && tcpClient.Connected;
        }

        /// <summary>
        /// 关闭设备连接
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool Close()
        {
            try
            {
                // 关闭激光器
                LaserOff();

                // 停止状态监控
                StopStatusMonitoring();

                // 关闭网络连接
                networkStream?.Close();
                tcpClient?.Close();

                Status.IsConnected = false;
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"关闭IPG激光器异常：{ex.Message}");
                return false;
            }
        }

        #endregion

        #region 激光器控制

        /// <summary>
        /// 打开激光器
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool LaserOn()
        {
            try
            {
                if (!IsOpen()) return false;

                string command = "LASER_ON\r\n";
                if (SendCommand(command))
                {
                    IsLaserEnabled = true;
                    if (Status is StatusIPG ipgStatus)
                    {
                        ipgStatus.IsLaserOn = true;
                        ipgStatus.LaserStatus = LaserStatus.LaserOn;
                    }
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"IPG激光器开启异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 关闭激光器
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool LaserOff()
        {
            try
            {
                if (!IsOpen()) return false;

                string command = "LASER_OFF\r\n";
                if (SendCommand(command))
                {
                    IsLaserEnabled = false;
                    if (Status is StatusIPG ipgStatus)
                    {
                        ipgStatus.IsLaserOn = false;
                        ipgStatus.LaserStatus = LaserStatus.LaserOff;
                    }
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"IPG激光器关闭异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置激光器功率
        /// </summary>
        /// <param name="power">功率值(0-100%)</param>
        /// <returns>是否成功</returns>
        public override bool SetPower(double power)
        {
            try
            {
                if (!IsOpen()) return false;

                power = Math.Max(0, Math.Min(100, power));
                string command = $"SET_POWER {power:F2}\r\n";
                
                if (SendCommand(command))
                {
                    Power = power;
                    if (Status is StatusIPG ipgStatus)
                    {
                        ipgStatus.CurrentPower = power;
                    }
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置IPG激光器功率异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取激光器功率
        /// </summary>
        /// <param name="power">功率值</param>
        /// <returns>是否成功</returns>
        public override bool GetPower(ref double power)
        {
            try
            {
                if (!IsOpen()) return false;

                string command = "GET_POWER\r\n";
                string response = string.Empty;
                
                if (SendCommand(command, out response))
                {
                    if (double.TryParse(response.Trim(), out power))
                    {
                        Power = power;
                        if (Status is StatusIPG ipgStatus)
                        {
                            ipgStatus.CurrentPower = power;
                        }
                        return true;
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取IPG激光器功率异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置激光器频率
        /// </summary>
        /// <param name="frequency">频率值(Hz)</param>
        /// <returns>是否成功</returns>
        public override bool SetFrequency(double frequency)
        {
            try
            {
                if (!IsOpen()) return false;

                frequency = Math.Max(MinFrequency, Math.Min(MaxFrequency, frequency));
                string command = $"SET_FREQ {frequency:F0}\r\n";
                
                if (SendCommand(command))
                {
                    Frequency = frequency;
                    if (Status is StatusIPG ipgStatus)
                    {
                        ipgStatus.CurrentFrequency = frequency;
                    }
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置IPG激光器频率异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取激光器频率
        /// </summary>
        /// <param name="frequency">频率值</param>
        /// <returns>是否成功</returns>
        public override bool GetFrequency(ref double frequency)
        {
            try
            {
                if (!IsOpen()) return false;

                string command = "GET_FREQ\r\n";
                string response = string.Empty;
                
                if (SendCommand(command, out response))
                {
                    if (double.TryParse(response.Trim(), out frequency))
                    {
                        Frequency = frequency;
                        if (Status is StatusIPG ipgStatus)
                        {
                            ipgStatus.CurrentFrequency = frequency;
                        }
                        return true;
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取IPG激光器频率异常：{ex.Message}");
                return false;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化激光器
        /// </summary>
        /// <returns>是否成功</returns>
        private bool InitializeLaser()
        {
            try
            {
                // 获取激光器状态
                if (!UpdateLaserStatus()) return false;

                // 设置默认参数
                SetPower(0);
                SetFrequency(1000);
                SetPulseWidth(100);

                if (Status is StatusIPG ipgStatus)
                {
                    ipgStatus.LaserStatus = LaserStatus.Ready;
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化IPG激光器异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 发送命令
        /// </summary>
        /// <param name="command">命令字符串</param>
        /// <returns>是否成功</returns>
        private bool SendCommand(string command)
        {
            string response;
            return SendCommand(command, out response);
        }

        /// <summary>
        /// 发送命令并获取响应
        /// </summary>
        /// <param name="command">命令字符串</param>
        /// <param name="response">响应字符串</param>
        /// <returns>是否成功</returns>
        private bool SendCommand(string command, out string response)
        {
            response = string.Empty;
            try
            {
                lock (objLock)
                {
                    if (networkStream == null || !networkStream.CanWrite)
                        return false;

                    // 发送命令
                    byte[] data = Encoding.ASCII.GetBytes(command);
                    networkStream.Write(data, 0, data.Length);

                    // 读取响应
                    byte[] buffer = new byte[1024];
                    int bytesRead = networkStream.Read(buffer, 0, buffer.Length);
                    response = Encoding.ASCII.GetString(buffer, 0, bytesRead);

                    return true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"IPG激光器发送命令异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 启动状态监控
        /// </summary>
        private void StartStatusMonitoring()
        {
            statusCancellationToken = new CancellationTokenSource();
            Task.Run(() => StatusMonitoringLoop(statusCancellationToken.Token));
        }

        /// <summary>
        /// 停止状态监控
        /// </summary>
        private void StopStatusMonitoring()
        {
            statusCancellationToken?.Cancel();
        }

        /// <summary>
        /// 状态监控循环
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        private void StatusMonitoringLoop(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested && IsOpen())
            {
                try
                {
                    UpdateLaserStatus();
                    Thread.Sleep(1000);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"IPG激光器状态监控异常：{ex.Message}");
                    break;
                }
            }
        }

        /// <summary>
        /// 更新激光器状态
        /// </summary>
        /// <returns>是否成功</returns>
        private bool UpdateLaserStatus()
        {
            try
            {
                // 更新功率
                double power = 0;
                GetPower(ref power);

                // 更新频率
                double frequency = 0;
                GetFrequency(ref frequency);

                // 更新温度
                double temperature = 0;
                GetTemperature(ref temperature);

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新IPG激光器状态异常：{ex.Message}");
                return false;
            }
        }

        #endregion

        #region 其他激光器方法实现

        /// <summary>
        /// 设置脉冲宽度
        /// </summary>
        /// <param name="pulseWidth">脉冲宽度(μs)</param>
        /// <returns>是否成功</returns>
        public override bool SetPulseWidth(double pulseWidth)
        {
            try
            {
                if (!IsOpen()) return false;

                pulseWidth = Math.Max(1, Math.Min(10000, pulseWidth));
                string command = $"SET_PULSE_WIDTH {pulseWidth:F2}\r\n";

                if (SendCommand(command))
                {
                    PulseWidth = pulseWidth;
                    if (Status is StatusIPG ipgStatus)
                    {
                        ipgStatus.CurrentPulseWidth = pulseWidth;
                    }
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置IPG激光器脉冲宽度异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取脉冲宽度
        /// </summary>
        /// <param name="pulseWidth">脉冲宽度</param>
        /// <returns>是否成功</returns>
        public override bool GetPulseWidth(ref double pulseWidth)
        {
            try
            {
                if (!IsOpen()) return false;

                string command = "GET_PULSE_WIDTH\r\n";
                string response = string.Empty;

                if (SendCommand(command, out response))
                {
                    if (double.TryParse(response.Trim(), out pulseWidth))
                    {
                        PulseWidth = pulseWidth;
                        if (Status is StatusIPG ipgStatus)
                        {
                            ipgStatus.CurrentPulseWidth = pulseWidth;
                        }
                        return true;
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取IPG激光器脉冲宽度异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置激光器模式
        /// </summary>
        /// <param name="mode">激光器模式</param>
        /// <returns>是否成功</returns>
        public override bool SetLaserMode(LaserMode mode)
        {
            try
            {
                if (!IsOpen()) return false;

                string modeStr = mode.ToString().ToUpper();
                string command = $"SET_MODE {modeStr}\r\n";

                if (SendCommand(command))
                {
                    LaserMode = mode;
                    if (Status is StatusIPG ipgStatus)
                    {
                        ipgStatus.CurrentMode = mode;
                    }
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置IPG激光器模式异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取激光器模式
        /// </summary>
        /// <param name="mode">激光器模式</param>
        /// <returns>是否成功</returns>
        public override bool GetLaserMode(ref LaserMode mode)
        {
            try
            {
                if (!IsOpen()) return false;

                string command = "GET_MODE\r\n";
                string response = string.Empty;

                if (SendCommand(command, out response))
                {
                    if (Enum.TryParse(response.Trim(), true, out mode))
                    {
                        LaserMode = mode;
                        if (Status is StatusIPG ipgStatus)
                        {
                            ipgStatus.CurrentMode = mode;
                        }
                        return true;
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取IPG激光器模式异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查激光器是否准备就绪
        /// </summary>
        /// <returns>是否准备就绪</returns>
        public override bool IsReady()
        {
            try
            {
                if (!IsOpen()) return false;

                string command = "GET_STATUS\r\n";
                string response = string.Empty;

                if (SendCommand(command, out response))
                {
                    return response.Trim().ToUpper().Contains("READY");
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查IPG激光器就绪状态异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查激光器是否有错误
        /// </summary>
        /// <returns>是否有错误</returns>
        public override bool HasError()
        {
            try
            {
                if (!IsOpen()) return true;

                string command = "GET_ERROR\r\n";
                string response = string.Empty;

                if (SendCommand(command, out response))
                {
                    return !response.Trim().ToUpper().Contains("NO_ERROR");
                }
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查IPG激光器错误状态异常：{ex.Message}");
                return true;
            }
        }

        /// <summary>
        /// 获取激光器错误信息
        /// </summary>
        /// <returns>错误信息</returns>
        public override string GetErrorMessage()
        {
            try
            {
                if (!IsOpen()) return "设备未连接";

                string command = "GET_ERROR\r\n";
                string response = string.Empty;

                if (SendCommand(command, out response))
                {
                    return response.Trim();
                }
                return "无法获取错误信息";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取IPG激光器错误信息异常：{ex.Message}");
                return $"获取错误信息异常：{ex.Message}";
            }
        }

        /// <summary>
        /// 清除激光器错误
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool ClearError()
        {
            try
            {
                if (!IsOpen()) return false;

                string command = "CLEAR_ERROR\r\n";
                if (SendCommand(command))
                {
                    if (Status is StatusIPG ipgStatus)
                    {
                        ipgStatus.ClearError();
                    }
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清除IPG激光器错误异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 激光器自检
        /// </summary>
        /// <returns>自检结果</returns>
        public override bool SelfTest()
        {
            try
            {
                if (!IsOpen()) return false;

                string command = "SELF_TEST\r\n";
                string response = string.Empty;

                if (SendCommand(command, out response))
                {
                    return response.Trim().ToUpper().Contains("PASS");
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"IPG激光器自检异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取激光器温度
        /// </summary>
        /// <param name="temperature">温度值(℃)</param>
        /// <returns>是否成功</returns>
        public override bool GetTemperature(ref double temperature)
        {
            try
            {
                if (!IsOpen()) return false;

                string command = "GET_TEMP\r\n";
                string response = string.Empty;

                if (SendCommand(command, out response))
                {
                    if (double.TryParse(response.Trim(), out temperature))
                    {
                        if (Status is StatusIPG ipgStatus)
                        {
                            ipgStatus.Temperature = temperature;
                        }
                        return true;
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取IPG激光器温度异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取激光器工作时间
        /// </summary>
        /// <param name="workingHours">工作时间(小时)</param>
        /// <returns>是否成功</returns>
        public override bool GetWorkingHours(ref double workingHours)
        {
            try
            {
                if (!IsOpen()) return false;

                string command = "GET_WORKING_HOURS\r\n";
                string response = string.Empty;

                if (SendCommand(command, out response))
                {
                    if (double.TryParse(response.Trim(), out workingHours))
                    {
                        if (Status is StatusIPG ipgStatus)
                        {
                            ipgStatus.WorkingHours = workingHours;
                        }
                        return true;
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取IPG激光器工作时间异常：{ex.Message}");
                return false;
            }
        }

        #endregion
    }
}
