<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
    </startup>
    
    <appSettings>
        <!-- 应用程序默认配置 -->
        <add key="DefaultTheme" value="Light" />
        <add key="AutoSaveSettings" value="true" />
        <add key="WindowStateRemember" value="true" />
        <add key="LogLevel" value="Info" />
        <add key="CacheEnabled" value="true" />
        <add key="PerformanceMonitoring" value="true" />

        <!-- 禁用UI自动化相关功能 -->
        <add key="DisableUIAutomation" value="true" />
        <add key="DISABLE_XAMLDIAGNOSTICS" value="1" />
    </appSettings>

    <runtime>
        <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
            <!-- 程序集绑定重定向 -->
            <dependentAssembly>
                <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />
            </dependentAssembly>
        </assemblyBinding>

        <!-- 禁用并发垃圾回收以提高稳定性 -->
        <gcConcurrent enabled="false" />

        <!-- 启用旧版本兼容性 -->
        <legacyCorruptedStateExceptionsPolicy enabled="true" />
    </runtime>
</configuration>
