using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using McLaser.Core.Common;
using McLaser.Core.Framework.Configuration;
using McLaser.Core.Framework.Container;
using McLaser.Core.Framework.Logging;
using McLaser.Core.Framework.Services;
using McLaser.Core.Framework.UI;
using AppNavigationService = McLaser.Core.Navigation.INavigationService;
using AppPageChangedEventArgs = McLaser.Core.Navigation.PageChangedEventArgs;
using System.ComponentModel.Composition;
using McLaser.Core.Navigation;
using McLaser.Core.Container;
using McLaser.Core.Modules.RecipeManager;

namespace McLaser.Core.ApplicationBase
{
    /// <summary>
    /// 主窗口ViewModel
    /// 展示McLaser.Core框架的MVVM模式使用
    /// </summary>
    [Export]
    public class MainViewModel : ViewModelBase
    {
        private readonly IContainer _container;
        private readonly IThemeService _themeService;
        private readonly IWindowManager _windowManager;
        private readonly IDialogService _dialogService;
        private readonly IConfigurationService _configurationService;
        private readonly ILogger _logger;
        private readonly AppNavigationService _navigationService;

        private string _title = "McLaser示例应用程序";
        private string _statusMessage = "就绪";
        private string _currentTheme = "Light";
        private string _currentPageTitle = "主页";
        private bool _isThemeMenuOpen;
        private FrameworkElement? _currentPageContent;

        [ImportingConstructor]
        public MainViewModel(
            IThemeService themeService,
            IWindowManager windowManager,
            IDialogService dialogService,
            ILogger logger,
            AppNavigationService? navigationService = null)
        {
            _themeService = themeService ?? throw new ArgumentNullException(nameof(themeService));
            _windowManager = windowManager ?? throw new ArgumentNullException(nameof(windowManager));
            _dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _navigationService = navigationService ?? new McLaser.Core.Navigation.NavigationService(_logger);

            InitializeNavigation();
            InitializeCommands();
            InitializeData();
            SubscribeToEvents();

            Test.Create();
            //Test.Save();
            Test.Load();

           
            //Test.Save();
            RecipeData3.Instance.IsEnable = true;
            RecipeData3.Instance.Destripe = "Lusunping";
            Test.Save();
            Test.Load();

        }

        /// <summary>
        /// 默认构造函数（备用方案）
        /// </summary>
        public MainViewModel()
        {
            // 尝试从容器获取服务
            var container = ContainerManager.Current;
            if (container != null)
            {
                _container = container;
                _themeService = container.TryResolve<IThemeService>();
                _windowManager = container.TryResolve<IWindowManager>();
                _dialogService = container.TryResolve<IDialogService>();
                _configurationService = container.TryResolve<IConfigurationService>();
                _logger = container.TryResolve<ILogger>();
            }

            // 如果任何服务为null，使用null对象模式
            _container ??= null!;
            _themeService ??= null!;
            _windowManager ??= null!;
            _dialogService ??= null!;
            _configurationService ??= null!;
            _logger = _logger ?? new DefaultLogger("MainViewModel");
            _navigationService = new McLaser.Core.Navigation.NavigationService(_logger);

            InitializeNavigation();
            InitializeCommands();
            InitializeDataSafely();
            SubscribeToEventsSafely();
        }

        #region 属性

        /// <summary>
        /// 窗口标题
        /// </summary>
        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }

        /// <summary>
        /// 状态消息
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        /// <summary>
        /// 当前主题
        /// </summary>
        public string CurrentTheme
        {
            get => _currentTheme;
            set => SetProperty(ref _currentTheme, value);
        }

        /// <summary>
        /// 当前页面标题
        /// </summary>
        public string CurrentPageTitle
        {
            get => _currentPageTitle;
            set => SetProperty(ref _currentPageTitle, value);
        }

        /// <summary>
        /// 当前页面内容
        /// </summary>
        public FrameworkElement? CurrentPageContent
        {
            get => _currentPageContent;
            set => SetProperty(ref _currentPageContent, value);
        }

        /// <summary>
        /// 导航ViewModel
        /// </summary>
        public NavigationViewModel NavigationViewModel { get; private set; } = null!;

        /// <summary>
        /// 主题菜单是否打开
        /// </summary>
        public bool IsThemeMenuOpen
        {
            get => _isThemeMenuOpen;
            set => SetProperty(ref _isThemeMenuOpen, value);
        }

        /// <summary>
        /// 可用主题列表
        /// </summary>
        public ObservableCollection<string> AvailableThemes { get; } = new ObservableCollection<string>();

        /// <summary>
        /// 活动窗口列表
        /// </summary>
        public ObservableCollection<string> ActiveWindows { get; } = new ObservableCollection<string>();

        #endregion

        #region 命令

        /// <summary>
        /// 切换主题命令
        /// </summary>
        public ICommand SwitchThemeCommand { get; private set; } = null!;

        /// <summary>
        /// 打开设置窗口命令
        /// </summary>
        public ICommand OpenSettingsCommand { get; private set; } = null!;

        /// <summary>
        /// 打开数据输入窗口命令
        /// </summary>
        public ICommand OpenDataInputCommand { get; private set; } = null!;

        /// <summary>
        /// 显示关于对话框命令
        /// </summary>
        public ICommand ShowAboutCommand { get; private set; } = null!;

        /// <summary>
        /// 退出应用程序命令
        /// </summary>
        public ICommand ExitCommand { get; private set; } = null!;

        /// <summary>
        /// 刷新状态命令
        /// </summary>
        public ICommand RefreshStatusCommand { get; private set; } = null!;

        /// <summary>
        /// 测试对话框命令
        /// </summary>
        public ICommand TestDialogCommand { get; private set; } = null!;

        /// <summary>
        /// 测试配置命令
        /// </summary>
        public ICommand TestConfigurationCommand { get; private set; } = null!;

        /// <summary>
        /// 打开模块演示窗口命令
        /// </summary>
        public ICommand OpenModuleDemoCommand { get; private set; } = null!;

        /// <summary>
        /// 打开设备管理器测试窗口命令
        /// </summary>
        public ICommand OpenDeviceManagerTestCommand { get; private set; } = null!;

        /// <summary>
        /// 打开EventBus演示命令
        /// </summary>
        public ICommand OpenEventBusDemoCommand { get; private set; } = null!;

        /// <summary>
        /// 打开异常处理演示命令
        /// </summary>
        public ICommand OpenExceptionDemoCommand { get; private set; } = null!;

        /// <summary>
        /// 打开插件管理演示命令
        /// </summary>
        public ICommand OpenPluginDemoCommand { get; private set; } = null!;

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化导航系统
        /// </summary>
        private void InitializeNavigation()
        {
            try
            {
                // 注册页面
                RegisterPages();

                // 创建导航ViewModel
                NavigationViewModel = new NavigationViewModel(_navigationService, _logger);

                // 订阅导航事件
                _navigationService.PageChanged += OnNavigationPageChanged;

                // 导航到主页
                _navigationService.NavigateTo("home");

                _logger?.LogInfo("导航系统初始化完成");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"导航系统初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 注册所有页面
        /// </summary>
        private void RegisterPages()
        {
            //使用Ioc接口或者反射，批量注册界面
            var exports = IoC.GetAll<INavigationPage>();


            var pages = new[]
            {
                // 主页
                new PageInfo
                {
                    Id = "home",
                    Title = "主页",
                    Description = "应用程序主页",
                    Icon = "🏠",
                    PageFactory = () => exports.First(x=>x.GetType().ToString().Contains("Home")) as FrameworkElement,
                    ViewModelFactory = () =>  (exports.First(x=>x.GetType().ToString().Contains("Home")) as FrameworkElement).DataContext,
                    IsSingleton = true
                },

                // 设备管理器
                new PageInfo
                {
                    Id = "device-manager",
                    Title = "设备管理器",
                    Description = "管理所有设备",
                    Icon = "⚙",
                    PageFactory = () => exports.First(x=>x.GetType().ToString().Contains("DeviceManager")) as FrameworkElement,
                    IsSingleton = true
                },

                // 设备状态
                new PageInfo
                {
                    Id = "device-status",
                    Title = "设备状态",
                    Description = "查看设备状态",
                    Icon = "📊",
                    PageFactory = () => CreateDeviceStatusPage(),
                    IsSingleton = true
                },

                // 事件总线演示
                new PageInfo
                {
                    Id = "eventbus-demo",
                    Title = "事件总线演示",
                    Description = "事件总线功能演示",
                    Icon = "📡",
                    PageFactory = () => CreateEventBusDemoPage(),
                    IsSingleton = true
                },

                // 异常处理演示
                new PageInfo
                {
                    Id = "exception-demo",
                    Title = "异常处理演示",
                    Description = "异常处理功能演示",
                    Icon = "⚠",
                    PageFactory = () => CreateExceptionDemoPage(),
                    IsSingleton = true
                },

                // 插件管理演示
                new PageInfo
                {
                    Id = "plugin-demo",
                    Title = "插件管理演示",
                    Description = "插件管理功能演示",
                    Icon = "🔌",
                    PageFactory = () => CreatePluginDemoPage(),
                    IsSingleton = true
                },

                // 数据输入
                new PageInfo
                {
                    Id = "data-input",
                    Title = "数据输入",
                    Description = "数据输入工具",
                    Icon = "📝",
                    PageFactory = () => CreateDataInputPage(),
                    IsSingleton = true
                },

                // 设置
                new PageInfo
                {
                    Id = "settings",
                    Title = "设置",
                    Description = "应用程序设置",
                    Icon = "⚙",
                    PageFactory = () => CreateSettingsPage(),
                    IsSingleton = true
                }
            };

            _navigationService.RegisterPages(pages);
        }

        /// <summary>
        /// 初始化命令
        /// </summary>
        private void InitializeCommands()
        {
            SwitchThemeCommand = new RelayCommand<string>(ExecuteSwitchTheme, CanExecuteSwitchTheme);
            OpenSettingsCommand = new RelayCommand(ExecuteOpenSettings);
            OpenDataInputCommand = new RelayCommand(ExecuteOpenDataInput);
            ShowAboutCommand = new RelayCommand(ExecuteShowAbout);
            ExitCommand = new RelayCommand(ExecuteExit);
            RefreshStatusCommand = new RelayCommand(ExecuteRefreshStatus);
            TestDialogCommand = new RelayCommand(ExecuteTestDialog);
            TestConfigurationCommand = new RelayCommand(ExecuteTestConfiguration);
            OpenModuleDemoCommand = new RelayCommand(ExecuteOpenModuleDemo);
            OpenDeviceManagerTestCommand = new RelayCommand(ExecuteOpenDeviceManagerTest);
            OpenEventBusDemoCommand = new RelayCommand(ExecuteOpenEventBusDemo);
            OpenExceptionDemoCommand = new RelayCommand(ExecuteOpenExceptionDemo);
            OpenPluginDemoCommand = new RelayCommand(ExecuteOpenPluginDemo);
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            try
            {
                // 加载可用主题
                LoadAvailableThemes();

                // 设置当前主题
                CurrentTheme = _themeService.CurrentTheme;

                // 更新状态
                UpdateStatus("应用程序已就绪");

                _logger?.LogInfo("MainViewModel初始化完成");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"MainViewModel初始化失败: {ex.Message}");
                UpdateStatus($"初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 订阅事件
        /// </summary>
        private void SubscribeToEvents()
        {
            // 订阅主题变更事件
            _themeService.ThemeChanged += OnThemeChanged;

            // 订阅窗口事件
            _windowManager.WindowOpened += OnWindowOpened;
            _windowManager.WindowClosed += OnWindowClosed;
        }

        /// <summary>
        /// 导航页面变更事件处理
        /// </summary>
        private void OnNavigationPageChanged(object? sender, AppPageChangedEventArgs e)
        {
            try
            {
                // 更新当前页面内容
                CurrentPageContent = _navigationService.CurrentPageInstance;
                CurrentPageTitle = e.NewPage?.Title ?? "未知页面";

                UpdateStatus($"已切换到页面: {e.NewPage?.Title}");
                _logger?.LogInfo($"页面已切换: {e.OldPage?.Title} -> {e.NewPage?.Title}");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"处理页面切换事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载可用主题
        /// </summary>
        private void LoadAvailableThemes()
        {
            AvailableThemes.Clear();
            foreach (var theme in _themeService.AvailableThemes)
            {
                AvailableThemes.Add(theme);
            }
        }

        /// <summary>
        /// 更新状态消息
        /// </summary>
        /// <param name="message">状态消息</param>
        private void UpdateStatus(string message)
        {
            StatusMessage = $"{DateTime.Now:HH:mm:ss} - {message}";
        }

        /// <summary>
        /// 更新活动窗口列表
        /// </summary>
        private void UpdateActiveWindows()
        {
            ActiveWindows.Clear();
            foreach (var window in _windowManager.ActiveWindows)
            {
                ActiveWindows.Add($"{window.GetType().Name} - {window.Title}");
            }
        }

        #endregion

        #region 命令执行方法

        /// <summary>
        /// 执行切换主题命令
        /// </summary>
        /// <param name="themeName">主题名称</param>
        private void ExecuteSwitchTheme(string? themeName)
        {
            if (string.IsNullOrEmpty(themeName)) return;

            try
            {
                if (_themeService?.ApplyTheme(themeName ?? string.Empty) == true)
                {
                    CurrentTheme = themeName ?? string.Empty;
                    UpdateStatus($"已切换到 {themeName} 主题");
                    _logger?.LogInfo($"主题已切换到: {themeName}");
                }
                else
                {
                    // 即使服务不可用，也更新UI状态
                    CurrentTheme = themeName ?? string.Empty;
                    UpdateStatus($"主题已设置为: {themeName} (服务不可用)");
                    _logger?.LogWarning($"主题服务不可用，仅更新UI状态: {themeName}");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"切换主题时发生错误: {ex.Message}");
                UpdateStatus($"切换主题错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 判断是否可以执行切换主题命令
        /// </summary>
        /// <param name="themeName">主题名称</param>
        /// <returns>是否可以执行</returns>
        private bool CanExecuteSwitchTheme(string? themeName)
        {
            return !string.IsNullOrEmpty(themeName) && themeName != CurrentTheme;
        }

        /// <summary>
        /// 执行打开设置窗口命令
        /// </summary>
        private void ExecuteOpenSettings()
        {
            //try
            //{
            //    if (_container != null)
            //    {
            //        var settingsViewModel = _container.TryResolve<SettingsViewModel>();
            //        if (settingsViewModel == null)
            //        {
            //            // 手动创建SettingsViewModel
            //            settingsViewModel = new SettingsViewModel(
            //                _themeService ?? null!,
            //                _configurationService ?? null!,
            //                _dialogService ?? null!,
            //                _logger ?? null!);
            //        }

            //        var settingsWindow = new SettingsWindow
            //        {
            //            DataContext = settingsViewModel,
            //            Owner = _windowManager?.MainWindow
            //        };

            //        if (_windowManager != null)
            //        {
            //            _windowManager.ShowWindow(settingsWindow, _windowManager.MainWindow);
            //        }
            //        else
            //        {
            //            settingsWindow.Show();
            //        }
            //        UpdateStatus("设置窗口已打开");
            //    }
            //    else
            //    {
            //        UpdateStatus("容器服务不可用，无法打开设置窗口");
            //    }
            //}
            //catch (Exception ex)
            //{
            //    _logger?.LogError($"打开设置窗口失败: {ex.Message}");
            //    if (_dialogService != null)
            //    {
            //        _dialogService.ShowMessage($"打开设置窗口失败：{ex.Message}", "错误", MessageType.Error);
            //    }
            //    else
            //    {
            //        System.Windows.MessageBox.Show($"打开设置窗口失败：{ex.Message}", "错误");
            //    }
            //}
        }

        /// <summary>
        /// 执行打开数据输入窗口命令
        /// </summary>
        private void ExecuteOpenDataInput()
        {
            try
            {
                //if (_container != null)
                //{
                //    var dataInputViewModel = _container.TryResolve<DataInputViewModel>();
                //    if (dataInputViewModel == null)
                //    {
                //        // 手动创建DataInputViewModel
                //        dataInputViewModel = new DataInputViewModel(
                //            _dialogService ?? null!,
                //            _logger ?? null!);
                //    }

                //    var dataInputWindow = new DataInputWindow
                //    {
                //        DataContext = dataInputViewModel,
                //        Owner = _windowManager?.MainWindow
                //    };

                //    if (_windowManager != null)
                //    {
                //        _windowManager.ShowWindow(dataInputWindow, _windowManager.MainWindow);
                //    }
                //    else
                //    {
                //        dataInputWindow.Show();
                //    }
                //    UpdateStatus("数据输入窗口已打开");
                //}
                //else
                //{
                //    UpdateStatus("容器服务不可用，无法打开数据输入窗口");
                //}
            }
            catch (Exception ex)
            {
                _logger?.LogError($"打开数据输入窗口失败: {ex.Message}");
                if (_dialogService != null)
                {
                    _dialogService.ShowMessage($"打开数据输入窗口失败：{ex.Message}", "错误", MessageType.Error);
                }
                else
                {
                    System.Windows.MessageBox.Show($"打开数据输入窗口失败：{ex.Message}", "错误");
                }
            }
        }

        /// <summary>
        /// 执行显示关于对话框命令
        /// </summary>
        private void ExecuteShowAbout()
        {
            var aboutMessage = "McLaser示例应用程序\n\n" +
                              "版本: 1.0.0\n" +
                              "基于: McLaser.Core框架\n\n" +
                              "这是一个展示McLaser.Core框架功能的完整示例应用程序。\n\n" +
                              "功能特性:\n" +
                              "• 统一DI容器架构\n" +
                              "• 主题管理系统\n" +
                              "• 窗口管理器\n" +
                              "• 数据验证框架\n" +
                              "• 配置管理服务\n" +
                              "• MVVM模式支持";

            _dialogService?.ShowMessage(aboutMessage, "关于", MessageType.Information);
            UpdateStatus("已显示关于信息");
        }

        /// <summary>
        /// 执行退出应用程序命令
        /// </summary>
        private void ExecuteExit()
        {
            var result = _dialogService?.ShowMessage("确定要退出应用程序吗？", "确认退出", MessageType.Question);
            if (result == true)
            {
                System.Windows.Application.Current.Shutdown();
            }
        }

        /// <summary>
        /// 执行刷新状态命令
        /// </summary>
        private void ExecuteRefreshStatus()
        {
            try
            {
                // 刷新主题信息
                CurrentTheme = _themeService.CurrentTheme;
                LoadAvailableThemes();

                // 刷新窗口信息
                UpdateActiveWindows();

                UpdateStatus("状态已刷新");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"刷新状态失败: {ex.Message}");
                UpdateStatus($"刷新失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行测试对话框命令
        /// </summary>
        private void ExecuteTestDialog()
        {
            try
            {
                if (_dialogService != null)
                {
                    // 测试不同类型的对话框
                    _dialogService.ShowMessage("这是一个信息对话框示例。", "信息", MessageType.Information);

                    var warning = _dialogService.ShowMessage("这是一个确认对话框示例。\n是否继续？", "确认", MessageType.Question);
                    if (warning == true)
                    {
                        _dialogService.ShowMessage("您选择了继续。", "结果", MessageType.Information);
                    }
                }
                else
                {
                    // 使用系统对话框作为备用
                    System.Windows.MessageBox.Show("这是一个信息对话框示例。", "信息");

                    var result = System.Windows.MessageBox.Show("这是一个确认对话框示例。\n是否继续？", "确认",
                        System.Windows.MessageBoxButton.YesNo, System.Windows.MessageBoxImage.Question);
                    if (result == System.Windows.MessageBoxResult.Yes)
                    {
                        System.Windows.MessageBox.Show("您选择了继续。", "结果");
                    }
                }

                UpdateStatus("对话框测试完成");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"测试对话框失败: {ex.Message}");
                UpdateStatus($"对话框测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行测试配置命令
        /// </summary>
        private void ExecuteTestConfiguration()
        {
            try
            {
                if (_configurationService != null)
                {
                    // 测试配置服务
                    var testKey = "TestConfiguration";
                    var testValue = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

                    _configurationService.SetValue(testKey, testValue);
                    var retrievedValue = _configurationService.GetValue<string>(testKey);

                    var message = $"配置测试成功！\n\n设置值: {testValue}\n获取值: {retrievedValue}";

                    if (_dialogService != null)
                    {
                        _dialogService.ShowMessage(message, "配置测试", MessageType.Information);
                    }
                    else
                    {
                        System.Windows.MessageBox.Show(message, "配置测试");
                    }
                }
                else
                {
                    var message = "配置服务不可用，无法进行测试。";
                    if (_dialogService != null)
                    {
                        _dialogService.ShowMessage(message, "配置测试", MessageType.Warning);
                    }
                    else
                    {
                        System.Windows.MessageBox.Show(message, "配置测试");
                    }
                }

                UpdateStatus("配置测试完成");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"测试配置失败: {ex.Message}");
                var errorMessage = $"配置测试失败：{ex.Message}";
                if (_dialogService != null)
                {
                    _dialogService.ShowMessage(errorMessage, "错误", MessageType.Error);
                }
                else
                {
                    System.Windows.MessageBox.Show(errorMessage, "错误");
                }
            }
        }

        /// <summary>
        /// 执行打开模块演示窗口命令
        /// </summary>
        private void ExecuteOpenModuleDemo()
        {
            try
            {
                //if (_container != null)
                //{
                //    var moduleDemoViewModel = _container.TryResolve<ModuleDemoViewModel>();
                //    if (moduleDemoViewModel == null)
                //    {
                //        // 手动创建ModuleDemoViewModel
                //        var logger = _container.TryResolve<ILogger>();
                //        var jsonService = _container.TryResolve<McLaser.Core.Framework.Serialization.IJsonService>();
                //        var styleService = _container.TryResolve<McLaser.Core.Framework.Styles.IStyleService>();
                //        var userService = _container.TryResolve<McLaser.Core.Framework.Security.IUserService>();

                //        if (jsonService != null && styleService != null && userService != null)
                //        {
                //            moduleDemoViewModel = new ModuleDemoViewModel(logger, jsonService, styleService, userService);
                //        }
                //        else
                //        {
                //            UpdateStatus("无法创建模块演示窗口：缺少必要的服务");
                //            return;
                //        }
                //    }

                //    var moduleDemoWindow = new ModuleDemoWindow
                //    {
                //        DataContext = moduleDemoViewModel,
                //        Owner = _windowManager?.MainWindow
                //    };

                //    if (_windowManager != null)
                //    {
                //        _windowManager.ShowWindow(moduleDemoWindow, _windowManager.MainWindow);
                //    }
                //    else
                //    {
                //        moduleDemoWindow.Show();
                //    }
                //    UpdateStatus("模块演示窗口已打开");
                //}
                //else
                //{
                //    UpdateStatus("容器服务不可用，无法打开模块演示窗口");
                //}
            }
            catch (Exception ex)
            {
                _logger?.LogError($"打开模块演示窗口失败: {ex.Message}");
                if (_dialogService != null)
                {
                    _dialogService.ShowMessage($"打开模块演示窗口失败：{ex.Message}", "错误", MessageType.Error);
                }
                else
                {
                    System.Windows.MessageBox.Show($"打开模块演示窗口失败：{ex.Message}", "错误");
                }
            }
        }

        /// <summary>
        /// 执行打开设备管理器测试窗口命令
        /// </summary>
        private void ExecuteOpenDeviceManagerTest()
        {
            //try
            //{
            //    var deviceManagerTestWindow = new DeviceManagerWindow
            //    {
            //        Owner = _windowManager?.MainWindow
            //    };

            //    if (_windowManager != null)
            //    {
            //        _windowManager.ShowWindow(deviceManagerTestWindow, _windowManager.MainWindow);
            //    }
            //    else
            //    {
            //        deviceManagerTestWindow.Show();
            //    }
            //    UpdateStatus("设备管理器测试窗口已打开");
            //}
            //catch (Exception ex)
            //{
            //    _logger?.LogError($"打开设备管理器测试窗口失败: {ex.Message}");
            //    if (_dialogService != null)
            //    {
            //        _dialogService.ShowMessage($"打开设备管理器测试窗口失败：{ex.Message}", "错误", MessageType.Error);
            //    }
            //    else
            //    {
            //        System.Windows.MessageBox.Show($"打开设备管理器测试窗口失败：{ex.Message}", "错误");
            //    }
            //}
        }

        /// <summary>
        /// 执行打开EventBus演示窗口命令
        /// </summary>
        private void ExecuteOpenEventBusDemo()
        {
            //try
            //{
            //    var eventBusDemoWindow = new EventBusDemoWindow
            //    {
            //        Owner = _windowManager?.MainWindow
            //    };

            //    if (_windowManager != null)
            //    {
            //        _windowManager.ShowWindow(eventBusDemoWindow, _windowManager.MainWindow);
            //    }
            //    else
            //    {
            //        eventBusDemoWindow.Show();
            //    }
            //    UpdateStatus("EventBus演示窗口已打开");
            //}
            //catch (Exception ex)
            //{
            //    _logger?.LogError($"打开EventBus演示窗口失败: {ex.Message}");
            //    if (_dialogService != null)
            //    {
            //        _dialogService.ShowMessage($"打开EventBus演示窗口失败：{ex.Message}", "错误", MessageType.Error);
            //    }
            //    else
            //    {
            //        System.Windows.MessageBox.Show($"打开EventBus演示窗口失败：{ex.Message}", "错误");
            //    }
            //}
        }

        /// <summary>
        /// 执行打开异常处理演示窗口命令
        /// </summary>
        private void ExecuteOpenExceptionDemo()
        {
            //try
            //{
            //    var exceptionDemoWindow = new ExceptionDemoWindow
            //    {
            //        Owner = _windowManager?.MainWindow
            //    };

            //    if (_windowManager != null)
            //    {
            //        _windowManager.ShowWindow(exceptionDemoWindow, _windowManager.MainWindow);
            //    }
            //    else
            //    {
            //        exceptionDemoWindow.Show();
            //    }
            //    UpdateStatus("异常处理演示窗口已打开");
            //}
            //catch (Exception ex)
            //{
            //    _logger?.LogError($"打开异常处理演示窗口失败: {ex.Message}");
            //    if (_dialogService != null)
            //    {
            //        _dialogService.ShowMessage($"打开异常处理演示窗口失败：{ex.Message}", "错误", MessageType.Error);
            //    }
            //    else
            //    {
            //        System.Windows.MessageBox.Show($"打开异常处理演示窗口失败：{ex.Message}", "错误");
            //    }
            //}
        }

        /// <summary>
        /// 执行打开插件管理演示窗口命令
        /// </summary>
        private void ExecuteOpenPluginDemo()
        {
            try
            {
                //var pluginDemoWindow = new Views.PluginDemoWindow
                //{
                //    Owner = _windowManager?.MainWindow
                //};

                //if (_windowManager != null)
                //{
                //    _windowManager.ShowWindow(pluginDemoWindow, _windowManager.MainWindow);
                //}
                //else
                //{
                //    pluginDemoWindow.Show();
                //}
                //UpdateStatus("插件管理演示窗口已打开");
            }
            catch (Exception ex)
            {
                _logger?.Error(ex, "打开插件管理演示窗口失败");
                if (_dialogService != null)
                {
                    _dialogService.ShowMessage($"打开插件管理演示窗口失败：{ex.Message}", "错误", MessageType.Error);
                }
                else
                {
                    System.Windows.MessageBox.Show($"打开插件管理演示窗口失败：{ex.Message}", "错误");
                }
            }
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 主题变更事件处理
        /// </summary>
        private void OnThemeChanged(object? sender, ThemeChangedEventArgs e)
        {
            CurrentTheme = e.NewTheme;
            UpdateStatus($"主题已变更: {e.OldTheme} -> {e.NewTheme}");
        }

        /// <summary>
        /// 窗口打开事件处理
        /// </summary>
        private void OnWindowOpened(object? sender, WindowEventArgs e)
        {
            UpdateActiveWindows();
            UpdateStatus($"窗口已打开: {e.Window.GetType().Name}");
        }

        /// <summary>
        /// 窗口关闭事件处理
        /// </summary>
        private void OnWindowClosed(object? sender, WindowEventArgs e)
        {
            UpdateActiveWindows();
            UpdateStatus($"窗口已关闭: {e.Window.GetType().Name}");
        }

        /// <summary>
        /// 安全初始化数据
        /// </summary>
        private void InitializeDataSafely()
        {
            try
            {
                // 加载可用主题
                LoadAvailableThemesSafely();

                // 设置当前主题
                CurrentTheme = _themeService?.CurrentTheme ?? "Light";

                // 更新状态
                UpdateStatus("应用程序已就绪");

                _logger?.LogInfo("MainViewModel初始化完成");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"MainViewModel初始化失败: {ex.Message}");
                UpdateStatus($"初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 安全订阅事件
        /// </summary>
        private void SubscribeToEventsSafely()
        {
            try
            {
                // 订阅主题变更事件
                if (_themeService != null)
                    _themeService.ThemeChanged += OnThemeChanged;

                // 订阅窗口事件
                if (_windowManager != null)
                {
                    _windowManager.WindowOpened += OnWindowOpened;
                    _windowManager.WindowClosed += OnWindowClosed;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"事件订阅失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 安全加载可用主题
        /// </summary>
        private void LoadAvailableThemesSafely()
        {
            AvailableThemes.Clear();
            try
            {
                if (_themeService?.AvailableThemes != null)
                {
                    foreach (var theme in _themeService.AvailableThemes)
                    {
                        AvailableThemes.Add(theme);
                    }
                }
                else
                {
                    // 添加默认主题
                    AvailableThemes.Add("Light");
                    AvailableThemes.Add("Dark");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"加载主题失败: {ex.Message}");
                // 添加默认主题
                AvailableThemes.Add("Light");
                AvailableThemes.Add("Dark");
            }
        }

        /// <summary>
        /// 创建设备管理器页面
        /// </summary>
        private FrameworkElement CreateDeviceManagerPage()
        {
            return null;
            //try
            //{
            //    // 使用McLaser.Device库中的标准设备管理器控件
            //    var deviceManagerControl = new McLaser.Device.Views.DeviceManagerControl();

            //    _logger?.LogInfo("设备管理器页面已创建，使用McLaser.Device标准控件");
            //    return deviceManagerControl;
            //}
            //catch (Exception ex)
            //{
            //    _logger?.LogError($"创建设备管理器页面失败: {ex.Message}");

            //    // 如果标准控件创建失败，使用简化版本作为备用方案
            //    return CreateFallbackDeviceManagerPage();
            //}
        }

        /// <summary>
        /// 创建备用的简化设备管理器页面
        /// </summary>
        private FrameworkElement CreateFallbackDeviceManagerPage()
        {
            try
            {

                // 创建简化的设备管理器界面
                var grid = new System.Windows.Controls.Grid();
                grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = System.Windows.GridLength.Auto });
                grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = new System.Windows.GridLength(1, System.Windows.GridUnitType.Star) });
                grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = System.Windows.GridLength.Auto });

                // 标题栏
                var titleBorder = new System.Windows.Controls.Border
                {
                    Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(45, 55, 72)),
                    Padding = new Thickness(15, 10,0,0)
                };
                System.Windows.Controls.Grid.SetRow(titleBorder, 0);

                var titlePanel = new System.Windows.Controls.StackPanel { Orientation = System.Windows.Controls.Orientation.Horizontal };
                titlePanel.Children.Add(new System.Windows.Controls.TextBlock
                {
                    Text = "⚙",
                    FontSize = 20,
                    Foreground = System.Windows.Media.Brushes.White,
                    VerticalAlignment = System.Windows.VerticalAlignment.Center,
                    Margin = new System.Windows.Thickness(0, 0, 10, 0)
                });
                titlePanel.Children.Add(new System.Windows.Controls.TextBlock
                {
                    Text = "设备管理器",
                    FontSize = 16,
                    FontWeight = System.Windows.FontWeights.Bold,
                    Foreground = System.Windows.Media.Brushes.White,
                    VerticalAlignment = System.Windows.VerticalAlignment.Center
                });
                titlePanel.Children.Add(new System.Windows.Controls.TextBlock
                {
                    Text = " - McLaser.Device 标准功能",
                    FontSize = 12,
                    Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(204, 204, 204)),
                    VerticalAlignment = System.Windows.VerticalAlignment.Center,
                    Margin = new System.Windows.Thickness(5, 0, 0, 0)
                });
                titleBorder.Child = titlePanel;
                grid.Children.Add(titleBorder);

                // 主内容区域
                var contentBorder = new System.Windows.Controls.Border
                {
                    Background = System.Windows.Media.Brushes.White,
                    Margin = new System.Windows.Thickness(5),
                    Padding = new System.Windows.Thickness(20)
                };
                System.Windows.Controls.Grid.SetRow(contentBorder, 1);

                var contentPanel = new System.Windows.Controls.StackPanel();

                // 初始化按钮
                var initButton = new System.Windows.Controls.Button
                {
                    Content = "初始化设备管理器",
                    Padding = new System.Windows.Thickness(20, 10,0,0),
                    Margin = new System.Windows.Thickness(0, 0, 0, 20),
                    HorizontalAlignment = System.Windows.HorizontalAlignment.Left
                };
                initButton.Click += (s, e) =>
                {
                    try
                    {
                        // 暂时模拟初始化过程
                        var statusText = contentPanel.Children.OfType<System.Windows.Controls.TextBlock>()
                            .FirstOrDefault(t => t.Name == "StatusText");
                        if (statusText != null)
                        {
                            statusText.Text = "设备管理器初始化成功（模拟）";
                            statusText.Foreground = System.Windows.Media.Brushes.Green;
                        }
                    }
                    catch (Exception ex)
                    {
                        var statusText = contentPanel.Children.OfType<System.Windows.Controls.TextBlock>()
                            .FirstOrDefault(t => t.Name == "StatusText");
                        if (statusText != null)
                        {
                            statusText.Text = $"初始化异常: {ex.Message}";
                            statusText.Foreground = System.Windows.Media.Brushes.Red;
                        }
                    }
                };
                contentPanel.Children.Add(initButton);

                // 状态显示
                var statusText = new System.Windows.Controls.TextBlock
                {
                    Name = "StatusText",
                    Text = "设备管理器未初始化",
                    FontSize = 14,
                    Margin = new System.Windows.Thickness(0, 0, 0, 20),
                    Foreground = System.Windows.Media.Brushes.Gray
                };
                contentPanel.Children.Add(statusText);

                // 设备信息
                var infoText = new System.Windows.Controls.TextBlock
                {
                    Text = "McLaser.Device 设备管理器提供以下功能：\n\n" +
                           "• 设备搜索和发现\n" +
                           "• 设备连接和断开\n" +
                           "• 设备状态监控\n" +
                           "• 设备配置管理\n" +
                           "• 支持相机、运动控制卡、激光器、传感器等设备类型\n\n" +
                           "注意：当前为简化版本，完整功能需要修复McLaser.Device项目的XAML编译问题。",
                    FontSize = 12,
                    TextWrapping = System.Windows.TextWrapping.Wrap,
                    LineHeight = 20
                };
                contentPanel.Children.Add(infoText);

                contentBorder.Child = contentPanel;
                grid.Children.Add(contentBorder);

                // 状态栏
                var statusBorder = new System.Windows.Controls.Border
                {
                    Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(240, 240, 240)),
                    Padding = new System.Windows.Thickness(10, 5, 0, 0  )
                };
                System.Windows.Controls.Grid.SetRow(statusBorder, 2);

                var statusPanel = new System.Windows.Controls.StackPanel { Orientation = System.Windows.Controls.Orientation.Horizontal };
                statusPanel.Children.Add(new System.Windows.Controls.TextBlock
                {
                    Text = "设备管理器页面 - 简化版本（等待XAML编译问题修复）",
                    FontSize = 12
                });
                statusBorder.Child = statusPanel;
                grid.Children.Add(statusBorder);

                _logger?.LogInfo("设备管理器页面已创建，使用简化界面（备用方案）");
                return grid;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"创建备用设备管理器页面失败: {ex.Message}");
                return new System.Windows.Controls.TextBlock
                {
                    Text = $"设备管理器页面加载失败: {ex.Message}",
                    HorizontalAlignment = System.Windows.HorizontalAlignment.Center,
                    VerticalAlignment = System.Windows.VerticalAlignment.Center,
                    FontSize = 16,
                    Foreground = System.Windows.Media.Brushes.Red
                };
            }
        }

        /// <summary>
        /// 创建设备状态页面
        /// </summary>
        private FrameworkElement CreateDeviceStatusPage()
        {
            try
            {
                // 创建一个简单的设备状态页面
                var grid = new System.Windows.Controls.Grid();
                grid.Margin = new Thickness(20);

                // 添加行定义
                grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = GridLength.Auto });
                grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = new GridLength(1, GridUnitType.Star) });

                // 标题
                var title = new System.Windows.Controls.TextBlock
                {
                    Text = "设备状态监控",
                    FontSize = 24,
                    FontWeight = System.Windows.FontWeights.Bold,
                    HorizontalAlignment = System.Windows.HorizontalAlignment.Center,
                    Margin = new Thickness(0, 0, 0, 20)
                };
                System.Windows.Controls.Grid.SetRow(title, 0);
                grid.Children.Add(title);

                // 内容区域
                var content = new System.Windows.Controls.TextBlock
                {
                    Text = "设备状态页面\n\n这里将显示所有设备的实时状态信息。\n\n功能包括：\n• 设备在线状态\n• 设备运行参数\n• 设备报警信息\n• 设备性能统计",
                    FontSize = 16,
                    HorizontalAlignment = System.Windows.HorizontalAlignment.Center,
                    VerticalAlignment = System.Windows.VerticalAlignment.Center,
                    TextAlignment = System.Windows.TextAlignment.Center
                };
                System.Windows.Controls.Grid.SetRow(content, 1);
                grid.Children.Add(content);

                return grid;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"创建设备状态页面失败: {ex.Message}");
                return new System.Windows.Controls.TextBlock { Text = $"设备状态页面加载失败: {ex.Message}" };
            }
        }

        /// <summary>
        /// 创建事件总线演示页面
        /// </summary>
        private FrameworkElement CreateEventBusDemoPage()
        {
            return null;
            //try
            //{
            //    var eventBusDemoWindow = new EventBusDemoWindow();
            //    // 确保窗口已初始化
            //    eventBusDemoWindow.InitializeComponent();

            //    // 获取窗口内容并从窗口中移除
            //    var content = eventBusDemoWindow.Content as FrameworkElement;
            //    if (content != null)
            //    {
            //        eventBusDemoWindow.Content = null; // 从窗口中移除内容
            //        return content;
            //    }

            //    return new System.Windows.Controls.TextBlock { Text = "事件总线演示页面加载失败" };
            //}
            //catch (Exception ex)
            //{
            //    _logger?.LogError($"创建事件总线演示页面失败: {ex.Message}");
            //    return new System.Windows.Controls.TextBlock { Text = $"事件总线演示页面加载失败: {ex.Message}" };
            //}
        }

        /// <summary>
        /// 创建异常处理演示页面
        /// </summary>
        private FrameworkElement CreateExceptionDemoPage()
        {
            return null;
            //try
            //{
            //    var exceptionDemoWindow = new ExceptionDemoWindow();
            //    // 确保窗口已初始化
            //    exceptionDemoWindow.InitializeComponent();

            //    // 获取窗口内容并从窗口中移除
            //    var content = exceptionDemoWindow.Content as FrameworkElement;
            //    if (content != null)
            //    {
            //        exceptionDemoWindow.Content = null; // 从窗口中移除内容
            //        return content;
            //    }

            //    return new System.Windows.Controls.TextBlock { Text = "异常处理演示页面加载失败" };
            //}
            //catch (Exception ex)
            //{
            //    _logger?.LogError($"创建异常处理演示页面失败: {ex.Message}");
            //    return new System.Windows.Controls.TextBlock { Text = $"异常处理演示页面加载失败: {ex.Message}" };
            //}
        }

        /// <summary>
        /// 创建插件管理演示页面
        /// </summary>
        private FrameworkElement CreatePluginDemoPage()
        {
            return null;
            //try
            //{
            //    var pluginDemoWindow = new PluginDemoWindow();
            //    // 确保窗口已初始化
            //    pluginDemoWindow.InitializeComponent();

            //    // 获取窗口内容并从窗口中移除
            //    var content = pluginDemoWindow.Content as FrameworkElement;
            //    if (content != null)
            //    {
            //        pluginDemoWindow.Content = null; // 从窗口中移除内容
            //        return content;
            //    }

            //    return new System.Windows.Controls.TextBlock { Text = "插件管理演示页面加载失败" };
            //}
            //catch (Exception ex)
            //{
            //    _logger?.LogError($"创建插件管理演示页面失败: {ex.Message}");
            //    return new System.Windows.Controls.TextBlock { Text = $"插件管理演示页面加载失败: {ex.Message}" };
            //}
        }

        /// <summary>
        /// 创建数据输入页面
        /// </summary>
        private FrameworkElement CreateDataInputPage()
        {
            return null;
            //try
            //{
            //    var dataInputWindow = new DataInputWindow();
            //    // 确保窗口已初始化
            //    dataInputWindow.InitializeComponent();

            //    // 获取窗口内容并从窗口中移除
            //    var content = dataInputWindow.Content as FrameworkElement;
            //    if (content != null)
            //    {
            //        dataInputWindow.Content = null; // 从窗口中移除内容
            //        return content;
            //    }

            //    return new System.Windows.Controls.TextBlock { Text = "数据输入页面加载失败" };
            //}
            //catch (Exception ex)
            //{
            //    _logger?.LogError($"创建数据输入页面失败: {ex.Message}");
            //    return new System.Windows.Controls.TextBlock { Text = $"数据输入页面加载失败: {ex.Message}" };
            //}
        }

        /// <summary>
        /// 创建设置页面
        /// </summary>
        private FrameworkElement CreateSettingsPage()
        {
            return null;
            //try
            //{
            //    var settingsWindow = new SettingsWindow();
            //    // 确保窗口已初始化
            //    settingsWindow.InitializeComponent();

            //    // 获取窗口内容并从窗口中移除
            //    var content = settingsWindow.Content as FrameworkElement;
            //    if (content != null)
            //    {
            //        settingsWindow.Content = null; // 从窗口中移除内容
            //        return content;
            //    }

            //    return new System.Windows.Controls.TextBlock { Text = "设置页面加载失败" };
            //}
            //catch (Exception ex)
            //{
            //    _logger?.LogError($"创建设置页面失败: {ex.Message}");
            //    return new System.Windows.Controls.TextBlock { Text = $"设置页面加载失败: {ex.Message}" };
            //}
        }

        #endregion

        #region 清理

        /// <summary>
        /// 清理资源
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // 取消事件订阅
                _themeService.ThemeChanged -= OnThemeChanged;
                _windowManager.WindowOpened -= OnWindowOpened;
                _windowManager.WindowClosed -= OnWindowClosed;
                _navigationService.PageChanged -= OnNavigationPageChanged;

                // 清理导航ViewModel
                NavigationViewModel?.Dispose();
            }

            base.Dispose(disposing);
        }

        #endregion
    }
}
