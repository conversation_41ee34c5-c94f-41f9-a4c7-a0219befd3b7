//using System;
//using System.Collections.Generic;

//namespace McLaser.Core.Framework.Services
//{
//    /// <summary>
//    /// 导航服务接口
//    /// 提供页面/窗口导航功能
//    /// </summary>
//    public interface INavigationService
//    {
//        /// <summary>
//        /// 当前页面/视图
//        /// </summary>
//        object? CurrentView { get; }

//        /// <summary>
//        /// 是否可以后退
//        /// </summary>
//        bool CanGoBack { get; }

//        /// <summary>
//        /// 是否可以前进
//        /// </summary>
//        bool CanGoForward { get; }

//        /// <summary>
//        /// 导航到指定视图
//        /// </summary>
//        /// <typeparam name="T">视图类型</typeparam>
//        /// <param name="parameter">导航参数</param>
//        void NavigateTo<T>(object? parameter = null) where T : class;

//        /// <summary>
//        /// 导航到指定视图
//        /// </summary>
//        /// <param name="viewType">视图类型</param>
//        /// <param name="parameter">导航参数</param>
//        void NavigateTo(Type viewType, object? parameter = null);

//        /// <summary>
//        /// 导航到指定视图（通过名称）
//        /// </summary>
//        /// <param name="viewName">视图名称</param>
//        /// <param name="parameter">导航参数</param>
//        void NavigateTo(string viewName, object? parameter = null);

//        /// <summary>
//        /// 后退
//        /// </summary>
//        void GoBack();

//        /// <summary>
//        /// 前进
//        /// </summary>
//        void GoForward();

//        /// <summary>
//        /// 清除导航历史
//        /// </summary>
//        void ClearHistory();

//        /// <summary>
//        /// 注册视图
//        /// </summary>
//        /// <typeparam name="TView">视图类型</typeparam>
//        /// <typeparam name="TViewModel">视图模型类型</typeparam>
//        /// <param name="viewName">视图名称</param>
//        void RegisterView<TView, TViewModel>(string? viewName = null)
//            where TView : class
//            where TViewModel : class;

//        /// <summary>
//        /// 注册视图
//        /// </summary>
//        /// <param name="viewType">视图类型</param>
//        /// <param name="viewModelType">视图模型类型</param>
//        /// <param name="viewName">视图名称</param>
//        void RegisterView(Type viewType, Type? viewModelType = null, string? viewName = null);

//        /// <summary>
//        /// 导航事件
//        /// </summary>
//        event EventHandler<NavigationEventArgs>? Navigated;

//        /// <summary>
//        /// 导航前事件
//        /// </summary>
//        event EventHandler<NavigatingEventArgs>? Navigating;
//    }

//    /// <summary>
//    /// 导航事件参数
//    /// </summary>
//    public class NavigationEventArgs : EventArgs
//    {
//        /// <summary>
//        /// 目标视图类型
//        /// </summary>
//        public Type ViewType { get; }

//        /// <summary>
//        /// 导航参数
//        /// </summary>
//        public object? Parameter { get; }

//        /// <summary>
//        /// 源视图类型
//        /// </summary>
//        public Type? SourceViewType { get; }

//        public NavigationEventArgs(Type viewType, object? parameter = null, Type? sourceViewType = null)
//        {
//            ViewType = viewType;
//            Parameter = parameter;
//            SourceViewType = sourceViewType;
//        }
//    }

//    /// <summary>
//    /// 导航前事件参数
//    /// </summary>
//    public class NavigatingEventArgs : EventArgs
//    {
//        /// <summary>
//        /// 目标视图类型
//        /// </summary>
//        public Type ViewType { get; }

//        /// <summary>
//        /// 导航参数
//        /// </summary>
//        public object? Parameter { get; }

//        /// <summary>
//        /// 源视图类型
//        /// </summary>
//        public Type? SourceViewType { get; }

//        /// <summary>
//        /// 是否取消导航
//        /// </summary>
//        public bool Cancel { get; set; }

//        public NavigatingEventArgs(Type viewType, object? parameter = null, Type? sourceViewType = null)
//        {
//            ViewType = viewType;
//            Parameter = parameter;
//            SourceViewType = sourceViewType;
//        }
//    }

//    /// <summary>
//    /// 视图注册信息
//    /// </summary>
//    public class ViewRegistration
//    {
//        /// <summary>
//        /// 视图类型
//        /// </summary>
//        public Type ViewType { get; }

//        /// <summary>
//        /// 视图模型类型
//        /// </summary>
//        public Type? ViewModelType { get; }

//        /// <summary>
//        /// 视图名称
//        /// </summary>
//        public string ViewName { get; }

//        public ViewRegistration(Type viewType, Type? viewModelType = null, string? viewName = null)
//        {
//            ViewType = viewType;
//            ViewModelType = viewModelType;
//            ViewName = viewName ?? viewType.Name;
//        }
//    }
//}
