using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace McLaser.Devices
{
    /// <summary>
    /// 布尔值转可见性转换器
    /// </summary>
    public class BoolToVisibilityConverter : IValueConverter
    {
        /// <summary>
        /// 转换布尔值为可见性
        /// </summary>
        /// <param name="value">布尔值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">参数，可选，格式为"Visible:Collapsed"或"Visible:Hidden"</param>
        /// <param name="culture">区域信息</param>
        /// <returns>可见性</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (!(value is bool))
                return DependencyProperty.UnsetValue;

            bool boolValue = (bool)value;
            
            // 默认值
            Visibility trueVisibility = Visibility.Visible;
            Visibility falseVisibility = Visibility.Collapsed;
            
            // 解析参数
            if (parameter is string)
            {
                string param = parameter as string;
                string[] parts = param.Split(':');
                
                if (parts.Length == 2)
                {
                    // 解析True对应的可见性
                    if (parts[0].Equals("Visible", StringComparison.OrdinalIgnoreCase))
                        trueVisibility = Visibility.Visible;
                    else if (parts[0].Equals("Collapsed", StringComparison.OrdinalIgnoreCase))
                        trueVisibility = Visibility.Collapsed;
                    else if (parts[0].Equals("Hidden", StringComparison.OrdinalIgnoreCase))
                        trueVisibility = Visibility.Hidden;
                    
                    // 解析False对应的可见性
                    if (parts[1].Equals("Visible", StringComparison.OrdinalIgnoreCase))
                        falseVisibility = Visibility.Visible;
                    else if (parts[1].Equals("Collapsed", StringComparison.OrdinalIgnoreCase))
                        falseVisibility = Visibility.Collapsed;
                    else if (parts[1].Equals("Hidden", StringComparison.OrdinalIgnoreCase))
                        falseVisibility = Visibility.Hidden;
                }
            }
            
            return boolValue ? trueVisibility : falseVisibility;
        }

        /// <summary>
        /// 转换可见性为布尔值
        /// </summary>
        /// <param name="value">可见性</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">参数</param>
        /// <param name="culture">区域信息</param>
        /// <returns>布尔值</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (!(value is Visibility))
                return DependencyProperty.UnsetValue;
                
            Visibility visibility = (Visibility)value;
            return visibility == Visibility.Visible;
        }
    }
} 