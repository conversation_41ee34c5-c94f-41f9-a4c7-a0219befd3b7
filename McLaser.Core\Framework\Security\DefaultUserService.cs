using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using McLaser.Core.Framework.Logging;

namespace McLaser.Core.Framework.Security
{
    /// <summary>
    /// 默认用户服务实现
    /// 提供完整的用户管理和认证功能
    /// </summary>
    public class DefaultUserService : IUserService
    {
        #region 字段和属性

        private readonly ILogger? _logger;
        private readonly ConcurrentDictionary<string, User> _users;
        private readonly ConcurrentDictionary<string, string> _userPasswords; // userId -> hashedPassword
        private readonly ConcurrentDictionary<string, UserSession> _sessions;
        private User? _currentUser;
        private UserSession? _currentSession;

        /// <summary>
        /// 当前登录用户
        /// </summary>
        public User? CurrentUser => _currentUser;

        /// <summary>
        /// 是否已登录
        /// </summary>
        public bool IsAuthenticated => _currentUser != null && IsSessionValid();

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志服务（可选）</param>
        public DefaultUserService(ILogger? logger = null)
        {
            _logger = logger;
            _users = new ConcurrentDictionary<string, User>();
            _userPasswords = new ConcurrentDictionary<string, string>();
            _sessions = new ConcurrentDictionary<string, UserSession>();

            // 初始化默认用户
            InitializeDefaultUsers();

            _logger?.LogInfo("用户服务已初始化");
        }

        #endregion

        #region 初始化

        /// <summary>
        /// 初始化默认用户
        /// </summary>
        private void InitializeDefaultUsers()
        {
            try
            {
                // 创建管理员用户
                var adminUser = new User
                {
                    Id = "admin-001",
                    Username = "admin",
                    Email = "<EMAIL>",
                    DisplayName = "系统管理员",
                    FirstName = "系统",
                    LastName = "管理员",
                    IsEnabled = true,
                    EmailConfirmed = true,
                    RoleIds = new List<string> { "admin" }
                };

                var adminResult = CreateUserAsync(adminUser, "admin123").Result;
                if (adminResult.Success)
                {
                    _logger?.LogInfo("默认管理员用户已创建");
                }

                // 创建普通用户
                var normalUser = new User
                {
                    Id = "user-001",
                    Username = "user",
                    Email = "<EMAIL>",
                    DisplayName = "普通用户",
                    FirstName = "普通",
                    LastName = "用户",
                    IsEnabled = true,
                    EmailConfirmed = true,
                    RoleIds = new List<string> { "user" }
                };

                var userResult = CreateUserAsync(normalUser, "user123").Result;
                if (userResult.Success)
                {
                    _logger?.LogInfo("默认普通用户已创建");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("初始化默认用户失败", ex);
            }
        }

        #endregion

        #region 用户管理

        /// <summary>
        /// 创建用户
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <param name="password">密码</param>
        /// <returns>创建结果</returns>
        public async Task<UserOperationResult> CreateUserAsync(User user, string password)
        {
            if (user == null)
                return UserOperationResult.CreateFailure("用户信息不能为空");

            if (string.IsNullOrWhiteSpace(password))
                return UserOperationResult.CreateFailure("密码不能为空");

            try
            {
                // 检查用户名是否已存在
                if (_users.Values.Any(u => u.Username.Equals(user.Username, StringComparison.OrdinalIgnoreCase)))
                {
                    return UserOperationResult.CreateFailure("用户名已存在", "USERNAME_EXISTS");
                }

                // 检查邮箱是否已存在
                if (_users.Values.Any(u => u.Email.Equals(user.Email, StringComparison.OrdinalIgnoreCase)))
                {
                    return UserOperationResult.CreateFailure("邮箱已存在", "EMAIL_EXISTS");
                }

                // 生成用户ID（如果未提供）
                if (string.IsNullOrEmpty(user.Id))
                {
                    user.Id = Guid.NewGuid().ToString();
                }

                // 哈希密码
                var hashedPassword = HashPassword(password);

                // 保存用户
                user.CreatedAt = DateTime.UtcNow;
                _users.TryAdd(user.Id, user);
                _userPasswords.TryAdd(user.Id, hashedPassword);

                // 触发用户创建事件
                UserCreated?.Invoke(this, new UserCreatedEventArgs(user));

                _logger?.LogInfo($"用户已创建: {user.Username}");
                return UserOperationResult.CreateSuccess(user);
            }
            catch (Exception ex)
            {
                _logger?.LogError($"创建用户失败: {user?.Username}", ex);
                return UserOperationResult.CreateFailure($"创建用户失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新用户信息
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <returns>更新结果</returns>
        public async Task<UserOperationResult> UpdateUserAsync(User user)
        {
            if (user == null)
                return UserOperationResult.CreateFailure("用户信息不能为空");

            try
            {
                if (!_users.ContainsKey(user.Id))
                {
                    return UserOperationResult.CreateFailure("用户不存在", "USER_NOT_FOUND");
                }

                // 检查用户名冲突（排除自己）
                if (_users.Values.Any(u => u.Id != user.Id && u.Username.Equals(user.Username, StringComparison.OrdinalIgnoreCase)))
                {
                    return UserOperationResult.CreateFailure("用户名已存在", "USERNAME_EXISTS");
                }

                // 检查邮箱冲突（排除自己）
                if (_users.Values.Any(u => u.Id != user.Id && u.Email.Equals(user.Email, StringComparison.OrdinalIgnoreCase)))
                {
                    return UserOperationResult.CreateFailure("邮箱已存在", "EMAIL_EXISTS");
                }

                user.UpdatedAt = DateTime.UtcNow;
                _users.TryUpdate(user.Id, user, _users[user.Id]);

                // 触发用户更新事件
                UserUpdated?.Invoke(this, new UserUpdatedEventArgs(user));

                _logger?.LogInfo($"用户信息已更新: {user.Username}");
                return UserOperationResult.CreateSuccess(user);
            }
            catch (Exception ex)
            {
                _logger?.LogError($"更新用户失败: {user?.Username}", ex);
                return UserOperationResult.CreateFailure($"更新用户失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 删除用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>删除结果</returns>
        public async Task<UserOperationResult> DeleteUserAsync(string userId)
        {
            if (string.IsNullOrWhiteSpace(userId))
                return UserOperationResult.CreateFailure("用户ID不能为空");

            try
            {
                if (!_users.TryRemove(userId, out var user))
                {
                    return UserOperationResult.CreateFailure("用户不存在", "USER_NOT_FOUND");
                }

                // 删除密码
                _userPasswords.TryRemove(userId, out _);

                // 删除相关会话
                var userSessions = _sessions.Values.Where(s => s.UserId == userId).ToList();
                foreach (var session in userSessions)
                {
                    _sessions.TryRemove(session.Id, out _);
                }

                // 触发用户删除事件
                UserDeleted?.Invoke(this, new UserDeletedEventArgs(user.Id, user.Username));

                _logger?.LogInfo($"用户已删除: {user.Username}");
                return UserOperationResult.CreateSuccess();
            }
            catch (Exception ex)
            {
                _logger?.LogError($"删除用户失败: {userId}", ex);
                return UserOperationResult.CreateFailure($"删除用户失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 根据ID获取用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户信息</returns>
        public async Task<User?> GetUserByIdAsync(string userId)
        {
            if (string.IsNullOrWhiteSpace(userId))
                return null;

            _users.TryGetValue(userId, out var user);
            return user;
        }

        /// <summary>
        /// 根据用户名获取用户
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>用户信息</returns>
        public async Task<User?> GetUserByUsernameAsync(string username)
        {
            if (string.IsNullOrWhiteSpace(username))
                return null;

            return _users.Values.FirstOrDefault(u => u.Username.Equals(username, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 获取所有用户
        /// </summary>
        /// <returns>用户列表</returns>
        public async Task<IEnumerable<User>> GetAllUsersAsync()
        {
            return _users.Values.ToList();
        }

        /// <summary>
        /// 搜索用户
        /// </summary>
        /// <param name="searchTerm">搜索条件</param>
        /// <returns>用户列表</returns>
        public async Task<IEnumerable<User>> SearchUsersAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllUsersAsync();

            var term = searchTerm.ToLowerInvariant();
            return _users.Values.Where(u =>
                u.Username.ToLowerInvariant().Contains(term) ||
                u.Email.ToLowerInvariant().Contains(term) ||
                (u.DisplayName?.ToLowerInvariant().Contains(term) ?? false) ||
                (u.FirstName?.ToLowerInvariant().Contains(term) ?? false) ||
                (u.LastName?.ToLowerInvariant().Contains(term) ?? false)
            ).ToList();
        }

        #endregion

        #region 认证功能

        /// <summary>
        /// 用户登录
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        /// <returns>登录结果</returns>
        public async Task<AuthenticationResult> LoginAsync(string username, string password)
        {
            if (string.IsNullOrWhiteSpace(username))
                return AuthenticationResult.CreateFailure("用户名不能为空", "INVALID_USERNAME");

            if (string.IsNullOrWhiteSpace(password))
                return AuthenticationResult.CreateFailure("密码不能为空", "INVALID_PASSWORD");

            try
            {
                var user = await GetUserByUsernameAsync(username);
                if (user == null)
                {
                    _logger?.LogWarning($"登录失败：用户不存在 - {username}");
                    return AuthenticationResult.CreateFailure("用户名或密码错误", "INVALID_CREDENTIALS");
                }

                if (!user.IsEnabled)
                {
                    _logger?.LogWarning($"登录失败：用户已禁用 - {username}");
                    return AuthenticationResult.CreateFailure("用户已被禁用", "USER_DISABLED");
                }

                if (user.IsLocked && user.LockoutEnd > DateTime.UtcNow)
                {
                    _logger?.LogWarning($"登录失败：用户已锁定 - {username}");
                    return AuthenticationResult.CreateFailure("用户已被锁定", "USER_LOCKED");
                }

                // 验证密码
                if (!_userPasswords.TryGetValue(user.Id, out var hashedPassword) ||
                    !VerifyPassword(password, hashedPassword))
                {
                    // 增加失败次数
                    user.AccessFailedCount++;
                    if (user.AccessFailedCount >= 5)
                    {
                        user.IsLocked = true;
                        user.LockoutEnd = DateTime.UtcNow.AddMinutes(30);
                        _logger?.LogWarning($"用户因多次登录失败被锁定 - {username}");
                    }

                    _logger?.LogWarning($"登录失败：密码错误 - {username}");
                    return AuthenticationResult.CreateFailure("用户名或密码错误", "INVALID_CREDENTIALS");
                }

                // 重置失败次数和锁定状态
                user.AccessFailedCount = 0;
                user.IsLocked = false;
                user.LockoutEnd = null;
                user.LastLoginAt = DateTime.UtcNow;
                user.LastActivityAt = DateTime.UtcNow;

                // 创建会话
                var session = CreateSession(user);
                _currentUser = user;
                _currentSession = session;

                // 触发登录事件
                UserLoggedIn?.Invoke(this, new UserLoginEventArgs(user, session, DateTime.UtcNow));

                _logger?.LogInfo($"用户登录成功: {username}");
                return AuthenticationResult.CreateSuccess(user, session);
            }
            catch (Exception ex)
            {
                _logger?.LogError($"登录过程中发生错误: {username}", ex);
                return AuthenticationResult.CreateFailure($"登录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 用户登出
        /// </summary>
        /// <returns>登出结果</returns>
        public async Task<bool> LogoutAsync()
        {
            try
            {
                if (_currentUser != null && _currentSession != null)
                {
                    // 使会话失效
                    _currentSession.IsActive = false;
                    _sessions.TryUpdate(_currentSession.Id, _currentSession, _currentSession);

                    // 触发登出事件
                    UserLoggedOut?.Invoke(this, new UserLogoutEventArgs(_currentUser, _currentSession, DateTime.UtcNow));

                    _logger?.LogInfo($"用户登出: {_currentUser.Username}");

                    _currentUser = null;
                    _currentSession = null;
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger?.LogError("用户登出失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="oldPassword">旧密码</param>
        /// <param name="newPassword">新密码</param>
        /// <returns>修改结果</returns>
        public async Task<UserOperationResult> ChangePasswordAsync(string userId, string oldPassword, string newPassword)
        {
            if (string.IsNullOrWhiteSpace(userId))
                return UserOperationResult.CreateFailure("用户ID不能为空");

            if (string.IsNullOrWhiteSpace(oldPassword))
                return UserOperationResult.CreateFailure("旧密码不能为空");

            if (string.IsNullOrWhiteSpace(newPassword))
                return UserOperationResult.CreateFailure("新密码不能为空");

            try
            {
                var user = await GetUserByIdAsync(userId);
                if (user == null)
                {
                    return UserOperationResult.CreateFailure("用户不存在", "USER_NOT_FOUND");
                }

                // 验证旧密码
                if (!_userPasswords.TryGetValue(userId, out var currentHashedPassword) ||
                    !VerifyPassword(oldPassword, currentHashedPassword))
                {
                    return UserOperationResult.CreateFailure("旧密码错误", "INVALID_OLD_PASSWORD");
                }

                // 设置新密码
                var newHashedPassword = HashPassword(newPassword);
                _userPasswords.TryUpdate(userId, newHashedPassword, currentHashedPassword);

                // 触发密码变更事件
                PasswordChanged?.Invoke(this, new PasswordChangedEventArgs(user.Id));

                _logger?.LogInfo($"用户密码已修改: {user.Username}");
                return UserOperationResult.CreateSuccess();
            }
            catch (Exception ex)
            {
                _logger?.LogError($"修改密码失败: {userId}", ex);
                return UserOperationResult.CreateFailure($"修改密码失败: {ex.Message}");
            }
        }

        #endregion

        #region 密码工具

        /// <summary>
        /// 哈希密码
        /// </summary>
        /// <param name="password">明文密码</param>
        /// <returns>哈希后的密码</returns>
        private string HashPassword(string password)
        {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "McLaser_Salt"));
            return Convert.ToBase64String(hashedBytes);
        }

        /// <summary>
        /// 验证密码
        /// </summary>
        /// <param name="password">明文密码</param>
        /// <param name="hashedPassword">哈希密码</param>
        /// <returns>是否匹配</returns>
        private bool VerifyPassword(string password, string hashedPassword)
        {
            var inputHash = HashPassword(password);
            return inputHash.Equals(hashedPassword, StringComparison.Ordinal);
        }

        /// <summary>
        /// 重置密码
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="newPassword">新密码</param>
        /// <returns>重置结果</returns>
        public async Task<UserOperationResult> ResetPasswordAsync(string userId, string newPassword)
        {
            if (string.IsNullOrWhiteSpace(userId))
                return UserOperationResult.CreateFailure("用户ID不能为空");

            if (string.IsNullOrWhiteSpace(newPassword))
                return UserOperationResult.CreateFailure("新密码不能为空");

            try
            {
                var user = await GetUserByIdAsync(userId);
                if (user == null)
                {
                    return UserOperationResult.CreateFailure("用户不存在", "USER_NOT_FOUND");
                }

                // 设置新密码
                var newHashedPassword = HashPassword(newPassword);
                _userPasswords.AddOrUpdate(userId, newHashedPassword, (key, oldValue) => newHashedPassword);

                // 重置失败次数和锁定状态
                user.AccessFailedCount = 0;
                user.IsLocked = false;
                user.LockoutEnd = null;

                // 触发密码变更事件
                PasswordChanged?.Invoke(this, new PasswordChangedEventArgs(user.Id, null, null, true));

                _logger?.LogInfo($"用户密码已重置: {user.Username}");
                return UserOperationResult.CreateSuccess();
            }
            catch (Exception ex)
            {
                _logger?.LogError($"重置密码失败: {userId}", ex);
                return UserOperationResult.CreateFailure($"重置密码失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证密码
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="password">密码</param>
        /// <returns>验证结果</returns>
        public async Task<bool> ValidatePasswordAsync(string userId, string password)
        {
            if (string.IsNullOrWhiteSpace(userId) || string.IsNullOrWhiteSpace(password))
                return false;

            try
            {
                if (_userPasswords.TryGetValue(userId, out var hashedPassword))
                {
                    return VerifyPassword(password, hashedPassword);
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"验证密码失败: {userId}", ex);
                return false;
            }
        }

        #endregion

        #region 会话管理

        /// <summary>
        /// 获取当前会话信息
        /// </summary>
        /// <returns>会话信息</returns>
        public UserSession? GetCurrentSession()
        {
            return _currentSession;
        }

        /// <summary>
        /// 刷新会话
        /// </summary>
        /// <returns>刷新结果</returns>
        public async Task<bool> RefreshSessionAsync()
        {
            try
            {
                if (_currentSession != null && _currentSession.IsValid)
                {
                    _currentSession.LastAccessedAt = DateTime.UtcNow;
                    _currentSession.ExpiresAt = DateTime.UtcNow.AddHours(8); // 延长8小时

                    _sessions.TryUpdate(_currentSession.Id, _currentSession, _currentSession);

                    _logger?.LogInfo($"会话已刷新: {_currentSession.Id}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger?.LogError("刷新会话失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 检查会话是否有效
        /// </summary>
        /// <returns>是否有效</returns>
        public bool IsSessionValid()
        {
            return _currentSession?.IsValid ?? false;
        }

        /// <summary>
        /// 获取会话剩余时间
        /// </summary>
        /// <returns>剩余时间</returns>
        public TimeSpan GetSessionRemainingTime()
        {
            return _currentSession?.RemainingTime ?? TimeSpan.Zero;
        }

        /// <summary>
        /// 创建会话
        /// </summary>
        /// <param name="user">用户</param>
        /// <returns>会话</returns>
        private UserSession CreateSession(User user)
        {
            var session = new UserSession
            {
                Id = Guid.NewGuid().ToString(),
                UserId = user.Id,
                Token = GenerateToken(),
                RefreshToken = GenerateToken(),
                CreatedAt = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.AddHours(8),
                LastAccessedAt = DateTime.UtcNow,
                IsActive = true
            };

            _sessions.TryAdd(session.Id, session);
            return session;
        }

        /// <summary>
        /// 生成令牌
        /// </summary>
        /// <returns>令牌</returns>
        private string GenerateToken()
        {
            return Guid.NewGuid().ToString("N") + DateTime.UtcNow.Ticks.ToString("X");
        }

        #endregion

        #region 用户状态

        /// <summary>
        /// 启用用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>操作结果</returns>
        public async Task<UserOperationResult> EnableUserAsync(string userId)
        {
            if (string.IsNullOrWhiteSpace(userId))
                return UserOperationResult.CreateFailure("用户ID不能为空");

            try
            {
                var user = await GetUserByIdAsync(userId);
                if (user == null)
                {
                    return UserOperationResult.CreateFailure("用户不存在", "USER_NOT_FOUND");
                }

                user.IsEnabled = true;
                user.UpdatedAt = DateTime.UtcNow;

                _logger?.LogInfo($"用户已启用: {user.Username}");
                return UserOperationResult.CreateSuccess();
            }
            catch (Exception ex)
            {
                _logger?.LogError($"启用用户失败: {userId}", ex);
                return UserOperationResult.CreateFailure($"启用用户失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 禁用用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>操作结果</returns>
        public async Task<UserOperationResult> DisableUserAsync(string userId)
        {
            if (string.IsNullOrWhiteSpace(userId))
                return UserOperationResult.CreateFailure("用户ID不能为空");

            try
            {
                var user = await GetUserByIdAsync(userId);
                if (user == null)
                {
                    return UserOperationResult.CreateFailure("用户不存在", "USER_NOT_FOUND");
                }

                user.IsEnabled = false;
                user.UpdatedAt = DateTime.UtcNow;

                // 使该用户的所有会话失效
                var userSessions = _sessions.Values.Where(s => s.UserId == userId).ToList();
                foreach (var session in userSessions)
                {
                    session.IsActive = false;
                    _sessions.TryUpdate(session.Id, session, session);
                }

                _logger?.LogInfo($"用户已禁用: {user.Username}");
                return UserOperationResult.CreateSuccess();
            }
            catch (Exception ex)
            {
                _logger?.LogError($"禁用用户失败: {userId}", ex);
                return UserOperationResult.CreateFailure($"禁用用户失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 锁定用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="lockoutEnd">锁定结束时间</param>
        /// <returns>操作结果</returns>
        public async Task<UserOperationResult> LockUserAsync(string userId, DateTime? lockoutEnd = null)
        {
            if (string.IsNullOrWhiteSpace(userId))
                return UserOperationResult.CreateFailure("用户ID不能为空");

            try
            {
                var user = await GetUserByIdAsync(userId);
                if (user == null)
                {
                    return UserOperationResult.CreateFailure("用户不存在", "USER_NOT_FOUND");
                }

                user.IsLocked = true;
                user.LockoutEnd = lockoutEnd ?? DateTime.UtcNow.AddHours(24);
                user.UpdatedAt = DateTime.UtcNow;

                // 使该用户的所有会话失效
                var userSessions = _sessions.Values.Where(s => s.UserId == userId).ToList();
                foreach (var session in userSessions)
                {
                    session.IsActive = false;
                    _sessions.TryUpdate(session.Id, session, session);
                }

                _logger?.LogInfo($"用户已锁定: {user.Username}, 锁定至: {user.LockoutEnd}");
                return UserOperationResult.CreateSuccess();
            }
            catch (Exception ex)
            {
                _logger?.LogError($"锁定用户失败: {userId}", ex);
                return UserOperationResult.CreateFailure($"锁定用户失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 解锁用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>操作结果</returns>
        public async Task<UserOperationResult> UnlockUserAsync(string userId)
        {
            if (string.IsNullOrWhiteSpace(userId))
                return UserOperationResult.CreateFailure("用户ID不能为空");

            try
            {
                var user = await GetUserByIdAsync(userId);
                if (user == null)
                {
                    return UserOperationResult.CreateFailure("用户不存在", "USER_NOT_FOUND");
                }

                user.IsLocked = false;
                user.LockoutEnd = null;
                user.AccessFailedCount = 0;
                user.UpdatedAt = DateTime.UtcNow;

                _logger?.LogInfo($"用户已解锁: {user.Username}");
                return UserOperationResult.CreateSuccess();
            }
            catch (Exception ex)
            {
                _logger?.LogError($"解锁用户失败: {userId}", ex);
                return UserOperationResult.CreateFailure($"解锁用户失败: {ex.Message}");
            }
        }

        #endregion

        #region 事件

        /// <summary>
        /// 用户登录事件
        /// </summary>
        public event EventHandler<UserLoginEventArgs>? UserLoggedIn;

        /// <summary>
        /// 用户登出事件
        /// </summary>
        public event EventHandler<UserLogoutEventArgs>? UserLoggedOut;

        /// <summary>
        /// 用户创建事件
        /// </summary>
        public event EventHandler<UserCreatedEventArgs>? UserCreated;

        /// <summary>
        /// 用户更新事件
        /// </summary>
        public event EventHandler<UserUpdatedEventArgs>? UserUpdated;

        /// <summary>
        /// 用户删除事件
        /// </summary>
        public event EventHandler<UserDeletedEventArgs>? UserDeleted;

        /// <summary>
        /// 密码变更事件
        /// </summary>
        public event EventHandler<PasswordChangedEventArgs>? PasswordChanged;

        #endregion
    }
}
