﻿using HalconDotNet;
using McLaser.Modules.Vision.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace McLaser.Modules.Vision
{
    public class VisionLib
    {
        public delegate int CalImageProcess(HImage image);

        private static string RemoveHalconError(string input)
        {
            // 正则表达式，用于匹配 "HALCON error #" 后跟数字和冒号  
            string pattern = @"^HALCON error #\d+: ?";

            // 使用正则表达式替换匹配到的部分为空字符串  
            string result = Regex.Replace(input, pattern, "").Trim();

            return result;
        }

        // 图像采集 
        //public static bool MV_ImageGrab(CameraBase source, out HImage hImage)
        //{
        //    try
        //    {
        //        hImage = new HImage();
        //        source.GrabImageData(out ImageData imageData);
        //        if (imageData.PixelDataPtr != null && imageData.PixelDataPtr != (IntPtr)0)
        //        {
        //            // 获取图像
        //            hImage = new HImage("byte", (HTuple)source.Width, (HTuple)source.Height, imageData.PixelDataPtr);
        //            return true;
        //        }
        //        else
        //        {
        //            return false;
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        hImage = new HImage();
        //        return false;
        //    }
        //}

        // 打开相机
        //public static void MV_CameraConnect(CameraBase cam)
        //{
        //    //if (cam == null)
        //    //{
        //    //    throw new VisionException(MV_ERR.相机不存在, "相机不存在");
        //    //}
        //    //var device = cam as IDevice;
        //    //if (!device.Open())
        //    //{
        //    //    throw new VisionException(MV_ERR.连接相机错误, "连接相机错误");
        //    //}
        //}

        //// 关闭相机
        //public static void MV_CameraClose(CameraBase cam)
        //{
        //    //if (cam == null)
        //    //{
        //    //    throw new VisionException(MV_ERR.相机不存在, "相机不存在");
        //    //}
        //    //var device = cam as IDevice;
        //    //if (!device.Close())
        //    //{
        //    //    throw new VisionException(MV_ERR.关闭相机错误, "关闭相机错误");
        //    //}
        //}

        // 图像采集 --用于软触发
        //public static void MV_ImageGrab(ICam cam, out HImage hImage)
        //{
        //    hImage = new HImage();
        //    if(((CameraBase)cam).DeviceType == "Dir")
        //    {
        //        hImage.Dispose();
        //        hImage = cam.GrabImage();
        //    }
        //    else
        //    {
        //        cam.GrabImage(out ImageData imageData);
        //        if (imageData.PixelDataPtr != null && imageData.PixelDataPtr != (IntPtr)0)
        //        {
        //            // 获取图像
        //            HImage image = new HImage("byte", (int)imageData.Width, (int)imageData.Height, imageData.PixelDataPtr);
        //            // 旋转图像
        //            PretreatHelp.RotateImage(image, out HImage rotateImage, imageData.RotateImageAngle);
        //            image.Dispose();
        //            // 镜像
        //            PretreatHelp.MirrorImage(rotateImage, out HImage mirrorImage, imageData.MirrorImageType);
        //            rotateImage.Dispose();
        //            // 更新图片
        //            hImage.Dispose();
        //            hImage = mirrorImage;
        //        }
        //        else
        //        {
        //            throw new VisionException(MV_ERR.采集错误, "未采集到图像");
        //        }
        //    }
        //}

        // 用于硬触发
        //public static void MV_ImageGrab(ICam cam, CalImageProcess calImageProcess)
        //{
        //    if (cam != null)
        //    {
        //        cam.GrabImage(out ImageData imageData);
        //        if (imageData.PixelDataPtr != null)
        //        {
        //            // 获取图像
        //            HImage image = new HImage("byte", (int)imageData.Width, (int)imageData.Height, imageData.PixelDataPtr);
        //            // 旋转图像
        //            PretreatHelp.RotateImage(image, out HImage rotateImage, imageData.RotateImageAngle);
        //            image.Dispose();
        //            // 镜像
        //            PretreatHelp.MirrorImage(rotateImage, out HImage mirrorImage, imageData.MirrorImageType);
        //            rotateImage.Dispose();
        //            // 调用回调
        //            calImageProcess(mirrorImage);
        //        }
        //        else
        //        {
        //            throw new VisionException(MV_ERR.采集错误, "未采集到图像");
        //        }
        //    }
        //    else
        //    {
        //        throw new VisionException(MV_ERR.相机不存在, "输入的相机不存在");
        //    }
        //}

        //public static MV_ERR MV_RegisterImageGrabEvent()
        //{

        //    //具体实现
        //}

        #region 图像相关
        public static void MV_GetImageSize(HImage image, out int width, out int height)
        {
            try
            {
                image.GetImageSize(out width, out height);
            }
            catch (Exception ex)
            {
                width = 0; height = 0;
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        #endregion
        #region 预处理
        // 二值化
        public static void MV_Threshold(HImage inImage, int minGray, int maxGray, out HObject outObject)
        {
            try
            {
                HOperatorSet.Threshold(inImage, out outObject, minGray, maxGray);
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 均值二值化
        public static void MV_VarThreshold(HImage inImage, int maskWidth, int maskHeight, double absThreshold, BinCompareType type, out HObject outObject)
        {
            try
            {
                HOperatorSet.VarThreshold(inImage, out outObject, maskWidth, maskHeight, 0.2, absThreshold, REnum.EnumToStr(type));
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 自动二值化
        public static void MV_AutoThreshold(HImage inImage, BinExtractionArea type, out HObject outObject)
        {
            try
            {
                HOperatorSet.AutoThreshold(inImage, out outObject, REnum.EnumToStr(type));
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }

        // 彩色转灰
        public static void MV_TransImage(HImage inImage, out HImage outImage, TransImageType ImageType, TransImageChannel Channel)
        {
            try
            {
                outImage = new HImage();
                HObject Temp1 = new HObject(); Temp1.GenEmptyObj(); Temp1.Dispose();
                HObject Temp2 = new HObject(); Temp2.GenEmptyObj(); Temp2.Dispose();
                HObject Temp3 = new HObject(); Temp3.GenEmptyObj(); Temp3.Dispose();
                HObject Image1 = new HObject(); Image1.GenEmptyObj(); Image1.Dispose();
                HObject Image2 = new HObject(); Image2.GenEmptyObj(); Image2.Dispose();
                HObject Image3 = new HObject(); Image3.GenEmptyObj(); Image3.Dispose();
                switch (ImageType)
                {
                    case TransImageType.通用比例转换:
                    case TransImageType.RGB:
                        HOperatorSet.Decompose3(inImage, out Image1, out Image2, out Image3);
                        break;
                    case TransImageType.HSV:
                        HOperatorSet.Decompose3(inImage, out Temp1, out Temp2, out Temp3);
                        HOperatorSet.TransFromRgb(Temp1, Temp2, Temp3, out Image1, out Image2, out Image3, "hsv");
                        break;
                    case TransImageType.HSI:
                    case TransImageType.YUV:
                        HOperatorSet.Decompose3(inImage, out Temp1, out Temp2, out Temp3);
                        HOperatorSet.TransFromRgb(Temp1, Temp2, Temp3, out Image1, out Image2, out Image3, "hsi");
                        break;
                    default:
                        break;
                }
                switch (Channel)
                {
                    case TransImageChannel.第一通道:
                        outImage = new HImage(Image1);
                        break;
                    case TransImageChannel.第二通道:
                        outImage = new HImage(Image2);
                        break;
                    case TransImageChannel.第三通道:
                        outImage = new HImage(Image3);
                        break;
                    default:
                        break;
                }
                Temp1.Dispose();
                Temp2.Dispose();
                Temp3.Dispose();
                Image1.Dispose();
                Image2.Dispose();
                Image3.Dispose();
            }
            catch (Exception ex)
            {
                outImage = new HImage();
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 图像镜像
        public static void MV_MirrorImage(HImage inImage, out HImage outImage, MirrorImageType W)
        {
            try
            {
                String mode = "";
                switch (W)
                {
                    case MirrorImageType.水平镜像:
                        mode = "column";
                        break;
                    case MirrorImageType.垂直镜像:
                        mode = "row";
                        break;
                    case MirrorImageType.对角镜像:
                        mode = "diagonal";
                        break;
                }
                HOperatorSet.MirrorImage(inImage, out HObject imageMirror, mode);
                outImage = new HImage(imageMirror);
                imageMirror.Dispose();
            }
            catch (Exception ex)
            {
                outImage = new HImage();
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 图像旋转
        public static void MV_RotateImage(HImage inImage, out HImage outImage, RotateImageAngle angle)
        {
            try
            {
                double a = 0;
                switch (angle)
                {
                    case RotateImageAngle._90:
                        a = 90;
                        break;
                    case RotateImageAngle._180:
                        a = 180;
                        break;
                    case RotateImageAngle._270:
                        a = 270;
                        break;
                }
                HOperatorSet.RotateImage(inImage, out HObject imageRotate, a, "constant");
                outImage = new HImage(imageRotate);
                imageRotate.Dispose();
            }
            catch (Exception ex)
            {
                outImage = new HImage();
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 修改图像尺寸
        public static void MV_ChangeFormat(HImage inImage, out HImage outImage, int W, int H)
        {
            try
            {
                HOperatorSet.ChangeFormat(inImage, out HObject imagePart, W, H);
                outImage = new HImage(imagePart);
                imagePart.Dispose();
            }
            catch (Exception ex)
            {
                outImage = new HImage();
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 缩放图像尺寸
        public static void MV_ZoomImageFactor(HImage inImage, out HImage outImage, int W, int H)
        {
            try
            {
                HOperatorSet.ZoomImageFactor(inImage, out HObject imageZoomed, W, H, "bilinear");
                outImage = new HImage(imageZoomed);
                imageZoomed.Dispose();
            }
            catch (Exception ex)
            {
                outImage = new HImage();
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 深度转灰度

        // 裁剪图像

        // 倾斜补正

        // 均值滤波
        public static void MV_MeanImage(HImage inImage, out HImage outImage, int W, int H)
        {
            try
            {
                HOperatorSet.MeanImage(inImage, out HObject region, W, H);
                outImage = new HImage(region);
                region.Dispose();
            }
            catch (Exception ex)
            {
                outImage = new HImage();
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 中值滤波
        public static void MV_MedianImage(HImage inImage, out HImage outImage, int radius, int margin)
        {
            try
            {
                HOperatorSet.MedianImage(inImage, out HObject imageMedian, "square", radius, margin);
                outImage = new HImage(imageMedian);
                imageMedian.Dispose();
            }
            catch (Exception ex)
            {
                outImage = new HImage();
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 高斯滤波
        public static void MV_GaussImage(HImage inImage, out HImage outImage, int Size)
        {
            try
            {
                HOperatorSet.GaussImage(inImage, out HObject ImageGauss, Size);
                outImage = new HImage(ImageGauss);
                ImageGauss.Dispose();
            }
            catch (Exception ex)
            {
                outImage = new HImage();
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 灰度膨胀
        public static void MV_GrayDilation(HImage inImage, out HImage outImage, int W, int H)
        {
            try
            {
                HOperatorSet.GrayDilationRect(inImage, out HObject ImageGauss, W, H);
                outImage = new HImage(ImageGauss);
                ImageGauss.Dispose();
            }
            catch (Exception ex)
            {
                outImage = new HImage();
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 灰度腐蚀
        public static void MV_GrayErosion(HImage inImage, out HImage outImage, int W, int H)
        {
            try
            {
                HOperatorSet.GrayErosionRect(inImage, out HObject ImageGauss, W, H);
                outImage = new HImage(ImageGauss);
                ImageGauss.Dispose();
            }
            catch (Exception ex)
            {
                outImage = new HImage();
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 锐化
        public static void MV_EmphaSize(HImage inImage, out HImage outImage, int W, int H, double Comp)
        {
            try
            {
                HOperatorSet.Emphasize(inImage, out HObject imageEmphasize, W, H, Comp);
                outImage = new HImage(imageEmphasize);
                imageEmphasize.Dispose();
            }
            catch (Exception ex)
            {
                outImage = new HImage();
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 对比度
        public static void MV_Illuminate(HImage inImage, out HImage outImage, int W, int H, double Comp)
        {
            try
            {
                HOperatorSet.Illuminate(inImage, out HObject imageIlluminate, W, H, Comp);
                outImage = new HImage(imageIlluminate);
                imageIlluminate.Dispose();
            }
            catch (Exception ex)
            {
                outImage = new HImage();
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        public static void MV_ScaleImage(HImage inImage, out HImage outImage, double mult, double add)
        {
            try
            {
                HOperatorSet.ScaleImage(inImage, out HObject imageScaled, mult, add);
                outImage = new HImage(imageScaled);
                imageScaled.Dispose();
            }
            catch (Exception ex)
            {
                outImage = new HImage();
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 灰度开运算
        public static void MV_Opening(HImage inImage, out HImage outImage, int W, int H)
        {
            try
            {
                HOperatorSet.GrayOpeningRect(inImage, out HObject imageOpening, W, H);
                outImage = new HImage(imageOpening);
                imageOpening.Dispose();
            }
            catch (Exception ex)
            {
                outImage = new HImage();
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 灰度闭运算
        public static void MV_Closing(HImage inImage, out HImage outImage, int W, int H)
        {
            try
            {
                HOperatorSet.GrayClosingRect(inImage, out HObject imageClosing, W, H);
                outImage = new HImage(imageClosing);
                imageClosing.Dispose();
            }
            catch (Exception ex)
            {
                outImage = new HImage();
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        public static void MV_InvertImage(HImage inImage, out HImage outImage, bool A)
        {
            try
            {
                HOperatorSet.InvertImage(inImage, out HObject imageInvert);
                outImage = new HImage(imageInvert);
                imageInvert.Dispose();
            }
            catch (Exception ex)
            {
                outImage = new HImage();
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 二值化
        public static void MV_Threshold(HImage inImage, out HImage outImage, double LowThreshold, double HeighThreshold, bool IsReverse)
        {
            try
            {
                //区域处理
                HObject ho_Regions, ho_Image_out1, ho_Image_out2;
                HTuple hv_Width = new HTuple(), hv_Height = new HTuple();
                HOperatorSet.Threshold(inImage, out ho_Regions, LowThreshold, HeighThreshold);
                if (IsReverse)
                {
                    HOperatorSet.GetImageSize(inImage, out hv_Width, out hv_Height);
                    HOperatorSet.GenImageConst(out ho_Image_out1, "byte", hv_Width, hv_Height);
                    HOperatorSet.OverpaintRegion(ho_Image_out1, ho_Regions, 255, "fill");
                    HOperatorSet.GenImageProto(ho_Image_out1, out ho_Image_out2, 255);
                    HOperatorSet.OverpaintRegion(ho_Image_out2, ho_Regions, 0, "fill");
                }
                else
                {
                    HOperatorSet.GetImageSize(inImage, out hv_Width, out hv_Height);
                    HOperatorSet.GenImageConst(out ho_Image_out1, "byte", hv_Width, hv_Height);
                    HOperatorSet.OverpaintRegion(ho_Image_out1, ho_Regions, 0, "fill");
                    HOperatorSet.GenImageProto(ho_Image_out1, out ho_Image_out2, 0);
                    HOperatorSet.OverpaintRegion(ho_Image_out2, ho_Regions, 255, "fill");
                }
                outImage = new HImage(ho_Image_out2);
                ho_Image_out2.Dispose();
            }
            catch (Exception ex)
            {
                outImage = new HImage();
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 
        public static void MV_VarThreshold(HImage inImage, out HImage outImage, double VarWidth, double VarHeight, double VarSkew, VarThresholdType varType)
        {
            try
            {
                HObject ho_Regions, ho_Image_out1, ho_Image_out2;
                HTuple hv_Width = new HTuple(), hv_Height = new HTuple();
                string VarType = "";
                switch (varType)
                {
                    case VarThresholdType.等于:
                        VarType = "equal";
                        break;
                    case VarThresholdType.不等于:
                        VarType = "not_equal";
                        break;
                    case VarThresholdType.小于等于:
                        VarType = "dark";
                        break;
                    case VarThresholdType.大于等于:
                        VarType = "light";
                        break;
                }
                HOperatorSet.VarThreshold(inImage, out ho_Regions, VarWidth, VarHeight, VarSkew / 100, 30, VarType);
                HOperatorSet.GetImageSize(inImage, out hv_Width, out hv_Height);
                HOperatorSet.GenImageConst(out ho_Image_out1, "byte", hv_Width, hv_Height);
                HOperatorSet.OverpaintRegion(ho_Image_out1, ho_Regions, 0, "fill");
                HOperatorSet.GenImageProto(ho_Image_out1, out ho_Image_out2, 0);
                HOperatorSet.OverpaintRegion(ho_Image_out2, ho_Regions, 255, "fill");
                outImage = new HImage(ho_Image_out2);
                ho_Image_out2.Dispose();
            }
            catch (Exception ex)
            {
                outImage = new HImage();
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        public static void MV_ExecuteAllPretreat(HImage inmage, List<PretreatParam> param, out HImage outImage)
        {
            outImage = new HImage();
            try
            {
                HImage image = null;
                HImage TempImage = inmage.CopyImage();
                foreach (var item in param)
                {
                    if (item.Enable)
                    {
                        switch (item.Name)
                        {
                            //TODO：图像调整 - Obj
                            case OperatorType.彩色转灰:
                                MV_TransImage(TempImage, out image, item.TransImageType, item.TransImageChannel);// MirrorImage);     
                                break;
                            case OperatorType.图像镜像:
                                MV_MirrorImage(TempImage, out image, item.MirrorImageType);// MirrorImage);             
                                break;
                            case OperatorType.图像旋转:
                                MV_RotateImage(TempImage, out image, item.RotateImageAngle);//RotateImageAngle);
                                break;
                            case OperatorType.修改图像尺寸:
                                MV_ChangeFormat(TempImage, out image, item.ChangeImageWidth, item.ChangeImageHeight);//ChangeFormatWidth, ChangeFormatHeight);
                                break;
                            //TODO：滤波 - Obj
                            case OperatorType.均值滤波:
                                MV_MeanImage(TempImage, out image, item.MeanImageWidth, item.MeanImageHeight);
                                break;
                            case OperatorType.中值滤波:
                                MV_MedianImage(TempImage, out image, item.MedianImageWidth, item.MedianImageHeight);
                                break;
                            case OperatorType.高斯滤波:
                                MV_GaussImage(TempImage, out image, item.GaussImageSize);
                                break;
                            //TODO：形态学运算 - Obj
                            case OperatorType.灰度膨胀:
                                MV_GrayErosion(TempImage, out image, item.GrayErosionWidth, item.GrayErosionHeight);
                                break;
                            case OperatorType.灰度腐蚀:
                                MV_GrayDilation(TempImage, out image, item.GrayDilationWidth, item.GrayDilationHeight);
                                break;
                            //TODO：图像增强 - Obj
                            case OperatorType.锐化:
                                MV_EmphaSize(TempImage, out image, item.EmphaSizeWidth, item.EmphaSizeHeight, item.EmphaSizeFactor);
                                break;
                            case OperatorType.对比度:
                                MV_Illuminate(TempImage, out image, item.IlluminateWidth, item.IlluminateHeight, item.IlluminateFactor);
                                break;
                            case OperatorType.亮度调节:
                                MV_ScaleImage(TempImage, out image, item.ScaleImageMult, item.ScaleImageAdd);
                                break;
                            case OperatorType.灰度开运算:
                                MV_Opening(TempImage, out image, item.OpeningWidth, item.OpeningHeight);
                                break;
                            case OperatorType.灰度闭运算:
                                MV_Closing(TempImage, out image, item.ClosingWidth, item.ClosingHeight);
                                break;
                            case OperatorType.反色:
                                MV_InvertImage(TempImage, out image, item.InvertImageLogic);
                                break;
                            //TODO：二值化 - Obj
                            case OperatorType.二值化:
                                MV_Threshold(TempImage, out image, item.ThresholdLow, item.ThresholdHight, item.ThresholdReverse);
                                break;
                            case OperatorType.均值二值化:
                                MV_VarThreshold(TempImage, out image, item.VarThresholdWidth, item.VarThresholdHeight, item.VarThresholdSkew, item.VarThresholdType);
                                break;
                        }
                        TempImage.Dispose();
                        TempImage = new HImage(image);
                        image.Dispose();
                    }
                }
                outImage.Dispose();
                outImage = TempImage;
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        #endregion
        #region blob 区域操作
        // 连通
        public static void MV_Connection(HObject inObject, out HObject outObject)
        {
            try
            {
                HOperatorSet.Connection(inObject, out outObject);
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 相减
        public static void MV_Difference(HObject inObject, HObject subObject, out HObject outObject)
        {
            try
            {
                HOperatorSet.Difference(inObject, subObject, out outObject);
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 合并
        public static void MV_Union(HObject inObject, out HObject outObject)
        {
            try
            {
                HOperatorSet.Union1(inObject, out outObject);
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 相交
        public static void MV_Intersection(HObject inObject1, HObject inObject2, out HObject outObject)
        {
            try
            {
                HOperatorSet.Intersection(inObject1, inObject2, out outObject);
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        //孔洞填充
        public static void MV_FillUp(HObject inObject, out HObject outObject)
        {
            try
            {
                HOperatorSet.FillUp(inObject, out outObject);
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 补集
        public static void MV_Complement(HObject inObject1, out HObject outObject)
        {
            try
            {
                HOperatorSet.Complement(inObject1, out outObject);
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 膨胀
        public static void MV_DilationCircle(HObject inObject, double radius, out HObject outObject)
        {
            try
            {
                HOperatorSet.DilationCircle(inObject, out outObject, radius);
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        public static void MV_DilationRectangle(HObject inObject, double width, double height, out HObject outObject)
        {
            try
            {
                HOperatorSet.DilationRectangle1(inObject, out outObject, width, height);
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 腐蚀
        public static void MV_ErosionCircle(HObject inObject, double radius, out HObject outObject)
        {
            try
            {
                HOperatorSet.ErosionCircle(inObject, out outObject, radius);
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        public static void MV_ErosionRectangle(HObject inObject, double width, double height, out HObject outObject)
        {
            try
            {
                HOperatorSet.ErosionRectangle1(inObject, out outObject, width, height);
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 开运算
        public static void MV_OpeningCircle(HObject inObject, double radius, out HObject outObject)
        {
            try
            {
                HOperatorSet.OpeningCircle(inObject, out outObject, radius);
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        public static void MV_OpeningRectangle(HObject inObject, double width, double height, out HObject outObject)
        {
            try
            {
                HOperatorSet.OpeningRectangle1(inObject, out outObject, width, height);
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 闭运算
        public static void MV_ClosingCircle(HObject inObject, double radius, out HObject outObject)
        {
            try
            {
                HOperatorSet.ClosingCircle(inObject, out outObject, radius);
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        public static void MV_ClosingRectangle(HObject inObject, double width, double height, out HObject outObject)
        {
            try
            {
                HOperatorSet.ClosingRectangle1(inObject, out outObject, width, height);
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 特征筛选
        public static void MV_SelectShape(HObject inObject, string feature, ConditionalRelation relation, double min, double max, out HObject outObject)
        {
            outObject = new HObject();
            try
            {
                switch (relation)
                {
                    case ConditionalRelation.AND:
                        {
                            outObject.Dispose();
                            HOperatorSet.SelectShape(inObject, out outObject, feature, "and", min, max);
                        }
                        break;
                    case ConditionalRelation.OR:
                        {
                            outObject.Dispose();
                            HOperatorSet.SelectShape(inObject, out outObject, feature, "or", min, max);
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 转换
        public static void MV_ShapeTrans(HObject inObject, ConversionType type, out HObject outObject)
        {
            try
            {
                HOperatorSet.ShapeTrans(inObject, out outObject, REnum.EnumToStr(type));
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 矩形分割
        public static void MV_PartitionRectangle(HObject inObject, double width, double height, out HObject outObject)
        {
            try
            {
                HOperatorSet.PartitionRectangle(inObject, out outObject, width, height);
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 动态分割
        public static void MV_PartitionDynamic(HObject inObject, double distance, double percent, out HObject outObject)
        {
            try
            {
                HOperatorSet.PartitionDynamic(inObject, out outObject, distance, percent);
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 获取最大区域
        public static void MV_GetMaxRegion(HObject inObject, out HObject outObject)
        {
            try
            {
                HOperatorSet.SelectShapeStd(inObject, out outObject, "max_area", 70);
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 亮度

        // 执行所有区域分析
        public static void MV_ExecuteAllRegionAnaly(HObject inObject, List<RegionAnalyParam> param, out HObject outObject)
        {
            outObject = new HObject();
            try
            {
                HObject TempObject = new HObject();
                List<HObject> hObjects = new List<HObject>() { inObject.Clone() };
                try
                {
                    foreach (var item in param)
                    {
                        if (item.Enable)
                        {
                            if (item.ID == 0 || item.UsedID == "上一个区域")
                            {
                                TempObject = hObjects.Last();
                            }
                            else
                            {
                                TempObject = hObjects[Convert.ToInt32(item.UsedID) + 1];
                            }
                            switch (item.Name)
                            {
                                case RegionAnalysis.连通:
                                    {
                                        MV_Connection(TempObject, out HObject hObject);
                                        hObjects.Add(hObject);
                                    }
                                    break;
                                case RegionAnalysis.合并:
                                    {
                                        MV_Union(TempObject, out HObject hObject);
                                        hObjects.Add(hObject);
                                    }
                                    break;
                                case RegionAnalysis.补集:
                                    {
                                        MV_Complement(TempObject, out HObject hObject);
                                        hObjects.Add(hObject);
                                    }
                                    break;
                                case RegionAnalysis.相减:
                                    {
                                        HObject hObject1 = null;
                                        if (item.AreaAction == "上一个区域")
                                        {
                                            hObject1 = hObjects.Last();
                                        }
                                        else
                                        {
                                            hObject1 = hObjects[Convert.ToInt32(item.AreaAction)];
                                        }
                                        MV_Difference(TempObject, hObject1, out HObject hObject);
                                        hObjects.Add(hObject);
                                    }
                                    break;
                                case RegionAnalysis.相交:
                                    {
                                        HObject hObject1 = null;
                                        if (item.AreaAction == "上一个区域")
                                        {
                                            hObject1 = hObjects.Last();
                                        }
                                        else
                                        {
                                            hObject1 = hObjects[Convert.ToInt32(item.AreaAction)];
                                        }
                                        MV_Intersection(TempObject, hObject1, out HObject hObject);
                                        hObjects.Add(hObject);
                                    }
                                    break;
                                case RegionAnalysis.孔洞填充:
                                    {
                                        MV_FillUp(TempObject, out HObject hObject);
                                        hObjects.Add(hObject);
                                    }
                                    break;
                                case RegionAnalysis.闭运算:
                                    {
                                        HObject hObject = null;
                                        switch (item.BlobElementType)
                                        {
                                            case BlobElementType.圆形:
                                                MV_ClosingCircle(TempObject, item.Diameter / 2, out hObject);
                                                break;
                                            case BlobElementType.矩形:
                                                MV_ClosingRectangle(TempObject, item.BlobWidth, item.BlobHeight, out hObject);
                                                break;
                                        }
                                        hObjects.Add(hObject);
                                    }
                                    break;
                                case RegionAnalysis.开运算:
                                    {
                                        HObject hObject = null;
                                        switch (item.BlobElementType)
                                        {
                                            case BlobElementType.圆形:
                                                MV_OpeningCircle(TempObject, item.Diameter / 2, out hObject);
                                                break;
                                            case BlobElementType.矩形:
                                                MV_OpeningRectangle(TempObject, item.BlobWidth, item.BlobHeight, out hObject);
                                                break;
                                        }
                                        hObjects.Add(hObject);
                                    }
                                    break;
                                case RegionAnalysis.腐蚀:
                                    {
                                        {
                                            HObject hObject = null;
                                            switch (item.BlobElementType)
                                            {
                                                case BlobElementType.圆形:
                                                    MV_ErosionCircle(TempObject, item.Diameter / 2, out hObject);
                                                    break;
                                                case BlobElementType.矩形:
                                                    MV_ErosionRectangle(TempObject, item.BlobWidth, item.BlobHeight, out hObject);
                                                    break;
                                            }
                                            hObjects.Add(hObject);
                                        }
                                    }
                                    break;
                                case RegionAnalysis.膨胀:
                                    {
                                        {
                                            HObject hObject = null;
                                            switch (item.BlobElementType)
                                            {
                                                case BlobElementType.圆形:
                                                    MV_DilationCircle(TempObject, item.Diameter / 2, out hObject);
                                                    break;
                                                case BlobElementType.矩形:
                                                    MV_DilationRectangle(TempObject, item.BlobWidth, item.BlobHeight, out hObject);
                                                    break;
                                            }
                                            hObjects.Add(hObject);
                                        }
                                    }
                                    break;
                                case RegionAnalysis.特征筛选:
                                    {
                                        HObject hObject = null;
                                        foreach (var data in item.FeatureParams)
                                        {
                                            MV_SelectShape(TempObject, REnum.EnumToStr(data.Name), item.ConditionalRelation, data.MinValue, data.MaxValue, out hObject);
                                            TempObject?.Dispose();
                                            TempObject = hObject;
                                        }
                                        hObjects.Add(hObject);
                                    }
                                    break;
                                case RegionAnalysis.转换:
                                    {
                                        MV_ShapeTrans(TempObject, item.ConversionType, out HObject hObject);
                                        hObjects.Add(hObject);
                                    }
                                    break;
                                case RegionAnalysis.矩形分割:
                                    {
                                        MV_PartitionRectangle(TempObject, item.RectWidth, item.RectHeight, out HObject hObject);
                                        hObjects.Add(hObject);
                                    }
                                    break;
                                case RegionAnalysis.动态分割:
                                    {
                                        MV_PartitionDynamic(TempObject, item.ApproximateWidth, item.Percentage, out HObject hObject);
                                        hObjects.Add(hObject);
                                    }
                                    break;
                                case RegionAnalysis.获取最大区域:
                                    {
                                        MV_GetMaxRegion(TempObject, out HObject hObject);
                                        hObjects.Add(hObject);
                                    }
                                    break;
                                case RegionAnalysis.亮度:
                                    break;

                            }

                        }
                    }
                }
                catch (Exception ex)
                {
                    throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
                }

                outObject.Dispose();
                outObject = hObjects.Last().Clone();
                foreach (var hObject in hObjects)
                {
                    hObject?.Dispose();
                }
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, ex.Message);
            }
        }
        // 获取每个区域的特征
        public static void MV_GetAllFeatures(HObject inObject, out List<FeatureResult> results)
        {
            results = new List<FeatureResult>();
            try
            {
                HTuple feature = new HTuple(new String[] { "area", "row", "column", "width", "height", "orientation", "rect2_phi", "rect2_len1", "rect2_len2", "circularity", "compactness", "convexity", "rectangularity" });
                HOperatorSet.RegionFeatures(inObject, "max_area", out HTuple value);
                for (int i = 0; i < value / 13; i += 13)
                {
                    results.Add(new FeatureResult(value[i].D, value[i + 1].D, value[i + 2].D, value[i + 3].D, value[i + 4].D, value[i + 5].D, value[i + 6].D, value[i + 7], value[i + 8].D, value[i + 9].D, value[i + 10].D, value[i + 11].D));
                }
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        #endregion
        #region 模板匹配
        public static void MV_MatchModel(HImage image, MatchModelParam param, MatchModel model, out MatchModelResult result)
        {
            result = new MatchModelResult();
            // 临时数据
            HTuple Row = new HTuple();
            HTuple Col = new HTuple();
            HTuple Phi = new HTuple();
            HTuple Score = new HTuple();
            try
            {
                switch (param.ModelType)
                {
                    case ModelType.形状模板:
                        {
                            HOperatorSet.FindShapeModel(
                                    image,
                                    model.ModelImageHandle,
                                    Math.Round(param.StartPhi * Math.PI / 180.0, 3),
                                    Math.Round((param.EndPhi - param.StartPhi) * Math.PI / 180.0, 3),
                                    new HTuple(param.MinScore),
                                    param.MathNum,
                                    param.MaxOverlap,
                                    new HTuple("least_squares"),
                                    new HTuple(param.Levels, param.EndMatchLevel),
                                    param.GreedDeg,
                                    out Row, out Col, out Phi, out Score);
                        }
                        break;
                    case ModelType.灰度模板:
                        {
                            HOperatorSet.FindNccModel(
                                    image,
                                    model.ModelImageHandle,
                                    Math.Round(param.StartPhi * Math.PI / 180.0, 3),
                                    Math.Round((param.EndPhi - param.StartPhi) * Math.PI / 180.0, 3),
                                    param.MinScore,
                                    param.MathNum,
                                    param.MaxOverlap,
                                    "true",
                                     new HTuple(param.Levels, param.EndMatchLevel),
                                    out Row, out Col, out Phi, out Score);
                        }
                        break;
                    case ModelType.可伸缩形状模板:
                        {
                            HOperatorSet.FindScaledShapeModel(
                                    image,
                                    model.ModelImageHandle,
                                    Math.Round(param.StartPhi * Math.PI / 180.0, 3),
                                    Math.Round((param.EndPhi - param.StartPhi) * Math.PI / 180.0, 3),
                                    param.MinScale,
                                    param.MaxScale,
                                    new HTuple(param.MinScore),
                                    param.MathNum,
                                    param.MaxOverlap,
                                    new HTuple("least_squares"),
                                    new HTuple(param.Levels, param.EndMatchLevel),
                                    param.GreedDeg,
                                    out Row, out Col, out Phi, out var _, out Score);
                        }
                        break;
                }
                if (Score.Length > 0)
                {
                    result.MatchNum = Score.Length;
                    result.Cross = new HObject();
                    result.MatchOutline = new HXLDCont();
                    result.MatchOutline.GenEmptyObj();


                    if (param.ModelType == ModelType.灰度模板)
                    {
                        //获取模板区域
                        HRegion modelRegion = ((HNCCModel)model.ModelImageHandle).GetNccModelRegion();
                        //获取匹配数据和轮廓
                        for (int i = 0; i < Score.Length; i++)
                        {
                            result.MatchPoint.Add(new PointXY(Col[i], Row[i]));
                            result.MatchPhi.Add(Phi[i]);
                            result.MatchScore.Add(Score[i]);
                            //result.RealPoint.Add(new PointF(qx[i], qy[i]));

                            HOperatorSet.VectorAngleToRigid(0, 0, 0, Row[i], Col[i], Phi[i], out HTuple tempMat2D);
                            {
                                HRegion hRegion = modelRegion.AffineTransRegion(new HHomMat2D(tempMat2D), "nearest_neighbor");
                                tempMat2D.Dispose();

                                HXLDCont xld = hRegion.GenContourRegionXld("border");
                                hRegion.Dispose();

                                HXLDCont tempxld = result.MatchOutline.ConcatObj(xld);
                                result.MatchOutline.Dispose(); xld.Dispose();
                                result.MatchOutline = tempxld;
                            }
                        }
                        modelRegion.Dispose();
                    }
                    else
                    {
                        //获取模板轮廓
                        HXLDCont modelContour = ((HShapeModel)model.ModelImageHandle).GetShapeModelContours(1);
                        //获取匹配数据和轮廓
                        for (int i = 0; i < Score.Length; i++)
                        {
                            result.MatchPoint.Add(new PointXY(Col[i], Row[i]));
                            result.MatchPhi.Add(Phi[i]);
                            result.MatchScore.Add(Score[i]);


                            HOperatorSet.VectorAngleToRigid(0, 0, 0, Row[i], Col[i], Phi[i], out HTuple tempMat2D);
                            {
                                HXLDCont xld = modelContour.AffineTransContourXld(new HHomMat2D(tempMat2D));
                                tempMat2D.Dispose();


                                HXLDCont tempxld = result.MatchOutline.ConcatObj(xld);


                                result.MatchOutline.Dispose();
                                xld.Dispose();
                                result.MatchOutline = tempxld;
                            }
                        }
                        modelContour.Dispose();
                    }
                    result.Cross.Dispose();
                    HOperatorSet.GenCrossContourXld(out result.Cross, Row, Col, 35, 0);

                    return;
                }
            }
            catch (Exception ex)
            {
                result.MatchNum = 0;
                throw new VisionException(MV_ERR.出现异常, $"MV_MatchModel函数出现异常：{RemoveHalconError(ex.Message)}");
            }

            //throw new VisionException(MV_ERR.抓取模板错误, "MV_MatchModel函数抓取模板错误");
        }
        public static void MV_CreateModel(HImage image, MatchModelParam param, ref MatchModel model, bool isRecreate = false)
        {
            if (image == null || !image.IsInitialized())
            {
                throw new VisionException(MV_ERR.图像为空, "MV_CreateModel函数的输入图像为空");
            }
            try
            {
                if (!isRecreate)
                {
                    model.ModelImageReduce?.Dispose();
                    model.OutImage?.Dispose();
                    model.ModelImageReduce = image.CopyImage();
                    model.OutImage = image.CropDomain();
                }

                switch (param.ModelType)
                {
                    case ModelType.形状模板:
                        {
                            //清理模板句柄
                            if (model.ModelImageHandle != null)
                            {
                                //HOperatorSet.ClearShapeModel(model.ModelImageHandle);
                                model.ModelImageHandle.Dispose();
                                model.ModelImageHandle = null;
                            }

                            //创建模板
                            model.ModelImageHandle = new HShapeModel();
                            ((HShapeModel)model.ModelImageHandle).CreateShapeModel(
                                    image,                                                                        //图像
                                    new HTuple(param.Levels),                                                                 //金字塔级别
                                    Math.Round(param.StartPhi * Math.PI / 180.0, 3),                              //模板旋转的起始角度
                                    Math.Round((param.EndPhi - param.StartPhi) * Math.PI / 180.0, 3),             //模板旋转的角度范围
                                    new HTuple("auto"),                                                           //旋转角度的步长， >=0 and <=pi/16 
                                    new HTuple("auto"),                                                            //设置模板优化和模板创建方法  none
                                    REnum.EnumToStr(param.CompType),                                               //匹配方法设置
                                    new HTuple(param.Contrast, param.Contrast, param.MinLength),                  //对比度
                                    new HTuple(param.MinContrast));                                               //最小对比度
                            ((HShapeModel)model.ModelImageHandle).GetShapeModelParams(out double angleStart, out double angleExtent, out double angleStep, out double scaleMin, out double scaleMax, out double scaleStep, out string metric, out int minContrast);

                            //设置超时时间
                            ((HShapeModel)model.ModelImageHandle).SetShapeModelParam("timeout", param.TimeOut);
                        }
                        break;
                    case ModelType.灰度模板:
                        {
                            //清理模板句柄
                            if (model.ModelImageHandle != null)
                            {
                                //HOperatorSet.ClearNccModel(model.ModelImageHandle);
                                model.ModelImageHandle.Dispose();
                                model.ModelImageHandle = null;
                            }

                            //创建模板
                            model.ModelImageHandle = new HNCCModel();
                            ((HNCCModel)model.ModelImageHandle).CreateNccModel(
                                    image,                                                                            //图像
                                    new HTuple(param.Levels),                                                                     //金字塔级别
                                    Math.Round(param.StartPhi * Math.PI / 180.0, 3),                                  //模板旋转的起始角度
                                    Math.Round((param.EndPhi - param.StartPhi) * Math.PI / 180.0, 3),                 //模板旋转的角度范围, >=0
                                    new HTuple("auto"),                                                                           //旋转角度的步长， >=0 and <=pi/16
                                    REnum.EnumToStr(param.CompType));
                            ((HNCCModel)model.ModelImageHandle).GetNccModelParams(out double angleStart, out double angleExtent, out double angleStep, out string metric);
                            //设置超时时间
                            ((HNCCModel)model.ModelImageHandle).SetNccModelParam("timeout", param.TimeOut);
                        }
                        break;
                    case ModelType.可伸缩形状模板:
                        {
                            //清理模板句柄
                            if (model.ModelImageHandle != null)
                            {
                                //HOperatorSet.ClearShapeModel(model.ModelImageHandle);
                                model.ModelImageHandle.Dispose();
                                model.ModelImageHandle = null;
                            }

                            //创建模板
                            model.ModelImageHandle = new HShapeModel();
                            ((HShapeModel)model.ModelImageHandle).CreateScaledShapeModel(
                                    image,                                                                            //图像
                                    param.Levels,                                                                     //金字塔级别
                                    Math.Round(param.StartPhi * Math.PI / 180.0, 3),                                  //模板旋转的起始角度
                                    Math.Round((param.EndPhi - param.StartPhi) * Math.PI / 180.0, 3),                 //模板旋转的角度范围
                                    "auto",                                                                           //旋转角度的步长， >=0 and <=pi/16 
                                    param.MinScale,                                                                   //模板最小比例 0.8
                                    param.MaxScale,                                                                   //模板最大比例   1.1
                                    "auto",                                                                           //模板比例的步长  "auto"
                                    "auto",                                                                           //设置模板优化和模板创建方法  none
                                    REnum.EnumToStr(param.CompType),                                        //匹配方法设置
                                    new HTuple(param.Contrast, param.Contrast, param.MinLength),                      //对比度
                                    new HTuple(param.MinContrast));                                                   //最小对比度

                            //设置超时时间
                            ((HShapeModel)model.ModelImageHandle).SetShapeModelParam("timeout", param.TimeOut);
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"MV_CreateModel函数出现异常：{ex.Message}");
            }
        }
        public static void MV_FindModelImage(HImage image, MatchModelParam param, MatchModel model, out MatchModelResult result)
        {
            result = new MatchModelResult();
            model.ContourXld?.Dispose();
            try
            {
                HTuple row;
                HTuple column;
                HTuple angle;
                HTuple score;

                HOperatorSet.AreaCenter(image, out HTuple imageArea, out HTuple imageMidR, out HTuple imageMidC);
                HOperatorSet.AreaCenter(model.OutImage, out HTuple outImageArea, out HTuple outImageMidR, out HTuple outImageMidC);

                // 查找模板
                switch (param.ModelType)
                {
                    case ModelType.形状模板:
                        {
                            ((HShapeModel)model.ModelImageHandle).FindShapeModel(
                                    model.ModelImageReduce,                                                         //图像
                                    Math.Round(param.StartPhi * Math.PI / 180.0, 3),                                //模板旋转的起始角度
                                    Math.Round((param.EndPhi - param.StartPhi) * Math.PI / 180.0, 3),               //模板旋转的角度范围
                                    new HTuple(param.MinScore),                                                     //匹配最小分数
                                    1,                                                                              //匹配最大个数
                                    param.MaxOverlap,                                                               //最大覆盖
                                    new HTuple("least_squares"),
                                    new HTuple(param.Levels, param.EndMatchLevel),                           //金字塔层数
                                    param.GreedDeg,                                                                 //贪婪度
                                    out row, out column, out angle, out score);                                     //结果
                                                                                                                    //转换为物理坐标
                            if (score.Length > 0)
                            {
                                //转换为物理坐标
                                //HOperatorSet.AffineTransPoint2d(param.homMat2D, column, row, out HTuple qx, out HTuple qy);

                                // 修改模板坐标
                                result.MatchNum = score.Length;
                                result.MatchPoint.Add(new PointXY(column, row));
                                result.MatchPhi.Add(angle);
                                result.MatchScore.Add(score);
                                //result.RealPoint.Add(new PointF(qx, qy));

                                HOperatorSet.AreaCenter(model.ModelImageReduce, out HTuple FromArea, out HTuple FromY, out HTuple FromX);

                                // 检测结果-对XLD应用任意加法 2D 变换
                                HOperatorSet.VectorAngleToRigid(0, 0, 0, outImageMidR - (FromY - row[0]), outImageMidC - (FromX - column[0]), 0, out HTuple tempMat2D);
                                model.ContourXld?.Dispose();
                                HXLDCont xld = ((HShapeModel)model.ModelImageHandle).GetShapeModelContours(1);
                                model.ContourXld = xld.AffineTransContourXld(new HHomMat2D(tempMat2D));
                                xld.Dispose(); tempMat2D.Dispose();

                                // 根据设定自定义中心  更新模板原点
                                if (param.CenterCoordinates != null)
                                {
                                    ((HShapeModel)model.ModelImageHandle).SetShapeModelOrigin(param.CenterCoordinates.StartY - imageMidR, param.CenterCoordinates.StartX - imageMidC);
                                }
                                else
                                {
                                    param.CenterCoordinates = new ROICoordinates(outImageMidC, outImageMidR);
                                }
                                return;
                            }
                        }
                        break;

                    case ModelType.灰度模板:
                        {
                            ((HNCCModel)model.ModelImageHandle).FindNccModel(
                                    model.ModelImageReduce,                                                    //图像
                                    Math.Round(param.StartPhi * Math.PI / 180.0, 3),                           //模板旋转的起始角度
                                    Math.Round((param.EndPhi - param.StartPhi) * Math.PI / 180.0, 3),          //模板旋转的角度范围
                                    param.MinScore,                                                            //匹配最小分数
                                    1,                                                                         //匹配最大个数
                                    param.MaxOverlap,                                                          //最大覆盖
                                    "true",
                                    new HTuple(param.Levels, param.EndMatchLevel),                      //匹配金字塔层数
                                    out row, out column, out angle, out score);
                            if (score.Length > 0)
                            {
                                //转换为物理坐标
                                //HOperatorSet.AffineTransPoint2d(param.homMat2D, column, row, out HTuple qx, out HTuple qy);

                                // 修改模板坐标
                                result.MatchNum = score.Length;
                                result.MatchPoint.Add(new PointXY(column.D, row.D));
                                result.MatchPhi.Add(angle);
                                result.MatchScore.Add(score);
                                //result.RealPoint.Add(new PointF(qx, qy));

                                // 根据设定自定义中心  更新模板原点
                                if (param.CenterCoordinates != null)
                                {
                                    ((HNCCModel)model.ModelImageHandle).SetNccModelOrigin(param.CenterCoordinates.StartY - imageMidR, param.CenterCoordinates.StartX - imageMidC);
                                }
                                else
                                {
                                    param.CenterCoordinates = new ROICoordinates(outImageMidC, outImageMidR);
                                }
                                return;
                            }
                        }
                        break;
                    case ModelType.可伸缩形状模板:
                        {
                            ((HShapeModel)model.ModelImageHandle).FindScaledShapeModel(
                                    model.ModelImageReduce,                                                    //图像
                                    Math.Round(param.StartPhi * Math.PI / 180.0, 3),                           //模板旋转的起始角度
                                    Math.Round((param.EndPhi - param.StartPhi) * Math.PI / 180.0, 3),          //模板旋转的角度范围
                                    param.MinScale,                                                            //模板最小比例 0.9
                                    param.MaxScale,                                                            //模板最大比例 1.1
                                    new HTuple(param.MinScore),                                                //匹配最小分数
                                    1,                                                                         //匹配最大个数
                                    param.MaxOverlap,                                                          //最大覆盖
                                    new HTuple("least_squares"),
                                    new HTuple(param.Levels, param.EndMatchLevel),                      //金字塔层数
                                    param.GreedDeg,                                                            //贪婪度
                                    out row, out column, out angle, out var _, out score);                     //结果
                            if (score.Length > 0)
                            {
                                //转换为物理坐标
                                HOperatorSet.AffineTransPoint2d(param.homMat2D, column, row, out HTuple qx, out HTuple qy);

                                // 修改模板坐标
                                result.MatchNum = score.Length;
                                result.MatchPoint.Add(new PointXY(column, row));
                                result.MatchScore.Add(score);
                                result.RealPoint.Add(new PointXY(qx, qy));

                                HOperatorSet.AreaCenter(model.ModelImageReduce, out HTuple FromArea, out HTuple FromY, out HTuple FromX);

                                // 检测结果-对XLD应用任意加法 2D 变换
                                HOperatorSet.VectorAngleToRigid(0, 0, 0, outImageMidR - (FromY - row[0]), outImageMidC - (FromX - column[0]), 0, out HTuple tempMat2D);
                                model.ContourXld?.Dispose();
                                HXLDCont xld = ((HShapeModel)model.ModelImageHandle).GetShapeModelContours(1);
                                model.ContourXld = xld.AffineTransContourXld(new HHomMat2D(tempMat2D));
                                xld.Dispose(); tempMat2D.Dispose();

                                // 根据设定自定义中心  更新模板原点
                                //if (param.CenterCoordinates != null)
                                //{
                                //    ((HShapeModel)model.ModelImageHandle).SetShapeModelOrigin(param.CenterCoordinates.StartY - imageMidR, param.CenterCoordinates.StartX - imageMidC);
                                //}
                                //else
                                //{
                                //    param.CenterCoordinates = new ROICoordinates(outImageMidC, outImageMidR);
                                //}
                                return;
                            }
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                result.MatchNum = 0;
                throw new VisionException(MV_ERR.出现异常, $"MV_FindModelImage函数异常：{RemoveHalconError(ex.Message)}");
            }
            throw new VisionException(MV_ERR.抓取模板错误, "MV_FindModelImage函数抓取模板错误");
        }
        public static void MV_FindModelImageEx(HImage image, MatchModelParam param, MatchModel model, out MatchModelResult result)
        {
            result = new MatchModelResult();
            model.ContourXld?.Dispose();
            try
            {
                HTuple row;
                HTuple column;
                HTuple angle;
                HTuple score;

                HOperatorSet.AreaCenter(image, out HTuple imageArea, out HTuple imageMidR, out HTuple imageMidC);
                HOperatorSet.AreaCenter(model.OutImage, out HTuple outImageArea, out HTuple outImageMidR, out HTuple outImageMidC);

                // 查找模板
                switch (param.ModelType)
                {
                    case ModelType.形状模板:
                        {
                            ((HShapeModel)model.ModelImageHandle).FindShapeModel(
                                    image,                                                         //图像
                                    Math.Round(param.StartPhi * Math.PI / 180.0, 3),                                //模板旋转的起始角度
                                    Math.Round((param.EndPhi - param.StartPhi) * Math.PI / 180.0, 3),               //模板旋转的角度范围
                                    new HTuple(param.MinScore),                                                     //匹配最小分数
                                    1,                                                                              //匹配最大个数
                                    param.MaxOverlap,                                                               //最大覆盖
                                    new HTuple("least_squares"),
                                    new HTuple(param.Levels, param.EndMatchLevel),                           //金字塔层数
                                    param.GreedDeg,                                                                 //贪婪度
                                    out row, out column, out angle, out score);                                     //结果
                                                                                                                    //转换为物理坐标
                            if (score.Length > 0)
                            {
                                //转换为物理坐标
                                //HOperatorSet.AffineTransPoint2d(param.homMat2D, column, row, out HTuple qx, out HTuple qy);

                                // 修改模板坐标
                                result.MatchNum = score.Length;
                                result.MatchPoint.Add(new PointXY(column, row));
                                result.MatchPhi.Add(angle);
                                result.MatchScore.Add(score);
                                //result.RealPoint.Add(new PointF(qx, qy));

                                // 检测结果-对XLD应用任意加法 2D 变换
                                HOperatorSet.VectorAngleToRigid(0, 0, 0, row[0], column[0], 0, out HTuple tempMat2D);
                                model.ContourXld?.Dispose();
                                HXLDCont xld = ((HShapeModel)model.ModelImageHandle).GetShapeModelContours(1);
                                model.ContourXld = xld.AffineTransContourXld(new HHomMat2D(tempMat2D));
                                xld.Dispose(); tempMat2D.Dispose();

                                // 根据设定自定义中心  更新模板原点
                                if (param.CenterCoordinates != null)
                                {
                                    ((HShapeModel)model.ModelImageHandle).SetShapeModelOrigin(param.CenterCoordinates.StartY - imageMidR, param.CenterCoordinates.StartX - imageMidC);
                                }
                                else
                                {
                                    param.CenterCoordinates = new ROICoordinates(outImageMidC, outImageMidR);
                                }
                                return;
                            }
                        }
                        break;

                    case ModelType.灰度模板:
                        {
                            ((HNCCModel)model.ModelImageHandle).FindNccModel(
                                    model.ModelImageReduce,                                                    //图像
                                    Math.Round(param.StartPhi * Math.PI / 180.0, 3),                           //模板旋转的起始角度
                                    Math.Round((param.EndPhi - param.StartPhi) * Math.PI / 180.0, 3),          //模板旋转的角度范围
                                    param.MinScore,                                                            //匹配最小分数
                                    1,                                                                         //匹配最大个数
                                    param.MaxOverlap,                                                          //最大覆盖
                                    "true",
                                    new HTuple(param.Levels, param.EndMatchLevel),                      //匹配金字塔层数
                                    out row, out column, out angle, out score);
                            if (score.Length > 0)
                            {
                                //转换为物理坐标
                                //HOperatorSet.AffineTransPoint2d(param.homMat2D, column, row, out HTuple qx, out HTuple qy);

                                // 修改模板坐标
                                result.MatchNum = score.Length;
                                result.MatchPoint.Add(new PointXY(column.D, row.D));
                                result.MatchPhi.Add(angle);
                                result.MatchScore.Add(score);
                                //result.RealPoint.Add(new PointF(qx, qy));

                                // 根据设定自定义中心  更新模板原点
                                if (param.CenterCoordinates != null)
                                {
                                    ((HNCCModel)model.ModelImageHandle).SetNccModelOrigin(param.CenterCoordinates.StartY - imageMidR, param.CenterCoordinates.StartX - imageMidC);
                                }
                                else
                                {
                                    param.CenterCoordinates = new ROICoordinates(outImageMidC, outImageMidR);
                                }
                                return;
                            }
                        }
                        break;
                    case ModelType.可伸缩形状模板:
                        {
                            ((HShapeModel)model.ModelImageHandle).FindScaledShapeModel(
                                    model.ModelImageReduce,                                                    //图像
                                    Math.Round(param.StartPhi * Math.PI / 180.0, 3),                           //模板旋转的起始角度
                                    Math.Round((param.EndPhi - param.StartPhi) * Math.PI / 180.0, 3),          //模板旋转的角度范围
                                    param.MinScale,                                                            //模板最小比例 0.9
                                    param.MaxScale,                                                            //模板最大比例 1.1
                                    new HTuple(param.MinScore),                                                //匹配最小分数
                                    1,                                                                         //匹配最大个数
                                    param.MaxOverlap,                                                          //最大覆盖
                                    new HTuple("least_squares"),
                                    new HTuple(param.Levels, param.EndMatchLevel),                      //金字塔层数
                                    param.GreedDeg,                                                            //贪婪度
                                    out row, out column, out angle, out var _, out score);                     //结果
                            if (score.Length > 0)
                            {
                                //转换为物理坐标
                                HOperatorSet.AffineTransPoint2d(param.homMat2D, column, row, out HTuple qx, out HTuple qy);

                                // 修改模板坐标
                                result.MatchNum = score.Length;
                                result.MatchPoint.Add(new PointXY(column, row));
                                result.MatchScore.Add(score);
                                result.RealPoint.Add(new PointXY(qx, qy));


                                // 检测结果-对XLD应用任意加法 2D 变换
                                HOperatorSet.VectorAngleToRigid(0, 0, 0, row[0], column[0], 0, out HTuple tempMat2D);
                                model.ContourXld?.Dispose();
                                HXLDCont xld = ((HShapeModel)model.ModelImageHandle).GetShapeModelContours(1);
                                model.ContourXld = xld.AffineTransContourXld(new HHomMat2D(tempMat2D));
                                xld.Dispose(); tempMat2D.Dispose();

                                // 根据设定自定义中心  更新模板原点
                                if (param.CenterCoordinates != null)
                                {
                                    ((HShapeModel)model.ModelImageHandle).SetShapeModelOrigin(param.CenterCoordinates.StartY - imageMidR, param.CenterCoordinates.StartX - imageMidC);
                                }
                                else
                                {
                                    param.CenterCoordinates = new ROICoordinates(outImageMidC, outImageMidR);
                                }
                                return;
                            }
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                result.MatchNum = 0;
                throw new VisionException(MV_ERR.出现异常, $"MV_FindModelImage函数异常：{RemoveHalconError(ex.Message)}");
            }
            throw new VisionException(MV_ERR.抓取模板错误, "MV_FindModelImage函数抓取模板错误");
        }
        public static void MV_RecreateModel(MatchModelParam param, MatchModel model, out MatchModelResult result)
        {
            result = new MatchModelResult();
            try
            {
                HImage image = model.OutImage.CopyImage();

                //剪切图片
                if (param.finalRegion != null && param.finalRegion.IsInitialized())
                {
                    HObject regionDifference = new HObject(), imageReduced = new HObject();
                    try
                    {
                        HOperatorSet.Difference(image, param.finalRegion, out regionDifference);
                        HOperatorSet.ReduceDomain(image, regionDifference, out imageReduced);
                        image.Dispose();
                        image = new HImage(imageReduced);
                    }
                    finally
                    {
                        regionDifference.Dispose();
                        imageReduced.Dispose();
                    }
                }

                MV_CreateModel(image, param, ref model, true);

                MV_FindModelImageEx(image, param, model, out result);
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        public static void MV_DestroyModel(MatchModel model)
        {
            model.OutImage?.Dispose();
            model.OutImage = null;
            model.ContourXld?.Dispose();
            model.ContourXld = null;
            model.ModelImageHandle?.Dispose();
            model.ModelImageHandle = null;
        }
        public static void MV_SetModelOrigin(MatchModelParam param, MatchModel model, double x, double y)
        {
            try
            {
                if (param.ModelType == ModelType.灰度模板)
                {
                    ((HNCCModel)model.ModelImageHandle).SetNccModelOrigin(y, x);
                }
                else
                {
                    ((HShapeModel)model.ModelImageHandle).SetShapeModelOrigin(y, x);
                }
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        #endregion
        #region 标定
        public static void MV_NPointCalibration(NPointCalibParam param, List<NPoint> points, out NPointCalibResult result)
        {
            result = new NPointCalibResult();
            try
            {
                switch (param.PointType)
                {
                    case PointType.Nine:
                        {
                            List<double> ImageX = new List<double>();
                            List<double> ImageY = new List<double>();
                            List<double> RobotX = new List<double>();
                            List<double> RobotY = new List<double>();
                            for (int i = 0; i < points.Count; i++)
                            {
                                ImageX.Add(points[i].ImageX);
                                ImageY.Add(points[i].ImageY);

                                RobotX.Add(points[i].RobotX);
                                RobotY.Add(points[i].RobotY);
                            }
                            //平移矩阵
                            param.HomMat2DTrans = new HHomMat2D();
                            param.HomMat2DTrans.VectorToHomMat2d(new HTuple(ImageX.ToArray()), new HTuple(ImageY.ToArray()), new HTuple(RobotX.ToArray()), new HTuple(RobotY.ToArray()));
                            //RMS计算
                            double calibRms = MV_CalRMS(param.HomMat2DTrans, ImageX.ToArray(), ImageY.ToArray(), RobotX.ToArray(), RobotY.ToArray());
                            //获取标定结果信息
                            HOperatorSet.HomMat2dToAffinePar(param.HomMat2DTrans, out HTuple PixelX, out HTuple PixelY, out HTuple RotationAngle, out HTuple TiltAngle, out HTuple TranslateX, out HTuple TranslateY);
                            result = new NPointCalibResult(Math.Round(TranslateX.D, 4), Math.Round(TranslateY.D, 4), PixelX.D, PixelY.D, Math.Round(RotationAngle.D, 6), Math.Round(TiltAngle.D, 6), Math.Round(calibRms, 5), 0, 0);
                        }
                        break;
                    case PointType.FourTeen:
                        {
                            List<double> ImageX_9 = new List<double>();
                            List<double> ImageY_9 = new List<double>();
                            List<double> RobotX_9 = new List<double>();
                            List<double> RobotY_9 = new List<double>();
                            List<double> ImageX_14 = new List<double>();
                            List<double> ImageY_14 = new List<double>();
                            List<double> RobotX_14 = new List<double>();
                            List<double> RobotY_14 = new List<double>();
                            foreach (NPoint point in points)
                            {
                                if (point.Index < 10)
                                {
                                    ImageX_9.Add(point.ImageX);
                                    ImageY_9.Add(point.ImageY);
                                    RobotX_9.Add(point.RobotX);
                                    RobotY_9.Add(point.RobotY);

                                }
                                else
                                {
                                    ImageX_14.Add(point.ImageX);
                                    ImageY_14.Add(point.ImageY);
                                    RobotX_14.Add(point.RobotX);
                                    RobotY_14.Add(point.RobotY);

                                }
                            }
                            //标定矩阵
                            param.HomMat2DTrans = new HHomMat2D();
                            param.HomMat2DTrans.VectorToHomMat2d(new HTuple(ImageX_9.ToArray()), new HTuple(ImageY_9.ToArray()), new HTuple(RobotX_9.ToArray()), new HTuple(RobotY_9.ToArray()));
                            //RMS计算
                            double calibRms = MV_CalRMS(param.HomMat2DTrans, ImageX_9.ToArray(), ImageY_9.ToArray(), RobotX_9.ToArray(), RobotY_9.ToArray());
                            Fit.FitCircle(ImageX_14.ToList(), ImageY_14.ToList(), out Circle circle);
                            param.RotateCenterX = circle.CenterX;
                            param.RotateCenterY = circle.CenterY;
                            switch (param.AngleType)
                            {
                                case AngleType.固定: //基准点作为定点旋转
                                                   //mRotateCenterX = mBaseX + 旋转中心X;
                                                   //mRotateCenterY = mBaseY + 旋转中心Y;
                                    HOperatorSet.VectorAngleToRigid(circle.CenterX, circle.CenterY, 0, circle.CenterX, circle.CenterY, 0, out param.HomMat2DRotate);//旋转矩阵

                                    break;
                                case AngleType.变化:
                                    //mRotateCenterX = (mMarkX - mBaseX) + 旋转中心X;//探针X-相机X+旋转中心X
                                    //mRotateCenterY = (mMarkY - mBaseY) + 旋转中心Y;//探针Y-相机Y+旋转中心Y 
                                    HOperatorSet.VectorAngleToRigid(circle.CenterX, circle.CenterY, 0, circle.CenterX, circle.CenterY, param.BaseAngle, out param.HomMat2DRotate);//旋转矩阵
                                    break;
                            }
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"MV_NPointCalibration函数出现异常：{RemoveHalconError(ex.Message)}");
            }
        }
        public static void MV_CameraCalibration(HImage image, CameraCalParam param, out CameraCalResult result, out HObject cross)
        {
            result = new CameraCalResult();
            cross = new HObject();
            if (image == null || !image.IsInitialized())
            {
                throw new VisionException(MV_ERR.图像为空, "MV_CameraCalibration函数的输入图像为空");
            }
            // 边缘提取,根据长度和圆度剔除异常圆
            HOperatorSet.EdgesSubPix(image, out HObject Edges, "canny", 1, param.LowThreshold, param.HighThreshold);
            HOperatorSet.SelectShapeXld(Edges, out Edges, "circularity", "and", param.MinRound, param.MaxRound);
            HOperatorSet.FitCircleContourXld(Edges, "algebraic", -1, 0, 0, 3, 2, out HTuple Row, out HTuple Col, out HTuple Radius, out HTuple StartPhi, out HTuple EndPhi, out HTuple order);
            Edges.Dispose();

            // 检查是否找到标志点
            if (Row == null || Row.Length < 1)
            {
                throw new VisionException(MV_ERR.未找到相机标志点, "MV_CameraCalibration函数没有找到标志点");
            }
            //try
            //{
            //    // 显示轮廓
            //    HOperatorSet.GenCrossContourXld(out cross, Row, Col, 20, 0);

            //    // 计算像素比  随机取一个点到其他点的距离,取最小的作为点间距,不允许孤立的点
            //    int seed = 0;
            //    Gen.SortPairs(ref Row, ref Col);
            //    HTuple chooseRow = HTuple.TupleGenConst(Row.Length - 1, Row[seed]);
            //    HTuple chooseCol = HTuple.TupleGenConst(Col.Length - 1, Col[seed]);
            //    HTuple tmpRow = Row.TupleRemove(seed);
            //    HTuple tmpCol = Col.TupleRemove(seed);
            //    double distance = HMisc.DistancePp(chooseRow, chooseCol, tmpRow, tmpCol).TupleMin().D;

            //    HTuple PositionX = new HTuple(), PositionY = new HTuple();
            //    for (int i = 0; i < Row.Length; i++)
            //    {
            //        if (i == seed)
            //        {
            //            PositionX = PositionX.TupleConcat(0f);
            //            PositionY = PositionY.TupleConcat(0f);
            //            continue;
            //        }
            //        int sRow = (int)((Row[i].D - Row[seed].D) / distance + ((Row[i].D - Row[seed].D) > 0 ? 1 : -1) * 0.5);//四舍五入
            //        int sCol = (int)((Col[i].D - Col[seed].D) / distance + ((Col[i].D - Col[seed].D) > 0 ? 1 : -1) * 0.5);//四舍五入
            //        PositionX = PositionX.TupleConcat(sCol * param.Distance);
            //        PositionY = PositionY.TupleConcat(sRow * param.Distance);
            //    }
            //    HTuple chooseX = HTuple.TupleGenConst(PositionX.Length - 1, PositionX[seed]);
            //    HTuple chooseY = HTuple.TupleGenConst(PositionY.Length - 1, PositionY[seed]);
            //    HTuple tmpX = PositionX.TupleRemove(seed);
            //    HTuple tmpY = PositionY.TupleRemove(seed);

            //    // 计算出像素当量
            //    double PixCount = HMisc.DistancePp(chooseRow, chooseCol, tmpRow, tmpCol).TupleSum().D;
            //    double mmCount = HMisc.DistancePp(chooseY, chooseX, tmpY, tmpX).TupleSum().D;
            //    double Scale = 1f;
            //    Scale = mmCount / PixCount;

            //    // 计算标定板角度偏差
            //    HHomMat2D hom = new HHomMat2D();
            //    hom.VectorToHomMat2d(PositionX, PositionY, Col, Row);
            //    double RMS = MV_CalRMS(hom, PositionX, PositionY, Col, Row);
            //    double sx = hom.HomMat2dToAffinePar(out double sy, out double phi, out double theta, out double tx, out double ty);
            //    hom = new HHomMat2D();
            //    hom = hom.HomMat2dRotate(phi, 0, 0);
            //    hom = hom.HomMat2dTranslate(Scale * Col[seed].D, Scale * Row[seed].D);
            //    PositionX = hom.AffineTransPoint2d(PositionX, PositionY, out PositionY);

            //    // 孔板放置夹角
            //    HHomMat2D homPhi = new HHomMat2D();
            //    homPhi = homPhi.HomMat2dRotate(phi, Col[seed].D, Row[seed].D);

            //    // 计算比例
            //    tmpCol = homPhi.AffineTransPoint2d(Col, Row, out tmpRow);

            //    // 更新数据
            //    result = new CameraCalResult(0, distance, Scale, Row.Length, tx, ty, phi, theta, RMS);
            //}
            //catch (Exception ex)
            //{
            //    throw new VisionException(MV_ERR.出现异常, $"MV_CameraCalibration函数出现异常：{RemoveHalconError(ex.Message)}");
            //}
        }
        public static double MV_CalRMS(HHomMat2D hom2d, HTuple x_Image, HTuple y_Image, HTuple x_Robot, HTuple y_Robot)
        {
            try
            {
                double num = 0.0;
                HHomMat2D hHomMat2D = hom2d.HomMat2dInvert();
                for (int i = 0; i < x_Image.Length; i++)
                {
                    double qy;
                    double column = hHomMat2D.AffineTransPoint2d(x_Robot[i].D, y_Robot[i].D, out qy);
                    double num2 = HMisc.DistancePp(qy, column, y_Image[i], x_Image[i]);
                    num += num2 * num2;
                }
                return Math.Sqrt(num / (double)x_Image.Length);
            }
            catch (Exception)
            {
                return -1.0;
            }
        }
        #endregion
        #region 仿射变换
        // 刚性变换矩阵
        public static void MV_VectorAngleToRigid(double row1, double col1, double angle1, double row2, double col2, double angle2, out HTuple homMat2D)
        {
            try
            {
                HOperatorSet.VectorAngleToRigid(row1, col1, angle1, row2, col2, angle2, out homMat2D);
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }

        // 点仿射
        public static void MV_Affine2d(HTuple mat2D, double col, double row, out double qx, out double qy)
        {
            try
            {
                HOperatorSet.AffineTransPoint2d(mat2D, row, col, out HTuple Qy, out HTuple Qx);
                qx = Qx.D;
                qy = Qy.D;
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }

        //线仿射
        public static void MV_Affine2d(HTuple mat2D, Line line, out Line tranLine)
        {
            tranLine = new Line();
            HHomMat2D TempHomMat2D = new HHomMat2D(mat2D);
            if (TempHomMat2D.RawData.Length == 0)
            {
                throw new VisionException(MV_ERR.出现异常, "MV_Affine2d函数的仿射矩阵为空");
            }
            HTuple X0 = new HTuple();
            HTuple X1 = new HTuple();
            tranLine.StartY = TempHomMat2D.AffineTransPoint2d(line.StartY, line.StartX, out X0);
            tranLine.StartX = X0;
            tranLine.EndY = TempHomMat2D.AffineTransPoint2d(line.EndY, line.EndX, out X1);
            tranLine.EndX = X1;
        }

        //圆仿射
        public static void MV_Affine2d(HTuple mat2D, Circle circle, out Circle tranCircle)
        {
            tranCircle = new Circle();
            HHomMat2D TempHomMat2D = new HHomMat2D(mat2D);
            if (TempHomMat2D.RawData.Length == 0)
            {
                throw new VisionException(MV_ERR.出现异常, "MV_Affine2d函数的仿射矩阵为空");
            }
            HTuple X0 = new HTuple();
            HTuple X1 = new HTuple();
            tranCircle.CenterY = TempHomMat2D.AffineTransPoint2d(circle.CenterY, circle.CenterX, out X0);
            tranCircle.CenterX = X0;
            tranCircle.Radius = circle.Radius;
        }
        public static void MV_Affine2d(HTuple mat2D, Rectangle2 intRect, out Rectangle2 tranRect)
        {
            tranRect = new Rectangle2();
            HHomMat2D TempHomMat2D = new HHomMat2D(mat2D);
            if (TempHomMat2D.RawData.Length == 0)
            {
                throw new VisionException(MV_ERR.出现异常, "MV_Affine2d函数的仿射矩阵为空");
            }
            double row, col, Phi;
            row = TempHomMat2D.AffineTransPoint2d(intRect.CenterY, intRect.CenterX, out col);
            Phi = ((HTuple)TempHomMat2D[0]).TupleAcos().D;
            tranRect.Length1 = intRect.Length1;
            tranRect.Length2 = intRect.Length2;
            tranRect.CenterY = row;
            tranRect.CenterX = col;

            // 2. 计算变换后的角度（从仿射矩阵提取旋转分量）
            double cosPhi = TempHomMat2D[0].D;  // 旋转矩阵的cos分量
            double sinPhi = TempHomMat2D[1].D;  // 旋转矩阵的sin分量
            double phi = Math.Atan2(sinPhi, cosPhi);  // 计算旋转角度（弧度）

            // 3. 更新矩形的角度（考虑角度叠加的模运算）
            tranRect.Phi = intRect.Phi + phi;
            tranRect.Phi = WrapAngle(tranRect.Phi);  // 确保角度在[-π, π]范围内
        }

        // 辅助函数：将角度限制在[-π, π]范围内
        private static double WrapAngle(double angle)
        {
            angle = angle % (2 * Math.PI);
            if (angle > Math.PI)
                angle -= 2 * Math.PI;
            else if (angle < -Math.PI)
                angle += 2 * Math.PI;
            return angle;
        }
        #endregion
        #region 测量
        //一维测量       
        private static double _row, _col, _phi, _length1, _length2, _width, _height;
        private static string _interpolation = "";
        private static HTuple _measureHandle;
        private static void MV_GenMeasureRectangle2(double row, double column, double phi, double length1, double length2, double width, double height, string interpolation, ref HTuple measureHandle)
        {
            if (_row != row ||
               _col != column ||
               _phi != phi ||
               _length1 != length1 ||
               _length2 != length2 ||
               _width != width ||
               _height != height ||
               _interpolation != interpolation ||
               measureHandle == null)
            {
                HOperatorSet.GenMeasureRectangle2(row, column, -phi, length1, length2, width, height, interpolation, out HTuple handle);
                measureHandle?.Dispose();
                measureHandle = handle;
            }

            _row = row;
            _col = column;
            _phi = phi;
            _length1 = length1;
            _length2 = length2;
            _width = width;
            _height = height;
            _interpolation = interpolation;

        }
        public static void MV_ApplyPos(HImage image,  MeasureParam param, out MeasurePosResult result)
        {
            result = new MeasurePosResult();
            try
            {
                List<PointXY> distanceXY = new List<PointXY>();
                List<double> rows = new List<double>();
                List<double> cols = new List<double>();

                image.GetImageSize(out HTuple width, out HTuple height);
                MV_GenMeasureRectangle2(param.CenterRow, param.CenterCol, -param.Phi, param.Length1, param.Length2, width, height, param.MeasInterpolation.ToString(), ref _measureHandle);
                HOperatorSet.MeasurePos(image, _measureHandle, param.Sigma, param.MinEdgeCurvature, REnum.EnumToStr(param.MeasMode), REnum.EnumToStr(param.MeasSelect), out HTuple rowEdge, out HTuple columnEdge, out HTuple amplitude, out HTuple distance);
                if (rowEdge.Length > 0)
                {
                    for (int i = 0; i < rowEdge.Length; i++)
                    {
                        distanceXY.Add(new PointXY(columnEdge[i] - width / 2, rowEdge[i] - height / 2));
                        rows.Add(rowEdge[i]);
                        cols.Add(columnEdge[i]);
                    }
                    result = new MeasurePosResult(rows, cols, distanceXY, rowEdge, columnEdge);
                    return;
                }
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"MV_ApplyPos函数出现异常：{RemoveHalconError(ex.Message)}");
            }

            throw new VisionException(MV_ERR.一维测量错误, "MV_ApplyPos函数查找点失败");
        }
        public static void MV_ApplyPairs(HImage image,  MeasureParam param, out MeasurePairResult result)
        {
            result = new MeasurePairResult();
            if (image == null)
            {
                throw new VisionException(MV_ERR.图像为空, "MV_ApplyPairs函数的输入图像为空");
            }
            try
            {
                image.GetImageSize(out HTuple width, out HTuple height);
                MV_GenMeasureRectangle2(param.CenterRow, param.CenterCol, -param.Phi, param.Length1, param.Length2, width, height, param.MeasInterpolation.ToString(), ref _measureHandle);
                HOperatorSet.MeasurePairs(image, _measureHandle, param.Sigma, param.MinEdgeCurvature, REnum.EnumToStr(param.MeasMode), REnum.EnumToStr(param.MeasSelect), out HTuple rowEdgeFirst, out HTuple columnEdgeFirst, out HTuple amplitudeFirst, out HTuple rowEdgeSecond, out HTuple columnEdgeSecond, out HTuple amplitudeSecond, out HTuple intraDistance, out HTuple interDistance);
                if (rowEdgeFirst.Length > 0)
                {
                    double sinStart = new HTuple(Math.PI / 2 - param.Phi).TupleSin(); //角度为-phi
                    double cosStart = new HTuple(Math.PI / 2 - param.Phi).TupleCos();
                    double sinEnd = new HTuple(-Math.PI / 2 - param.Phi).TupleSin();
                    double cosEnd = new HTuple(-Math.PI / 2 - param.Phi).TupleCos();
                    HXLDCont hXLDCont = new HXLDCont();
                    HXLDCont midLinehXLD = new HXLDCont();
                    hXLDCont.GenEmptyObj();
                    midLinehXLD.GenEmptyObj();
                    List<PointXY> firstLineStarts = new List<PointXY>();
                    List<PointXY> firstLineEnds = new List<PointXY>();
                    List<PointXY> secondLineStarts = new List<PointXY>();
                    List<PointXY> secondLineEnds = new List<PointXY>();
                    List<double> intraDis = intraDistance.ToDArr().ToList();
                    List<double> interDis = interDistance.ToDArr().ToList();

                    for (int i = 0; i < rowEdgeFirst.Length; i++)
                    {
                        //生成线段
                        firstLineStarts.Add(new PointXY(cosStart * param.Length2 + columnEdgeFirst[i].D, -sinStart * param.Length2 + rowEdgeFirst[i].D));
                        firstLineEnds.Add(new PointXY(cosEnd * param.Length2 + columnEdgeFirst[i].D, -sinEnd * param.Length2 + rowEdgeFirst[i].D));
                        secondLineStarts.Add(new PointXY(cosStart * param.Length2 + columnEdgeSecond[i].D, -sinStart * param.Length2 + rowEdgeSecond[i].D));
                        secondLineEnds.Add(new PointXY(cosEnd * param.Length2 + columnEdgeSecond[i].D, -sinEnd * param.Length2 + rowEdgeSecond[i].D));

                        HTuple rowFirstTuple = new HTuple(firstLineStarts[i].Y, firstLineEnds[i].Y);
                        HTuple cowFirstTuple = new HTuple(firstLineStarts[i].X, firstLineEnds[i].X);
                        HTuple rowSecondTuple = new HTuple(secondLineStarts[i].Y, secondLineEnds[i].Y);
                        HTuple cowSecondTuple = new HTuple(secondLineStarts[i].X, secondLineEnds[i].X);

                        HXLDCont hXLDFirst = new HXLDCont(rowFirstTuple, cowFirstTuple);
                        HXLDCont hXLDSecond = new HXLDCont(rowSecondTuple, cowSecondTuple);
                        HXLDCont tempXLDCont1 = hXLDCont.ConcatObj(hXLDFirst);
                        hXLDCont.Dispose();
                        HXLDCont tempXLDCont2 = tempXLDCont1.ConcatObj(hXLDSecond);
                        hXLDCont = tempXLDCont2;
                        tempXLDCont1.Dispose();
                        hXLDFirst.Dispose();
                        hXLDSecond.Dispose();
                        HXLDCont hXLDLineWidth = new HXLDCont(new HTuple(rowEdgeFirst[i].D, rowEdgeSecond[i].D), new HTuple(columnEdgeFirst[i].D, columnEdgeSecond[i].D));
                        midLinehXLD = midLinehXLD.ConcatObj(hXLDLineWidth);
                        hXLDLineWidth.Dispose();
                    }
                    result = new MeasurePairResult(firstLineStarts, firstLineEnds, secondLineStarts, secondLineEnds, intraDis, interDis, hXLDCont, midLinehXLD);
                    return;
                }
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"MV_ApplyPairs函数出现异常：{RemoveHalconError(ex.Message)}");
            }

            throw new VisionException(MV_ERR.一维测量错误, "MV_ApplyPairs函数查找边缘对失败");
        }
        public static void MV_SetMetrologyOrigin(HTuple handle, double x, double y, double phi)
        {
            try
            {
                double[] model = { y, x, phi };
                HTuple hTuple = new HTuple(model);
                HOperatorSet.SetMetrologyModelParam(handle, "reference_system", hTuple);
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        public static void MV_AlineMetrologyModel(HTuple handle, double row, double column, double phi)
        {
            try
            {
                HOperatorSet.AlignMetrologyModel(handle, row, column, phi);
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 二维测量
        public static void MV_MeasLine(HImage image, MetrologyParam param, out MetrologyResult outLine, out HTuple outR, out HTuple outC, out HObject outXld, HRegion disableRegion = null)
        {
            outR = new HTuple();
            outC = new HTuple();
            outXld = new HXLDCont();
            outLine = new MetrologyResult();
            outLine.Result = new Line();
            Line line = (Line)outLine.Result;
            try
            {
                if (param.MetrologyHandle != null && param.MetrologyHandle.Length == 6)
                {
                    // 清理卡尺模板
                    HOperatorSet.ClearMetrologyModel(param.MetrologyHandle);
                    param.MetrologyHandle.Dispose();
                }

                HOperatorSet.CreateMetrologyModel(out HTuple metrologyHandle);

                param.MetrologyHandle = metrologyHandle;


                // 最强边的计算
                //if (meas.ParamValue[1] == "strongest")
                //{
                //    MeasLine1D(image, line, meas, out outLine, out outR, out outC, out outXld, disableRegion);
                //    return;
                //}

                // 降低直线拟合的最低得分
                HOperatorSet.AddMetrologyObjectLineMeasure(param.MetrologyHandle, param.ShapeParams[0], param.ShapeParams[1], param.ShapeParams[2], param.ShapeParams[3], param.Length1, param.Length2, 1, param.Threshold, new HTuple(), new HTuple(), out HTuple index);
                HOperatorSet.SetMetrologyObjectParam(param.MetrologyHandle, "all", "measure_transition", REnum.EnumToStr(param.MeasMode));
                HOperatorSet.SetMetrologyObjectParam(param.MetrologyHandle, "all", "measure_select", REnum.EnumToStr(param.MeasSelect));
                HOperatorSet.SetMetrologyObjectParam(param.MetrologyHandle, "all", "measure_threshold", param.Threshold);
                HOperatorSet.SetMetrologyObjectParam(param.MetrologyHandle, "all", "measure_distance", param.MeasDis);
                HOperatorSet.SetMetrologyObjectParam(param.MetrologyHandle, "all", "min_score", param.MinScore);
                //HOperatorSet.SetMetrologyObjectParam(param.MetrologyHandle, "all", "distance_threshold", param.DistanceThreshold);
                if (disableRegion != null && disableRegion.IsInitialized())
                {
                    HOperatorSet.ApplyMetrologyModel(image, param.MetrologyHandle);
                    // 单个测量区域 刚好 有一大半在屏蔽区域,一小部分在有效区域,这时候也会测出一个点这个点在屏蔽区域内,导致精度损失约为1个像素左右.需要喷绘之后,再进行点是否在屏蔽区域判断
                    HOperatorSet.GetMetrologyObjectMeasures(out outXld, param.MetrologyHandle, "all", "all", out outR, out outC);

                    List<double> tempOutR = new List<double>(), tempOutC = new List<double>();
                    for (int i = 0; i < outR.DArr.Length - 1; i++)
                    {
                        //0 表示没有包含
                        if (disableRegion.TestRegionPoint(outR[i].D, outC[i].D) == 0)
                        {
                            tempOutR.Add(outR[i].D);
                            tempOutC.Add(outC[i].D);
                        }
                    }
                    outR = new HTuple(tempOutR.ToArray());
                    outC = new HTuple(tempOutC.ToArray());
                }
                else
                {
                    HOperatorSet.ApplyMetrologyModel(image, param.MetrologyHandle);
                    HOperatorSet.GetMetrologyObjectMeasures(out outXld, param.MetrologyHandle, "all", "all", out outR, out outC);
                }
                HOperatorSet.GetMetrologyObjectResult(param.MetrologyHandle, "all", "all", "result_type", "all_param", out HTuple lineResult);
                if (lineResult.TupleLength() >= 4)
                {
                    line.StartY = Math.Round(lineResult[0].D, 4);
                    line.StartX = Math.Round(lineResult[1].D, 4);
                    line.EndY = Math.Round(lineResult[2].D, 4);
                    line.EndX = Math.Round(lineResult[3].D, 4);
                    line.MidX = (line.StartX + line.EndX) / 2;
                    line.MidY = (line.StartY + line.EndY) / 2;
                    line.Phi = Math.Round(HMisc.AngleLx(line.StartY, line.StartX, line.EndY, line.EndX), 4);
                    line.Dist = Math.Round(HMisc.DistancePp(line.StartX, line.StartY, line.EndX, line.EndY), 4);
                    line.Y = outR;
                    line.X = outC;
                    line.Nx = line.EndX - line.StartX;
                    line.Ny = line.StartY - line.EndY;
                    HOperatorSet.GenContourPolygonXld(out HObject contour, new HTuple(new double[] { lineResult[0].D, lineResult[2].D }), new HTuple(new double[] { lineResult[1].D, lineResult[3].D }));
                    outLine.ResultRegion = contour;
                    return;
                }
                else
                {

                    //if (Fit.FitLine(outR.ToDArr().ToList(), outC.ToDArr().ToList(), out outLine))
                    //    outLine = line;
                }
                throw new VisionException(MV_ERR.二维测量错误, "MV_MeasLine函数查找直线失败");
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 二维测量
        public static void MV_MeasLines(HImage image, MetrologyParam param, int length, string[] selects, string[] transitions, out List<MetrologyResult> outLines, out HTuple outR, out HTuple outC, out HObject outXld)
        {
            outR = new HTuple();
            outC = new HTuple();
            outXld = new HXLDCont();
            outLines = new List<MetrologyResult>();
            try
            {
                if (param.MetrologyHandle != null && param.MetrologyHandle.Length == 6)
                {
                    // 清理卡尺模板
                    HOperatorSet.ClearMetrologyModel(param.MetrologyHandle);
                    param.MetrologyHandle.Dispose();
                }

                HOperatorSet.CreateMetrologyModel(out HTuple metrologyHandle);

                param.MetrologyHandle = metrologyHandle;


                // 最强边的计算
                //if (meas.ParamValue[1] == "strongest")
                //{
                //    MeasLine1D(image, line, meas, out outLine, out outR, out outC, out outXld, disableRegion);
                //    return;
                //}

                // 降低直线拟合的最低得分
                HTuple lineRow1 = new HTuple(), lineCol1 = new HTuple();
                HTuple lineRow2 = new HTuple(), lineCol2 = new HTuple();
                for (int j = 0; j < length; j++)
                {
                    lineRow1[j] = param.ShapeParams[0];
                    lineCol1[j] = param.ShapeParams[1];
                    lineRow2[j] = param.ShapeParams[2];
                    lineCol2[j] = param.ShapeParams[3];
                }

                HOperatorSet.AddMetrologyObjectLineMeasure(param.MetrologyHandle, lineRow1, lineCol1, lineRow2, lineCol2, param.Length1, param.Length2, 1, param.Threshold, new HTuple(), new HTuple(), out HTuple index);

                int i = 0;
                for (; i < index.Length; i++)
                {
                    HOperatorSet.SetMetrologyObjectParam(param.MetrologyHandle, i, "measure_transition", transitions[i]);
                    HOperatorSet.SetMetrologyObjectParam(param.MetrologyHandle, i, "measure_select", selects[i]);
                }

                HOperatorSet.SetMetrologyObjectParam(param.MetrologyHandle, "all", "measure_distance", param.MeasDis);
                HOperatorSet.SetMetrologyObjectParam(param.MetrologyHandle, "all", "min_score", param.MinScore);

                HOperatorSet.ApplyMetrologyModel(image, param.MetrologyHandle);
                HOperatorSet.GetMetrologyObjectMeasures(out outXld, param.MetrologyHandle, "all", "all", out outR, out outC);


                HOperatorSet.GetMetrologyObjectResult(param.MetrologyHandle, index, "all", "result_type", "all_param", out HTuple lineResult);
                if (lineResult.TupleLength() >= 4 * index.Length)
                {
                    for (int j = 0; j < lineResult.Length; j += 4)
                    {
                        Line line = new Line();
                        line.StartY = Math.Round(lineResult[j].D, 4);
                        line.StartX = Math.Round(lineResult[j + 1].D, 4);
                        line.EndY = Math.Round(lineResult[j + 2].D, 4);
                        line.EndX = Math.Round(lineResult[j + 3].D, 4);
                        line.MidX = (line.StartX + line.EndX) / 2;
                        line.MidY = (line.StartY + line.EndY) / 2;
                        line.Phi = Math.Round(HMisc.AngleLx(line.StartY, line.StartX, line.EndY, line.EndX), 4);
                        line.Dist = Math.Round(HMisc.DistancePp(line.StartX, line.StartY, line.EndX, line.EndY), 4);
                        line.Y = outR;
                        line.X = outC;
                        line.Nx = line.EndX - line.StartX;
                        line.Ny = line.StartY - line.EndY;
                        HOperatorSet.GenContourPolygonXld(out HObject contour, new HTuple(new double[] { lineResult[j].D, lineResult[j + 2].D }), new HTuple(new double[] { lineResult[j + 1].D, lineResult[j + 3].D }));
                        outLines.Add(new MetrologyResult() { Result = line, ResultRegion = contour });
                    }
                    return;
                }

                throw new VisionException(MV_ERR.二维测量错误, "查找直线失败");
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        public static void MV_MeasCircle(HImage image, MetrologyParam param, out MetrologyResult outCircle, out HTuple outR, out HTuple outC, out HObject outXld, HRegion disableRegion = null)
        {
            outR = new HTuple();
            outC = new HTuple();
            outXld = new HXLDCont();
            outCircle = new MetrologyResult(MetrologyType.圆, typeof(Circle), new Circle());
            Circle circle = (Circle)outCircle.Result;
            try
            {
                if (param.MetrologyHandle != null && param.MetrologyHandle.Length == 6)
                {
                    // 清理卡尺模板
                    HOperatorSet.ClearMetrologyModel(param.MetrologyHandle);
                    param.MetrologyHandle.Dispose();
                }

                HOperatorSet.CreateMetrologyModel(out HTuple metrologyHandle);

                param.MetrologyHandle = metrologyHandle;

                // 降低圆拟合的最低得分
                HOperatorSet.AddMetrologyObjectCircleMeasure(param.MetrologyHandle, param.ShapeParams[0], param.ShapeParams[1], param.ShapeParams[2], param.Length1, param.Length2, 1, param.Threshold, new HTuple(), new HTuple(), out HTuple index);
                HOperatorSet.SetMetrologyObjectParam(param.MetrologyHandle, "all", "measure_transition", REnum.EnumToStr(param.MeasMode));
                HOperatorSet.SetMetrologyObjectParam(param.MetrologyHandle, "all", "measure_select", REnum.EnumToStr(param.MeasSelect));
                HOperatorSet.SetMetrologyObjectParam(param.MetrologyHandle, "all", "measure_threshold", param.Threshold);
                HOperatorSet.SetMetrologyObjectParam(param.MetrologyHandle, "all", "measure_distance", param.MeasDis);
                HOperatorSet.SetMetrologyObjectParam(param.MetrologyHandle, "all", "min_score", param.MinScore);

                HOperatorSet.ApplyMetrologyModel(image, param.MetrologyHandle);
                HOperatorSet.GetMetrologyObjectMeasures(out outXld, param.MetrologyHandle, "all", "all", out outR, out outC);
                if (disableRegion != null && disableRegion.IsInitialized() && disableRegion.Area > 0 && outR.Length > 0)
                {
                    List<double> tempOutR = new List<double>(), tempOutC = new List<double>();
                    for (int i = 0; i < outR.DArr.Length - 1; i++)
                    {
                        //0 表示没有包含
                        if (disableRegion.TestRegionPoint(outR[i].D, outC[i].D) == 0)
                        {
                            tempOutR.Add(outR[i].D);
                            tempOutC.Add(outC[i].D);
                        }
                    }
                    outR = new HTuple(tempOutR.ToArray());
                    outC = new HTuple(tempOutC.ToArray());
                    Fit.FitCircle(outR.ToDArr().ToList(), outC.ToDArr().ToList(), out circle);
                    return;
                }
                else
                {
                    HOperatorSet.GetMetrologyObjectResult(param.MetrologyHandle, new HTuple("all"), new HTuple("all"), new HTuple("result_type"), new HTuple("all_param"), out HTuple circleResult);
                    if (circleResult.TupleLength() >= 3)
                    {
                        circle.CenterY = Math.Round(circleResult[0].D, 4);
                        circle.CenterX = Math.Round(circleResult[1].D, 4);
                        circle.Radius = Math.Round(circleResult[2].D, 4);
                        HOperatorSet.GenCircle(out HObject circleRegion, circleResult[0].D, circleResult[1].D, circleResult[2].D);
                        outCircle.ResultRegion = new HRegion(circleRegion);
                        circleRegion.Dispose();
                        return;
                    }
                    else
                    {
                        //Fit.FitCircle(outR.ToDArr().ToList(), outC.ToDArr().ToList(), out circle);
                        //return;
                    }

                }
                throw new VisionException(MV_ERR.二维测量错误, "MV_MeasLine函数查找圆失败");
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        // 距离
        public static void MV_DistancePp(double row1, double col1, double row2, double col2, out double distance)
        {
            try
            {
                distance = HMisc.DistancePp(row1, col1, row2, col2);
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        #endregion
        #region 几何关系
        //交点 
        // <param name="isParallel">平行1，不平行0
        public static void MV_IntersectionLines(double line1Row1, double line1Column1, double line1Row2, double line1Column2, double line2Row1, double line2Column1, double line2Row2, double line2Column2, out double row, out double column, out double deg, out int isParallel)
        {
            try
            {
                row = 0.0; column = 0.0; deg = 0.0; isParallel = 0;
                HMisc.IntersectionLl(line1Row1, line1Column1, line1Row2, line1Column2, line2Row1, line2Column1, line2Row2, line2Column2, out row, out column, out isParallel);
                deg = HMisc.AngleLl(line1Row1, line1Column1, line1Row2, line1Column2, line2Row1, line2Column1, line2Row2, line2Column2);
                if (Math.Abs(deg) < 0.0174 || Math.Abs(deg - 3.14) < 0.0174)
                {
                    isParallel = 1;
                }
                else
                {
                    isParallel = 0;
                }
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }

        //交点
        public static void MV_IntersectionLines(double line1Row1, double line1Column1, double line1Row2, double line1Column2, double line2Row1, double line2Column1, double line2Row2, double line2Column2, out double row, out double column, out double isOverlapping)
        {
            try
            {
                // 获取两直线的交点
                HOperatorSet.IntersectionLines(line1Row1, line1Column1, line1Row2, line1Column2, line2Row1, line2Column1, line2Row2, line2Column2, out HTuple row1, out HTuple column1, out HTuple isOverlapping1);
                row = row1;
                column = column1;
                isOverlapping = isOverlapping1;
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }

        public static void MV_DistanceLl(double line1Row1, double line1Column1, double line1Row2, double line1Column2, double line2Row1, double line2Column1, double line2Row2, double line2Column2, out double distance)
        {
            try
            {
                HOperatorSet.AngleLl(line1Row1, line1Column1, line1Row2, line1Column2, line2Row1, line2Column1, line2Row2, line2Column2, out HTuple angle);
                bool isParallel = Math.Abs(angle.D) < 1e-6 || Math.Abs(angle.D - Math.PI) < 1e-6;
                if (isParallel)
                {
                    HOperatorSet.DistancePl(line1Row1, line1Column1, line2Row1, line2Column1, line2Row2, line2Column2, out HTuple dis);
                    distance = dis.D;
                }
                else
                {
                    HOperatorSet.IntersectionLines(line1Row1, line1Column1, line1Row2, line1Column2, line2Row1, line2Column1, line2Row2, line2Column2, out HTuple row1, out HTuple column1, out HTuple isOverlapping);
                    if (isOverlapping)
                    {
                        distance = 0;
                    }
                    else
                    {
                        HOperatorSet.DistancePp(line1Row1, line1Column1, line2Row1, line2Column1, out HTuple dis1);
                        HOperatorSet.DistancePp(line1Row1, line1Column1, line2Row2, line2Column2, out HTuple dis2);
                        HOperatorSet.DistancePp(line1Row2, line1Column2, line2Row1, line2Column1, out HTuple dis3);
                        HOperatorSet.DistancePp(line1Row2, line1Column2, line2Row2, line2Column2, out HTuple dis4);
                        distance = Math.Min(Math.Min(dis1.D, dis2.D), Math.Min(dis3.D, dis4.D));
                    }
                }
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }

        public static void MV_DistanceSs(double line1Row1, double line1Column1, double line1Row2, double line1Column2, double line2Row1, double line2Column1, double line2Row2, double line2Column2, out double distanceMin, out double distanceMax)
        {
            try
            {
                HOperatorSet.DistanceSs(line1Row1, line1Column1, line1Row2, line1Column2, line2Row1, line2Column1, line2Row2, line2Column2, out HTuple disMin, out HTuple disMax);
                distanceMin = disMin.D;
                distanceMax = disMax.D;
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }

        //点点构建 生成直线
        public static void MV_GenLineContour(double x1, double y1, double x2, double y2, out HObject ResultXLD)
        {
            try
            {
                HOperatorSet.GenContourPolygonXld(out ResultXLD, new HTuple(y1, y2), new HTuple(x1, x2));
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }

        // 点线构建 求点到线的垂足
        public static void MV_PLPedal(double X, double Y, Line line, out double outX, out double outY, out double Dis)
        {
            try
            {
                HMisc.ProjectionPl(Y, X, line.StartY, line.StartX, line.EndY, line.EndX, out outY, out outX);
                Dis = HMisc.DistancePl(Y, X, line.StartY, line.StartX, line.EndY, line.EndX);
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"{RemoveHalconError(ex.Message)}");
            }
        }
        #endregion

        #region 二维码
        // 创建+学习模板 是否更新图像？清屏？
        public static void MV_TeachQRCode(HImage image, Bcr2dParam dataQRCode, out string outStr)
        {
            outStr = string.Empty;
            try
            {
                // 创建二维码模板
                //if(dataQRCode.IsEnableDMCode == true && dataQRCode.IsEnableQRCode == true)
                //{
                //    HOperatorSet.CreateDataCode2dModel("QR Code", "default_parameters", REnum.EnumToStr(dataQRCode.CodeApplyMode), out dataQRCode.DataCodeHandle);
                //    HOperatorSet.CreateDataCode2dModel("Data Matrix ECC 200", "default_parameters", REnum.EnumToStr(dataQRCode.CodeApplyMode), out dataQRCode.DataCodeHandle);
                //    //HOperatorSet.SetDataCode2dParam(dataQRCode.DataCodeHandle, "symbol_shape", REnum.EnumToStr(dataQRCode.DMCodeType));
                //}
                //else
                if (dataQRCode.IsEnableQRCode == true)
                {
                    HOperatorSet.CreateDataCode2dModel("QR Code", "default_parameters", REnum.EnumToStr(dataQRCode.CodeApplyMode), out dataQRCode.DataCodeHandle);
                }
                else /*if(dataQRCode.IsEnableDMCode)*/
                {
                    HOperatorSet.CreateDataCode2dModel("Data Matrix ECC 200", "default_parameters", REnum.EnumToStr(dataQRCode.CodeApplyMode), out dataQRCode.DataCodeHandle);
                    HOperatorSet.SetDataCode2dParam(dataQRCode.DataCodeHandle, "symbol_shape", REnum.EnumToStr(dataQRCode.DMCodeType));
                }

                HOperatorSet.SetDataCode2dParam(dataQRCode.DataCodeHandle, "polarity", REnum.EnumToStr(dataQRCode.CodePolarity));
                HOperatorSet.SetDataCode2dParam(dataQRCode.DataCodeHandle, "mirrored", REnum.EnumToStr(dataQRCode.CodeMirrored));
                //HOperatorSet.SetDataCode2dParam(dataQRCode.DataCodeHandle, "module_size_min", dataQRCode.ModuleSizeMin);
                //HOperatorSet.SetDataCode2dParam(dataQRCode.DataCodeHandle, "module_size_max", dataQRCode.ModuleSizeMax);
                HOperatorSet.SetDataCode2dParam(dataQRCode.DataCodeHandle, "timeout", dataQRCode.TimeOut);

                // 查找二维码
                HOperatorSet.FindDataCode2d(image, out HObject hObject, dataQRCode.DataCodeHandle, new HTuple(), new HTuple(), out HTuple hv_ResuleHandle, out HTuple hv_ResultStr);
                hv_ResuleHandle.Dispose();
                if (hv_ResultStr.Length > 0)
                {
                    outStr = hv_ResultStr.S;
                    return;
                }
            }
            catch (Exception ex)
            {
                dataQRCode.DataCodeHandle?.Dispose();
                dataQRCode.DataCodeHandle = null;
                throw new VisionException(MV_ERR.出现异常, $"TeachQRCode函数出现异常：{RemoveHalconError(ex.Message)}");
            }

            throw new VisionException(MV_ERR.二维码错误, "TeachQRCode函数错误");
        }

        public static void MV_FindQRCode(HImage image, Bcr2dParam dataQRCode, out string outStr)
        {
            outStr = string.Empty;
            try
            {
                if (dataQRCode.DataCodeHandle == null)
                {
                    MV_TeachQRCode(image, dataQRCode, out outStr);
                    return;
                }
                else
                {
                    // 识别二维码
                    HOperatorSet.FindDataCode2d(image, out HObject hObject, dataQRCode.DataCodeHandle, new HTuple(), new HTuple(), out HTuple hv_ResuleHandle, out HTuple hv_ResultStr);
                    hv_ResuleHandle.Dispose();
                    if (hv_ResultStr.Length > 0)
                    {
                        // 获取结果
                        outStr = hv_ResultStr.S;
                        return;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"FindQRCode函数出现异常:{RemoveHalconError(ex.Message)}");
            }

            throw new VisionException(MV_ERR.二维码错误, "FindQRCode函数读取二维码内容失败");
        }

        public static void MV_SetQRCodeParam(Bcr2dParam dataQRCode, string param)
        {
            try
            {
                switch (param)
                {
                    case "QRCode":
                    case "DMCode":
                        {
                            HOperatorSet.ClearDataCode2dModel(dataQRCode.DataCodeHandle);
                            dataQRCode.DataCodeHandle?.Dispose();
                            dataQRCode.DataCodeHandle = null;
                        }
                        break;
                    case "default_parameters":
                        {
                            HOperatorSet.SetDataCode2dParam(dataQRCode.DataCodeHandle, "default_parameters", REnum.EnumToStr(dataQRCode.CodeApplyMode));
                        }
                        break;
                    case "polarity":
                        {
                            HOperatorSet.SetDataCode2dParam(dataQRCode.DataCodeHandle, "polarity", REnum.EnumToStr(dataQRCode.CodePolarity));
                        }
                        break;
                    case "mirrored":
                        {
                            HOperatorSet.SetDataCode2dParam(dataQRCode.DataCodeHandle, "mirrored", REnum.EnumToStr(dataQRCode.CodeMirrored));
                        }
                        break;
                    case "module_size_min":
                        {
                            HOperatorSet.SetDataCode2dParam(dataQRCode.DataCodeHandle, "module_size_min", dataQRCode.ModuleSizeMin);
                        }
                        break;
                    case "module_size_max":
                        {
                            HOperatorSet.SetDataCode2dParam(dataQRCode.DataCodeHandle, "module_size_max", dataQRCode.ModuleSizeMax);
                        }
                        break;
                    case "timeout":
                        {
                            HOperatorSet.SetDataCode2dParam(dataQRCode.DataCodeHandle, "timeout", dataQRCode.TimeOut);
                        }
                        break;
                    case "symbol_shape":
                        {
                            if (!dataQRCode.IsEnableQRCode)
                            {
                                HOperatorSet.SetDataCode2dParam(dataQRCode.DataCodeHandle, "symbol_shape", REnum.EnumToStr(dataQRCode.DMCodeType));
                            }
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                throw new VisionException(MV_ERR.出现异常, $"MV_SetQRCodeParam函数出现异常:{RemoveHalconError(ex.Message)}");
            }
        }
        #endregion




        //深度学习异常值检测
        //public static void MV_PreprocessDataset(string imageDir, string anomalyLabelDir, HTuple imageSubDirs, string outPutDir, AnomalyDetectionParam param)
        //{
        //    try
        //    {
        //        //1.) 准备   ***
        //        //读取并预处理异常检测数据集
        //        HOperatorSet.CreateDict(out HTuple genParamDataset);
        //        HOperatorSet.SetDictTuple(genParamDataset, "image_sub_dirs", imageSubDirs);
        //        ReadDlDatasetAnomaly(imageDir, anomalyLabelDir, new HTuple(), new HTuple(), genParamDataset, out HTuple dLDataset);
        //        param.DLDataset?.Dispose();
        //        param.DLDataset = dLDataset;
        //        //拆分数据集
        //        SplitDlDataset(dLDataset, 50, 10, new HTuple());
        //        //加载异常检测模型并设置关键参数
        //        param.DLModelHandle?.Dispose();
        //        HOperatorSet.ReadDlModel("initial_dl_anomaly_medium.hdl", out HTuple dLModelHandle);
        //        param.DLModelHandle = dLModelHandle;
        //        HOperatorSet.SetDlModelParam(param.DLModelHandle, "image_width", param.ImageWidth);
        //        HOperatorSet.SetDlModelParam(param.DLModelHandle, "image_height", param.ImageHeight);
        //        //HOperatorSet.SetDlModelParam(param.DLModelHandle, "complexity", hv_Complexity);
        //        //设置硬件
        //        HOperatorSet.QueryAvailableDlDevices((new HTuple("runtime")).TupleConcat("id"), (new HTuple("cpu")).TupleConcat(0), out HTuple dLDevice);
        //        HOperatorSet.SetDlModelParam(param.DLModelHandle, "device", dLDevice);
        //        //设置预处理参数，对数据集进行预处理
        //        CreateDlPreprocessParamFromModel(param.DLModelHandle, "constant_values", "full_domain", new HTuple(), new HTuple(), new HTuple(), out HTuple dLPreprocessParam);
        //        HOperatorSet.CreateDict(out HTuple preprocessSettings);
        //        HOperatorSet.SetDictTuple(preprocessSettings, "overwrite_files", "true");
        //        PreprocessDlDataset(dLDataset, outPutDir, dLPreprocessParam, preprocessSettings, out HTuple dLDatasetFileName);

        //    }
        //    catch (Exception) 
        //    {
        //        throw new VisionException(MV_ERR.出现异常, "MV_PreprocessDataset函数出现异常");
        //    }
        //}
        //public static void MV_TrainDlModel(AnomalyDetectionParam param)
        //{
        //    try
        //    {
        //        HOperatorSet.CreateDict(out HTuple trainParamAnomaly);
        //        //设置训练误差阈值和最大训练周期数。如果训练误差低于此阈值，则训练结束。否则，训练将继续进行，直到达到最大纪元数。
        //        HOperatorSet.SetDictTuple(trainParamAnomaly, "error_threshold", 0.001);
        //        //设置域比率，该比率控制用于训练的每个图像的比例。通过设置更大的值，可以提高训练模型的性能。但是，这将增加培训时间。
        //        HOperatorSet.SetDictTuple(trainParamAnomaly, "domain_ratio", 0.25);
        //        //正则化噪声有助于使训练更加稳健。如果训练失败，请尝试设置更高的值。
        //        HOperatorSet.SetDictTuple(trainParamAnomaly, "regularization_noise", 0.01);
        //        CreateDlTrainParam(param.DLModelHandle, param.MaxNumEpochs, new HTuple(), param.EnableDisplayTrain, 73, "anomaly", trainParamAnomaly, out HTuple trainParam);
        //        TrainDlModel(param.DLDataset, param.DLModelHandle, trainParam, 0, out HTuple trainResults, out HTuple trainInfos, out HTuple evaluationInfos);

        //    }
        //    catch(Exception)
        //    {
        //        throw new VisionException(MV_ERR.出现异常, "MV_TrainDlModel函数出现异常");
        //    }
        //}
        //public static void MV_EvaluateDLModel(AnomalyDetectionParam param)
        //{
        //    try
        //    {
        //        //计算分类和分割阈值并评估训练模型的性能
        //        //设置用于计算异常分数的因子，如果存在小缺陷，则较大的值可能是合适的。
        //        HOperatorSet.SetDlModelParam(param.DLModelHandle, "standard_deviation_factor",  param.StandardDeviationFactor);

        //        HOperatorSet.CreateDict(out HTuple genParamThresholds);
        //        HOperatorSet.SetDictTuple(genParamThresholds, "enable_display", 1);
        //        ComputeDlAnomalyThresholds(param.DLModelHandle, param.DLDataset, genParamThresholds, out HTuple anomalySegmentationThreshold, out HTuple anomalyClassificationThresholds);
        //        param.AnomalySegmentationThreshold = anomalySegmentationThreshold;
        //        param.AnomalyClassificationThresholds = anomalyClassificationThresholds;
        //        //设置通用评估参数并在测试拆分上评估模型
        //        HOperatorSet.CreateDict(out HTuple genParamEvaluation);
        //        HOperatorSet.SetDictTuple(genParamEvaluation, "measures", "all");
        //        HOperatorSet.SetDictTuple(genParamEvaluation, "anomaly_classification_thresholds", anomalyClassificationThresholds);

        //        EvaluateDlModel(param.DLDataset, param.DLModelHandle, "split", "test", genParamEvaluation, out HTuple evaluationResult, out HTuple evalParams);

        //        //可视化测试图像的图像级异常分数的直方图以及用于评估的分类阈值
        //        HOperatorSet.CreateDict(out HTuple genParamDisplay);
        //        HOperatorSet.SetDictTuple(genParamDisplay, "display_mode", (new HTuple("score_histogram")).TupleConcat("score_legend"));
        //        HOperatorSet.CreateDict(out HTuple windowDict);
        //        DevDisplayAnomalyDetectionEvaluation(evaluationResult, evalParams, genParamDisplay, windowDict);

        //        DevCloseWindowDict(windowDict);

        //        //可视化多个评估结果，例如精确度、召回率和混淆矩阵对于给定的分类阈值。
        //        HOperatorSet.SetDictTuple(genParamDisplay, "display_mode", ((new HTuple("pie_charts_precision")).TupleConcat("pie_charts_recall")).TupleConcat("absolute_confusion_matrix"));
        //        //按一个阈值的索引选择评估结果
        //        HTuple classificationThresholdIndex = (new HTuple(anomalyClassificationThresholds.TupleLength())) - 1;
        //        HOperatorSet.SetDictTuple(genParamDisplay, "classification_threshold_index", classificationThresholdIndex);

        //        HOperatorSet.CreateDict(out HTuple windowDict1);
        //        DevDisplayAnomalyDetectionEvaluation(evaluationResult, evalParams, genParamDisplay, windowDict1);
        //        DevCloseWindowDict(windowDict1);
        //    }
        //    catch(Exception)
        //    {
        //        throw new VisionException(MV_ERR.出现异常, "MV_EvaluateDLModel函数出现异常");
        //    }
        //}




    }
}
