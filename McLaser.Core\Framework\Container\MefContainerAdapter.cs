using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.ComponentModel.Composition.Hosting;
using System.Diagnostics;
using System.Linq;
using System.Reflection;

namespace McLaser.Core.Framework.Container
{
    /// <summary>
    /// MEF容器适配器
    /// 将MEF容器适配为统一的IContainer接口
    /// </summary>
    public class MefContainerAdapter : IContainer
    {
        private readonly CompositionContainer _mefContainer;
        private readonly Dictionary<string, object> _keyedServices = new();
        private readonly Dictionary<Type, Func<IContainer, object>> _factories = new();
        private readonly ContainerStatistics _statistics = new();
        private bool _disposed;

        /// <summary>
        /// 容器统计信息
        /// </summary>
        public IContainerStatistics Statistics => _statistics;

        /// <summary>
        /// 服务解析事件
        /// </summary>
        public event EventHandler<ServiceResolvedEventArgs>? ServiceResolved;

        /// <summary>
        /// 服务注册事件
        /// </summary>
        public event EventHandler<ServiceRegisteredEventArgs>? ServiceRegistered;

        /// <summary>
        /// 初始化MEF容器适配器
        /// </summary>
        /// <param name="mefContainer">MEF容器实例</param>
        public MefContainerAdapter(CompositionContainer mefContainer)
        {
            _mefContainer = mefContainer ?? throw new ArgumentNullException(nameof(mefContainer));
        }

        /// <summary>
        /// 使用默认配置创建MEF容器适配器
        /// </summary>
        /// <returns>MEF容器适配器实例</returns>
        public static MefContainerAdapter CreateDefault()
        {
            var catalog = CreateDefaultCatalog();
            var container = new CompositionContainer(catalog);
            return new MefContainerAdapter(container);
        }

        /// <summary>
        /// 注册单例服务
        /// </summary>
        /// <typeparam name="TService">服务接口类型</typeparam>
        /// <typeparam name="TImplementation">服务实现类型</typeparam>
        public void RegisterSingleton<TService, TImplementation>()
            where TService : class
            where TImplementation : class, TService
        {
            // MEF通过Export特性处理，这里记录注册信息
            OnServiceRegistered(typeof(TService), typeof(TImplementation), ServiceLifetime.Singleton);
        }

        /// <summary>
        /// 注册单例服务实例
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <param name="instance">服务实例</param>
        public void RegisterSingleton<TService>(TService instance) where TService : class
        {
            if (instance == null)
                throw new ArgumentNullException(nameof(instance));

            var batch = new CompositionBatch();
            batch.AddExportedValue<TService>(instance);
            _mefContainer.Compose(batch);

            OnServiceRegistered(typeof(TService), instance.GetType(), ServiceLifetime.Singleton);
        }

        /// <summary>
        /// 注册瞬态服务
        /// </summary>
        /// <typeparam name="TService">服务接口类型</typeparam>
        /// <typeparam name="TImplementation">服务实现类型</typeparam>
        public void RegisterTransient<TService, TImplementation>()
            where TService : class
            where TImplementation : class, TService
        {
            // MEF默认是瞬态的，记录注册信息
            OnServiceRegistered(typeof(TService), typeof(TImplementation), ServiceLifetime.Transient);
        }

        /// <summary>
        /// 注册瞬态服务（自注册）
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        public void RegisterTransient<TService>()
            where TService : class
        {
            // MEF默认是瞬态的，记录注册信息
            OnServiceRegistered(typeof(TService), typeof(TService), ServiceLifetime.Transient);
        }

        /// <summary>
        /// 注册作用域服务
        /// </summary>
        /// <typeparam name="TService">服务接口类型</typeparam>
        /// <typeparam name="TImplementation">服务实现类型</typeparam>
        public void RegisterScoped<TService, TImplementation>()
            where TService : class
            where TImplementation : class, TService
        {
            // MEF不直接支持作用域，使用单例模拟
            RegisterSingleton<TService, TImplementation>();
        }

        /// <summary>
        /// 注册服务工厂
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <param name="factory">服务工厂方法</param>
        public void RegisterFactory<TService>(Func<IContainer, TService> factory) where TService : class
        {
            if (factory == null)
                throw new ArgumentNullException(nameof(factory));

            _factories[typeof(TService)] = container => factory(container);
            OnServiceRegistered(typeof(TService), null, ServiceLifetime.Transient);
        }

        /// <summary>
        /// 注册带键的服务
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <param name="key">服务键</param>
        /// <param name="instance">服务实例</param>
        public void RegisterKeyed<TService>(string key, TService instance) where TService : class
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("服务键不能为空", nameof(key));
            if (instance == null)
                throw new ArgumentNullException(nameof(instance));

            var fullKey = $"{typeof(TService).FullName}:{key}";
            _keyedServices[fullKey] = instance;

            OnServiceRegistered(typeof(TService), instance.GetType(), ServiceLifetime.Singleton, key);
        }

        /// <summary>
        /// 解析服务
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <returns>服务实例</returns>
        public TService Resolve<TService>() where TService : class
        {
            var stopwatch = Stopwatch.StartNew();
            var fromCache = false;

            try
            {
                // 首先检查工厂注册
                if (_factories.TryGetValue(typeof(TService), out var factory))
                {
                    var instance = (TService)factory(this);
                    _statistics.RecordResolution(stopwatch.Elapsed.TotalMilliseconds, fromCache);
                    OnServiceResolved(typeof(TService), instance, stopwatch.Elapsed.TotalMilliseconds, fromCache);
                    return instance;
                }

                // 使用MEF解析
                var service = _mefContainer.GetExportedValue<TService>();
                _statistics.RecordResolution(stopwatch.Elapsed.TotalMilliseconds, fromCache);
                OnServiceResolved(typeof(TService), service, stopwatch.Elapsed.TotalMilliseconds, fromCache);
                return service;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"无法解析服务 {typeof(TService).Name}", ex);
            }
        }

        /// <summary>
        /// 解析服务
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <returns>服务实例</returns>
        public object Resolve(Type serviceType)
        {
            var stopwatch = Stopwatch.StartNew();
            var fromCache = false;

            try
            {
                // 首先检查工厂注册
                if (_factories.TryGetValue(serviceType, out var factory))
                {
                    var instance = factory(this);
                    _statistics.RecordResolution(stopwatch.Elapsed.TotalMilliseconds, fromCache);
                    OnServiceResolved(serviceType, instance, stopwatch.Elapsed.TotalMilliseconds, fromCache);
                    return instance;
                }

                // 使用MEF解析
                var service = _mefContainer.GetExportedValue<object>(AttributedModelServices.GetContractName(serviceType));
                _statistics.RecordResolution(stopwatch.Elapsed.TotalMilliseconds, fromCache);
                OnServiceResolved(serviceType, service, stopwatch.Elapsed.TotalMilliseconds, fromCache);
                return service;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"无法解析服务 {serviceType.Name}", ex);
            }
        }

        /// <summary>
        /// 尝试解析服务
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <returns>服务实例，如果未找到则返回null</returns>
        public TService? TryResolve<TService>() where TService : class
        {
            try
            {
                return Resolve<TService>();
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 尝试解析服务
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <returns>服务实例，如果未找到则返回null</returns>
        public object? TryResolve(Type serviceType)
        {
            try
            {
                return Resolve(serviceType);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 解析带键的服务
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <param name="key">服务键</param>
        /// <returns>服务实例</returns>
        public TService ResolveKeyed<TService>(string key) where TService : class
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("服务键不能为空", nameof(key));

            var fullKey = $"{typeof(TService).FullName}:{key}";
            if (_keyedServices.TryGetValue(fullKey, out var service))
            {
                return (TService)service;
            }

            throw new InvalidOperationException($"未找到键为 {key} 的服务 {typeof(TService).Name}");
        }

        /// <summary>
        /// 解析所有指定类型的服务
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <returns>服务实例集合</returns>
        public IEnumerable<TService> ResolveAll<TService>() where TService : class
        {
            return _mefContainer.GetExportedValues<TService>();
        }

        /// <summary>
        /// 解析所有指定类型的服务
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <returns>服务实例集合</returns>
        public IEnumerable<object> ResolveAll(Type serviceType)
        {
            return _mefContainer.GetExportedValues<object>(AttributedModelServices.GetContractName(serviceType));
        }

        /// <summary>
        /// 检查服务是否已注册
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <returns>是否已注册</returns>
        public bool IsRegistered<TService>() where TService : class
        {
            return IsRegistered(typeof(TService));
        }

        /// <summary>
        /// 检查服务是否已注册
        /// </summary>
        /// <param name="serviceType">服务类型</param>
        /// <returns>是否已注册</returns>
        public bool IsRegistered(Type serviceType)
        {
            if (_factories.ContainsKey(serviceType))
                return true;

            var exports = _mefContainer.GetExports<object>(AttributedModelServices.GetContractName(serviceType));
            return exports.Any();
        }

        /// <summary>
        /// 检查带键的服务是否已注册
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <param name="key">服务键</param>
        /// <returns>是否已注册</returns>
        public bool IsKeyedRegistered<TService>(string key) where TService : class
        {
            if (string.IsNullOrEmpty(key))
                return false;

            var fullKey = $"{typeof(TService).FullName}:{key}";
            return _keyedServices.ContainsKey(fullKey);
        }

        /// <summary>
        /// 创建子容器
        /// </summary>
        /// <returns>子容器实例</returns>
        public IContainer CreateChildContainer()
        {
            // MEF不直接支持子容器，创建新的适配器实例
            var childCatalog = CreateDefaultCatalog();
            var childContainer = new CompositionContainer(childCatalog, _mefContainer);
            return new MefContainerAdapter(childContainer);
        }

        /// <summary>
        /// 获取所有已注册的服务类型
        /// </summary>
        /// <returns>服务类型集合</returns>
        public IEnumerable<Type> GetRegisteredTypes()
        {
            var types = new HashSet<Type>();

            // 添加工厂注册的类型
            foreach (var type in _factories.Keys)
            {
                types.Add(type);
            }

            // 添加MEF导出的类型
            foreach (var export in _mefContainer.Catalog.Parts.SelectMany(p => p.ExportDefinitions))
            {
                if (Type.GetType(export.ContractName) is Type contractType)
                {
                    types.Add(contractType);
                }
            }

            return types;
        }

        /// <summary>
        /// 创建默认目录
        /// </summary>
        /// <returns>聚合目录</returns>
        private static AggregateCatalog CreateDefaultCatalog()
        {
            var assemblies = AppDomain.CurrentDomain.GetAssemblies()
                .Where(a => a.GetName().Name?.StartsWith("McLaser", StringComparison.OrdinalIgnoreCase) == true)
                .ToList();

            var entryAssembly = Assembly.GetEntryAssembly();
            if (entryAssembly != null && !assemblies.Contains(entryAssembly))
            {
                assemblies.Add(entryAssembly);
            }

            return new AggregateCatalog(assemblies.Select(a => new AssemblyCatalog(a)));
        }

        /// <summary>
        /// 触发服务解析事件
        /// </summary>
        protected virtual void OnServiceResolved(Type serviceType, object serviceInstance, double resolutionTime, bool fromCache)
        {
            ServiceResolved?.Invoke(this, new ServiceResolvedEventArgs(serviceType, serviceInstance, resolutionTime, fromCache));
        }

        /// <summary>
        /// 触发服务注册事件
        /// </summary>
        protected virtual void OnServiceRegistered(Type serviceType, Type? implementationType, ServiceLifetime lifetime, string? key = null)
        {
            _statistics.IncrementRegistrations();
            ServiceRegistered?.Invoke(this, new ServiceRegisteredEventArgs(serviceType, implementationType, lifetime, key));
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _mefContainer?.Dispose();
                _keyedServices.Clear();
                _factories.Clear();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// 容器统计信息实现
    /// </summary>
    internal class ContainerStatistics : IContainerStatistics
    {
        private int _totalRegistrations;
        private long _totalResolutions;
        private double _totalResolutionTime;
        private long _cacheHits;
        private readonly object _lockObject = new object();

        /// <summary>
        /// 总注册服务数量
        /// </summary>
        public int TotalRegistrations
        {
            get
            {
                lock (_lockObject)
                {
                    return _totalRegistrations;
                }
            }
        }

        /// <summary>
        /// 总解析次数
        /// </summary>
        public long TotalResolutions
        {
            get
            {
                lock (_lockObject)
                {
                    return _totalResolutions;
                }
            }
        }

        /// <summary>
        /// 平均解析时间（毫秒）
        /// </summary>
        public double AverageResolutionTime
        {
            get
            {
                lock (_lockObject)
                {
                    return _totalResolutions > 0 ? _totalResolutionTime / _totalResolutions : 0;
                }
            }
        }

        /// <summary>
        /// 缓存命中率
        /// </summary>
        public double CacheHitRate
        {
            get
            {
                lock (_lockObject)
                {
                    return _totalResolutions > 0 ? (double)_cacheHits / _totalResolutions : 0;
                }
            }
        }

        /// <summary>
        /// 记录服务解析
        /// </summary>
        /// <param name="resolutionTime">解析时间</param>
        /// <param name="fromCache">是否来自缓存</param>
        public void RecordResolution(double resolutionTime, bool fromCache)
        {
            lock (_lockObject)
            {
                _totalResolutions++;
                _totalResolutionTime += resolutionTime;
                if (fromCache)
                {
                    _cacheHits++;
                }
            }
        }

        /// <summary>
        /// 增加注册计数
        /// </summary>
        public void IncrementRegistrations()
        {
            lock (_lockObject)
            {
                _totalRegistrations++;
            }
        }

        /// <summary>
        /// 重置统计信息
        /// </summary>
        public void Reset()
        {
            lock (_lockObject)
            {
                _totalRegistrations = 0;
                _totalResolutions = 0;
                _totalResolutionTime = 0;
                _cacheHits = 0;
            }
        }
    }
}
