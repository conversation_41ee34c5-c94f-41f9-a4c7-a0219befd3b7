﻿<UserControl x:Class="McLaser.Device.CameraView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:McLaser.Device"
             xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">

	    <UserControl.Resources>
        <local:BoolToInverseConverter x:Key="BoolToInverseConverter"/>
		
		<!-- 现代化样式 -->
		<Style x:Key="ModernGroupBoxStyle" TargetType="GroupBox">
			<Setter Property="Margin" Value="0,0,0,12"/>
			<Setter Property="Padding" Value="12,8,12,12"/>
			<Setter Property="BorderBrush" Value="#E0E0E0"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="Background" Value="White"/>
			<Setter Property="Template">
				<Setter.Value>
					<ControlTemplate TargetType="GroupBox">
						<Grid>
							<Border Background="{TemplateBinding Background}"
									BorderBrush="{TemplateBinding BorderBrush}"
									BorderThickness="{TemplateBinding BorderThickness}"
									CornerRadius="4"
									Padding="{TemplateBinding Padding}">
								<Grid>
									<Grid.RowDefinitions>
										<RowDefinition Height="Auto"/>
										<RowDefinition Height="*"/>
									</Grid.RowDefinitions>
									
									<ContentPresenter Grid.Row="1" Content="{TemplateBinding Content}"/>
								</Grid>
							</Border>
							<Border Background="#2196F3" 
									CornerRadius="4,4,0,0" 
									Padding="8,6" 
									HorizontalAlignment="Left"
									VerticalAlignment="Top"
									Margin="12,0,0,0">
								<ContentPresenter ContentSource="Header" 
												  TextBlock.Foreground="White"
												  TextBlock.FontWeight="SemiBold"/>
							</Border>
						</Grid>
					</ControlTemplate>
				</Setter.Value>
			</Setter>
		</Style>
		
		<Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
			<Setter Property="Height" Value="30"/>
			<Setter Property="Padding" Value="8,0"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="BorderBrush" Value="#BDBDBD"/>
			<Setter Property="Background" Value="White"/>
			<Setter Property="VerticalContentAlignment" Value="Center"/>
			<Setter Property="Template">
				<Setter.Value>
					<ControlTemplate TargetType="TextBox">
						<Border Background="{TemplateBinding Background}"
								BorderBrush="{TemplateBinding BorderBrush}"
								BorderThickness="{TemplateBinding BorderThickness}"
								CornerRadius="4">
							<ScrollViewer x:Name="PART_ContentHost" 
										Padding="{TemplateBinding Padding}"
										VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
										Focusable="False"/>
						</Border>
						<ControlTemplate.Triggers>
							<Trigger Property="IsMouseOver" Value="True">
								<Setter Property="BorderBrush" Value="#2196F3"/>
							</Trigger>
							<Trigger Property="IsFocused" Value="True">
								<Setter Property="BorderBrush" Value="#2196F3"/>
							</Trigger>
						</ControlTemplate.Triggers>
					</ControlTemplate>
				</Setter.Value>
			</Setter>
		</Style>
		
		<Style x:Key="ModernComboBoxStyle" TargetType="ComboBox">
			<Setter Property="Height" Value="30"/>
			<Setter Property="Padding" Value="8,0"/>
			<Setter Property="BorderThickness" Value="1"/>
			<Setter Property="BorderBrush" Value="#BDBDBD"/>
			<Setter Property="Background" Value="White"/>
			<Setter Property="VerticalContentAlignment" Value="Center"/>
		</Style>
		
		<Style x:Key="ModernButtonStyle" TargetType="Button">
			<Setter Property="Height" Value="30"/>
			<Setter Property="MinWidth" Value="80"/>
			<Setter Property="Padding" Value="12,0"/>
			<Setter Property="Background" Value="#2196F3"/>
			<Setter Property="Foreground" Value="White"/>
			<Setter Property="BorderThickness" Value="0"/>
			<Setter Property="Template">
				<Setter.Value>
					<ControlTemplate TargetType="Button">
						<Border Background="{TemplateBinding Background}"
								BorderBrush="{TemplateBinding BorderBrush}"
								BorderThickness="{TemplateBinding BorderThickness}"
								CornerRadius="4">
							<ContentPresenter HorizontalAlignment="Center"
											  VerticalAlignment="Center"/>
						</Border>
						<ControlTemplate.Triggers>
							<Trigger Property="IsMouseOver" Value="True">
								<Setter Property="Background" Value="#1976D2"/>
							</Trigger>
							<Trigger Property="IsPressed" Value="True">
								<Setter Property="Background" Value="#0D47A1"/>
							</Trigger>
							<Trigger Property="IsEnabled" Value="False">
								<Setter Property="Background" Value="#BDBDBD"/>
							</Trigger>
						</ControlTemplate.Triggers>
					</ControlTemplate>
				</Setter.Value>
			</Setter>
		</Style>
		
		<Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
			<Setter Property="Background" Value="#F44336"/>
			<Style.Triggers>
				<Trigger Property="IsMouseOver" Value="True">
					<Setter Property="Background" Value="#D32F2F"/>
				</Trigger>
				<Trigger Property="IsPressed" Value="True">
					<Setter Property="Background" Value="#B71C1C"/>
				</Trigger>
			</Style.Triggers>
		</Style>
		
		<Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
			<Setter Property="Background" Value="#4CAF50"/>
			<Style.Triggers>
				<Trigger Property="IsMouseOver" Value="True">
					<Setter Property="Background" Value="#388E3C"/>
				</Trigger>
				<Trigger Property="IsPressed" Value="True">
					<Setter Property="Background" Value="#1B5E20"/>
				</Trigger>
			</Style.Triggers>
		</Style>
	</UserControl.Resources>
	
	<Grid Margin="0">
		<Grid.ColumnDefinitions>
			<ColumnDefinition Width="350" />
			<ColumnDefinition Width="6" />
			<ColumnDefinition />
		</Grid.ColumnDefinitions>
		
		<!-- 左侧控制面板 -->
		<Border Grid.Column="0" Background="#F5F5F5" CornerRadius="4">
			<StackPanel Margin="12">
				<!-- 相机选择 -->
				<GroupBox Header="相机选择" Style="{StaticResource ModernGroupBoxStyle}">
					<Grid Margin="0,8,0,0">
						<Grid.RowDefinitions>
							<RowDefinition Height="Auto" />
							<RowDefinition Height="Auto" />
						</Grid.RowDefinitions>
						
						<!-- 相机名称 -->
						<Grid Grid.Row="0" Margin="0,0,0,12">
							<Grid.ColumnDefinitions>
								<ColumnDefinition Width="80"/>
								<ColumnDefinition Width="*"/>
							</Grid.ColumnDefinitions>
							<TextBlock Grid.Column="0" Text="相机名称" VerticalAlignment="Center"/>
							<TextBox Grid.Column="1" 
									 Style="{StaticResource ModernTextBoxStyle}"
									 Text="{Binding SelectedCamera.Name, Mode=TwoWay}"/>
						</Grid>
						
						<!-- 相机序号 -->
						<Grid Grid.Row="1">
							<Grid.ColumnDefinitions>
								<ColumnDefinition Width="80"/>
								<ColumnDefinition Width="*"/>
							</Grid.ColumnDefinitions>
							<TextBlock Grid.Column="0" Text="相机序号" VerticalAlignment="Center"/>
							<ComboBox Grid.Column="1"
									  x:Name="cmbCameraNo"
									  Style="{StaticResource ModernComboBoxStyle}"
									  DisplayMemberPath="SerialNO"
									  ItemsSource="{Binding CameraNos}"
									  SelectedIndex="{Binding CameraNoIndex}"
									  SelectedValuePath="SerialNO">
								<i:Interaction.Triggers>
									<i:EventTrigger EventName="DropDownOpened">
										<i:InvokeCommandAction Command="{Binding ComboBoxOpenedCommand}"/>
									</i:EventTrigger>
								</i:Interaction.Triggers>
							</ComboBox>
						</Grid>
					</Grid>
				</GroupBox>
				
				<!-- 相机参数 -->
				<GroupBox Header="相机参数" Style="{StaticResource ModernGroupBoxStyle}">
					<StackPanel Margin="0,8,0,0">
						<!-- 触发模式 -->
						<Grid Margin="0,0,0,12">
							<Grid.ColumnDefinitions>
								<ColumnDefinition Width="80"/>
								<ColumnDefinition Width="*"/>
							</Grid.ColumnDefinitions>
							<TextBlock Grid.Column="0" Text="触发模式" VerticalAlignment="Center"/>
							<ComboBox Grid.Column="1"
									  Style="{StaticResource ModernComboBoxStyle}"
									  ItemsSource="{Binding SelectedCamera.TrigModes}"
									  SelectedItem="{Binding SelectedCamera.TrigMode}"/>
						</Grid>
						
						<!-- 曝光时间 -->
						<Grid Margin="0,0,0,12">
							<Grid.ColumnDefinitions>
								<ColumnDefinition Width="80"/>
								<ColumnDefinition Width="*"/>
							</Grid.ColumnDefinitions>
							<TextBlock Grid.Column="0" Text="曝光时间" VerticalAlignment="Center"/>
							<TextBox Grid.Column="1"
									 Style="{StaticResource ModernTextBoxStyle}"
									 Text="{Binding SelectedCamera.Exposure}"/>
						</Grid>
						
						<!-- 增益 -->
						<Grid>
							<Grid.ColumnDefinitions>
								<ColumnDefinition Width="80"/>
								<ColumnDefinition Width="*"/>
							</Grid.ColumnDefinitions>
							<TextBlock Grid.Column="0" Text="增益" VerticalAlignment="Center"/>
							<TextBox Grid.Column="1"
									 Style="{StaticResource ModernTextBoxStyle}"
									 Text="{Binding SelectedCamera.Gain}"/>
						</Grid>
					</StackPanel>
				</GroupBox>
				
				<!-- 相机操作 -->
				<GroupBox Header="相机操作" Style="{StaticResource ModernGroupBoxStyle}">
					<UniformGrid Columns="2" Rows="2" Margin="0,8,0,0" HorizontalAlignment="Stretch">
						<Button Margin="4"
								Style="{StaticResource SuccessButtonStyle}"
								Command="{Binding CamOpenCommand}"
								Content="连接相机"
								IsEnabled="{Binding SelectedCamera.Status.IsOpen, Converter={StaticResource BoolToInverseConverter}}"/>
						
						<Button Margin="4"
								Style="{StaticResource DangerButtonStyle}"
								Command="{Binding CamCloseCommand}"
								Content="断开相机"/>
						
						<Button Margin="4"
								Style="{StaticResource ModernButtonStyle}"
								Command="{Binding CamGrabOnceCommand}"
								Content="单次采集"/>
						
						<Button Margin="4"
								Style="{StaticResource ModernButtonStyle}"
								Command="{Binding CamGrabContinueCommand}"
								Content="连续采集"/>
					</UniformGrid>
				</GroupBox>
				
				<!-- 相机状态 -->
				<GroupBox Header="相机状态" Style="{StaticResource ModernGroupBoxStyle}">
					<Grid Margin="0,8,0,0">
						<Border Background="{Binding SelectedCamera.Status.IsOpen, Converter={StaticResource BoolToInverseConverter}, ConverterParameter=Colors.Green:Colors.Red}"
								CornerRadius="4"
								Padding="12,8">
							<StackPanel>
								<TextBlock FontWeight="SemiBold"
										   Foreground="White"
										   FontSize="14"
										   Text="{Binding SelectedCamera.Status.IsOpen, Converter={StaticResource BoolToInverseConverter}, ConverterParameter=已连接:未连接}"/>
								<TextBlock Foreground="White"
										   FontSize="12"
										   Margin="0,4,0,0"
										   Text="{Binding SelectedCamera.Status.StatusMessage}"/>
							</StackPanel>
						</Border>
					</Grid>
				</GroupBox>
			</StackPanel>
		</Border>
		
		<!-- 分隔线 -->
		<Rectangle Grid.Column="1" Fill="#E0E0E0" Width="1" Margin="2,0"/>
		
		<!-- 图像显示区域 -->
		<Border Grid.Column="2" Background="Black" CornerRadius="4">
			<ContentControl Margin="0" Content="{Binding DesignControl}"/>
		</Border>
	</Grid>
</UserControl>
