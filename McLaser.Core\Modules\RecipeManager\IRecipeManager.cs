﻿using McLaser.Core.Common;
using McLaser.Core.Container;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace McLaser.Core.Modules.RecipeManager
{
    public abstract class RecipeInstance<T> : IRecipeItem where T : class
    {
        private bool _disposed = false;

        private static T _instance;
        private static readonly object m_object = new object();
        public static T Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (m_object)
                    {
                        if (_instance == null)
                        {
                            _instance = Activator.CreateInstance(typeof(T)) as T;
                            //var items = IoC.Get<IRecipeManager>().Current.RecipeItems;
                            //if (items != null)
                            //{
                            //    _instance = items.Where(x => x.GetType() == typeof(T)).FirstOrDefault() as T;
                            //    if (_instance == null)
                            //    {
                            //        _instance = Activator.CreateInstance(typeof(T)) as T;
                            //    }
                            //}

                        }
                    }
                }
                return _instance;
            }
            private set { _instance = value; }
        }

        private IRecipe _owner;
        public virtual IRecipe Owner
        {
            get=> _owner;
            set
            {
                _owner = value;
                _instance = this as T;
            }
        }
        public virtual string FileName { get; set; }
        public virtual bool Load()
        {
            try
            {
                if(Owner == null){ return false; }
                string fileName = $"{Owner.Path}\\{Owner.Name}\\{this.GetType().Name}.json";
                var recipeItem = JsonHelper.DeserializeObject<T>(fileName);
                if (recipeItem != null)
                {
                    _instance = recipeItem;
                }
                else
                {
                    _instance = Activator.CreateInstance(typeof(T)) as T;
                }
            }
            catch (Exception ex)
            {
            }
            return true;
        }

        public virtual bool Save()
        {
       
            try
            {
                if (Owner == null) { return false; }
                string fileName = $"{Owner.Path}\\{Owner.Name}\\{this.GetType().Name}.json";
                var ret = JsonHelper.SerialObject(fileName, _instance);
                return ret; 
            }
            catch (Exception ex)
            {
                return false;   
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    try
                    {
                      
                    }
                    catch (Exception ex)
                    {
                
                    }
                }
                _disposed = true;
            }
        }
    }


    public interface IRecipeManager
    {
        IRecipe Current { get; set; }
        bool Create(out IRecipe recipe);
        void Add(IRecipeItem project);
        void Delete(Func<IRecipeItem, bool> func);
        IEnumerable<IRecipeItem> Where(Func<IRecipeItem, bool> func = null);
        Action<IRecipeItem, IRecipeItem> CurrentChanged { get; set; }

        bool Load(string recipeName);
        bool Save();

    }





}
