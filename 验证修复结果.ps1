# McLaser_V1 错误修复验证脚本
Write-Host "=== McLaser_V1 错误修复验证 ===" -ForegroundColor Cyan
Write-Host ""

# 1. 检查项目结构
Write-Host "1. 检查项目结构..." -ForegroundColor Yellow
$requiredFiles = @(
    "McLaser.App\Core\AppCore.cs",
    "McLaser.App\Core\ConsoleLogger.cs",
    "McLaser.App\McLaser.App.csproj",
    "McLaser.Core\Framework\Bootstrapper\DefaultServiceRegistry.cs",
    "错误修复总结.md"
)

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "  ✅ $file" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $file" -ForegroundColor Red
    }
}

Write-Host ""

# 2. 编译验证
Write-Host "2. 编译验证..." -ForegroundColor Yellow
Write-Host "  编译 McLaser.Core..." -ForegroundColor Gray
$coreResult = dotnet build McLaser.Core --verbosity quiet 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "  ✅ McLaser.Core 编译成功" -ForegroundColor Green
} else {
    Write-Host "  ❌ McLaser.Core 编译失败" -ForegroundColor Red
    Write-Host "  错误: $coreResult" -ForegroundColor Red
}

Write-Host "  编译 McLaser.App..." -ForegroundColor Gray
$appResult = dotnet build McLaser.App --verbosity quiet 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "  ✅ McLaser.App 编译成功" -ForegroundColor Green
} else {
    Write-Host "  ❌ McLaser.App 编译失败" -ForegroundColor Red
    Write-Host "  错误: $appResult" -ForegroundColor Red
}

Write-Host ""

# 3. 启动测试
Write-Host "3. 启动测试..." -ForegroundColor Yellow
Write-Host "  启动应用程序（5秒测试）..." -ForegroundColor Gray

try {
    $process = Start-Process -FilePath "dotnet" -ArgumentList "run --project McLaser.App" -PassThru -WindowStyle Hidden -ErrorAction Stop

    # 等待5秒
    Start-Sleep -Seconds 5

    # 检查进程状态
    if (!$process.HasExited) {
        Write-Host "  ✅ 应用程序成功启动并运行" -ForegroundColor Green
        $process.Kill()
        $process.WaitForExit(2000)
        Write-Host "  ✅ 应用程序正常停止" -ForegroundColor Green
    } else {
        Write-Host "  ❌ 应用程序启动后立即退出，退出代码: $($process.ExitCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "  ❌ 应用程序启动失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 4. 关键修复点验证
Write-Host "4. 关键修复点验证..." -ForegroundColor Yellow

# 检查AppCore.cs中的服务注册
$appCoreContent = Get-Content "McLaser.App\Core\AppCore.cs" -Raw
if ($appCoreContent -match "RegisterFactory<ILogger>") {
    Write-Host "  ✅ ILogger使用工厂模式注册" -ForegroundColor Green
} else {
    Write-Host "  ❌ ILogger注册方式未修复" -ForegroundColor Red
}

if ($appCoreContent -match "TryResolve") {
    Write-Host "  ✅ 使用安全的TryResolve方法" -ForegroundColor Green
} else {
    Write-Host "  ❌ 未使用安全解析方法" -ForegroundColor Red
}

# 检查DefaultServiceRegistry.cs中的循环依赖检测
$registryContent = Get-Content "McLaser.Core\Framework\Bootstrapper\DefaultServiceRegistry.cs" -Raw
if ($registryContent -match "_resolvingTypes") {
    Write-Host "  ✅ 添加了循环依赖检测" -ForegroundColor Green
} else {
    Write-Host "  ❌ 未添加循环依赖检测" -ForegroundColor Red
}

# 检查ConsoleLogger.cs
if (Test-Path "McLaser.App\Core\ConsoleLogger.cs") {
    $loggerContent = Get-Content "McLaser.App\Core\ConsoleLogger.cs" -Raw
    if ($loggerContent -match "public void Debug" -and
        $loggerContent -match "public void Info" -and
        $loggerContent -match "public bool IsDebugEnabled") {
        Write-Host "  ✅ ConsoleLogger正确实现ILogger接口" -ForegroundColor Green
    } else {
        Write-Host "  ❌ ConsoleLogger接口实现不完整" -ForegroundColor Red
    }
} else {
    Write-Host "  ❌ ConsoleLogger文件不存在" -ForegroundColor Red
}

Write-Host ""

# 5. 总结
Write-Host "5. 修复总结..." -ForegroundColor Yellow
Write-Host "  主要修复内容:" -ForegroundColor Gray
Write-Host "    • 优化服务注册顺序，避免循环依赖" -ForegroundColor White
Write-Host "    • 使用工厂模式注册ILogger服务" -ForegroundColor White
Write-Host "    • 添加循环依赖检测机制" -ForegroundColor White
Write-Host "    • 实现后备ConsoleLogger" -ForegroundColor White
Write-Host "    • 使用安全的TryResolve方法" -ForegroundColor White

Write-Host ""
Write-Host "=== 验证完成 ===" -ForegroundColor Cyan
