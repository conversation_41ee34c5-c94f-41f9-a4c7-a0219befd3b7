# 检查项目文件中的引用问题
Write-Host "=== 检查项目引用 ===" -ForegroundColor Cyan

# 查找所有.csproj文件
$projectFiles = Get-ChildItem -Path . -Name "*.csproj" -Recurse

foreach ($file in $projectFiles) {
    Write-Host "检查文件: $file" -ForegroundColor Yellow
    
    try {
        # 读取文件内容
        $content = Get-Content $file -Raw -Encoding UTF8
        
        # 检查是否包含错误的<n>标签
        if ($content -match '<n>([^<]+)</n>') {
            Write-Host "  ❌ 发现错误的<n>标签" -ForegroundColor Red
            $matches = [regex]::Matches($content, '<n>([^<]+)</n>')
            foreach ($match in $matches) {
                Write-Host "    - $($match.Value)" -ForegroundColor Red
            }
        } else {
            Write-Host "  ✅ 无错误标签" -ForegroundColor Green
        }
        
        # 检查项目引用
        if ($content -match '<ProjectReference Include="([^"]+)"') {
            Write-Host "  项目引用:" -ForegroundColor Cyan
            $matches = [regex]::Matches($content, '<ProjectReference Include="([^"]+)"')
            foreach ($match in $matches) {
                Write-Host "    - $($match.Groups[1].Value)" -ForegroundColor White
            }
        }
    }
    catch {
        Write-Host "  ❌ 检查失败: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host ""
}

Write-Host "=== 检查完成 ===" -ForegroundColor Cyan
