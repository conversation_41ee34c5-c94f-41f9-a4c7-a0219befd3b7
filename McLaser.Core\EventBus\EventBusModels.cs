using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace McLaser.Core.EventBus
{
    /// <summary>
    /// 事件发布前事件参数
    /// </summary>
    public class EventPublishingEventArgs : EventArgs
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="eventData">事件数据</param>
        public EventPublishingEventArgs(Type eventType, object eventData)
        {
            EventType = eventType;
            EventData = eventData;
            Timestamp = DateTime.UtcNow;
        }

        /// <summary>
        /// 事件类型
        /// </summary>
        public Type EventType { get; }

        /// <summary>
        /// 事件数据
        /// </summary>
        public object EventData { get; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// 是否取消发布
        /// </summary>
        public bool Cancel { get; set; }
    }

    /// <summary>
    /// 事件发布后事件参数
    /// </summary>
    public class EventPublishedEventArgs : EventArgs
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="eventData">事件数据</param>
        /// <param name="handlerCount">处理器数量</param>
        /// <param name="elapsedMilliseconds">处理耗时</param>
        public EventPublishedEventArgs(Type eventType, object eventData, int handlerCount, long elapsedMilliseconds)
        {
            EventType = eventType;
            EventData = eventData;
            HandlerCount = handlerCount;
            ElapsedMilliseconds = elapsedMilliseconds;
            Timestamp = DateTime.UtcNow;
        }

        /// <summary>
        /// 事件类型
        /// </summary>
        public Type EventType { get; }

        /// <summary>
        /// 事件数据
        /// </summary>
        public object EventData { get; }

        /// <summary>
        /// 处理器数量
        /// </summary>
        public int HandlerCount { get; }

        /// <summary>
        /// 处理耗时（毫秒）
        /// </summary>
        public long ElapsedMilliseconds { get; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; }
    }

    /// <summary>
    /// 事件处理异常事件参数
    /// </summary>
    public class EventHandlingExceptionEventArgs : EventArgs
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="eventData">事件数据</param>
        /// <param name="handler">事件处理器</param>
        /// <param name="exception">异常对象</param>
        public EventHandlingExceptionEventArgs(Type eventType, object eventData, IEventHandler handler, Exception exception)
        {
            EventType = eventType;
            EventData = eventData;
            Handler = handler;
            Exception = exception;
            Timestamp = DateTime.UtcNow;
        }

        /// <summary>
        /// 事件类型
        /// </summary>
        public Type EventType { get; }

        /// <summary>
        /// 事件数据
        /// </summary>
        public object EventData { get; }

        /// <summary>
        /// 事件处理器
        /// </summary>
        public IEventHandler Handler { get; }

        /// <summary>
        /// 异常对象
        /// </summary>
        public Exception Exception { get; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// 是否已处理异常
        /// </summary>
        public bool Handled { get; set; }
    }

    /// <summary>
    /// 事件拦截器上下文
    /// </summary>
    public class EventInterceptorContext
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="eventData">事件数据</param>
        public EventInterceptorContext(Type eventType, object eventData)
        {
            EventType = eventType;
            EventData = eventData;
            Properties = new Dictionary<string, object>();
            Timestamp = DateTime.UtcNow;
        }

        /// <summary>
        /// 事件类型
        /// </summary>
        public Type EventType { get; }

        /// <summary>
        /// 事件数据
        /// </summary>
        public object EventData { get; set; }

        /// <summary>
        /// 上下文属性
        /// </summary>
        public IDictionary<string, object> Properties { get; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// 是否取消处理
        /// </summary>
        public bool Cancel { get; set; }
    }

    /// <summary>
    /// 事件订阅信息
    /// </summary>
    public class EventSubscriptionInfo
    {
        /// <summary>
        /// 订阅标识
        /// </summary>
        public string SubscriptionId { get; set; }

        /// <summary>
        /// 事件类型
        /// </summary>
        public Type EventType { get; set; }

        /// <summary>
        /// 事件处理器
        /// </summary>
        public IEventHandler Handler { get; set; }

        /// <summary>
        /// 订阅时间
        /// </summary>
        public DateTime SubscribedAt { get; set; }

        /// <summary>
        /// 处理次数
        /// </summary>
        public long HandledCount { get; set; }

        /// <summary>
        /// 最后处理时间
        /// </summary>
        public DateTime? LastHandledAt { get; set; }

        /// <summary>
        /// 平均处理时间（毫秒）
        /// </summary>
        public double AverageHandlingTime { get; set; }

        /// <summary>
        /// 异常次数
        /// </summary>
        public long ExceptionCount { get; set; }

        /// <summary>
        /// 最后异常时间
        /// </summary>
        public DateTime? LastExceptionAt { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;
    }

    /// <summary>
    /// 事件总线统计信息
    /// </summary>
    public class EventBusStatistics
    {
        /// <summary>
        /// 发布的事件总数
        /// </summary>
        public long TotalEventsPublished { get; set; }

        /// <summary>
        /// 处理的事件总数
        /// </summary>
        public long TotalEventsHandled { get; set; }

        /// <summary>
        /// 异常总数
        /// </summary>
        public long TotalExceptions { get; set; }

        /// <summary>
        /// 订阅总数
        /// </summary>
        public int TotalSubscriptions { get; set; }

        /// <summary>
        /// 活跃订阅数
        /// </summary>
        public int ActiveSubscriptions { get; set; }

        /// <summary>
        /// 平均事件处理时间（毫秒）
        /// </summary>
        public double AverageEventHandlingTime { get; set; }

        /// <summary>
        /// 最后事件发布时间
        /// </summary>
        public DateTime? LastEventPublishedAt { get; set; }

        /// <summary>
        /// 最后异常时间
        /// </summary>
        public DateTime? LastExceptionAt { get; set; }

        /// <summary>
        /// 事件类型统计
        /// </summary>
        public Dictionary<string, EventTypeStatistics> EventTypeStatistics { get; set; } = new Dictionary<string, EventTypeStatistics>();
    }

    /// <summary>
    /// 事件类型统计信息
    /// </summary>
    public class EventTypeStatistics
    {
        /// <summary>
        /// 事件类型名称
        /// </summary>
        public string EventTypeName { get; set; }

        /// <summary>
        /// 发布次数
        /// </summary>
        public long PublishedCount { get; set; }

        /// <summary>
        /// 处理次数
        /// </summary>
        public long HandledCount { get; set; }

        /// <summary>
        /// 异常次数
        /// </summary>
        public long ExceptionCount { get; set; }

        /// <summary>
        /// 平均处理时间（毫秒）
        /// </summary>
        public double AverageHandlingTime { get; set; }

        /// <summary>
        /// 最后发布时间
        /// </summary>
        public DateTime? LastPublishedAt { get; set; }

        /// <summary>
        /// 订阅者数量
        /// </summary>
        public int SubscriberCount { get; set; }
    }

    /// <summary>
    /// 存储的事件
    /// </summary>
    public class StoredEvent
    {
        /// <summary>
        /// 事件标识
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 事件类型名称
        /// </summary>
        public string EventTypeName { get; set; }

        /// <summary>
        /// 事件数据（JSON序列化）
        /// </summary>
        public string EventData { get; set; }

        /// <summary>
        /// 事件时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 事件来源
        /// </summary>
        public string Source { get; set; }

        /// <summary>
        /// 事件版本
        /// </summary>
        public int Version { get; set; }

        /// <summary>
        /// 事件元数据
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// 委托事件处理器
    /// </summary>
    /// <typeparam name="TEvent">事件类型</typeparam>
    public class DelegateEventHandler<TEvent> : IEventHandler<TEvent> where TEvent : class
    {
        private readonly Func<TEvent, Task> _handler;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="handler">处理委托</param>
        /// <param name="name">处理器名称</param>
        /// <param name="priority">优先级</param>
        public DelegateEventHandler(Func<TEvent, Task> handler, string name = null, int priority = 0)
        {
            _handler = handler ?? throw new ArgumentNullException(nameof(handler));
            Name = name ?? $"DelegateHandler_{Guid.NewGuid():N}";
            Priority = priority;
        }

        /// <summary>
        /// 构造函数（同步委托）
        /// </summary>
        /// <param name="handler">处理委托</param>
        /// <param name="name">处理器名称</param>
        /// <param name="priority">优先级</param>
        public DelegateEventHandler(Action<TEvent> handler, string name = null, int priority = 0)
        {
            if (handler == null)
                throw new ArgumentNullException(nameof(handler));

            _handler = eventData =>
            {
                handler(eventData);
                return Task.CompletedTask;
            };

            Name = name ?? $"DelegateHandler_{Guid.NewGuid():N}";
            Priority = priority;
        }

        /// <summary>
        /// 处理器名称
        /// </summary>
        public string Name { get; }

        /// <summary>
        /// 处理器优先级
        /// </summary>
        public int Priority { get; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 处理事件
        /// </summary>
        /// <param name="eventData">事件数据</param>
        /// <returns>异步任务</returns>
        public async Task HandleAsync(TEvent eventData)
        {
            if (IsEnabled)
            {
                await _handler(eventData);
            }
        }
    }

    /// <summary>
    /// 基于类型的事件过滤器
    /// </summary>
    public class TypeEventFilter : IEventFilter
    {
        private readonly HashSet<Type> _allowedTypes;
        private readonly bool _isWhitelist;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="allowedTypes">允许的类型</param>
        /// <param name="isWhitelist">是否为白名单模式</param>
        public TypeEventFilter(IEnumerable<Type> allowedTypes, bool isWhitelist = true)
        {
            _allowedTypes = new HashSet<Type>(allowedTypes ?? throw new ArgumentNullException(nameof(allowedTypes)));
            _isWhitelist = isWhitelist;
        }

        /// <summary>
        /// 过滤器名称
        /// </summary>
        public string Name => $"TypeFilter_{(_isWhitelist ? "Whitelist" : "Blacklist")}";

        /// <summary>
        /// 检查事件是否应该被处理
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="eventData">事件数据</param>
        /// <returns>是否应该处理</returns>
        public bool ShouldHandle(Type eventType, object eventData)
        {
            var contains = _allowedTypes.Contains(eventType);
            return _isWhitelist ? contains : !contains;
        }
    }

    /// <summary>
    /// 基于属性的事件过滤器
    /// </summary>
    public class PropertyEventFilter : IEventFilter
    {
        private readonly string _propertyName;
        private readonly object _expectedValue;
        private readonly Func<object, object, bool> _comparer;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        /// <param name="expectedValue">期望值</param>
        /// <param name="comparer">比较器</param>
        public PropertyEventFilter(string propertyName, object expectedValue, Func<object, object, bool> comparer = null)
        {
            _propertyName = propertyName ?? throw new ArgumentNullException(nameof(propertyName));
            _expectedValue = expectedValue;
            _comparer = comparer ?? ((actual, expected) => Equals(actual, expected));
        }

        /// <summary>
        /// 过滤器名称
        /// </summary>
        public string Name => $"PropertyFilter_{_propertyName}";

        /// <summary>
        /// 检查事件是否应该被处理
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="eventData">事件数据</param>
        /// <returns>是否应该处理</returns>
        public bool ShouldHandle(Type eventType, object eventData)
        {
            if (eventData == null)
                return false;

            try
            {
                var property = eventType.GetProperty(_propertyName);
                if (property == null)
                    return false;

                var actualValue = property.GetValue(eventData);
                return _comparer(actualValue, _expectedValue);
            }
            catch
            {
                return false;
            }
        }
    }
}
