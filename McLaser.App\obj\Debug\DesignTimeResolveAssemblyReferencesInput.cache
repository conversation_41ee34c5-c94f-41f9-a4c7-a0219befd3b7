   .winmd.dll.exe '   JC:\Users\<USER>\Desktop\柔性钙钛矿\GTK\McLaser_V1\docs\README.mdkC:\Users\<USER>\Desktop\柔性钙钛矿\GTK\McLaser_V1\docs\上拉框自动收起功能实现总结.mdPC:\Users\<USER>\Desktop\柔性钙钛矿\GTK\McLaser_V1\docs\使用指南.mdPC:\Users\<USER>\Desktop\柔性钙钛矿\GTK\McLaser_V1\docs\修复报告.mdVC:\Users\<USER>\Desktop\柔性钙钛矿\GTK\McLaser_V1\docs\功能演示指南.mdkC:\Users\<USER>\Desktop\柔性钙钛矿\GTK\McLaser_V1\docs\导航优化和进程退出修复总结.mdbC:\Users\<USER>\Desktop\柔性钙钛矿\GTK\McLaser_V1\docs\导航视觉指示优化总结.mdbC:\Users\<USER>\Desktop\柔性钙钛矿\GTK\McLaser_V1\docs\导航问题修复完成总结.md\C:\Users\<USER>\Desktop\柔性钙钛矿\GTK\McLaser_V1\docs\导航问题修复说明.md_C:\Users\<USER>\Desktop\柔性钙钛矿\GTK\McLaser_V1\docs\底部导航栏使用指南.mdkC:\Users\<USER>\Desktop\柔性钙钛矿\GTK\McLaser_V1\docs\底部导航栏图标显示修复总结.md_C:\Users\<USER>\Desktop\柔性钙钛矿\GTK\McLaser_V1\docs\底部导航栏实现总结.md\C:\Users\<USER>\Desktop\柔性钙钛矿\GTK\McLaser_V1\docs\按钮功能测试指南.mdVC:\Users\<USER>\Desktop\柔性钙钛矿\GTK\McLaser_V1\docs\最终修复验证.mdbC:\Users\<USER>\Desktop\柔性钙钛矿\GTK\McLaser_V1\docs\视觉指示功能测试指南.mdeC:\Users\<USER>\Desktop\柔性钙钛矿\GTK\McLaser_V1\docs\设备管理器页面集成总结.mdPC:\Users\<USER>\Desktop\柔性钙钛矿\GTK\McLaser_V1\docs\项目说明.mdXC:\Users\<USER>\Desktop\柔性钙钛矿\GTK\McLaser_V1\Properties\Settings.settingsFC:\Users\<USER>\Desktop\柔性钙钛矿\GTK\McLaser_V1\App.configGC:\Users\<USER>\Desktop\柔性钙钛矿\GTK\bin\halcondotnetxl.dllEC:\Users\<USER>\Desktop\柔性钙钛矿\GTK\bin\McLaser.Core.dllGC:\Users\<USER>\Desktop\柔性钙钛矿\GTK\bin\McLaser.Device.dlliC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\Microsoft.CSharp.dllQC:\Users\<USER>\Desktop\柔性钙钛矿\GTK\bin\Microsoft.Xaml.Behaviors.dllaC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\mscorlib.dllHC:\Users\<USER>\Desktop\柔性钙钛矿\GTK\bin\Newtonsoft.Json.dlliC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\PresentationCore.dllnC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\PresentationFramework.dllzC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.ComponentModel.Composition.dll~C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.ComponentModel.DataAnnotations.dlldC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Core.dllvC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Data.DataSetExtensions.dlldC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Data.dll_C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.dllhC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Net.Http.dlldC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Xaml.dllcC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Xml.dllhC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\System.Xml.Linq.dlldC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\WindowsBase.dll       UC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\   Full                 {CandidateAssemblyFiles}{HintPathFromItem}{TargetFrameworkDirectory}D{Registry:Software\Microsoft\.NETFramework,v4.7.2,AssemblyFoldersEx}
{RawFileName}5C:\Users\<USER>\Desktop\柔性钙钛矿\GTK\bin\     D{Registry:Software\Microsoft\.NETFramework,v4.7.2,AssemblyFoldersEx}{C:\Users\<USER>\Desktop\柔性钙钛矿\GTK\McLaser_V1\McLaser.App\obj\Debug\DesignTimeResolveAssemblyReferences.cache   UC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\]C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.7.2\Facades\.NETFramework,Version=v4.7.2.NET Framework 4.7.2v4.7.2msil
v4.0.30319         