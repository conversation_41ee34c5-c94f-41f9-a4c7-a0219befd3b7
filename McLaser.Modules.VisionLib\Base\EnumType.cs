﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Markup;

namespace McLaser.Modules.Vision
{
    // 枚举名称
    [AttributeUsage(AttributeTargets.Field, AllowMultiple = false)]
    public sealed class EnumDescriptionAttribute : Attribute
    {
        public string Description { get; }
        public EnumDescriptionAttribute(string description) : base()
        {
            this.Description = description;
        }
    }
    // 获取枚举字符串
    public static class REnum
    {
        public static string EnumToStr(Enum value)
        {
            if (value == null)
            {
                throw new ArgumentException("value");
            }
            string description = value.ToString();
            Type type = value.GetType();
            var fieldInfo = value.GetType().GetField(description);
            var attributes =
                (EnumDescriptionAttribute[])fieldInfo.GetCustomAttributes(typeof(EnumDescriptionAttribute), false);
            if (attributes != null && attributes.Length > 0)
            {
                description = attributes[0].Description;
            }
            return description;
        }

        // get all information of enum,include value,name and description
        public static List<string> GetEnumDescription(Type enumName)
        {
            List<string> list = new List<string>();
            // get enum fileds
            FieldInfo[] fields = enumName.GetFields();
            foreach (FieldInfo field in fields)
            {
                if (!field.FieldType.IsEnum)
                {
                    continue;
                }
                // get enum value
                int value = (int)enumName.InvokeMember(field.Name, BindingFlags.GetField, null, null, null);
                string text = field.Name;
                string description = string.Empty;
                object[] array = field.GetCustomAttributes(typeof(EnumDescriptionAttribute), false);
                if (array.Length > 0)
                {
                    description = ((EnumDescriptionAttribute)array[0]).Description;
                }
                else
                {
                    description = ""; //none description,set empty
                }
                //add to list
                list.Add(description);
            }
            return list;
        }
    }
    public class EnumDescriptionTypeConverter : EnumConverter
    {
        public EnumDescriptionTypeConverter(Type type) : base(type)
        {
        }

        public override object ConvertTo(ITypeDescriptorContext context, CultureInfo culture, object value, Type destinationType)
        {
            if (destinationType == typeof(string))
            {
                if (null != value)
                {
                    FieldInfo fi = value.GetType().GetField(value.ToString());

                    if (null != fi)
                    {
                        var attributes =
                            (DescriptionAttribute[])fi.GetCustomAttributes(typeof(DescriptionAttribute), false);

                        return ((attributes.Length > 0) && (!string.IsNullOrEmpty(attributes[0].Description)))
                            ? attributes[0].Description
                            : value.ToString();
                    }
                }

                return string.Empty;
            }
            return base.ConvertTo(context, culture, value, destinationType);
        }
    }

    public class EnumBindingSourceExtension : MarkupExtension
    {
        private Type _enumType;

        public Type EnumType
        {
            get { return _enumType; }
            set
            {
                if (value != _enumType)
                {
                    if (null != value)
                    {
                        var enumType = Nullable.GetUnderlyingType(value) ?? value;
                        if (!enumType.IsEnum)
                        {
                            throw new ArgumentException("Type must bu for an Enum");
                        }

                    }

                    _enumType = value;
                }
            }
        }

        public EnumBindingSourceExtension()
        {

        }

        public EnumBindingSourceExtension(Type enumType)
        {
            EnumType = enumType;
        }
        public override object ProvideValue(IServiceProvider serviceProvider)
        {
            if (null == _enumType)
            {
                throw new InvalidOperationException("The EnumTYpe must be specified.");
            }

            var actualEnumType = Nullable.GetUnderlyingType(_enumType) ?? _enumType;
            var enumValues = Enum.GetValues(actualEnumType);

            if (actualEnumType == _enumType)
            {
                return enumValues;
            }

            var tempArray = Array.CreateInstance(actualEnumType, enumValues.Length + 1);
            enumValues.CopyTo(tempArray, 1);

            return tempArray;
        }
    }

    public enum RunStatus
    {
        Running,
        OK,
        NG,
        None,
    }

    public enum ViewMode
    {
        One,
        Two,
        Three,
        Four,
        Five,
        Six,
        Seven,
        Eight,
        Night
    }

    [Serializable]
    [TypeConverter(typeof(EnumDescriptionTypeConverter))]
    public enum LoopMode
    {
        [Description("从Start到End-1递增")]
        Increase,
        [Description("从End-1到Start递减")]
        Decrease,
        [Description("无限循环")]
        Loop,
        [Description("遍历数组")]
        Foreach,
    }

    // 流程状态
    [Serializable]
    public enum RunMode
    {
        None = 0,
        // 运行一次
        RunOnce = 1,
        // 循环运行
        RunCycle = 2,
    }

    [Serializable]
    public enum ProjectAutoRunMode
    {
        主动执行 = 0,
        调用执行 = 1,
    }

    // 操作类型：加载，增加，删除
    public enum OperateType
    {
        Add,
        Load,
        Remove,
        Clear
    }

    [Serializable]
    public enum CommunicationType
    {
        TCP客户端 = 0,
        TCP服务器 = 1,
        UDP通讯 = 2,
        串口通讯 = 3,
    }

    public enum eTypes
    {
        Int,
        Double,
        String,
        HObject,
        HTuple,
        HImage,
        HRegion,
        HXld
    }

    public enum ProjectType
    {
        // 流程
        Process,
        // 方法
        Method,
        // 文件夹       
        Folder,
    }
    public enum MsgType
    {
        // 成功
        Success,
        // 消息  
        Info,
        // 警告 
        Warn,
        // 报错(不置位报警标志，设备可以继续运行)        
        Error,
        // 报警(置位报警标志，设备不能继续运行)       
        Alarm,
    }
    public enum AlarmState
    {
        // 报警中
        Active = 0,
        // 无报警
        NoActive = 1
    }

    public enum BaudRate
    {
        t9600,
        t115200,
    }

    public enum StopBit
    {
        One,
        Two
    }

    //奇偶校验
    public enum Paity
    {
        None,
        ODD,
        EVEN
    }
    public enum ButtonEnableControl
    {
        Starting,
        Stopping,
    }
    public enum DataBits
    {
        D8
    }
    public enum Result
    {
        OK,
        NG,
    }
    public enum ProcessMode
    {
        平面焊接,
        旋转焊接,
    }
    public enum RippleEditProgramType
    {
        WAIT,
        SPT
    }
    // 通过:指定,文件,相机获取图片
    public enum ImageSource
    {
        指定图像,
        文件目录,
        相机采集
    }
    public enum RunProjectType
    {
        单次执行,
        循环执行,
        停止执行
    }
    public enum EndMark
    {
        无,
        回车,
        换行,
    }
    public enum ActiveState
    {

        // 已激活

        Actived,

        // 未激活

        NotActived,

        // 试用

        Probation
    }
    public enum HomeMode
    {
        负极限_原点,
        正极限_原点,
        原点,
        负极限_Index,
        正极限_Index,
        零位置预设,
        负极限,
        正极限
    }
    public enum Direction
    {

        // 正向

        Positive = 1,

        // 负向

        Negative = -1
    }
    public enum DataType
    {
        Int,
        Double,
        String,
        Bool,
        IntAry,
        DoubleAry,
        StringAry,
        BoolAry,
        区域,
        ImageAry,

    }

    // 对齐方式
    public enum AlignMode
    {
        左边,
        中间,
        右边
    }
    public enum FilterMode
    {
        无,
        中值滤波,
        均值滤波,
        高斯滤波,
        平滑滤波
    }
    // 取值模式
    public enum ValueMode
    {
        平均值,
        最大值,
        最小值,

    }
    // 调整模式
    [Serializable]
    public enum ImageAdjust
    {
        None = 0,
        垂直镜像,
        水平镜像,
        顺时针90度,
        逆时针90度,
        旋转180度
    }


    // 触发模式
    [Serializable]
    public enum TrigMode
    {
        内触发 = 0,
        软触发,
        上升沿,
        下降沿,
    }

    //文件结尾字符
    [Serializable]
    public enum EnableEndStr
    {
        Have,
        No,
    }

    // PLC数据解析格式
    [Serializable]
    public enum PLCDataType
    {
        ABCD = 0,
        BADC,
        CDAB,
        DCBA,
    }
    // 改变参数类型
    [Serializable]
    public enum ChangType
    {
        曝光,
        触发,
        宽度,
        高度,
        增益
    }
    // 通信类型
    [Serializable]
    public enum PLCType
    {
        ModbusRtu = 0,
        ModbusTCP = 1,
    }
    // PLC int数据宽度
    [Serializable]
    public enum PLCDataWriteReadTypeEnum
    {
        布尔 = 0,
        整型 = 1,
        浮点 = 2,
        字符串 = 3,
    }
    [Serializable]
    public enum PLCIntDataLengthEnum
    {
        _16位 = 0,
        _32位 = 1,
        _64位 = 2,
    }
    // PLC double数据宽度
    [Serializable]
    public enum PLCDoubleDataLengthEnum
    {
        _32位 = 1,
        _64位 = 2,
    }
    // PLC
    [Serializable]
    public enum PLCFunctionCodeEnum
    {
        _01 = 0,
        _02 = 1,
        _03 = 2,
        _04 = 3,
    }

    public enum SearchRegion
    {
        矩形1,
        矩形2,
        链接区域
    }

    public enum ShieldedRegion
    {
        手绘区域,
        链接区域
    }

    public enum WorkingMode
    {
        图像模式,
        区域模式
    }

    public enum ScopeEffect
    {
        ROI绘制,
        ROI链接
    }

    public enum EditMode
    {
        正常显示,
        绘制涂抹,
        擦除涂抹,
    }
    #region 存储图像
    public enum ImageFileFormat
    {
        tiff,
        jpg,
        ply,
        png,
        bmp,
        tifff16
    }

    public enum SaveImageType
    {
        原图,
        截图
    }
    #endregion

    #region 预处理
    public enum OperatorType
    {
        彩色转灰,
        图像镜像,
        图像旋转,
        深度转彩色,
        修改图像尺寸,
        缩放图像尺寸,
        深度转灰度,
        裁剪图像,
        倾斜补正,
        斜切变换,
        阴影补正,

        均值滤波,
        中值滤波,
        高斯滤波,

        灰度膨胀,
        灰度腐蚀,

        锐化,
        对比度,
        亮度调节,
        灰度开运算,
        灰度闭运算,
        反色,
        边缘增强,

        二值化,
        均值二值化,

        逐行拆分,
        点云孔洞填充
    }
    public enum TransImageType
    {
        通用比例转换,
        RGB,
        HSV,
        HSI,
        YUV,
    }
    public enum TransImageChannel
    {
        第一通道,
        第二通道,
        第三通道,
    }
    public enum MirrorImageType
    {
        水平镜像,
        垂直镜像,
        对角镜像,
    }
    public enum VarThresholdType
    {
        大于等于,
        小于等于,
        等于,
        不等于,
    }
    public enum RotateImageAngle
    {
        _90,
        _180,
        _270,
    }
    #endregion
    #region Blob
    public enum BinarizationMode
    {
        二值化,
        均值二值化,
        二值化链接阈值,
        深度图二值化,
        自动二值化
    }

    public enum BinCompareType
    {
        [EnumDescription("light")]
        大于等于,
        [EnumDescription("dark")]
        小于等于,
        [EnumDescription("equal")]
        等于,
        [EnumDescription("not_equal")]
        不等于
    }

    public enum BinExtractionArea
    {
        [EnumDescription("light")]
        Light,
        [EnumDescription("dark")]
        Dark
    }

    public enum RegionAnalysis
    {
        连通,
        合并,
        补集,
        相减,
        相交,
        孔洞填充,

        闭运算,
        开运算,
        腐蚀,
        膨胀,

        特征筛选,
        转换,
        矩形分割,
        动态分割,
        获取最大区域,
        亮度
    }
    public enum BlobElementType
    {
        圆形,
        矩形
    }
    public enum ConditionalRelation
    {
        [EnumDescription("and")]
        AND,
        [EnumDescription("or")]
        OR
    }
    public enum ConversionType
    {
        [EnumDescription("rectangle2")]
        最小外接矩形2,
        [EnumDescription("convex")]
        凸形,
        [EnumDescription("ellipse")]
        椭圆,
        [EnumDescription("outer_circle")]
        最小外接圆,
        [EnumDescription("inner_circle")]
        最大内接圆,
        [EnumDescription("rectangle1")]
        最小外接矩形1,
        [EnumDescription("inner_rectangle1")]
        最大内接矩形1
    }
    public enum Features
    {
        [EnumDescription("area")]
        面积,
        [EnumDescription("column")]
        X,
        [EnumDescription("row")]
        Y,
        [EnumDescription("width")]
        宽度,
        [EnumDescription("height")]
        高度,
        [EnumDescription("orientation")]
        角度,
        [EnumDescription("rect2_phi")]
        最小外接矩形角度,
        [EnumDescription("rect2_len2")]
        最小外接矩形宽度,
        [EnumDescription("rect2_len1")]
        最小外接矩形高度,
        [EnumDescription("area")]
        亮度,
        [EnumDescription("circularity")]
        圆度,
        [EnumDescription("compactness")]
        紧密度,
        [EnumDescription("convexity")]
        凸度,
        [EnumDescription("rectangularity")]
        矩形度,
        [EnumDescription("max_diameter")]
        最大内直径
    }
    public enum Channels
    {
        R,
        G,
        B
    }
    #endregion
    #region 定位
    public enum ModelType
    {
        形状模板,
        灰度模板,
        可伸缩形状模板
    }

    public enum OperateModel
    {
        StartLearn,
        Edit,
        EndLearn,
        Cancel
    }

    public enum CompType
    {
        [EnumDescription("use_polarity")]
        黑白对比一致,
        [EnumDescription("ignore_color_polarity")]
        黑白对比不一致,
        [EnumDescription("ignore_global_polarity")]
        黑白对比局部不一致
    }

    public enum Optimization
    {
        [EnumDescription("point_reduction_high")]
        精细,
        [EnumDescription("point_reduction_medium")]
        正常,
        [EnumDescription("point_reduction_low")]
        粗略,
        [EnumDescription("auto")]
        自定义
    }

    public enum DrawShape
    {
        圆形,
        矩形,
    }
    #endregion
    #region 标定
    public enum MeasureCalibMode
    {
        孔板模式,
        比例模式,
        孔板校正图像模式,
        多相机孔板校正
    }

    public enum GrabImageMode
    {
        固定相机_先拍照再取或放,
        固定相机_抓取后拍照,
        运动相机_先拍照再取或放,
        第二臂运动相机先拍照,
        固定相机_先拍照再取或放_注册标准位置,
        运动相机_先拍照再取或放_注册标准位置,
    }

    public enum PointType
    {
        [EnumDescription("9")]
        Nine,
        [EnumDescription("14")]
        FourTeen
    }
    public enum CalType
    {
        全点读取,
        逐步读取,
        手动
    }
    public enum CamerType
    {
        固定,
        移动
    }
    public enum AngleType
    {
        固定,
        变化
    }
    #endregion
    #region 二维码
    public enum ROIType
    {
        全图,
        矩形ROI,
    }
    public enum CodePolarity
    {
        [EnumDescription("dark_on_light")]
        白底黑码,
        [EnumDescription("light_on_dark")]
        黑底白码,
        [EnumDescription("any")]
        任意
    }
    public enum CodeEdgeType
    {
        连续性,
        离散型,
        兼容模式
    }
    public enum CodeMirrored
    {
        [EnumDescription("no")]
        非镜像,
        [EnumDescription("yes")]
        镜像,
        [EnumDescription("any")]
        任意
    }
    public enum CodeAberration
    {
        非畸变,
        畸变
    }
    public enum CodeApplyMode
    {
        [EnumDescription("enhanced_recognition")]
        普通模式,
        [EnumDescription("maximum_recognition")]
        专家模式,
        [EnumDescription("standard_recognition")]
        极速模式
    }
    public enum DMCodeType
    {
        [EnumDescription("square")]
        正方形,
        [EnumDescription("rectangle")]
        长方形,
        [EnumDescription("any")]
        兼容模式
    }
    #endregion
    #region 测量
    //测量类型

    public enum MeasureType
    {
        线宽检测,
        线线测量,
        点线测量,
        点线测量Ex,
        点点测量
    }
    public enum MetrologyType
    {
        直线,
        圆,
        矩形
    }
    /// <summary>测量模式</summary>
    public enum MeasMode
    {
        [EnumDescription("negative")]
        由白到黑,
        [EnumDescription("positive")]
        由黑到白,
        [EnumDescription("all")]
        所有信息
    }

    public enum MeasSelect
    {
        [EnumDescription("first")]
        第一点,
        [EnumDescription("last")]
        最末点,
        [EnumDescription("all")]
        所有点,
    }

    public enum MeasInterpolation
    {
        nearest_neighbor,
        bicubic,
        bilinear,
    }
    #endregion
    #region 映射
    public enum CoordinateMapMode
    {
        图像坐标_世界坐标,
        世界坐标_图像坐标
    }

    public enum MapMode
    {
        点映射,
        点集映射
    }
    #endregion
}
