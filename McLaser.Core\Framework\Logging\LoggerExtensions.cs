using System;

namespace McLaser.Core.Framework.Logging
{
    /// <summary>
    /// ILogger扩展方法
    /// 提供便捷的日志记录方法
    /// </summary>
    public static class LoggerExtensions
    {
        /// <summary>
        /// 记录信息日志
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="message">消息</param>
        public static void LogInfo(this ILogger? logger, string message)
        {
            logger?.Info(message);
        }

        /// <summary>
        /// 记录信息日志
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="message">消息</param>
        /// <param name="args">格式化参数</param>
        public static void LogInfo(this ILogger? logger, string message, params object[] args)
        {
            logger?.Info(message, args);
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="message">消息</param>
        public static void LogError(this ILogger? logger, string message)
        {
            logger?.Error(message);
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="message">消息</param>
        /// <param name="args">格式化参数</param>
        public static void LogError(this ILogger? logger, string message, params object[] args)
        {
            logger?.Error(message, args);
        }

        /// <summary>
        /// 记录错误日志和异常
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="message">消息</param>
        /// <param name="exception">异常</param>
        public static void LogError(this ILogger? logger, string message, Exception exception)
        {
            logger?.Error(exception, message);
        }

        /// <summary>
        /// 记录警告日志
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="message">消息</param>
        public static void LogWarning(this ILogger? logger, string message)
        {
            logger?.Warn(message);
        }

        /// <summary>
        /// 记录警告日志
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="message">消息</param>
        /// <param name="args">格式化参数</param>
        public static void LogWarning(this ILogger? logger, string message, params object[] args)
        {
            logger?.Warn(message, args);
        }
    }
}
