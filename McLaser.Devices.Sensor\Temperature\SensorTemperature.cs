using System;
using System.ComponentModel;
using System.IO.Ports;
using System.Text;
using System.Threading;
using System.Xml.Serialization;
using McLaser.Device;

namespace McLaser.Devices.Sensor.Temperature
{
    /// <summary>
    /// 温度传感器驱动实现
    /// 支持串口通信的温度传感器设备
    /// </summary>
    [Category("传感器")]
    [DisplayName("温度传感器")]
    [Serializable]
    public class SensorTemperature : SensorBase
    {
        #region 私有字段

        /// <summary>
        /// 串口对象
        /// </summary>
        private SerialPort serialPort;

        /// <summary>
        /// 线程锁对象
        /// </summary>
        private static readonly object objLock = new object();

        /// <summary>
        /// 校准偏移量
        /// </summary>
        private double calibrationOffset = 0;

        /// <summary>
        /// 校准系数
        /// </summary>
        private double calibrationScale = 1.0;

        #endregion

        #region 属性

        /// <summary>
        /// 串口名称
        /// </summary>
        [Category("温度传感器连接"), DisplayName("串口名称")]
        public string PortName { get; set; } = "COM1";

        /// <summary>
        /// 波特率
        /// </summary>
        [Category("温度传感器连接"), DisplayName("波特率")]
        public int BaudRate { get; set; } = 9600;

        /// <summary>
        /// 数据位
        /// </summary>
        [Category("温度传感器连接"), DisplayName("数据位")]
        public int DataBits { get; set; } = 8;

        /// <summary>
        /// 停止位
        /// </summary>
        [Category("温度传感器连接"), DisplayName("停止位")]
        public StopBits StopBits { get; set; } = StopBits.One;

        /// <summary>
        /// 校验位
        /// </summary>
        [Category("温度传感器连接"), DisplayName("校验位")]
        public Parity Parity { get; set; } = Parity.None;

        /// <summary>
        /// 读取超时时间(毫秒)
        /// </summary>
        [Category("温度传感器连接"), DisplayName("读取超时(ms)")]
        public int ReadTimeout { get; set; } = 1000;

        /// <summary>
        /// 写入超时时间(毫秒)
        /// </summary>
        [Category("温度传感器连接"), DisplayName("写入超时(ms)")]
        public int WriteTimeout { get; set; } = 1000;

        /// <summary>
        /// 传感器类型
        /// </summary>
        [Category("温度传感器信息"), DisplayName("传感器类型")]
        public override SensorType SensorType => SensorType.Temperature;

        /// <summary>
        /// 测量单位
        /// </summary>
        [Category("温度传感器信息"), DisplayName("测量单位")]
        public override string Unit => "℃";

        /// <summary>
        /// 测量范围最小值
        /// </summary>
        [Category("温度传感器信息"), DisplayName("测量范围最小值")]
        public override double MinValue => -50.0;

        /// <summary>
        /// 测量范围最大值
        /// </summary>
        [Category("温度传感器信息"), DisplayName("测量范围最大值")]
        public override double MaxValue => 150.0;

        /// <summary>
        /// 测量精度
        /// </summary>
        [Category("温度传感器信息"), DisplayName("测量精度")]
        public override double Accuracy => 0.1;

        /// <summary>
        /// 传感器状态
        /// </summary>
        [Category("温度传感器状态"), DisplayName("传感器状态")]
        public override SensorStatus SensorStatus
        {
            get
            {
                if (Status is StatusTemperature tempStatus)
                    return tempStatus.SensorStatus;
                return SensorStatus.Unknown;
            }
        }

        /// <summary>
        /// 设备状态
        /// </summary>
        [XmlIgnore, Browsable(false)]
        public override StatusBase Status { get; set; } = new StatusTemperature();

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public SensorTemperature()
        {
            Name = "温度传感器";
            DeviceType = DeviceType.Sensor;
        }

        #endregion

        #region 设备基本操作

        /// <summary>
        /// 打开设备连接
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool Open()
        {
            try
            {
                if (!IsEnabled) return false;
                if (Status.IsConnected) Close();

                // 创建串口连接
                serialPort = new SerialPort(PortName, BaudRate, Parity, DataBits, StopBits);
                serialPort.ReadTimeout = ReadTimeout;
                serialPort.WriteTimeout = WriteTimeout;
                serialPort.NewLine = "\r\n";

                serialPort.Open();
                Status.IsConnected = true;

                // 初始化传感器
                if (!InitializeSensor())
                {
                    Close();
                    return false;
                }

                if (Status is StatusTemperature tempStatus)
                {
                    tempStatus.SensorStatus = SensorStatus.Ready;
                }

                return IsOpen();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"打开温度传感器异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查设备是否已打开
        /// </summary>
        /// <returns>是否已打开</returns>
        public override bool IsOpen()
        {
            return Status.IsConnected && serialPort != null && serialPort.IsOpen;
        }

        /// <summary>
        /// 关闭设备连接
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool Close()
        {
            try
            {
                // 停止采样
                StopSampling();

                // 关闭串口连接
                serialPort?.Close();
                serialPort?.Dispose();
                serialPort = null;

                Status.IsConnected = false;
                if (Status is StatusTemperature tempStatus)
                {
                    tempStatus.SensorStatus = SensorStatus.Offline;
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"关闭温度传感器异常：{ex.Message}");
                return false;
            }
        }

        #endregion

        #region 传感器操作

        /// <summary>
        /// 读取传感器值
        /// </summary>
        /// <param name="value">测量值</param>
        /// <returns>是否成功</returns>
        public override bool ReadValue(ref double value)
        {
            try
            {
                if (!IsOpen()) return false;

                string command = "READ_TEMP\r\n";
                string response = string.Empty;

                if (SendCommand(command, out response))
                {
                    if (double.TryParse(response.Trim(), out double rawValue))
                    {
                        // 应用校准
                        value = ApplyCalibration(rawValue);
                        CurrentValue = value;

                        if (Status is StatusTemperature tempStatus)
                        {
                            tempStatus.UpdateValue(value);
                        }

                        return true;
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"读取温度传感器值异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 读取多个传感器值
        /// </summary>
        /// <param name="values">测量值数组</param>
        /// <param name="count">读取数量</param>
        /// <returns>是否成功</returns>
        public override bool ReadValues(ref double[] values, int count)
        {
            try
            {
                if (!IsOpen() || count <= 0) return false;

                values = new double[count];
                for (int i = 0; i < count; i++)
                {
                    double value = 0;
                    if (!ReadValue(ref value))
                        return false;
                    
                    values[i] = value;
                    Thread.Sleep(10); // 短暂延时
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"读取温度传感器多个值异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 校准传感器
        /// </summary>
        /// <param name="referenceValue">参考值</param>
        /// <returns>是否成功</returns>
        public override bool Calibrate(double referenceValue)
        {
            try
            {
                if (!IsOpen()) return false;

                // 读取当前原始值
                double currentValue = 0;
                if (!ReadRawValue(ref currentValue))
                    return false;

                // 计算校准偏移量
                calibrationOffset = referenceValue - currentValue;
                IsCalibrated = true;

                if (Status is StatusTemperature tempStatus)
                {
                    tempStatus.UpdateCalibrationStatus(true);
                }

                OnCalibrationCompleted(CalibrationType.TwoPoint, true, $"校准完成，偏移量: {calibrationOffset:F3}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"校准温度传感器异常：{ex.Message}");
                OnCalibrationCompleted(CalibrationType.TwoPoint, false, $"校准失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 零点校准
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool ZeroCalibration()
        {
            return Calibrate(0.0);
        }

        /// <summary>
        /// 满量程校准
        /// </summary>
        /// <param name="fullScaleValue">满量程值</param>
        /// <returns>是否成功</returns>
        public override bool FullScaleCalibration(double fullScaleValue)
        {
            try
            {
                if (!IsOpen()) return false;

                // 读取当前原始值
                double currentValue = 0;
                if (!ReadRawValue(ref currentValue))
                    return false;

                // 计算校准系数
                if (Math.Abs(currentValue) > 0.001)
                {
                    calibrationScale = fullScaleValue / currentValue;
                    IsCalibrated = true;

                    if (Status is StatusTemperature tempStatus)
                    {
                        tempStatus.UpdateCalibrationStatus(true);
                    }

                    OnCalibrationCompleted(CalibrationType.FullScale, true, $"满量程校准完成，系数: {calibrationScale:F3}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"满量程校准温度传感器异常：{ex.Message}");
                OnCalibrationCompleted(CalibrationType.FullScale, false, $"满量程校准失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 重置校准
        /// </summary>
        /// <returns>是否成功</returns>
        public override bool ResetCalibration()
        {
            try
            {
                calibrationOffset = 0;
                calibrationScale = 1.0;
                IsCalibrated = false;

                if (Status is StatusTemperature tempStatus)
                {
                    tempStatus.UpdateCalibrationStatus(false);
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"重置温度传感器校准异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 传感器自检
        /// </summary>
        /// <returns>自检结果</returns>
        public override bool SelfTest()
        {
            try
            {
                if (!IsOpen()) return false;

                string command = "SELF_TEST\r\n";
                string response = string.Empty;

                if (SendCommand(command, out response))
                {
                    return response.Trim().ToUpper().Contains("PASS");
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"温度传感器自检异常：{ex.Message}");
                return false;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化传感器
        /// </summary>
        /// <returns>是否成功</returns>
        private bool InitializeSensor()
        {
            try
            {
                // 发送初始化命令
                string command = "INIT\r\n";
                string response = string.Empty;

                if (SendCommand(command, out response))
                {
                    return response.Trim().ToUpper().Contains("OK");
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化温度传感器异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 发送命令并获取响应
        /// </summary>
        /// <param name="command">命令字符串</param>
        /// <param name="response">响应字符串</param>
        /// <returns>是否成功</returns>
        private bool SendCommand(string command, out string response)
        {
            response = string.Empty;
            try
            {
                lock (objLock)
                {
                    if (serialPort == null || !serialPort.IsOpen)
                        return false;

                    // 清空缓冲区
                    serialPort.DiscardInBuffer();
                    serialPort.DiscardOutBuffer();

                    // 发送命令
                    serialPort.WriteLine(command);

                    // 读取响应
                    response = serialPort.ReadLine();

                    return true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"温度传感器发送命令异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 读取原始值（未校准）
        /// </summary>
        /// <param name="value">原始值</param>
        /// <returns>是否成功</returns>
        private bool ReadRawValue(ref double value)
        {
            try
            {
                string command = "READ_RAW\r\n";
                string response = string.Empty;

                if (SendCommand(command, out response))
                {
                    return double.TryParse(response.Trim(), out value);
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"读取温度传感器原始值异常：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 应用校准
        /// </summary>
        /// <param name="rawValue">原始值</param>
        /// <returns>校准后的值</returns>
        private double ApplyCalibration(double rawValue)
        {
            return (rawValue * calibrationScale) + calibrationOffset;
        }

        #endregion
    }
}
