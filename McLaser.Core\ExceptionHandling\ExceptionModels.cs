using System;
using System.Collections.Generic;
using System.Linq;

namespace McLaser.Core.ExceptionHandling
{
    /// <summary>
    /// 异常处理结果
    /// </summary>
    public class ExceptionHandlingResult
    {
        /// <summary>
        /// 是否已处理
        /// </summary>
        public bool IsHandled { get; set; }

        /// <summary>
        /// 是否应该记录日志
        /// </summary>
        public bool ShouldLog { get; set; } = true;

        /// <summary>
        /// 是否应该重新抛出异常
        /// </summary>
        public bool ShouldRethrow { get; set; } = false;

        /// <summary>
        /// 是否应该终止应用程序
        /// </summary>
        public bool ShouldTerminate { get; set; } = false;

        /// <summary>
        /// 用户友好的错误消息
        /// </summary>
        public string? UserMessage { get; set; }

        /// <summary>
        /// 技术错误消息
        /// </summary>
        public string? TechnicalMessage { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public string? ErrorCode { get; set; }

        /// <summary>
        /// 处理器名称
        /// </summary>
        public string? HandlerName { get; set; }

        /// <summary>
        /// 处理时间
        /// </summary>
        public DateTime HandledTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 处理耗时（毫秒）
        /// </summary>
        public long ProcessingTime { get; set; }

        /// <summary>
        /// 恢复结果
        /// </summary>
        public RecoveryResult? RecoveryResult { get; set; }

        /// <summary>
        /// 扩展数据
        /// </summary>
        public Dictionary<string, object> ExtendedData { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 创建已处理的结果
        /// </summary>
        /// <param name="userMessage">用户消息</param>
        /// <param name="handlerName">处理器名称</param>
        /// <returns>处理结果</returns>
        public static ExceptionHandlingResult Handled(string? userMessage = null, string? handlerName = null)
        {
            return new ExceptionHandlingResult
            {
                IsHandled = true,
                UserMessage = userMessage,
                HandlerName = handlerName
            };
        }

        /// <summary>
        /// 创建未处理的结果
        /// </summary>
        /// <param name="reason">未处理原因</param>
        /// <returns>处理结果</returns>
        public static ExceptionHandlingResult NotHandled(string? reason = null)
        {
            return new ExceptionHandlingResult
            {
                IsHandled = false,
                TechnicalMessage = reason
            };
        }

        /// <summary>
        /// 创建需要重新抛出的结果
        /// </summary>
        /// <param name="userMessage">用户消息</param>
        /// <param name="handlerName">处理器名称</param>
        /// <returns>处理结果</returns>
        public static ExceptionHandlingResult Rethrow(string? userMessage = null, string? handlerName = null)
        {
            return new ExceptionHandlingResult
            {
                IsHandled = true,
                ShouldRethrow = true,
                UserMessage = userMessage,
                HandlerName = handlerName
            };
        }

        /// <summary>
        /// 创建需要终止应用程序的结果
        /// </summary>
        /// <param name="userMessage">用户消息</param>
        /// <param name="handlerName">处理器名称</param>
        /// <returns>处理结果</returns>
        public static ExceptionHandlingResult Terminate(string? userMessage = null, string? handlerName = null)
        {
            return new ExceptionHandlingResult
            {
                IsHandled = true,
                ShouldTerminate = true,
                UserMessage = userMessage,
                HandlerName = handlerName
            };
        }
    }

    /// <summary>
    /// 异常上下文
    /// </summary>
    public class ExceptionContext
    {
        /// <summary>
        /// 上下文ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 异常发生时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 线程ID
        /// </summary>
        public int ThreadId { get; set; } = System.Threading.Thread.CurrentThread.ManagedThreadId;

        /// <summary>
        /// 应用程序域名称
        /// </summary>
        public string AppDomainName { get; set; } = AppDomain.CurrentDomain.FriendlyName;

        /// <summary>
        /// 机器名称
        /// </summary>
        public string MachineName { get; set; } = Environment.MachineName;

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; } = Environment.UserName;

        /// <summary>
        /// 进程ID
        /// </summary>
        public int ProcessId { get; set; } = System.Diagnostics.Process.GetCurrentProcess().Id;

        /// <summary>
        /// 异常来源
        /// </summary>
        public string? Source { get; set; }

        /// <summary>
        /// 操作名称
        /// </summary>
        public string? OperationName { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// 会话ID
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// 请求ID
        /// </summary>
        public string? RequestId { get; set; }

        /// <summary>
        /// 相关ID
        /// </summary>
        public string? CorrelationId { get; set; }

        /// <summary>
        /// 异常严重级别
        /// </summary>
        public ExceptionSeverity Severity { get; set; } = ExceptionSeverity.Error;

        /// <summary>
        /// 异常分类
        /// </summary>
        public ExceptionCategory Category { get; set; } = ExceptionCategory.Unknown;

        /// <summary>
        /// 是否为用户操作引起
        /// </summary>
        public bool IsUserInitiated { get; set; } = false;

        /// <summary>
        /// 是否为关键操作
        /// </summary>
        public bool IsCriticalOperation { get; set; } = false;

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; } = 0;

        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetryCount { get; set; } = 3;

        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> Properties { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 调用堆栈信息
        /// </summary>
        public string? StackTrace { get; set; }

        /// <summary>
        /// 内部异常信息
        /// </summary>
        public List<ExceptionInfo> InnerExceptions { get; set; } = new List<ExceptionInfo>();

        /// <summary>
        /// 获取属性值
        /// </summary>
        /// <typeparam name="T">属性类型</typeparam>
        /// <param name="key">属性键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>属性值</returns>
        public T GetProperty<T>(string key, T defaultValue = default!)
        {
            if (Properties.TryGetValue(key, out var value) && value is T typedValue)
            {
                return typedValue;
            }
            return defaultValue;
        }

        /// <summary>
        /// 设置属性值
        /// </summary>
        /// <param name="key">属性键</param>
        /// <param name="value">属性值</param>
        public void SetProperty(string key, object value)
        {
            Properties[key] = value;
        }

        /// <summary>
        /// 创建异常上下文
        /// </summary>
        /// <param name="source">异常来源</param>
        /// <param name="operationName">操作名称</param>
        /// <returns>异常上下文</returns>
        public static ExceptionContext Create(string? source = null, string? operationName = null)
        {
            return new ExceptionContext
            {
                Source = source,
                OperationName = operationName
            };
        }
    }

    /// <summary>
    /// 异常信息
    /// </summary>
    public class ExceptionInfo
    {
        /// <summary>
        /// 异常类型
        /// </summary>
        public string ExceptionType { get; set; } = string.Empty;

        /// <summary>
        /// 异常消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 异常来源
        /// </summary>
        public string? Source { get; set; }

        /// <summary>
        /// 堆栈跟踪
        /// </summary>
        public string? StackTrace { get; set; }

        /// <summary>
        /// 目标站点
        /// </summary>
        public string? TargetSite { get; set; }

        /// <summary>
        /// 帮助链接
        /// </summary>
        public string? HelpLink { get; set; }

        /// <summary>
        /// 数据字典
        /// </summary>
        public Dictionary<string, object> Data { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 从异常创建异常信息
        /// </summary>
        /// <param name="exception">异常</param>
        /// <returns>异常信息</returns>
        public static ExceptionInfo FromException(Exception exception)
        {
            var info = new ExceptionInfo
            {
                ExceptionType = exception.GetType().FullName ?? exception.GetType().Name,
                Message = exception.Message,
                Source = exception.Source,
                StackTrace = exception.StackTrace,
                TargetSite = exception.TargetSite?.ToString(),
                HelpLink = exception.HelpLink
            };

            // 复制数据字典
            foreach (var key in exception.Data.Keys)
            {
                if (key != null)
                {
                    info.Data[key.ToString()!] = exception.Data[key] ?? string.Empty;
                }
            }

            return info;
        }
    }

    /// <summary>
    /// 恢复结果
    /// </summary>
    public class RecoveryResult
    {
        /// <summary>
        /// 是否成功恢复
        /// </summary>
        public bool IsSuccessful { get; set; }

        /// <summary>
        /// 恢复消息
        /// </summary>
        public string? Message { get; set; }

        /// <summary>
        /// 恢复操作
        /// </summary>
        public string? RecoveryAction { get; set; }

        /// <summary>
        /// 恢复时间
        /// </summary>
        public DateTime RecoveryTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 恢复耗时（毫秒）
        /// </summary>
        public long RecoveryDuration { get; set; }

        /// <summary>
        /// 恢复数据
        /// </summary>
        public Dictionary<string, object> RecoveryData { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 创建成功的恢复结果
        /// </summary>
        /// <param name="message">恢复消息</param>
        /// <param name="action">恢复操作</param>
        /// <returns>恢复结果</returns>
        public static RecoveryResult Success(string? message = null, string? action = null)
        {
            return new RecoveryResult
            {
                IsSuccessful = true,
                Message = message,
                RecoveryAction = action
            };
        }

        /// <summary>
        /// 创建失败的恢复结果
        /// </summary>
        /// <param name="message">失败消息</param>
        /// <returns>恢复结果</returns>
        public static RecoveryResult Failure(string? message = null)
        {
            return new RecoveryResult
            {
                IsSuccessful = false,
                Message = message
            };
        }
    }

    /// <summary>
    /// 异常严重级别枚举
    /// </summary>
    public enum ExceptionSeverity
    {
        /// <summary>
        /// 信息
        /// </summary>
        Information = 0,

        /// <summary>
        /// 警告
        /// </summary>
        Warning = 1,

        /// <summary>
        /// 错误
        /// </summary>
        Error = 2,

        /// <summary>
        /// 严重错误
        /// </summary>
        Critical = 3,

        /// <summary>
        /// 致命错误
        /// </summary>
        Fatal = 4
    }

    /// <summary>
    /// 异常分类枚举
    /// </summary>
    public enum ExceptionCategory
    {
        /// <summary>
        /// 未知
        /// </summary>
        Unknown,

        /// <summary>
        /// 业务逻辑异常
        /// </summary>
        Business,

        /// <summary>
        /// 数据访问异常
        /// </summary>
        DataAccess,

        /// <summary>
        /// 网络通信异常
        /// </summary>
        Network,

        /// <summary>
        /// 文件系统异常
        /// </summary>
        FileSystem,

        /// <summary>
        /// 安全异常
        /// </summary>
        Security,

        /// <summary>
        /// 配置异常
        /// </summary>
        Configuration,

        /// <summary>
        /// 验证异常
        /// </summary>
        Validation,

        /// <summary>
        /// 外部服务异常
        /// </summary>
        ExternalService,

        /// <summary>
        /// 系统异常
        /// </summary>
        System,

        /// <summary>
        /// 用户界面异常
        /// </summary>
        UserInterface,

        /// <summary>
        /// 设备异常
        /// </summary>
        Device,

        /// <summary>
        /// 插件异常
        /// </summary>
        Plugin
    }

    /// <summary>
    /// 异常处理策略
    /// </summary>
    public class ExceptionHandlingPolicy
    {
        /// <summary>
        /// 策略名称
        /// </summary>
        public string Name { get; set; } = "Default";

        /// <summary>
        /// 是否启用全局异常处理
        /// </summary>
        public bool EnableGlobalHandling { get; set; } = true;

        /// <summary>
        /// 是否记录所有异常
        /// </summary>
        public bool LogAllExceptions { get; set; } = true;

        /// <summary>
        /// 是否启用异常恢复
        /// </summary>
        public bool EnableRecovery { get; set; } = true;

        /// <summary>
        /// 是否启用异常通知
        /// </summary>
        public bool EnableNotification { get; set; } = false;

        /// <summary>
        /// 默认重试次数
        /// </summary>
        public int DefaultRetryCount { get; set; } = 3;

        /// <summary>
        /// 重试间隔（毫秒）
        /// </summary>
        public int RetryInterval { get; set; } = 1000;

        /// <summary>
        /// 最大处理时间（毫秒）
        /// </summary>
        public int MaxHandlingTime { get; set; } = 30000;

        /// <summary>
        /// 异常处理规则
        /// </summary>
        public List<ExceptionHandlingRule> Rules { get; set; } = new List<ExceptionHandlingRule>();

        /// <summary>
        /// 忽略的异常类型
        /// </summary>
        public HashSet<string> IgnoredExceptionTypes { get; set; } = new HashSet<string>();

        /// <summary>
        /// 关键异常类型
        /// </summary>
        public HashSet<string> CriticalExceptionTypes { get; set; } = new HashSet<string>();

        /// <summary>
        /// 扩展配置
        /// </summary>
        public Dictionary<string, object> ExtendedConfiguration { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 获取异常处理规则
        /// </summary>
        /// <param name="exceptionType">异常类型</param>
        /// <returns>处理规则</returns>
        public ExceptionHandlingRule? GetRule(Type exceptionType)
        {
            return Rules.FirstOrDefault(r => r.ExceptionType == exceptionType.FullName ||
                                           r.ExceptionType == exceptionType.Name ||
                                           exceptionType.IsSubclassOf(Type.GetType(r.ExceptionType) ?? typeof(object)));
        }

        /// <summary>
        /// 是否应该忽略异常
        /// </summary>
        /// <param name="exceptionType">异常类型</param>
        /// <returns>是否忽略</returns>
        public bool ShouldIgnore(Type exceptionType)
        {
            return IgnoredExceptionTypes.Contains(exceptionType.FullName ?? exceptionType.Name) ||
                   IgnoredExceptionTypes.Contains(exceptionType.Name);
        }

        /// <summary>
        /// 是否为关键异常
        /// </summary>
        /// <param name="exceptionType">异常类型</param>
        /// <returns>是否关键</returns>
        public bool IsCritical(Type exceptionType)
        {
            return CriticalExceptionTypes.Contains(exceptionType.FullName ?? exceptionType.Name) ||
                   CriticalExceptionTypes.Contains(exceptionType.Name);
        }
    }

    /// <summary>
    /// 异常处理规则
    /// </summary>
    public class ExceptionHandlingRule
    {
        /// <summary>
        /// 规则名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 异常类型
        /// </summary>
        public string ExceptionType { get; set; } = string.Empty;

        /// <summary>
        /// 处理动作
        /// </summary>
        public ExceptionHandlingAction Action { get; set; } = ExceptionHandlingAction.Log;

        /// <summary>
        /// 是否记录日志
        /// </summary>
        public bool ShouldLog { get; set; } = true;

        /// <summary>
        /// 是否重新抛出
        /// </summary>
        public bool ShouldRethrow { get; set; } = false;

        /// <summary>
        /// 是否尝试恢复
        /// </summary>
        public bool ShouldRecover { get; set; } = false;

        /// <summary>
        /// 是否发送通知
        /// </summary>
        public bool ShouldNotify { get; set; } = false;

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; } = 0;

        /// <summary>
        /// 重试间隔（毫秒）
        /// </summary>
        public int RetryInterval { get; set; } = 1000;

        /// <summary>
        /// 用户消息模板
        /// </summary>
        public string? UserMessageTemplate { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public string? ErrorCode { get; set; }

        /// <summary>
        /// 处理器名称
        /// </summary>
        public string? HandlerName { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        public int Priority { get; set; } = 0;

        /// <summary>
        /// 条件表达式
        /// </summary>
        public string? Condition { get; set; }

        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> Properties { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// 异常处理动作枚举
    /// </summary>
    public enum ExceptionHandlingAction
    {
        /// <summary>
        /// 忽略
        /// </summary>
        Ignore,

        /// <summary>
        /// 记录日志
        /// </summary>
        Log,

        /// <summary>
        /// 记录日志并重新抛出
        /// </summary>
        LogAndRethrow,

        /// <summary>
        /// 尝试恢复
        /// </summary>
        Recover,

        /// <summary>
        /// 发送通知
        /// </summary>
        Notify,

        /// <summary>
        /// 终止应用程序
        /// </summary>
        Terminate,

        /// <summary>
        /// 自定义处理
        /// </summary>
        Custom
    }

    /// <summary>
    /// 通知配置
    /// </summary>
    public class NotificationConfiguration
    {
        /// <summary>
        /// 是否启用邮件通知
        /// </summary>
        public bool EnableEmailNotification { get; set; } = false;

        /// <summary>
        /// 邮件收件人列表
        /// </summary>
        public List<string> EmailRecipients { get; set; } = new List<string>();

        /// <summary>
        /// 邮件主题模板
        /// </summary>
        public string EmailSubjectTemplate { get; set; } = "异常通知: {ExceptionType}";

        /// <summary>
        /// 邮件内容模板
        /// </summary>
        public string EmailBodyTemplate { get; set; } = "发生异常: {Message}\n时间: {Timestamp}\n来源: {Source}";

        /// <summary>
        /// 是否启用短信通知
        /// </summary>
        public bool EnableSmsNotification { get; set; } = false;

        /// <summary>
        /// 短信收件人列表
        /// </summary>
        public List<string> SmsRecipients { get; set; } = new List<string>();

        /// <summary>
        /// 短信内容模板
        /// </summary>
        public string SmsTemplate { get; set; } = "异常: {ExceptionType} - {Message}";

        /// <summary>
        /// 是否启用Webhook通知
        /// </summary>
        public bool EnableWebhookNotification { get; set; } = false;

        /// <summary>
        /// Webhook URL列表
        /// </summary>
        public List<string> WebhookUrls { get; set; } = new List<string>();

        /// <summary>
        /// Webhook负载模板
        /// </summary>
        public string WebhookPayloadTemplate { get; set; } = "{{\"exception\": \"{ExceptionType}\", \"message\": \"{Message}\", \"timestamp\": \"{Timestamp}\"}}";

        /// <summary>
        /// 通知频率限制（分钟）
        /// </summary>
        public int NotificationRateLimit { get; set; } = 5;

        /// <summary>
        /// 最大通知次数
        /// </summary>
        public int MaxNotificationCount { get; set; } = 10;

        /// <summary>
        /// 扩展配置
        /// </summary>
        public Dictionary<string, object> ExtendedConfiguration { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// 异常处理统计信息
    /// </summary>
    public class ExceptionHandlingStatistics
    {
        /// <summary>
        /// 总异常数
        /// </summary>
        public long TotalExceptions { get; set; }

        /// <summary>
        /// 已处理异常数
        /// </summary>
        public long HandledException { get; set; }

        /// <summary>
        /// 未处理异常数
        /// </summary>
        public long UnhandledException { get; set; }

        /// <summary>
        /// 恢复成功数
        /// </summary>
        public long RecoveredExceptions { get; set; }

        /// <summary>
        /// 重试次数
        /// </summary>
        public long TotalRetries { get; set; }

        /// <summary>
        /// 通知发送数
        /// </summary>
        public long NotificationsSent { get; set; }

        /// <summary>
        /// 平均处理时间（毫秒）
        /// </summary>
        public double AverageHandlingTime { get; set; }

        /// <summary>
        /// 最大处理时间（毫秒）
        /// </summary>
        public long MaxHandlingTime { get; set; }

        /// <summary>
        /// 最小处理时间（毫秒）
        /// </summary>
        public long MinHandlingTime { get; set; } = long.MaxValue;

        /// <summary>
        /// 异常类型统计
        /// </summary>
        public Dictionary<string, long> ExceptionTypeCount { get; set; } = new Dictionary<string, long>();

        /// <summary>
        /// 处理器统计
        /// </summary>
        public Dictionary<string, long> HandlerCount { get; set; } = new Dictionary<string, long>();

        /// <summary>
        /// 严重级别统计
        /// </summary>
        public Dictionary<ExceptionSeverity, long> SeverityCount { get; set; } = new Dictionary<ExceptionSeverity, long>();

        /// <summary>
        /// 分类统计
        /// </summary>
        public Dictionary<ExceptionCategory, long> CategoryCount { get; set; } = new Dictionary<ExceptionCategory, long>();

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        /// <summary>
        /// 统计开始时间
        /// </summary>
        public DateTime StartTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 运行时间
        /// </summary>
        public TimeSpan Uptime => DateTime.Now - StartTime;

        /// <summary>
        /// 异常处理成功率
        /// </summary>
        public double SuccessRate => TotalExceptions > 0 ? (double)HandledException / TotalExceptions * 100 : 0;

        /// <summary>
        /// 恢复成功率
        /// </summary>
        public double RecoveryRate => HandledException > 0 ? (double)RecoveredExceptions / HandledException * 100 : 0;

        /// <summary>
        /// 重置统计信息
        /// </summary>
        public void Reset()
        {
            TotalExceptions = 0;
            HandledException = 0;
            UnhandledException = 0;
            RecoveredExceptions = 0;
            TotalRetries = 0;
            NotificationsSent = 0;
            AverageHandlingTime = 0;
            MaxHandlingTime = 0;
            MinHandlingTime = long.MaxValue;
            ExceptionTypeCount.Clear();
            HandlerCount.Clear();
            SeverityCount.Clear();
            CategoryCount.Clear();
            StartTime = DateTime.Now;
            LastUpdated = DateTime.Now;
        }
    }

    #region 事件参数类

    /// <summary>
    /// 异常已处理事件参数
    /// </summary>
    public class ExceptionHandledEventArgs : EventArgs
    {
        /// <summary>
        /// 异常
        /// </summary>
        public Exception Exception { get; }

        /// <summary>
        /// 异常上下文
        /// </summary>
        public ExceptionContext Context { get; }

        /// <summary>
        /// 处理结果
        /// </summary>
        public ExceptionHandlingResult Result { get; }

        /// <summary>
        /// 处理时间
        /// </summary>
        public DateTime HandledTime { get; }

        public ExceptionHandledEventArgs(Exception exception, ExceptionContext context, ExceptionHandlingResult result)
        {
            Exception = exception;
            Context = context;
            Result = result;
            HandledTime = DateTime.Now;
        }
    }

    /// <summary>
    /// 未处理异常事件参数
    /// </summary>
    public class UnhandledExceptionEventArgs : EventArgs
    {
        /// <summary>
        /// 异常
        /// </summary>
        public Exception Exception { get; }

        /// <summary>
        /// 异常上下文
        /// </summary>
        public ExceptionContext Context { get; }

        /// <summary>
        /// 是否正在终止
        /// </summary>
        public bool IsTerminating { get; }

        /// <summary>
        /// 异常时间
        /// </summary>
        public DateTime ExceptionTime { get; }

        public UnhandledExceptionEventArgs(Exception exception, ExceptionContext context, bool isTerminating = false)
        {
            Exception = exception;
            Context = context;
            IsTerminating = isTerminating;
            ExceptionTime = DateTime.Now;
        }
    }

    #endregion
}
