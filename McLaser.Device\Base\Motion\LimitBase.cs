using System;
using System.ComponentModel;
using System.Xml.Serialization;
using McLaser.Device;

namespace McLaser.Devices
{
    /// <summary>
    /// 限位基类
    /// 定义轴的运动限制参数
    /// </summary>
    [Serializable]
    public class LimitBase : ConfigBaseEx
    {
        /// <summary>
        /// 正限位位置(mm)
        /// </summary>
        [Category("限位"), DisplayName("正限位(mm)")]
        public double PosMax { get; set; } = 10000;

        /// <summary>
        /// 负限位位置(mm)
        /// </summary>
        [Category("限位"), DisplayName("负限位(mm)")]
        public double PosMin { get; set; } = -100;

        /// <summary>
        /// 最大速度(mm/s)
        /// </summary>
        [Category("限位"), DisplayName("最大速度(mm/s)")]
        public double VelMax { get; set; } = 10000;

        /// <summary>
        /// 最小速度(mm/s)
        /// </summary>
        [Category("限位"), DisplayName("最小速度(mm/s)")]
        public double VelMin { get; set; } = 0.001;

        /// <summary>
        /// 最大加速度(mm/s²)
        /// </summary>
        [Category("限位"), DisplayName("最大加速度(mm/s²)")]
        public double AccMax { get; set; } = 10000;

        /// <summary>
        /// 最小加速度(mm/s²)
        /// </summary>
        [Category("限位"), DisplayName("最小加速度(mm/s²)")]
        public double AccMin { get; set; } = 0.1;

        /// <summary>
        /// 真实最大位置脉冲
        /// </summary>
        [XmlIgnore, Browsable(false)]
        public double PosMaxPulse { get; set; } = 1;

        /// <summary>
        /// 真实最小位置脉冲
        /// </summary>
        [XmlIgnore, Browsable(false)]
        public double PosMinPulse { get; set; } = 1;

        /// <summary>
        /// 真实最大速度脉冲
        /// </summary>
        [XmlIgnore, Browsable(false)]
        public double VelMaxPulse { get; set; } = 1;

        /// <summary>
        /// 真实最小速度脉冲
        /// </summary>
        [XmlIgnore, Browsable(false)]
        public double VelMinPulse { get; set; } = 1;

        /// <summary>
        /// 真实最大加速度脉冲
        /// </summary>
        [XmlIgnore, Browsable(false)]
        public double AccMaxPulse { get; set; } = 1;

        /// <summary>
        /// 真实最小加速度脉冲
        /// </summary>
        [XmlIgnore, Browsable(false)]
        public double AccMinPulse { get; set; } = 1;

        /// <summary>
        /// 构造函数
        /// </summary>
        public LimitBase()
        {
        }

        /// <summary>
        /// 检查位置是否在限位范围内
        /// </summary>
        /// <param name="position">要检查的位置</param>
        /// <returns>位置是否在限位范围内</returns>
        public bool IsPositionInRange(double position)
        {
            return position >= PosMin && position <= PosMax;
        }

        /// <summary>
        /// 检查速度是否在限位范围内
        /// </summary>
        /// <param name="velocity">要检查的速度</param>
        /// <returns>速度是否在限位范围内</returns>
        public bool IsVelocityInRange(double velocity)
        {
            double absVel = Math.Abs(velocity);
            return absVel >= VelMin && absVel <= VelMax;
        }

        /// <summary>
        /// 检查加速度是否在限位范围内
        /// </summary>
        /// <param name="acceleration">要检查的加速度</param>
        /// <returns>加速度是否在限位范围内</returns>
        public bool IsAccelerationInRange(double acceleration)
        {
            double absAcc = Math.Abs(acceleration);
            return absAcc >= AccMin && absAcc <= AccMax;
        }

        /// <summary>
        /// 限制位置到有效范围内
        /// </summary>
        /// <param name="position">输入位置</param>
        /// <returns>限制后的位置</returns>
        public double ClampPosition(double position)
        {
            if (position < PosMin) return PosMin;
            if (position > PosMax) return PosMax;
            return position;
        }

        /// <summary>
        /// 限制速度到有效范围内
        /// </summary>
        /// <param name="velocity">输入速度</param>
        /// <returns>限制后的速度</returns>
        public double ClampVelocity(double velocity)
        {
            double absVel = Math.Abs(velocity);
            if (absVel < VelMin) absVel = VelMin;
            if (absVel > VelMax) absVel = VelMax;
            return velocity >= 0 ? absVel : -absVel;
        }

        /// <summary>
        /// 限制加速度到有效范围内
        /// </summary>
        /// <param name="acceleration">输入加速度</param>
        /// <returns>限制后的加速度</returns>
        public double ClampAcceleration(double acceleration)
        {
            double absAcc = Math.Abs(acceleration);
            if (absAcc < AccMin) absAcc = AccMin;
            if (absAcc > AccMax) absAcc = AccMax;
            return acceleration >= 0 ? absAcc : -absAcc;
        }
    }
}
