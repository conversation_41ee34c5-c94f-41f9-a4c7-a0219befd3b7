using System;
using System.ComponentModel;
using System.Xml.Serialization;
using McLaser.Device;

namespace McLaser.Devices.Laser
{
    /// <summary>
    /// 激光器基础类
    /// 提供激光器设备的基本实现和通用功能
    /// </summary>
    [Serializable]
    public abstract class LaserBase : DeviceBase, ILaser
    {
        #region 私有字段

        private double _power = 0;
        private double _frequency = 1000;
        private double _pulseWidth = 100;
        private bool _isLaserEnabled = false;
        private LaserMode _laserMode = LaserMode.Continuous;

        #endregion

        #region 属性

        /// <summary>
        /// 激光器功率(0-100%)
        /// </summary>
        [Category("激光器参数"), DisplayName("功率(%)")]
        public virtual double Power
        {
            get => _power;
            set
            {
                if (Math.Abs(_power - value) > 0.001)
                {
                    _power = Math.Max(0, Math.Min(100, value));
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 激光器频率(Hz)
        /// </summary>
        [Category("激光器参数"), DisplayName("频率(Hz)")]
        public virtual double Frequency
        {
            get => _frequency;
            set
            {
                if (Math.Abs(_frequency - value) > 0.001)
                {
                    _frequency = Math.Max(MinFrequency, Math.Min(MaxFrequency, value));
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 脉冲宽度(μs)
        /// </summary>
        [Category("激光器参数"), DisplayName("脉冲宽度(μs)")]
        public virtual double PulseWidth
        {
            get => _pulseWidth;
            set
            {
                if (Math.Abs(_pulseWidth - value) > 0.001)
                {
                    _pulseWidth = Math.Max(1, Math.Min(10000, value));
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 是否启用激光器
        /// </summary>
        [Category("激光器状态"), DisplayName("激光器启用")]
        public virtual bool IsLaserEnabled
        {
            get => _isLaserEnabled;
            set
            {
                if (_isLaserEnabled != value)
                {
                    _isLaserEnabled = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 激光器类型
        /// </summary>
        [Category("激光器信息"), DisplayName("激光器类型")]
        public abstract LaserType LaserType { get; }

        /// <summary>
        /// 最大功率(W)
        /// </summary>
        [Category("激光器信息"), DisplayName("最大功率(W)")]
        public virtual double MaxPower { get;  set; } = 100;

        /// <summary>
        /// 最小功率(W)
        /// </summary>
        [Category("激光器信息"), DisplayName("最小功率(W)")]
        public abstract double MinPower { get; }

        /// <summary>
        /// 最大频率(Hz)
        /// </summary>
        [Category("激光器信息"), DisplayName("最大频率(Hz)")]
        public abstract double MaxFrequency { get; }

        /// <summary>
        /// 最小频率(Hz)
        /// </summary>
        [Category("激光器信息"), DisplayName("最小频率(Hz)")]
        public abstract double MinFrequency { get; }

        /// <summary>
        /// 激光器状态
        /// </summary>
        [Category("激光器状态"), DisplayName("激光器状态")]
        public abstract LaserStatus LaserStatus { get; }

        /// <summary>
        /// 激光器模式
        /// </summary>
        [Category("激光器参数"), DisplayName("激光器模式")]
        public virtual LaserMode LaserMode
        {
            get => _laserMode;
            set
            {
                if (_laserMode != value)
                {
                    _laserMode = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        protected LaserBase()
        {
            DeviceType = DeviceType.Laser;
        }

        #endregion

        #region 抽象方法

        /// <summary>
        /// 打开激光器
        /// </summary>
        /// <returns>是否成功</returns>
        public abstract bool LaserOn();

        /// <summary>
        /// 关闭激光器
        /// </summary>
        /// <returns>是否成功</returns>
        public abstract bool LaserOff();

        /// <summary>
        /// 设置激光器功率
        /// </summary>
        /// <param name="power">功率值(0-100%)</param>
        /// <returns>是否成功</returns>
        public abstract bool SetPower(double power);

        /// <summary>
        /// 获取激光器功率
        /// </summary>
        /// <param name="power">功率值</param>
        /// <returns>是否成功</returns>
        public abstract bool GetPower(ref double power);

        /// <summary>
        /// 设置激光器频率
        /// </summary>
        /// <param name="frequency">频率值(Hz)</param>
        /// <returns>是否成功</returns>
        public abstract bool SetFrequency(double frequency);

        /// <summary>
        /// 获取激光器频率
        /// </summary>
        /// <param name="frequency">频率值</param>
        /// <returns>是否成功</returns>
        public abstract bool GetFrequency(ref double frequency);

        /// <summary>
        /// 设置脉冲宽度
        /// </summary>
        /// <param name="pulseWidth">脉冲宽度(μs)</param>
        /// <returns>是否成功</returns>
        public abstract bool SetPulseWidth(double pulseWidth);

        /// <summary>
        /// 获取脉冲宽度
        /// </summary>
        /// <param name="pulseWidth">脉冲宽度</param>
        /// <returns>是否成功</returns>
        public abstract bool GetPulseWidth(ref double pulseWidth);

        /// <summary>
        /// 设置激光器模式
        /// </summary>
        /// <param name="mode">激光器模式</param>
        /// <returns>是否成功</returns>
        public abstract bool SetLaserMode(LaserMode mode);

        /// <summary>
        /// 获取激光器模式
        /// </summary>
        /// <param name="mode">激光器模式</param>
        /// <returns>是否成功</returns>
        public abstract bool GetLaserMode(ref LaserMode mode);

        /// <summary>
        /// 检查激光器是否准备就绪
        /// </summary>
        /// <returns>是否准备就绪</returns>
        public abstract bool IsReady();

        /// <summary>
        /// 检查激光器是否有错误
        /// </summary>
        /// <returns>是否有错误</returns>
        public abstract bool HasError();

        /// <summary>
        /// 获取激光器错误信息
        /// </summary>
        /// <returns>错误信息</returns>
        public abstract string GetErrorMessage();

        /// <summary>
        /// 清除激光器错误
        /// </summary>
        /// <returns>是否成功</returns>
        public abstract bool ClearError();

        /// <summary>
        /// 激光器自检
        /// </summary>
        /// <returns>自检结果</returns>
        public abstract bool SelfTest();

        /// <summary>
        /// 获取激光器温度
        /// </summary>
        /// <param name="temperature">温度值(℃)</param>
        /// <returns>是否成功</returns>
        public abstract bool GetTemperature(ref double temperature);

        /// <summary>
        /// 获取激光器工作时间
        /// </summary>
        /// <param name="workingHours">工作时间(小时)</param>
        /// <returns>是否成功</returns>
        public abstract bool GetWorkingHours(ref double workingHours);

        #endregion

        #region 事件

        /// <summary>
        /// 激光器状态变化事件
        /// </summary>
        public event EventHandler<LaserStatusChangedEventArgs> LaserStatusChanged;

        /// <summary>
        /// 激光器错误事件
        /// </summary>
        public event EventHandler<LaserErrorEventArgs> LaserError;

        /// <summary>
        /// 激光器功率变化事件
        /// </summary>
        public event EventHandler<LaserPowerChangedEventArgs> PowerChanged;

        #endregion

        #region 受保护方法

        /// <summary>
        /// 触发状态变化事件
        /// </summary>
        /// <param name="oldStatus">旧状态</param>
        /// <param name="newStatus">新状态</param>
        protected virtual void OnLaserStatusChanged(LaserStatus oldStatus, LaserStatus newStatus)
        {
            LaserStatusChanged?.Invoke(this, new LaserStatusChangedEventArgs(oldStatus, newStatus));
        }

        /// <summary>
        /// 触发错误事件
        /// </summary>
        /// <param name="errorCode">错误代码</param>
        /// <param name="errorMessage">错误信息</param>
        protected virtual void OnLaserError(int errorCode, string errorMessage)
        {
            LaserError?.Invoke(this, new LaserErrorEventArgs(errorCode, errorMessage));
        }

        /// <summary>
        /// 触发功率变化事件
        /// </summary>
        /// <param name="oldPower">旧功率值</param>
        /// <param name="newPower">新功率值</param>
        protected virtual void OnPowerChanged(double oldPower, double newPower)
        {
            PowerChanged?.Invoke(this, new LaserPowerChangedEventArgs(oldPower, newPower));
        }

        #endregion

        #region 虚方法

        /// <summary>
        /// 验证激光器参数
        /// </summary>
        /// <returns>验证结果</returns>
        public virtual bool ValidateParameters()
        {
            try
            {
                // 检查功率范围
                if (Power < 0 || Power > 100)
                {
                    System.Diagnostics.Debug.WriteLine($"激光器功率 {Power} 超出范围 (0-100%)");
                    return false;
                }

                // 检查频率范围
                if (Frequency < MinFrequency || Frequency > MaxFrequency)
                {
                    System.Diagnostics.Debug.WriteLine($"激光器频率 {Frequency} 超出范围 ({MinFrequency}-{MaxFrequency}Hz)");
                    return false;
                }

                // 检查脉冲宽度
                if (PulseWidth <= 0)
                {
                    System.Diagnostics.Debug.WriteLine($"激光器脉冲宽度必须大于0");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"验证激光器参数异常：{ex.Message}");
                return false;
            }
        }

        #endregion
    }
}
