﻿using HalconDotNet;
using McLaser.Core.Common;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace McLaser.Modules.Vision
{


    [Serializable]
    public class ROI : ObservableObject
    {
        public string Id;
        public string Name = "";
        public int TextSize = 20;
        public Color Color = Color.Red;
        public bool Visiable = true;

        [DllImport("user32.dll")]
        public static extern IntPtr GetDC(IntPtr hWnd);

        public virtual void CreateROI(List<double> ROIData) { }

        private RoiType type = RoiType.None;
        public RoiType Type
        {
            get { return type; }
            protected set { type = value; }
        }

        public RoiCursor Cursor = RoiCursor.Default;

        private int _ActiveHandleId = -1;
        public int ActiveHandleId
        {
            get { return _ActiveHandleId; }
            set { Set(ref _ActiveHandleId, value); }
        }
        public virtual bool IsSelected(double x, double y, double factor)
        {
            return false;
        }


        /// <summary>
        /// 造型
        /// </summary>
        /// <param name="x"></param>
        /// <param name="y"></param>
        public virtual void Shape(double x, double y)
        {

        }

        public bool Selected = false;
        public virtual HRegion GetRegion()
        {
            return null;
        }

    }


}
