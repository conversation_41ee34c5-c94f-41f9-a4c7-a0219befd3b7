using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using McLaser.Core.Common;
using McLaser.Device;
 
using Newtonsoft.Json;

namespace McLaser.Device
{
    /// <summary>
    /// 设备分类组
    /// 用于按设备类别对设备进行分组管理
    /// </summary>
    public class DeviceCategoryGroup : ObservableObject
    {
        /// <summary>
        /// 设备类别
        /// </summary>
        public DeviceCategory Category { get; }

        /// <summary>
        /// 类别名称（用于显示）
        /// </summary>
        public string CategoryName { get; set; }    

        /// <summary>
        /// 类别图标
        /// </summary>
        public string CategoryIcon => GetCategoryIcon(Category);

       
        public ObservableCollection<IDevice> Devices { get; } = new ObservableCollection<IDevice>();

        public List<DeviceItem> Items { get; set; } = new List<DeviceItem>();

        /// <summary>
        /// 当前选中的设备（不序列化）
        /// </summary>
        [JsonIgnore]
        private IDevice _selectedDevice;

        /// <summary>
        /// 当前选中的设备
        /// </summary>
        [JsonIgnore]
        public IDevice SelectedDevice
        {
            get => _selectedDevice;
            set
            {
                if (_selectedDevice != value)
                {
                    _selectedDevice = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 设备数量
        /// </summary>
        [JsonIgnore]
        public int DeviceCount => Devices.Count;

      
        public DeviceCategoryGroup(DeviceCategory category)
        {
            Category = category;
            
            // 监听设备集合变化，更新设备数量
            Devices.CollectionChanged += (s, e) => OnPropertyChanged(nameof(DeviceCount));
        }

        public DeviceCategoryGroup(string categoryName)
        {
            CategoryName = categoryName;

            // 监听设备集合变化，更新设备数量
            Devices.CollectionChanged += (s, e) => OnPropertyChanged(nameof(DeviceCount));
        }

        /// <summary>
        /// 获取设备类别的显示名称
        /// </summary>
        /// <param name="category">设备类别</param>
        /// <returns>显示名称</returns>
        private string GetCategoryDisplayName(DeviceCategory category)
        {
            switch (category)
            {
                case DeviceCategory.None:
                    return "未知设备";
                case DeviceCategory.Camera:
                    return "相机设备";
                case DeviceCategory.BarcodeReader:
                    return "条码读取器";
                case DeviceCategory.NetworkDevice:
                    return "网络设备";
                case DeviceCategory.SerialDevice:
                    return "串口设备";
                case DeviceCategory.MotionController:
                    return "运动控制器";
                case DeviceCategory.Laser:
                    return "激光器设备";
                case DeviceCategory.Sensor:
                    return "传感器设备";
                case DeviceCategory.Folder:
                    return "文件夹";
                default:
                    return category.ToString();
            }
        }

        /// <summary>
        /// 获取设备类别的图标
        /// </summary>
        /// <param name="category">设备类别</param>
        /// <returns>图标字符</returns>
        private string GetCategoryIcon(DeviceCategory category)
        {
            switch (category)
            {
                case DeviceCategory.None:
                    return "❓";
                case DeviceCategory.Camera:
                    return "📷";
                case DeviceCategory.BarcodeReader:
                    return "📊";
                case DeviceCategory.NetworkDevice:
                    return "🌐";
                case DeviceCategory.SerialDevice:
                    return "🔌";
                case DeviceCategory.MotionController:
                    return "🎛️";
                case DeviceCategory.Laser:
                    return "🔴";
                case DeviceCategory.Sensor:
                    return "📡";
                case DeviceCategory.Folder:
                    return "📁";
                default:
                    return "🔧";
            }
        }

        /// <summary>
        /// 添加设备到分组
        /// </summary>
        /// <param name="device">要添加的设备</param>
        public void AddDevice(IDevice device)
        {
            if (device != null && device.Category == Category && !Devices.Contains(device))
            {
                Devices.Add(device);
            }
        }

        /// <summary>
        /// 从分组中移除设备
        /// </summary>
        /// <param name="device">要移除的设备</param>
        /// <returns>是否成功移除</returns>
        public bool RemoveDevice(IDevice device)
        {
            if (device != null && Devices.Contains(device))
            {
                // 如果移除的是当前选中的设备，清空选择
                if (SelectedDevice == device)
                {
                    SelectedDevice = null;
                }
                
                return Devices.Remove(device);
            }
            return false;
        }

        /// <summary>
        /// 根据ID查找设备
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>找到的设备，如果没找到返回null</returns>
        public IDevice FindDeviceById(string deviceId)
        {
            if (string.IsNullOrEmpty(deviceId)) return null;
            
            foreach (var device in Devices)
            {
                if (device.Id == deviceId)
                    return device;
            }
            return null;
        }

        /// <summary>
        /// 根据名称查找设备
        /// </summary>
        /// <param name="deviceName">设备名称</param>
        /// <returns>找到的设备，如果没找到返回null</returns>
        public IDevice FindDeviceByName(string deviceName)
        {
            if (string.IsNullOrEmpty(deviceName)) return null;
            
            foreach (var device in Devices)
            {
                if (device.Name == deviceName)
                    return device;
            }
            return null;
        }

        /// <summary>
        /// 清空所有设备
        /// </summary>
        public void ClearDevices()
        {
            SelectedDevice = null;
            Devices.Clear();
        }

        /// <summary>
        /// 重写ToString方法
        /// </summary>
        /// <returns>分组信息字符串</returns>
        public override string ToString()
        {
            return $"{CategoryName} ({DeviceCount} 设备)";
        }
    }
}
