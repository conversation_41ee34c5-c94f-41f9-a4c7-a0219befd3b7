using System;
using System.ComponentModel;
using McLaser.Device;

namespace McLaser.Devices.Camera
{
    /// <summary>
    /// 大恒图像相机状态类
    /// 用于记录和监控大恒相机的运行状态信息
    /// </summary>
    [Serializable]
    public class StatusDaheng : StatusCamera
    {
        #region 私有字段

        private bool _isConnected = false;
        private bool _isStreaming = false;
        private double _currentExposure = 0;
        private double _currentGain = 0;
        private int _frameCount = 0;
        private DateTime _lastFrameTime = DateTime.MinValue;
        private string _deviceTemperature = "未知";
        private string _errorMessage = string.Empty;
        private string _pixelFormat = "未知";
        private double _frameRate = 0;

        #endregion

        #region 属性

        /// <summary>
        /// 设备是否已连接
        /// </summary>
        [Category("大恒状态"), DisplayName("设备连接状态")]
        public override bool IsConnected
        {
            get => _isConnected;
            set
            {
                if (_isConnected != value)
                {
                    _isConnected = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// 是否正在流传输
        /// </summary>
        [Category("大恒状态"), DisplayName("流传输状态")]
        public bool IsStreaming
        {
            get => _isStreaming;
            set
            {
                if (_isStreaming != value)
                {
                    _isStreaming = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// 当前曝光时间
        /// </summary>
        [Category("大恒状态"), DisplayName("当前曝光时间(μs)")]
        public double CurrentExposure
        {
            get => _currentExposure;
            set
            {
                if (Math.Abs(_currentExposure - value) > 0.001)
                {
                    _currentExposure = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 当前增益值
        /// </summary>
        [Category("大恒状态"), DisplayName("当前增益")]
        public double CurrentGain
        {
            get => _currentGain;
            set
            {
                if (Math.Abs(_currentGain - value) > 0.001)
                {
                    _currentGain = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 帧计数
        /// </summary>
        [Category("大恒状态"), DisplayName("帧计数")]
        public int FrameCount
        {
            get => _frameCount;
            set
            {
                if (_frameCount != value)
                {
                    _frameCount = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 最后一帧时间
        /// </summary>
        [Category("大恒状态"), DisplayName("最后帧时间")]
        public DateTime LastFrameTime
        {
            get => _lastFrameTime;
            set
            {
                if (_lastFrameTime != value)
                {
                    _lastFrameTime = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 设备温度
        /// </summary>
        [Category("大恒状态"), DisplayName("设备温度")]
        public string DeviceTemperature
        {
            get => _deviceTemperature;
            set
            {
                if (_deviceTemperature != value)
                {
                    _deviceTemperature = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 错误信息
        /// </summary>
        [Category("大恒状态"), DisplayName("错误信息")]
        public string ErrorMessage
        {
            get => _errorMessage;
            set
            {
                if (_errorMessage != value)
                {
                    _errorMessage = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        /// <summary>
        /// 像素格式
        /// </summary>
        [Category("大恒状态"), DisplayName("像素格式")]
        public string PixelFormat
        {
            get => _pixelFormat;
            set
            {
                if (_pixelFormat != value)
                {
                    _pixelFormat = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 帧率
        /// </summary>
        [Category("大恒状态"), DisplayName("帧率(fps)")]
        public double FrameRate
        {
            get => _frameRate;
            set
            {
                if (Math.Abs(_frameRate - value) > 0.001)
                {
                    _frameRate = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 状态文本描述
        /// </summary>
        [Category("大恒状态"), DisplayName("状态描述")]
        public override string StatusText
        {
            get
            {
                if (!string.IsNullOrEmpty(_errorMessage))
                    return $"错误: {_errorMessage}";

                if (!_isConnected)
                    return "设备未连接";

                if (_isStreaming)
                    return $"正在采集 - 帧数: {_frameCount} - 帧率: {_frameRate:F1}fps";

                return "设备已连接";
            }
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public StatusDaheng()
        {
            // 初始化状态
            Reset();
        }

        #endregion

        #region 方法

        /// <summary>
        /// 重置状态
        /// </summary>
        public override void Reset()
        {
            base.Reset();
            
            IsConnected = false;
            IsStreaming = false;
            CurrentExposure = 0;
            CurrentGain = 0;
            FrameCount = 0;
            LastFrameTime = DateTime.MinValue;
            DeviceTemperature = "未知";
            ErrorMessage = string.Empty;
            PixelFormat = "未知";
            FrameRate = 0;
        }

        /// <summary>
        /// 更新帧信息
        /// </summary>
        public void UpdateFrameInfo()
        {
            FrameCount++;
            var now = DateTime.Now;
            
            if (LastFrameTime != DateTime.MinValue)
            {
                var timeSpan = now - LastFrameTime;
                if (timeSpan.TotalSeconds > 0)
                {
                    FrameRate = 1.0 / timeSpan.TotalSeconds;
                }
            }
            
            LastFrameTime = now;
        }

        /// <summary>
        /// 设置错误信息
        /// </summary>
        /// <param name="error">错误信息</param>
        public void SetError(string error)
        {
            ErrorMessage = error;
        }

        /// <summary>
        /// 清除错误信息
        /// </summary>
        public void ClearError()
        {
            ErrorMessage = string.Empty;
        }

        /// <summary>
        /// 更新相机参数
        /// </summary>
        /// <param name="exposure">曝光时间</param>
        /// <param name="gain">增益</param>
        /// <param name="pixelFormat">像素格式</param>
        public void UpdateCameraParameters(double exposure, double gain, string pixelFormat = null)
        {
            CurrentExposure = exposure;
            CurrentGain = gain;
            if (!string.IsNullOrEmpty(pixelFormat))
            {
                PixelFormat = pixelFormat;
            }
        }

        /// <summary>
        /// 获取状态摘要
        /// </summary>
        /// <returns>状态摘要字符串</returns>
        public override string GetStatusSummary()
        {
            return $"大恒相机 - {StatusText} | 曝光: {CurrentExposure:F1}μs | 增益: {CurrentGain:F2} | 格式: {PixelFormat}";
        }

        #endregion
    }
}
