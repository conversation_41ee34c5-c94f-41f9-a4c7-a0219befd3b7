﻿++解决方案 'McLaser_V1' ‎ (9 个项目，共 9 个)
i:{00000000-0000-0000-0000-000000000000}:McLaser_V1.sln
++McLaser.Devices.Camera
i:{00000000-0000-0000-0000-000000000000}:McLaser.Devices.Camera
++Properties
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.camera\properties\
i:{f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c7}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.laser\properties\
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.motion\properties\
i:{a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.sensor\properties\
i:{a00492b3-5112-a102-2864-a101dd8c8ada}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.plugins.samples\properties\
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\properties\
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\properties\
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\properties\
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\properties\
++引用
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:
i:{f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c7}:
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:
i:{a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6}:
i:{a00492b3-5112-a102-2864-a101dd8c8ada}:
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:
++Basler
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.camera\basler\
++Daheng
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.camera\daheng\
++Hik
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.camera\hik\
++AssemblyInfo.cs
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.camera\properties\assemblyinfo.cs
i:{f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c7}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.laser\properties\assemblyinfo.cs
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.motion\properties\assemblyinfo.cs
i:{a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.sensor\properties\assemblyinfo.cs
i:{a00492b3-5112-a102-2864-a101dd8c8ada}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.plugins.samples\properties\assemblyinfo.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\properties\assemblyinfo.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\properties\assemblyinfo.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\properties\assemblyinfo.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\properties\assemblyinfo.cs
++Basler.Pylon
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:
++CommunityToolkit.Mvvm
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:
i:{f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c7}:
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:
i:{a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6}:
++GxIAPINET
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:
++halcondotnetxl
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:
++McLaser.Device
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:
i:{00000000-0000-0000-0000-000000000000}:McLaser.Device
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:
++McLaser.Modules.Vision
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:
i:{00000000-0000-0000-0000-000000000000}:McLaser.Modules.Vision
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:
++Microsoft.CSharp
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:
i:{f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c7}:
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:
i:{a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6}:
i:{a00492b3-5112-a102-2864-a101dd8c8ada}:
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:
++MvCameraControl.Net
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:
++Newtonsoft.Json
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:
i:{f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c7}:
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:
i:{a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6}:
i:{a00492b3-5112-a102-2864-a101dd8c8ada}:
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:
++PresentationCore
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:
i:{f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c7}:
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:
i:{a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6}:
i:{a00492b3-5112-a102-2864-a101dd8c8ada}:
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:
++PresentationFramework
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:
i:{f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c7}:
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:
i:{a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6}:
i:{a00492b3-5112-a102-2864-a101dd8c8ada}:
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:
++System
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:
i:{f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c7}:
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:
i:{a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6}:
i:{a00492b3-5112-a102-2864-a101dd8c8ada}:
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:
++System.ComponentModel.Composition
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:
i:{f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c7}:
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:
i:{a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6}:
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:
++System.Core
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:
i:{f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c7}:
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:
i:{a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6}:
i:{a00492b3-5112-a102-2864-a101dd8c8ada}:
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:
++System.Data
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:
i:{f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c7}:
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:
i:{a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6}:
i:{a00492b3-5112-a102-2864-a101dd8c8ada}:
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:
++System.Data.DataSetExtensions
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:
i:{f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c7}:
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:
i:{a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6}:
i:{a00492b3-5112-a102-2864-a101dd8c8ada}:
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:
++System.Net.Http
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:
i:{f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c7}:
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:
i:{a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6}:
i:{a00492b3-5112-a102-2864-a101dd8c8ada}:
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:
++System.Windows.Forms
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:
i:{f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c7}:
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:
i:{a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6}:
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:
++System.Xaml
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:
i:{f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c7}:
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:
i:{a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6}:
i:{a00492b3-5112-a102-2864-a101dd8c8ada}:
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:
++System.Xml
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:
i:{f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c7}:
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:
i:{a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6}:
i:{a00492b3-5112-a102-2864-a101dd8c8ada}:
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:
++System.Xml.Linq
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:
i:{f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c7}:
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:
i:{a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6}:
i:{a00492b3-5112-a102-2864-a101dd8c8ada}:
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:
++WindowsBase
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:
i:{f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c7}:
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:
i:{a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6}:
i:{a00492b3-5112-a102-2864-a101dd8c8ada}:
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:
++CameraBasler.cs
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.camera\basler\camerabasler.cs
++StatusBasler.cs
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.camera\basler\statusbasler.cs
++CameraDaheng.cs
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.camera\daheng\cameradaheng.cs
++StatusDaheng.cs
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.camera\daheng\statusdaheng.cs
++CameraHIK.cs
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.camera\hik\camerahik.cs
++StatusHIK.cs
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.camera\hik\statushik.cs
++McLaser.Devices.Laser
i:{00000000-0000-0000-0000-000000000000}:McLaser.Devices.Laser
++Coherent
i:{f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c7}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.laser\coherent\
++IPG
i:{f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c7}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.laser\ipg\
++LaserBase.cs
i:{f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c7}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.laser\laserbase.cs
++StatusLaser.cs
i:{f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c7}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.laser\statuslaser.cs
++LaserCoherent.cs
i:{f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c7}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.laser\coherent\lasercoherent.cs
++StatusCoherent.cs
i:{f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c7}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.laser\coherent\statuscoherent.cs
++LaserIPG.cs
i:{f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c7}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.laser\ipg\laseripg.cs
++StatusIPG.cs
i:{f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c7}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.laser\ipg\statusipg.cs
++McLaser.Devices.Sensor
i:{00000000-0000-0000-0000-000000000000}:McLaser.Devices.Sensor
++Displacement
i:{a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.sensor\displacement\
++Temperature
i:{a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.sensor\temperature\
++SensorBase.cs
i:{a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.sensor\sensorbase.cs
++StatusSensor.cs
i:{a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.sensor\statussensor.cs
++SensorDisplacement.cs
i:{a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.sensor\displacement\sensordisplacement.cs
++StatusDisplacement.cs
i:{a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.sensor\displacement\statusdisplacement.cs
++SensorTemperature.cs
i:{a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.sensor\temperature\sensortemperature.cs
++StatusTemperature.cs
i:{a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.sensor\temperature\statustemperature.cs
++McLaser.Plugins.Samples
i:{00000000-0000-0000-0000-000000000000}:McLaser.Plugins.Samples
++CalculatorPlugin.cs
i:{a00492b3-5112-a102-2864-a101dd8c8ada}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.plugins.samples\calculatorplugin.cs
++LoggerPlugin.cs
i:{a00492b3-5112-a102-2864-a101dd8c8ada}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.plugins.samples\loggerplugin.cs
++packages.config
i:{a00492b3-5112-a102-2864-a101dd8c8ada}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.plugins.samples\packages.config
++PluginBase.cs
i:{a00492b3-5112-a102-2864-a101dd8c8ada}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.plugins.samples\pluginbase.cs
++PluginMetadataAttribute.cs
i:{a00492b3-5112-a102-2864-a101dd8c8ada}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.plugins.samples\pluginmetadataattribute.cs
++StatusBarPlugin.cs
i:{a00492b3-5112-a102-2864-a101dd8c8ada}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.plugins.samples\statusbarplugin.cs
++System.Drawing
i:{a00492b3-5112-a102-2864-a101dd8c8ada}:
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:
++McLaser.Core
i:{f2b3c4d5-e6f7-a8b9-c0d1-e2f3a4b5c6d7}:
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:
i:{00000000-0000-0000-0000-000000000000}:McLaser.Core
++System.ComponentModel.DataAnnotations
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:
++Common
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\common\
++Communication
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\communication\
++Configuration
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\configuration\
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\configuration\
++EventBus
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\eventbus\
++ExceptionHandling
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\exceptionhandling\
++Framework
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\
++Plugins
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\plugins\
++AsyncLock.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\common\asynclock.cs
++AsyncRelayCommand.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\common\asyncrelaycommand.cs
++DisposableBase.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\common\disposablebase.cs
++JsonHelper.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\common\jsonhelper.cs
++NotifyPropertyBase.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\common\notifypropertybase.cs
++RelayCommand.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\common\relaycommand.cs
++RetryHelper.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\common\retryhelper.cs
++TypeHelper.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\common\typehelper.cs
++ViewModelBase.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\common\viewmodelbase.cs
++CommunicationModels.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\communication\communicationmodels.cs
++ICommunicationChannel.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\communication\icommunicationchannel.cs
++IMessageProtocol.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\communication\imessageprotocol.cs
++MessageProtocolBase.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\communication\messageprotocolbase.cs
++SerialPortChannel.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\communication\serialportchannel.cs
++TcpCommunicationChannel.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\communication\tcpcommunicationchannel.cs
++UdpCommunicationChannel.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\communication\udpcommunicationchannel.cs
++ConfigurationManager.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\configuration\configurationmanager.cs
++ConfigurationModels.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\configuration\configurationmodels.cs
++IConfigurationManager.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\configuration\iconfigurationmanager.cs
++IConfigurationProvider.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\configuration\iconfigurationprovider.cs
++JsonConfigurationProvider.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\configuration\jsonconfigurationprovider.cs
++EventBus.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\eventbus\eventbus.cs
++EventBusModels.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\eventbus\eventbusmodels.cs
++IEventBus.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\eventbus\ieventbus.cs
++ExceptionModels.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\exceptionhandling\exceptionmodels.cs
++ExceptionPolicy.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\exceptionhandling\exceptionpolicy.cs
++GlobalExceptionHandler.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\exceptionhandling\globalexceptionhandler.cs
++IExceptionHandler.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\exceptionhandling\iexceptionhandler.cs
++Bootstrapper
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\bootstrapper\
++Caching
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\caching\
++Container
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\container\
++Data
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\data\
++IO
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\io\
++Logging
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\logging\
++Network
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\network\
++Performance
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\performance\
++Security
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\security\
++Serialization
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\serialization\
++Services
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\services\
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\services\
++Styles
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\styles\
++UI
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\ui\
++Validation
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\validation\
++Documentation
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\plugins\documentation\
++DataAnalyzers.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\plugins\dataanalyzers.cs
++IPlugin.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\plugins\iplugin.cs
++IPluginManager.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\plugins\ipluginmanager.cs
++PluginInterfaces.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\plugins\plugininterfaces.cs
++PluginLoader.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\plugins\pluginloader.cs
++PluginManager.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\plugins\pluginmanager.cs
++PluginModels.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\plugins\pluginmodels.cs
++ApplicationBase.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\applicationbase\applicationbase.cs
++ApplicationCoreBase.cs
++ApplicationInfo.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\bootstrapper\applicationinfo.cs
++DefaultServiceRegistry.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\bootstrapper\defaultserviceregistry.cs
++IApplicationBuilder.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\bootstrapper\iapplicationbuilder.cs
++IApplicationCore.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\bootstrapper\iapplicationcore.cs
++IConfigureableApplication.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\bootstrapper\iconfigureableapplication.cs
++IServiceProvider.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\bootstrapper\iserviceprovider.cs
++IServiceRegistry.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\bootstrapper\iserviceregistry.cs
++ICacheManager.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\caching\icachemanager.cs
++DefaultConfigurationService.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\configuration\defaultconfigurationservice.cs
++IConfigurationService.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\configuration\iconfigurationservice.cs
++ContainerManager.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\container\containermanager.cs
++ContainerServiceProviderAdapter.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\container\containerserviceprovideradapter.cs
++ContainerTests.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\container\containertests.cs
++DefaultContainerAdapter.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\container\defaultcontaineradapter.cs
++IContainer.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\container\icontainer.cs
++IoC.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\container\ioc.cs
++MefContainerAdapter.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\container\mefcontaineradapter.cs
++ServiceLocator.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\container\servicelocator.cs
++ServiceRegistration.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\container\serviceregistration.cs
++IRepository.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\data\irepository.cs
++IUnitOfWork.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\data\iunitofwork.cs
++AppPaths.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\io\apppaths.cs
++AppPathService.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\io\apppathservice.cs
++IAppPathServce.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\io\iapppathservce.cs
++IFileSystemService.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\io\ifilesystemservice.cs
++DefaultLogger.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\logging\defaultlogger.cs
++ILogger.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\logging\ilogger.cs
++ILoggerFactory.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\logging\iloggerfactory.cs
++LoggerExtensions.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\logging\loggerextensions.cs
++LogLevel.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\logging\loglevel.cs
++IHttpClientService.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\network\ihttpclientservice.cs
++IPerformanceCounter.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\performance\iperformancecounter.cs
++DefaultPermissionService.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\security\defaultpermissionservice.cs
++DefaultRoleService.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\security\defaultroleservice.cs
++DefaultUserService.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\security\defaultuserservice.cs
++IPermissionService.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\security\ipermissionservice.cs
++IRoleService.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\security\iroleservice.cs
++IUserService.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\security\iuserservice.cs
++SecurityEventArgs.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\security\securityeventargs.cs
++SecurityModels.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\security\securitymodels.cs
++DefaultJsonService.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\serialization\defaultjsonservice.cs
++IJsonService.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\serialization\ijsonservice.cs
++DefaultDialogService.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\services\defaultdialogservice.cs
++DefaultExceptionHandlingService.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\services\defaultexceptionhandlingservice.cs
++DefaultNavigationService.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\services\defaultnavigationservice.cs
++IDialogService.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\services\idialogservice.cs
++IExceptionHandlingService.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\services\iexceptionhandlingservice.cs
++INavigationService.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\services\inavigationservice.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\services\inavigationservice.cs
++DefaultStyleService.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\styles\defaultstyleservice.cs
++IStyleService.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\styles\istyleservice.cs
++BindingProxy.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\ui\bindingproxy.cs
++IThemeService.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\ui\ithemeservice.cs
++IWindowManager.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\ui\iwindowmanager.cs
++ThemeManager.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\ui\thememanager.cs
++WindowManager.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\ui\windowmanager.cs
++IValidationRule.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\validation\ivalidationrule.cs
++ValidationEngine.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\framework\validation\validationengine.cs
++插件使用示例.md
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\plugins\documentation\插件使用示例.md
++插件系统总结.md
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\plugins\documentation\插件系统总结.md
++插件应用场景说明.md
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\plugins\documentation\插件应用场景说明.md
++兼容现有架构的插件使用指南.md
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\plugins\documentation\兼容现有架构的插件使用指南.md
++Base
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\base\
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\base\
++RenderControl
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\rendercontrol\
++Fit.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\fit.cs
++Gen.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\gen.cs
++VisionLib.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\visionlib.cs
++System.Windows.Presentation
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:
++WindowsFormsIntegration
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:
++Circle.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\base\circle.cs
++EnumType.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\base\enumtype.cs
++HRoi.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\base\hroi.cs
++HText.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\base\htext.cs
++Line.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\base\line.cs
++MV_ERR.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\base\mv_err.cs
++Rectangle.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\base\rectangle.cs
++Rectangle2.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\base\rectangle2.cs
++ROI.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\base\roi.cs
++ROICoordinates.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\base\roicoordinates.cs
++RoiCursor.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\base\roicursor.cs
++ROILine.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\base\roiline.cs
++RoiType.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\base\roitype.cs
++VisionException.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\base\visionexception.cs
++VisionInfo.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\base\visioninfo.cs
++VisionParam.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\base\visionparam.cs
++VisionResult.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\base\visionresult.cs
++IDrawable.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\rendercontrol\idrawable.cs
++IRenderable.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\rendercontrol\irenderable.cs
++IRenderView.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\rendercontrol\irenderview.cs
++IRenderViewGroupEx.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\rendercontrol\irenderviewgroupex.cs
++IRenderViewManager.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\rendercontrol\irenderviewmanager.cs
++RenderView.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\rendercontrol\renderview.cs
++RenderViewManager.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\rendercontrol\renderviewmanager.cs
++RenderViewWpf.xaml
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\rendercontrol\renderviewwpf.xaml
++VisionView.xaml
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\rendercontrol\visionview.xaml
++RenderView.Designer.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\rendercontrol\renderview.designer.cs
++RenderView.resx
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\rendercontrol\renderview.resx
++RenderViewWpf.xaml.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\rendercontrol\renderviewwpf.xaml.cs
++VisionView.xaml.cs
i:{5f8a19fd-8e20-4c7d-916e-477d81850b3d}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.modules.visionlib\rendercontrol\visionview.xaml.cs
++McLaser.App
i:{00000000-0000-0000-0000-000000000000}:McLaser.App
++Controls
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\controls\
++Core
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\core\
++docs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\docs\
++Events
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\events\
++Models
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\models\
++Pages
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\pages\
++Themes
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\themes\
++ViewModels
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\devicemanager\viewmodels\
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\viewmodels\
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\applicationbase\viewmodels\
++Views
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.motion\pmac\views\
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\devicemanager\views\
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\views\
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\applicationbase\views\
++App.config
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\app.config
++App.xaml
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\app.xaml
++Program.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\program.cs
++Resources.resx
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\properties\resources.resx
++Settings.settings
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\properties\settings.settings
++Microsoft.Xaml.Behaviors
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:
++BottomNavigationBar.xaml
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\controls\bottomnavigationbar.xaml
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\applicationbase\views\bottomnavigationbar.xaml
++CategoryPopup.xaml
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\controls\categorypopup.xaml
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\applicationbase\views\categorypopup.xaml
++NavigationButton.xaml
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\controls\navigationbutton.xaml
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\applicationbase\views\navigationbutton.xaml
++AppCore.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\core\appcore.cs
++ConsoleLogger.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\core\consolelogger.cs
++SimpleExceptionHandler.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\core\simpleexceptionhandler.cs
++README.md
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\docs\readme.md
++按钮功能测试指南.md
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\docs\按钮功能测试指南.md
++导航视觉指示优化总结.md
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\docs\导航视觉指示优化总结.md
++导航问题修复说明.md
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\docs\导航问题修复说明.md
++导航问题修复完成总结.md
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\docs\导航问题修复完成总结.md
++导航优化和进程退出修复总结.md
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\docs\导航优化和进程退出修复总结.md
++底部导航栏实现总结.md
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\docs\底部导航栏实现总结.md
++底部导航栏使用指南.md
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\docs\底部导航栏使用指南.md
++底部导航栏图标显示修复总结.md
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\docs\底部导航栏图标显示修复总结.md
++功能演示指南.md
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\docs\功能演示指南.md
++上拉框自动收起功能实现总结.md
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\docs\上拉框自动收起功能实现总结.md
++设备管理器页面集成总结.md
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\docs\设备管理器页面集成总结.md
++使用指南.md
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\docs\使用指南.md
++视觉指示功能测试指南.md
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\docs\视觉指示功能测试指南.md
++项目说明.md
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\docs\项目说明.md
++修复报告.md
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\docs\修复报告.md
++最终修复验证.md
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\docs\最终修复验证.md
++DeviceStatusChangedEvent.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\events\devicestatuschangedevent.cs
++ExceptionEvent.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\events\exceptionevent.cs
++IEvent.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\events\ievent.cs
++SystemNotificationEvent.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\events\systemnotificationevent.cs
++UserActionEvent.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\events\useractionevent.cs
++ExceptionLogEntry.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\models\exceptionlogentry.cs
++HandlerInfo.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\models\handlerinfo.cs
++NavigationCategory.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\models\navigationcategory.cs
++NavigationItem.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\models\navigationitem.cs
++PageInfo.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\models\pageinfo.cs
++PluginDisplayInfo.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\models\plugindisplayinfo.cs
++HomePage.xaml
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\pages\homepage.xaml
++NavigationService.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\services\navigationservice.cs
++DarkTheme.xaml
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\themes\darktheme.xaml
++LightTheme.xaml
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\themes\lighttheme.xaml
++DataInputViewModel.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\viewmodels\datainputviewmodel.cs
++EventBusDemoViewModel.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\viewmodels\eventbusdemoviewmodel.cs
++ExceptionDemoViewModel.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\viewmodels\exceptiondemoviewmodel.cs
++HomePageViewModel.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\viewmodels\homepageviewmodel.cs
++MainViewModel.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\viewmodels\mainviewmodel.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\applicationbase\viewmodels\mainviewmodel.cs
++ModuleDemoViewModel.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\viewmodels\moduledemoviewmodel.cs
++NavigationItemViewModel.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\viewmodels\navigationitemviewmodel.cs
++NavigationViewModel.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\viewmodels\navigationviewmodel.cs
++PluginDemoViewModel.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\viewmodels\plugindemoviewmodel.cs
++SettingsViewModel.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\viewmodels\settingsviewmodel.cs
++DataInputWindow.xaml
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\views\datainputwindow.xaml
++EventBusDemoWindow.xaml
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\views\eventbusdemowindow.xaml
++ExceptionDemoWindow.xaml
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\views\exceptiondemowindow.xaml
++MainWindow.xaml
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\views\mainwindow.xaml
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\applicationbase\views\mainwindow.xaml
++ModuleDemoWindow.xaml
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\views\moduledemowindow.xaml
++PluginDemoWindow.xaml
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\views\plugindemowindow.xaml
++SettingsWindow.xaml
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\views\settingswindow.xaml
++App.xaml.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\app.xaml.cs
++Resources.Designer.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\properties\resources.designer.cs
++Settings.Designer.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\properties\settings.designer.cs
++BottomNavigationBar.xaml.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\controls\bottomnavigationbar.xaml.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\applicationbase\views\bottomnavigationbar.xaml.cs
++CategoryPopup.xaml.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\controls\categorypopup.xaml.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\applicationbase\views\categorypopup.xaml.cs
++NavigationButton.xaml.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\controls\navigationbutton.xaml.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\applicationbase\views\navigationbutton.xaml.cs
++HomePage.xaml.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\pages\homepage.xaml.cs
++DataInputWindow.xaml.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\views\datainputwindow.xaml.cs
++EventBusDemoWindow.xaml.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\views\eventbusdemowindow.xaml.cs
++ExceptionDemoWindow.xaml.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\views\exceptiondemowindow.xaml.cs
++MainWindow.xaml.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\views\mainwindow.xaml.cs
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\applicationbase\views\mainwindow.xaml.cs
++ModuleDemoWindow.xaml.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\views\moduledemowindow.xaml.cs
++PluginDemoWindow.xaml.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\views\plugindemowindow.xaml.cs
++SettingsWindow.xaml.cs
i:{a271967e-8208-422d-9a80-da32c4b6e6ec}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.app\views\settingswindow.xaml.cs
++McLaser.Devices.Motion
i:{00000000-0000-0000-0000-000000000000}:McLaser.Devices.Motion
++GTS
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.motion\gts\
++Pmac
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.motion\pmac\
++Virtual
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.motion\virtual\
++ODT.PMACGlobal
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:
++ODT.PowerPmacComLib
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:
++AxisPmac.cs
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.motion\pmac\axispmac.cs
++AxisPmacStatus.cs
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.motion\pmac\axispmacstatus.cs
++CardCoordinate.cs
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.motion\pmac\cardcoordinate.cs
++CardPmac.cs
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.motion\pmac\cardpmac.cs
++CoordinateStatus.cs
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.motion\pmac\coordinatestatus.cs
++IOPmac.cs
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.motion\pmac\iopmac.cs
++LimitPmac.cs
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.motion\pmac\limitpmac.cs
++StatusPmac.cs
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.motion\pmac\statuspmac.cs
++AxisVirtual.cs
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.motion\virtual\axisvirtual.cs
++CardVirtual.cs
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.motion\virtual\cardvirtual.cs
++IOVirtual.cs
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.motion\virtual\iovirtual.cs
++PmacConfigControl.xaml
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.motion\pmac\views\pmacconfigcontrol.xaml
++PmacConfigControl.xaml.cs
i:{a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.devices.motion\pmac\views\pmacconfigcontrol.xaml.cs
++Converters
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\converters\
++DeviceManager
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\devicemanager\
++ConfigBase.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\configbase.cs
++DeviceBase.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\devicebase.cs
++DeviceType.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\devicetype.cs
++IDevice.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\idevice.cs
++IDeviceConfiguration.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\ideviceconfiguration.cs
++ILaser.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\ilaser.cs
++ISensor.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\isensor.cs
++StatusBase.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\statusbase.cs
++Camera
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\base\camera\
++Motion
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\base\motion\
++BoolToColorConverter.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\converters\booltocolorconverter.cs
++BoolToForegroundConverter.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\converters\booltoforegroundconverter.cs
++BoolToRotateTransformConverter.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\converters\booltorotatetransformconverter.cs
++BoolToTextConverter.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\converters\booltotextconverter.cs
++BoolToVisibilityConverter.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\converters\booltovisibilityconverter.cs
++NullToVisibilityConverter.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\converters\nulltovisibilityconverter.cs
++DeviceCategoryGroup.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\devicemanager\devicecategorygroup.cs
++DeviceFactory.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\devicemanager\devicefactory.cs
++DeviceItemAttribute.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\devicemanager\deviceitemattribute.cs
++DeviceManager.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\devicemanager\devicemanager.cs
++DeviceTypeMetadata.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\devicemanager\devicetypemetadata.cs
++IDeviceManager.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\devicemanager\idevicemanager.cs
++BoolToInverseConverter.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\base\camera\booltoinverseconverter.cs
++CameraBase.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\base\camera\camerabase.cs
++CameraEnums.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\base\camera\cameraenums.cs
++CameraInfo.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\base\camera\camerainfo.cs
++CameraView.xaml
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\base\camera\cameraview.xaml
++CameraViewModel.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\base\camera\cameraviewmodel.cs
++ICamera.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\base\camera\icamera.cs
++ImageData.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\base\camera\imagedata.cs
++StatusCamera.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\base\camera\statuscamera.cs
++TrigMode.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\base\camera\trigmode.cs
++AxisBase.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\base\motion\axisbase.cs
++CardBase.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\base\motion\cardbase.cs
++CardView.xaml
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\base\motion\cardview.xaml
++CardViewModel.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\base\motion\cardviewmodel.cs
++IAxis.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\base\motion\iaxis.cs
++ICard.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\base\motion\icard.cs
++ICardConfigControl.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\base\motion\icardconfigcontrol.cs
++IOBase.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\base\motion\iobase.cs
++LimitBase.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\base\motion\limitbase.cs
++MotionEnums.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\base\motion\motionenums.cs
++StatusAxis.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\base\motion\statusaxis.cs
++UnitStatus.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\base\motion\unitstatus.cs
++DeviceManagerViewModel.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\devicemanager\viewmodels\devicemanagerviewmodel.cs
++DeviceTypeCategoryViewModel.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\devicemanager\viewmodels\devicetypecategoryviewmodel.cs
++DeviceTypeItemViewModel.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\devicemanager\viewmodels\devicetypeitemviewmodel.cs
++DeviceManagerControl.xaml
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\devicemanager\views\devicemanagercontrol.xaml
++CameraView.xaml.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\base\camera\cameraview.xaml.cs
++CardView.xaml.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\base\motion\cardview.xaml.cs
++DeviceManagerControl.xaml.cs
i:{f87277ce-d77d-47e0-b602-335625f0dba3}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.device\devicemanager\views\devicemanagercontrol.xaml.cs
++McLaser.Core (已卸载)
++解决方案 'McLaser_V1' ‎ (8 个项目，共 9 个)
++NewFolder1
++ApplicationBase
i:{b67b75f4-b65b-4fb4-9e9b-f86d2aa29b38}:c:\users\<USER>\desktop\柔性钙钛矿\gtk\mclaser_v1\mclaser.core\applicationbase\
