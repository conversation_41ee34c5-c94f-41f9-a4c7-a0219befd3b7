﻿using HalconDotNet;
using McLaser.Core.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace McLaser.Modules.Vision
{
    [Serializable]
    public class PointXY
    {
        public double X;
        public double Y;

        public PointXY() { }
        public PointXY(double x, double y)
        {
            X = x;
            Y = y;
        }
    }
    //定位结果
    [Serializable]
    public class MatchModelResult
    {
        // 匹配个数
        public int MatchNum;
        // 匹配点坐标
        public List<PointXY> MatchPoint;
        // 转换坐标系后的匹配点坐标
        public List<PointXY> RealPoint;
        // 匹配角度
        public List<double> MatchScore;
        // 匹配分数
        public List<double> MatchPhi;
        // 匹配轮廓
        public HXLDCont MatchOutline;
        // 匹配点
        public HObject Cross;

        public MatchModelResult()
        {
            MatchNum = 0;
            MatchPoint = new List<PointXY>();
            RealPoint = new List<PointXY>();
            MatchScore = new List<double>();
            MatchPhi = new List<double>();
        }

        public MatchModelResult(List<PointXY> points, List<double> angle)
        {
            MatchNum = points.Count;
            MatchPoint = points;
            MatchPhi = angle;
        }

        public MatchModelResult(int matchNum, List<PointXY> matchPoint, List<PointXY> realPoint, List<double> matchScore, List<double> matchPhi, HXLDCont matchOutline, HObject cross)
        {
            MatchNum = matchNum;
            MatchPoint = matchPoint;
            RealPoint = realPoint;
            MatchScore = matchScore;
            MatchPhi = matchPhi;
            MatchOutline = matchOutline;
            Cross = cross;
        }
    }

    //N点标定结果
    [Serializable]
    public class NPointCalibResult : ObservableObject
    {
        // 平移X
        public double TranslateX { get; set; }
        // 平移Y
        public double TranslateY { get; set; }
        // 像素当量X
        public double PixelX { get; set; }
        // 像素当量Y
        public double PixelY { get; set; }
        // 旋转角度
        public double RotationAngle { get; set; }
        // 倾斜角度
        public double TiltAngle { get; set; }
        // RMS误差
        public double CalibRms { get; set; }
        // 旋转中心X
        public double RotateCenterX { get; set; }
        // 旋转中心Y
        public double RotateCenterY { get; set; }

        /// 保存标定结果
        /// <param name="translateX"></param>
        /// <param name="translateY"></param>
        /// <param name="pixelX"></param>
        /// <param name="pixelY"></param>
        /// <param name="rotationAngle"></param>
        /// <param name="tiltAngle"></param>
        /// <param name="calibRms"></param>
        /// <param name="rotateCenterX"></param>
        /// <param name="rotateCenterY"></param>
        public NPointCalibResult(double translateX, double translateY, double pixelX, double pixelY, double rotationAngle, double tiltAngle, double calibRms, double rotateCenterX, double rotateCenterY)
        {
            TranslateX = translateX;
            TranslateY = translateY;
            PixelX = pixelX;
            PixelY = pixelY;
            RotationAngle = rotationAngle;
            TiltAngle = tiltAngle;
            CalibRms = calibRms;
            RotateCenterX = rotateCenterX;
            RotateCenterY = rotateCenterY;
        }

        public NPointCalibResult()
        {
        }
    }
    //相机标定结果
    [Serializable]
    public class CameraCalResult : ObservableObject
    {
        // 索引
        private double _ID;
        public double ID
        {
            get { return _ID; }
            set { Set(ref _ID, value); }
        }

        // 尺度
        private double _Scale;
        public double Scale
        {
            get { return _Scale; }
            set { Set(ref _Scale, value); }
        }

        // 像素精度
        private double _PixelPrecision;
        public double PixelPrecision
        {
            get { return _PixelPrecision; }
            set { Set(ref _PixelPrecision, value); }
        }

        // 标定个数
        private double _CalNum;
        public double CalNum
        {
            get { return _CalNum; }
            set { Set(ref _CalNum, value); }
        }

        // 平移X
        private double _TranslateX;
        public double TranslateX
        {
            get { return _TranslateX; }
            set { Set(ref _TranslateX, value); }
        }

        // 平移Y
        private double _TranslateY;
        public double TranslateY
        {
            get { return _TranslateY; }
            set { Set(ref _TranslateY, value); }
        }

        // 旋转角度
        private double _RotationAngle;
        public double RotationAngle
        {
            get { return _RotationAngle; }
            set { Set(ref _RotationAngle, value); }
        }

        // 倾斜角度
        private double _TiltAngle;
        public double TiltAngle
        {
            get { return _TiltAngle; }
            set { Set(ref _TiltAngle, value); }
        }

        // RMS误差
        private double _CalibRms;
        public double CalibRms
        {
            get { return _CalibRms; }
            set { Set(ref _CalibRms, value); }
        }

        public CameraCalResult()
        {

        }

        public CameraCalResult(double iD, double scale, double pixelPrecision, double calNum, double translateX, double translateY, double rotationAngle, double tiltAngle, double calibRms)
        {
            ID = iD;
            Scale = scale;
            PixelPrecision = pixelPrecision;
            CalNum = calNum;
            TranslateX = translateX;
            TranslateY = translateY;
            RotationAngle = rotationAngle;
            TiltAngle = tiltAngle;
            CalibRms = calibRms;
        }

    }
    //一维测量查找点的结果
    [Serializable]
    public class MeasurePosResult
    {
        public HTuple Rows { get; set; } = new HTuple();
        public HTuple Columns { get; set; } = new HTuple();
        public List<double> RowEdge { get; set; }
        public List<double> ColumnEdge { get; set; }
        //找到的点与图像中心点的距离
        public List<PointXY> Distance { get; set; }
        public MeasurePosResult(List<double> rowEdge, List<double> columnEdge, List<PointXY> distance)
        {
            RowEdge = rowEdge;
            ColumnEdge = columnEdge;
            Distance = distance;
        }

        public MeasurePosResult(List<double> rowEdge, List<double> columnEdge, List<PointXY> distance, HTuple rows, HTuple cols)
        {
            RowEdge = rowEdge;
            ColumnEdge = columnEdge;
            Distance = distance;
            Rows = rows;
            Columns = cols;
        }
        public MeasurePosResult() { }
    }
    //一维测量查找
    [Serializable]
    public class MeasurePairResult
    {
        //第一条线的起点和终点
        public List<PointXY> FirstLineStarts { get; set; }
        public List<PointXY> FirstLineEnds { get; set; }
        //第二条线的起点和终点
        public List<PointXY> SecondLineStarts { get; set; }
        public List<PointXY> SecondLineEnds { get; set; }

        //线宽和线内间距
        public List<double> IntraDistance { get; set; }
        public List<double> InterDistance { get; set; }
        public HXLDCont HXLDCont { get; set; }
        public HXLDCont MidLinehXLD { get; set; }
        public MeasurePairResult(List<PointXY> firstLineStarts, List<PointXY> firstLineEnds, List<PointXY> secondLineStarts, List<PointXY> secondLineEnds, List<double> intraDistance, List<double> interDistance, HXLDCont hXLDCont, HXLDCont midLinehXLD)
        {
            FirstLineStarts = firstLineStarts;
            FirstLineEnds = firstLineEnds;
            SecondLineStarts = secondLineStarts;
            SecondLineEnds = secondLineEnds;
            IntraDistance = intraDistance;
            InterDistance = interDistance;
            HXLDCont = hXLDCont;
            MidLinehXLD = midLinehXLD;
        }
        public MeasurePairResult() { }
    }
    //二维测量结果
    [Serializable]
    public class MetrologyResult
    {
        public List<PointXY> MetrologyPoints { get; set; } = new List<PointXY>();
        public MetrologyType MetrologyType { get; set; } = MetrologyType.直线;
        public Type ResultType { get; set; } = typeof(Line);
        public object Result { get; set; }
        public HObject ResultRegion { get; set; }
        public MetrologyResult() { }

        public MetrologyResult(MetrologyType metrologyType, Type resultType, object result)
        {
            MetrologyType = metrologyType;
            ResultType = resultType;
            Result = result;
        }

        public MetrologyResult(List<PointXY> metrologyPoints, MetrologyType metrologyType, Type resultType, object result, HObject resultRegion)
        {
            MetrologyPoints = metrologyPoints;
            MetrologyType = metrologyType;
            ResultType = resultType;
            Result = result;
            ResultRegion = resultRegion;
        }
    }
    [Serializable]
    public class FeatureResult : ObservableObject
    {
        public double Area { get; set; }
        public double Row { get; set; }
        public double Column { get; set; }
        public double Width { get; set; }
        public double Height { get; set; }
        public double Orientation { get; set; }
        public double Rect2Phi { get; set; }
        public double Rect2Len1 { get; set; }
        public double Rect2Len2 { get; set; }
        public double Circularity { get; set; }
        public double Compactness { get; set; }
        public double Rectangularity { get; set; }
        public FeatureResult() { }

        public FeatureResult(double area, double row, double column, double width, double height, double orientation, double rect2Phi, double rect2Len1, double rect2Len2, double circularity, double compactness, double rectangularity)
        {
            Area = area;
            Row = row;
            Column = column;
            Width = width;
            Height = height;
            Orientation = orientation;
            Rect2Phi = rect2Phi;
            Rect2Len1 = rect2Len1;
            Rect2Len2 = rect2Len2;
            Circularity = circularity;
            Compactness = compactness;
            Rectangularity = rectangularity;
        }
    }
    //深度学习异常值检测结果
    [Serializable]
    public class DeepStudyResult : ObservableObject
    {
        public string Result { get; set; } = "nok";
        public int ClassID { get; set; }
        public double DetectionScore { get; set; }
    }
}
