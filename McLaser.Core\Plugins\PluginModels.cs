using System;
using System.Collections.Generic;

namespace McLaser.Core.Plugins
{
    /// <summary>
    /// 插件状态枚举
    /// </summary>
    public enum PluginStatus
    {
        /// <summary>
        /// 未知状态
        /// </summary>
        Unknown,

        /// <summary>
        /// 未加载
        /// </summary>
        NotLoaded,

        /// <summary>
        /// 正在加载
        /// </summary>
        Loading,

        /// <summary>
        /// 已加载
        /// </summary>
        Loaded,

        /// <summary>
        /// 正在初始化
        /// </summary>
        Initializing,

        /// <summary>
        /// 已初始化
        /// </summary>
        Initialized,

        /// <summary>
        /// 正在启动
        /// </summary>
        Starting,

        /// <summary>
        /// 正在运行
        /// </summary>
        Running,

        /// <summary>
        /// 已暂停
        /// </summary>
        Paused,

        /// <summary>
        /// 正在停止
        /// </summary>
        Stopping,

        /// <summary>
        /// 已停止
        /// </summary>
        Stopped,

        /// <summary>
        /// 正在卸载
        /// </summary>
        Unloading,

        /// <summary>
        /// 错误状态
        /// </summary>
        Error,

        /// <summary>
        /// 已禁用
        /// </summary>
        Disabled
    }

    /// <summary>
    /// 插件类型枚举
    /// </summary>
    public enum PluginType
    {
        /// <summary>
        /// 通用插件
        /// </summary>
        General,

        /// <summary>
        /// 服务插件
        /// </summary>
        Service,

        /// <summary>
        /// UI插件
        /// </summary>
        UI,

        /// <summary>
        /// 数据处理插件
        /// </summary>
        DataProcessor,

        /// <summary>
        /// 通信插件
        /// </summary>
        Communication,

        /// <summary>
        /// 设备驱动插件
        /// </summary>
        DeviceDriver,

        /// <summary>
        /// 扩展插件
        /// </summary>
        Extension,

        /// <summary>
        /// 主题插件
        /// </summary>
        Theme,

        /// <summary>
        /// 语言包插件
        /// </summary>
        Language
    }

    /// <summary>
    /// 插件优先级枚举
    /// </summary>
    public enum PluginPriority
    {
        /// <summary>
        /// 最低优先级
        /// </summary>
        Lowest = 0,

        /// <summary>
        /// 低优先级
        /// </summary>
        Low = 25,

        /// <summary>
        /// 普通优先级
        /// </summary>
        Normal = 50,

        /// <summary>
        /// 高优先级
        /// </summary>
        High = 75,

        /// <summary>
        /// 最高优先级
        /// </summary>
        Highest = 100
    }

    /// <summary>
    /// 插件元数据
    /// </summary>
    public class PluginMetadata
    {
        /// <summary>
        /// 插件ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 插件名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 插件版本
        /// </summary>
        public Version Version { get; set; } = new Version(1, 0, 0, 0);

        /// <summary>
        /// 插件描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 插件作者
        /// </summary>
        public string Author { get; set; } = string.Empty;

        /// <summary>
        /// 插件类别
        /// </summary>
        public string Category { get; set; } = "General";

        /// <summary>
        /// 插件公司
        /// </summary>
        public string Company { get; set; } = string.Empty;

        /// <summary>
        /// 插件网站
        /// </summary>
        public string Website { get; set; } = string.Empty;

        /// <summary>
        /// 插件邮箱
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 插件类型
        /// </summary>
        public PluginType Type { get; set; } = PluginType.General;

        /// <summary>
        /// 插件优先级
        /// </summary>
        public PluginPriority Priority { get; set; } = PluginPriority.Normal;

        /// <summary>
        /// 插件标签
        /// </summary>
        public string[] Tags { get; set; } = Array.Empty<string>();

        /// <summary>
        /// 插件图标路径
        /// </summary>
        public string IconPath { get; set; } = string.Empty;

        /// <summary>
        /// 插件许可证
        /// </summary>
        public string License { get; set; } = string.Empty;

        /// <summary>
        /// 最小框架版本
        /// </summary>
        public Version MinFrameworkVersion { get; set; } = new Version(1, 0, 0, 0);

        /// <summary>
        /// 最大框架版本
        /// </summary>
        public Version? MaxFrameworkVersion { get; set; }

        /// <summary>
        /// 支持的平台
        /// </summary>
        public string[] SupportedPlatforms { get; set; } = Array.Empty<string>();

        /// <summary>
        /// 是否需要重启
        /// </summary>
        public bool RequiresRestart { get; set; } = false;

        /// <summary>
        /// 是否支持热更新
        /// </summary>
        public bool SupportsHotSwap { get; set; } = false;

        /// <summary>
        /// 是否为系统插件
        /// </summary>
        public bool IsSystemPlugin { get; set; } = false;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> Properties { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// 插件信息
    /// </summary>
    public class PluginInfo
    {
        /// <summary>
        /// 插件元数据
        /// </summary>
        public PluginMetadata Metadata { get; set; } = new PluginMetadata();

        /// <summary>
        /// 插件文件路径
        /// </summary>
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// 插件目录
        /// </summary>
        public string Directory { get; set; } = string.Empty;

        /// <summary>
        /// 插件程序集名称
        /// </summary>
        public string AssemblyName { get; set; } = string.Empty;

        /// <summary>
        /// 插件类型名称
        /// </summary>
        public string TypeName { get; set; } = string.Empty;

        /// <summary>
        /// 插件依赖项
        /// </summary>
        public IList<PluginDependency> Dependencies { get; set; } = new List<PluginDependency>();

        /// <summary>
        /// 插件配置架构
        /// </summary>
        public PluginConfigurationSchema? ConfigurationSchema { get; set; }

        /// <summary>
        /// 插件文件大小
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 插件文件哈希
        /// </summary>
        public string FileHash { get; set; } = string.Empty;

        /// <summary>
        /// 是否已验证
        /// </summary>
        public bool IsVerified { get; set; } = false;

        /// <summary>
        /// 验证时间
        /// </summary>
        public DateTime? VerifiedTime { get; set; }

        /// <summary>
        /// 是否已签名
        /// </summary>
        public bool IsSigned { get; set; } = false;

        /// <summary>
        /// 签名信息
        /// </summary>
        public string SignatureInfo { get; set; } = string.Empty;
    }

    /// <summary>
    /// 插件依赖项
    /// </summary>
    public class PluginDependency
    {
        /// <summary>
        /// 依赖插件ID
        /// </summary>
        public string PluginId { get; set; } = string.Empty;

        /// <summary>
        /// 依赖插件名称
        /// </summary>
        public string PluginName { get; set; } = string.Empty;

        /// <summary>
        /// 最小版本
        /// </summary>
        public Version MinVersion { get; set; } = new Version(1, 0, 0, 0);

        /// <summary>
        /// 最大版本
        /// </summary>
        public Version? MaxVersion { get; set; }

        /// <summary>
        /// 是否为可选依赖
        /// </summary>
        public bool IsOptional { get; set; } = false;

        /// <summary>
        /// 依赖描述
        /// </summary>
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// 插件配置架构
    /// </summary>
    public class PluginConfigurationSchema
    {
        /// <summary>
        /// 配置项列表
        /// </summary>
        public IList<PluginConfigurationItem> Items { get; set; } = new List<PluginConfigurationItem>();

        /// <summary>
        /// 配置组列表
        /// </summary>
        public IList<PluginConfigurationGroup> Groups { get; set; } = new List<PluginConfigurationGroup>();

        /// <summary>
        /// 架构版本
        /// </summary>
        public Version Version { get; set; } = new Version(1, 0, 0, 0);

        /// <summary>
        /// 架构描述
        /// </summary>
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// 插件配置项
    /// </summary>
    public class PluginConfigurationItem
    {
        /// <summary>
        /// 配置键
        /// </summary>
        public string Key { get; set; } = string.Empty;

        /// <summary>
        /// 配置名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 配置描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 配置类型
        /// </summary>
        public Type Type { get; set; } = typeof(string);

        /// <summary>
        /// 默认值
        /// </summary>
        public object? DefaultValue { get; set; }

        /// <summary>
        /// 是否必需
        /// </summary>
        public bool IsRequired { get; set; } = false;

        /// <summary>
        /// 是否只读
        /// </summary>
        public bool IsReadOnly { get; set; } = false;

        /// <summary>
        /// 可选值列表
        /// </summary>
        public object[]? PossibleValues { get; set; }

        /// <summary>
        /// 最小值
        /// </summary>
        public object? MinValue { get; set; }

        /// <summary>
        /// 最大值
        /// </summary>
        public object? MaxValue { get; set; }

        /// <summary>
        /// 验证正则表达式
        /// </summary>
        public string? ValidationPattern { get; set; }

        /// <summary>
        /// 验证错误消息
        /// </summary>
        public string? ValidationMessage { get; set; }

        /// <summary>
        /// 配置组
        /// </summary>
        public string Group { get; set; } = string.Empty;

        /// <summary>
        /// 显示顺序
        /// </summary>
        public int Order { get; set; } = 0;
    }

    /// <summary>
    /// 插件配置组
    /// </summary>
    public class PluginConfigurationGroup
    {
        /// <summary>
        /// 组名
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 组显示名称
        /// </summary>
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// 组描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 显示顺序
        /// </summary>
        public int Order { get; set; } = 0;

        /// <summary>
        /// 是否可折叠
        /// </summary>
        public bool IsCollapsible { get; set; } = true;

        /// <summary>
        /// 是否默认展开
        /// </summary>
        public bool IsExpanded { get; set; } = true;
    }

    /// <summary>
    /// 插件验证结果
    /// </summary>
    public class PluginValidationResult
    {
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid { get; set; } = true;

        /// <summary>
        /// 错误列表
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// 警告列表
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();

        /// <summary>
        /// 信息列表
        /// </summary>
        public List<string> Information { get; set; } = new List<string>();

        /// <summary>
        /// 验证时间
        /// </summary>
        public DateTime ValidationTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 添加错误
        /// </summary>
        /// <param name="error">错误消息</param>
        public void AddError(string error)
        {
            Errors.Add(error);
            IsValid = false;
        }

        /// <summary>
        /// 添加警告
        /// </summary>
        /// <param name="warning">警告消息</param>
        public void AddWarning(string warning)
        {
            Warnings.Add(warning);
        }

        /// <summary>
        /// 添加信息
        /// </summary>
        /// <param name="info">信息消息</param>
        public void AddInformation(string info)
        {
            Information.Add(info);
        }
    }

    /// <summary>
    /// 配置验证结果
    /// </summary>
    public class ConfigurationValidationResult
    {
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid { get; set; } = true;

        /// <summary>
        /// 错误列表
        /// </summary>
        public IList<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// 警告列表
        /// </summary>
        public IList<string> Warnings { get; set; } = new List<string>();

        /// <summary>
        /// 建议列表
        /// </summary>
        public IList<string> Suggestions { get; set; } = new List<string>();
    }

    /// <summary>
    /// 插件依赖解析结果
    /// </summary>
    public class PluginDependencyResolutionResult
    {
        /// <summary>
        /// 是否成功解析
        /// </summary>
        public bool IsResolved { get; set; } = true;

        /// <summary>
        /// 解析后的插件加载顺序
        /// </summary>
        public IList<PluginInfo> LoadOrder { get; set; } = new List<PluginInfo>();

        /// <summary>
        /// 缺失的依赖项
        /// </summary>
        public IList<PluginDependency> MissingDependencies { get; set; } = new List<PluginDependency>();

        /// <summary>
        /// 循环依赖项
        /// </summary>
        public IList<string> CircularDependencies { get; set; } = new List<string>();

        /// <summary>
        /// 版本冲突
        /// </summary>
        public IList<string> VersionConflicts { get; set; } = new List<string>();

        /// <summary>
        /// 解析错误
        /// </summary>
        public IList<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// 解析警告
        /// </summary>
        public IList<string> Warnings { get; set; } = new List<string>();
    }

    /// <summary>
    /// 插件管理器统计信息
    /// </summary>
    public class PluginManagerStatistics
    {
        /// <summary>
        /// 总插件数
        /// </summary>
        public int TotalPlugins { get; set; }

        /// <summary>
        /// 已加载插件数
        /// </summary>
        public int LoadedPlugins { get; set; }

        /// <summary>
        /// 正在运行插件数
        /// </summary>
        public int RunningPlugins { get; set; }

        /// <summary>
        /// 错误插件数
        /// </summary>
        public int ErrorPlugins { get; set; }

        /// <summary>
        /// 已禁用插件数
        /// </summary>
        public int DisabledPlugins { get; set; }

        /// <summary>
        /// 总加载次数
        /// </summary>
        public long TotalLoads { get; set; }

        /// <summary>
        /// 总卸载次数
        /// </summary>
        public long TotalUnloads { get; set; }

        /// <summary>
        /// 总启动次数
        /// </summary>
        public long TotalStarts { get; set; }

        /// <summary>
        /// 总停止次数
        /// </summary>
        public long TotalStops { get; set; }

        /// <summary>
        /// 总错误次数
        /// </summary>
        public long TotalErrors { get; set; }

        /// <summary>
        /// 平均加载时间（毫秒）
        /// </summary>
        public double AverageLoadTime { get; set; }

        /// <summary>
        /// 平均启动时间（毫秒）
        /// </summary>
        public double AverageStartTime { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        /// <summary>
        /// 启动时间
        /// </summary>
        public DateTime StartTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 运行时间
        /// </summary>
        public TimeSpan Uptime => DateTime.Now - StartTime;
    }

    #region 事件参数类

    /// <summary>
    /// 插件状态变更事件参数
    /// </summary>
    public class PluginStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 插件实例
        /// </summary>
        public IPlugin Plugin { get; }

        /// <summary>
        /// 旧状态
        /// </summary>
        public PluginStatus OldStatus { get; }

        /// <summary>
        /// 新状态
        /// </summary>
        public PluginStatus NewStatus { get; }

        /// <summary>
        /// 变更时间
        /// </summary>
        public DateTime Timestamp { get; }

        public PluginStatusChangedEventArgs(IPlugin plugin, PluginStatus oldStatus, PluginStatus newStatus)
        {
            Plugin = plugin;
            OldStatus = oldStatus;
            NewStatus = newStatus;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 插件错误事件参数
    /// </summary>
    public class PluginErrorEventArgs : EventArgs
    {
        /// <summary>
        /// 插件实例
        /// </summary>
        public IPlugin? Plugin { get; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; }

        /// <summary>
        /// 异常对象
        /// </summary>
        public Exception? Exception { get; }

        /// <summary>
        /// 错误时间
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// 是否为致命错误
        /// </summary>
        public bool IsFatal { get; }

        public PluginErrorEventArgs(IPlugin? plugin, string errorMessage, Exception? exception = null, bool isFatal = false)
        {
            Plugin = plugin;
            ErrorMessage = errorMessage;
            Exception = exception;
            IsFatal = isFatal;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 插件加载事件参数
    /// </summary>
    public class PluginLoadedEventArgs : EventArgs
    {
        /// <summary>
        /// 插件实例
        /// </summary>
        public IPlugin Plugin { get; }

        /// <summary>
        /// 加载时间
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// 加载耗时（毫秒）
        /// </summary>
        public long LoadTime { get; }

        public PluginLoadedEventArgs(IPlugin plugin, long loadTime)
        {
            Plugin = plugin;
            LoadTime = loadTime;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 插件卸载事件参数
    /// </summary>
    public class PluginUnloadedEventArgs : EventArgs
    {
        /// <summary>
        /// 插件ID
        /// </summary>
        public string PluginId { get; }

        /// <summary>
        /// 插件名称
        /// </summary>
        public string PluginName { get; }

        /// <summary>
        /// 卸载时间
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// 卸载耗时（毫秒）
        /// </summary>
        public long UnloadTime { get; }

        public PluginUnloadedEventArgs(string pluginId, string pluginName, long unloadTime)
        {
            PluginId = pluginId;
            PluginName = pluginName;
            UnloadTime = unloadTime;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 插件启动事件参数
    /// </summary>
    public class PluginStartedEventArgs : EventArgs
    {
        /// <summary>
        /// 插件实例
        /// </summary>
        public IPlugin Plugin { get; }

        /// <summary>
        /// 启动时间
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// 启动耗时（毫秒）
        /// </summary>
        public long StartTime { get; }

        public PluginStartedEventArgs(IPlugin plugin, long startTime)
        {
            Plugin = plugin;
            StartTime = startTime;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 插件停止事件参数
    /// </summary>
    public class PluginStoppedEventArgs : EventArgs
    {
        /// <summary>
        /// 插件实例
        /// </summary>
        public IPlugin Plugin { get; }

        /// <summary>
        /// 停止时间
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// 停止耗时（毫秒）
        /// </summary>
        public long StopTime { get; }

        public PluginStoppedEventArgs(IPlugin plugin, long stopTime)
        {
            Plugin = plugin;
            StopTime = stopTime;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 插件配置变更事件参数
    /// </summary>
    public class PluginConfigurationChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 插件实例
        /// </summary>
        public IPlugin Plugin { get; }

        /// <summary>
        /// 旧配置
        /// </summary>
        public Dictionary<string, object> OldConfiguration { get; }

        /// <summary>
        /// 新配置
        /// </summary>
        public Dictionary<string, object> NewConfiguration { get; }

        /// <summary>
        /// 变更时间
        /// </summary>
        public DateTime Timestamp { get; }

        public PluginConfigurationChangedEventArgs(IPlugin plugin, Dictionary<string, object> oldConfiguration, Dictionary<string, object> newConfiguration)
        {
            Plugin = plugin;
            OldConfiguration = oldConfiguration;
            NewConfiguration = newConfiguration;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 插件热更新事件参数
    /// </summary>
    public class PluginHotSwapEventArgs : EventArgs
    {
        /// <summary>
        /// 插件ID
        /// </summary>
        public string PluginId { get; }

        /// <summary>
        /// 旧版本
        /// </summary>
        public Version OldVersion { get; }

        /// <summary>
        /// 新版本
        /// </summary>
        public Version NewVersion { get; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccessful { get; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime Timestamp { get; }

        public PluginHotSwapEventArgs(string pluginId, Version oldVersion, Version newVersion, bool isSuccessful, string? errorMessage = null)
        {
            PluginId = pluginId;
            OldVersion = oldVersion;
            NewVersion = newVersion;
            IsSuccessful = isSuccessful;
            ErrorMessage = errorMessage;
            Timestamp = DateTime.Now;
        }
    }

    #endregion
}
