using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;

namespace McLaser.Device
{
    /// <summary>
    /// 设备管理器接口
    /// 定义设备管理器的核心功能和操作
    /// </summary>
    public interface IDeviceManager : INotifyPropertyChanged
    {
        #region 属性

        /// <summary>
        /// 设备分类组集合
        /// </summary>
        ObservableCollection<DeviceCategoryGroup> DeviceGroups { get; }

        /// <summary>
        /// 所有设备列表
        /// </summary>
        ObservableCollection<DeviceBase> Devices { get; }

        /// <summary>
        /// 相机设备列表
        /// </summary>
        ObservableCollection<DeviceBase> Cameras { get; }

        /// <summary>
        /// 运动控制卡列表
        /// </summary>
        ObservableCollection<DeviceBase> MotionCards { get; }

        /// <summary>
        /// 激光器设备列表
        /// </summary>
        ObservableCollection<DeviceBase> Lasers { get; }

        /// <summary>
        /// 传感器设备列表
        /// </summary>
        ObservableCollection<DeviceBase> Sensors { get; }

        /// <summary>
        /// 当前选中的设备
        /// </summary>
        IDevice SelectedDevice { get; set; }

        /// <summary>
        /// 当前配置界面
        /// </summary>
        FrameworkElement CurrentConfigurationUI { get; set; }

        /// <summary>
        /// 是否已初始化
        /// </summary>
        bool IsInitialized { get; }

        /// <summary>
        /// 是否正在监控
        /// </summary>
        bool IsMonitoring { get; }

        /// <summary>
        /// 状态消息
        /// </summary>
        string StatusMessage { get; set; }

        /// <summary>
        /// 已连接设备数量
        /// </summary>
        int ConnectedDeviceCount { get; }

        /// <summary>
        /// 设备总数
        /// </summary>
        int TotalDeviceCount { get; }

        #endregion

        #region 方法

        /// <summary>
        /// 初始化设备管理器
        /// </summary>
        /// <returns>初始化是否成功</returns>
        bool Initialize();

        /// <summary>
        /// 关闭设备管理器
        /// </summary>
        void Shutdown();

        /// <summary>
        /// 添加设备
        /// </summary>
        /// <param name="device">要添加的设备</param>
        void AddDevice(IDevice device);

        /// <summary>
        /// 移除设备
        /// </summary>
        /// <param name="deviceId">要移除的设备ID</param>
        void RemoveDevice(string deviceId);

        /// <summary>
        /// 根据ID获取设备
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>找到的设备，如果没找到返回null</returns>
        IDevice GetDeviceById(string deviceId);

        /// <summary>
        /// 根据名称获取设备
        /// </summary>
        /// <param name="deviceName">设备名称</param>
        /// <returns>找到的设备，如果没找到返回null</returns>
        IDevice GetDeviceByName(string deviceName);

        /// <summary>
        /// 获取指定类型的设备列表
        /// </summary>
        /// <param name="deviceType">设备类型</param>
        /// <returns>指定类型的设备列表</returns>
        List<IDevice> GetDevicesByType(DevicesType deviceType);

        /// <summary>
        /// 连接所有设备
        /// </summary>
        /// <returns>是否成功</returns>
        Task<bool> ConnectAllDevicesAsync();

        /// <summary>
        /// 断开所有设备
        /// </summary>
        /// <returns>是否成功</returns>
        Task<bool> DisconnectAllDevicesAsync();

        /// <summary>
        /// 开始监控所有设备
        /// </summary>
        /// <returns>是否成功</returns>
        bool StartMonitoring();

        /// <summary>
        /// 停止监控所有设备
        /// </summary>
        /// <returns>是否成功</returns>
        bool StopMonitoring();

        /// <summary>
        /// 保存设备配置
        /// </summary>
        /// <returns>是否成功</returns>
        bool SaveDeviceConfiguration();

        /// <summary>
        /// 加载设备配置
        /// </summary>
        /// <returns>是否成功</returns>
        bool LoadDeviceConfiguration();

        /// <summary>
        /// 清除所有设备
        /// </summary>
        void ClearAllDevices();

        /// <summary>
        /// 关闭所有设备
        /// </summary>
        void CloseAllDevices();

        /// <summary>
        /// 检查设备名称是否唯一
        /// </summary>
        /// <param name="name">设备名称</param>
        /// <param name="excludeDeviceId">排除的设备ID（用于重命名时排除自身）</param>
        /// <returns>名称是否唯一</returns>
        bool IsDeviceNameUnique(string name, string excludeDeviceId = null);

        /// <summary>
        /// 获取唯一的设备名称
        /// </summary>
        /// <param name="baseName">基础名称</param>
        /// <returns>唯一的设备名称</returns>
        string GetUniqueDeviceName(string baseName);

        #endregion

        #region 事件

        /// <summary>
        /// 设备添加事件
        /// </summary>
        event EventHandler<DeviceEventArgs> DeviceAdded;

        /// <summary>
        /// 设备移除事件
        /// </summary>
        event EventHandler<DeviceEventArgs> DeviceRemoved;

        /// <summary>
        /// 设备状态变更事件
        /// </summary>
        event EventHandler<DeviceStatusChangedEventArgs> DeviceStatusChanged;

        /// <summary>
        /// 设备管理器状态变更事件
        /// </summary>
        event EventHandler<ManagerStatusChangedEventArgs> ManagerStatusChanged;

        #endregion
    }

    /// <summary>
    /// 设备事件参数
    /// </summary>
    public class DeviceEventArgs : EventArgs
    {
        /// <summary>
        /// 设备
        /// </summary>
        public IDevice Device { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="device">设备</param>
        public DeviceEventArgs(IDevice device)
        {
            Device = device;
        }
    }

    /// <summary>
    /// 设备状态变更事件参数
    /// </summary>
    public class DeviceStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 设备
        /// </summary>
        public IDevice Device { get; set; }

        /// <summary>
        /// 旧状态
        /// </summary>
        public string OldStatus { get; set; }

        /// <summary>
        /// 新状态
        /// </summary>
        public string NewStatus { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="device">设备</param>
        /// <param name="oldStatus">旧状态</param>
        /// <param name="newStatus">新状态</param>
        public DeviceStatusChangedEventArgs(IDevice device, string oldStatus, string newStatus)
        {
            Device = device;
            OldStatus = oldStatus;
            NewStatus = newStatus;
        }
    }

    /// <summary>
    /// 管理器状态变更事件参数
    /// </summary>
    public class ManagerStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 状态消息
        /// </summary>
        public string StatusMessage { get; set; }

        /// <summary>
        /// 是否初始化
        /// </summary>
        public bool IsInitialized { get; set; }

        /// <summary>
        /// 是否监控中
        /// </summary>
        public bool IsMonitoring { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="statusMessage">状态消息</param>
        /// <param name="isInitialized">是否初始化</param>
        /// <param name="isMonitoring">是否监控中</param>
        public ManagerStatusChangedEventArgs(string statusMessage, bool isInitialized, bool isMonitoring)
        {
            StatusMessage = statusMessage;
            IsInitialized = isInitialized;
            IsMonitoring = isMonitoring;
        }
    }
}
