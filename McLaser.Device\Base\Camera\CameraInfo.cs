using System;

namespace McLaser.Devices
{
    /// <summary>
    /// 相机信息类
    /// 包含相机的基本识别信息
    /// </summary>
    [Serializable]
    public class CameraInfo
    {
        /// <summary>
        /// 相机名称
        /// </summary>
        public string CamName { get; set; } = string.Empty;

        /// <summary>
        /// 相机序列号
        /// </summary>
        public string SerialNO { get; set; } = string.Empty;

        /// <summary>
        /// 扩展信息（非序列化）
        /// 用于存储特定相机厂商的额外信息
        /// </summary>
        [NonSerialized]
        public object ExtInfo;

        /// <summary>
        /// 构造函数
        /// </summary>
        public CameraInfo()
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="camName">相机名称</param>
        /// <param name="serialNo">序列号</param>
        public CameraInfo(string camName, string serialNo)
        {
            CamName = camName;
            SerialNO = serialNo;
        }

        /// <summary>
        /// 重写ToString方法
        /// </summary>
        /// <returns>相机信息字符串</returns>
        public override string ToString()
        {
            return $"{CamName} ({SerialNO})";
        }
    }
}
