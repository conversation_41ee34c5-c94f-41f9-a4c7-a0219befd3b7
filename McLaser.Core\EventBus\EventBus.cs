using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace McLaser.Core.EventBus
{
    /// <summary>
    /// 事件总线实现
    /// 提供完整的事件发布/订阅功能，支持异步处理、过滤、拦截、持久化等特性
    /// </summary>
    public class EventBus : IEventBus, IDisposable
    {
        #region 私有字段

        private readonly ConcurrentDictionary<Type, ConcurrentBag<EventSubscriptionInfo>> _subscriptions;
        private readonly List<IEventFilter> _filters;
        private readonly List<IEventInterceptor> _interceptors;
        private readonly EventBusStatistics _statistics;
        private readonly SemaphoreSlim _concurrencySemaphore;
        private readonly object _lock = new object();

        private IEventStore _eventStore;
        private TimeSpan _handlingTimeout = TimeSpan.FromSeconds(30);
        private bool _asyncProcessing = true;
        private bool _disposed;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="maxConcurrency">最大并发处理数</param>
        public EventBus(int maxConcurrency = 100)
        {
            _subscriptions = new ConcurrentDictionary<Type, ConcurrentBag<EventSubscriptionInfo>>();
            _filters = new List<IEventFilter>();
            _interceptors = new List<IEventInterceptor>();
            _statistics = new EventBusStatistics();
            _concurrencySemaphore = new SemaphoreSlim(maxConcurrency, maxConcurrency);
        }

        #endregion

        #region 事件

        /// <summary>
        /// 事件发布前事件
        /// </summary>
        public event EventHandler<EventPublishingEventArgs> EventPublishing;

        /// <summary>
        /// 事件发布后事件
        /// </summary>
        public event EventHandler<EventPublishedEventArgs> EventPublished;

        /// <summary>
        /// 事件处理异常事件
        /// </summary>
        public event EventHandler<EventHandlingExceptionEventArgs> EventHandlingException;

        #endregion

        #region 事件发布

        /// <summary>
        /// 发布事件
        /// </summary>
        /// <typeparam name="TEvent">事件类型</typeparam>
        /// <param name="eventData">事件数据</param>
        public void Publish<TEvent>(TEvent eventData) where TEvent : class
        {
            PublishAsync(eventData).Wait();
        }

        /// <summary>
        /// 异步发布事件
        /// </summary>
        /// <typeparam name="TEvent">事件类型</typeparam>
        /// <param name="eventData">事件数据</param>
        /// <returns>异步任务</returns>
        public async Task PublishAsync<TEvent>(TEvent eventData) where TEvent : class
        {
            await PublishAsync(typeof(TEvent), eventData);
        }

        /// <summary>
        /// 发布事件（通过类型）
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="eventData">事件数据</param>
        public void Publish(Type eventType, object eventData)
        {
            PublishAsync(eventType, eventData).Wait();
        }

        /// <summary>
        /// 异步发布事件（通过类型）
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="eventData">事件数据</param>
        /// <returns>异步任务</returns>
        public async Task PublishAsync(Type eventType, object eventData)
        {
            if (eventType == null)
                throw new ArgumentNullException(nameof(eventType));

            if (eventData == null)
                throw new ArgumentNullException(nameof(eventData));

            var stopwatch = Stopwatch.StartNew();
            var handlerCount = 0;

            try
            {
                // 触发发布前事件
                var publishingArgs = new EventPublishingEventArgs(eventType, eventData);
                OnEventPublishing(publishingArgs);

                if (publishingArgs.Cancel)
                {
                    Debug.WriteLine($"事件发布被取消: {eventType.Name}");
                    return;
                }

                // 检查过滤器
                if (!ShouldHandleEvent(eventType, eventData))
                {
                    Debug.WriteLine($"事件被过滤器拦截: {eventType.Name}");
                    return;
                }

                // 执行拦截器（发布前）
                var interceptorContext = new EventInterceptorContext(eventType, eventData);
                await ExecuteInterceptorsBeforePublishAsync(interceptorContext);

                if (interceptorContext.Cancel)
                {
                    Debug.WriteLine($"事件被拦截器取消: {eventType.Name}");
                    return;
                }

                // 持久化事件
                if (_eventStore != null)
                {
                    await PersistEventAsync(eventType, eventData);
                }

                // 获取订阅者
                var subscriptions = GetSubscriptionsForEvent(eventType);
                handlerCount = subscriptions.Count();

                if (handlerCount == 0)
                {
                    Debug.WriteLine($"没有找到事件订阅者: {eventType.Name}");
                    return;
                }

                // 处理事件
                if (_asyncProcessing)
                {
                    await ProcessEventAsync(eventType, eventData, subscriptions);
                }
                else
                {
                    ProcessEventSync(eventType, eventData, subscriptions);
                }

                // 执行拦截器（发布后）
                await ExecuteInterceptorsAfterPublishAsync(interceptorContext);

                // 更新统计信息
                UpdatePublishStatistics(eventType, stopwatch.ElapsedMilliseconds, handlerCount);

                // 触发发布后事件
                OnEventPublished(new EventPublishedEventArgs(eventType, eventData, handlerCount, stopwatch.ElapsedMilliseconds));

                Debug.WriteLine($"事件发布完成: {eventType.Name}, 处理器数量: {handlerCount}, 耗时: {stopwatch.ElapsedMilliseconds}ms");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"事件发布失败: {eventType.Name}, 错误: {ex.Message}");
                _statistics.TotalExceptions++;
                _statistics.LastExceptionAt = DateTime.UtcNow;
                throw;
            }
            finally
            {
                stopwatch.Stop();
            }
        }

        #endregion

        #region 事件订阅

        /// <summary>
        /// 订阅事件
        /// </summary>
        /// <typeparam name="TEvent">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅标识</returns>
        public string Subscribe<TEvent>(IEventHandler<TEvent> handler) where TEvent : class
        {
            return Subscribe(typeof(TEvent), handler);
        }

        /// <summary>
        /// 订阅事件（使用委托）
        /// </summary>
        /// <typeparam name="TEvent">事件类型</typeparam>
        /// <param name="handler">事件处理委托</param>
        /// <returns>订阅标识</returns>
        public string Subscribe<TEvent>(Action<TEvent> handler) where TEvent : class
        {
            if (handler == null)
                throw new ArgumentNullException(nameof(handler));

            var delegateHandler = new DelegateEventHandler<TEvent>(handler);
            return Subscribe<TEvent>(delegateHandler);
        }

        /// <summary>
        /// 订阅事件（使用异步委托）
        /// </summary>
        /// <typeparam name="TEvent">事件类型</typeparam>
        /// <param name="handler">异步事件处理委托</param>
        /// <returns>订阅标识</returns>
        public string Subscribe<TEvent>(Func<TEvent, Task> handler) where TEvent : class
        {
            if (handler == null)
                throw new ArgumentNullException(nameof(handler));

            var delegateHandler = new DelegateEventHandler<TEvent>(handler);
            return Subscribe<TEvent>(delegateHandler);
        }

        /// <summary>
        /// 订阅事件（通过类型）
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅标识</returns>
        public string Subscribe(Type eventType, IEventHandler handler)
        {
            if (eventType == null)
                throw new ArgumentNullException(nameof(eventType));

            if (handler == null)
                throw new ArgumentNullException(nameof(handler));

            var subscriptionId = Guid.NewGuid().ToString("N");
            var subscription = new EventSubscriptionInfo
            {
                SubscriptionId = subscriptionId,
                EventType = eventType,
                Handler = handler,
                SubscribedAt = DateTime.UtcNow,
                IsEnabled = true
            };

            var subscriptions = _subscriptions.GetOrAdd(eventType, _ => new ConcurrentBag<EventSubscriptionInfo>());
            subscriptions.Add(subscription);

            _statistics.TotalSubscriptions++;
            _statistics.ActiveSubscriptions++;

            Debug.WriteLine($"事件订阅成功: {eventType.Name}, 处理器: {handler.Name}, 订阅ID: {subscriptionId}");
            return subscriptionId;
        }

        #endregion

        #region 事件取消订阅

        /// <summary>
        /// 取消订阅事件
        /// </summary>
        /// <param name="subscriptionId">订阅标识</param>
        /// <returns>是否取消成功</returns>
        public bool Unsubscribe(string subscriptionId)
        {
            if (string.IsNullOrEmpty(subscriptionId))
                return false;

            foreach (var kvp in _subscriptions)
            {
                var subscriptions = kvp.Value.ToList();
                var subscription = subscriptions.FirstOrDefault(s => s.SubscriptionId == subscriptionId);
                
                if (subscription != null)
                {
                    subscription.IsEnabled = false;
                    _statistics.ActiveSubscriptions--;
                    Debug.WriteLine($"取消事件订阅: {kvp.Key.Name}, 订阅ID: {subscriptionId}");
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 取消订阅事件
        /// </summary>
        /// <typeparam name="TEvent">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        /// <returns>是否取消成功</returns>
        public bool Unsubscribe<TEvent>(IEventHandler<TEvent> handler) where TEvent : class
        {
            if (handler == null)
                return false;

            var eventType = typeof(TEvent);
            if (!_subscriptions.TryGetValue(eventType, out var subscriptions))
                return false;

            var subscriptionList = subscriptions.ToList();
            var subscription = subscriptionList.FirstOrDefault(s => ReferenceEquals(s.Handler, handler));
            
            if (subscription != null)
            {
                subscription.IsEnabled = false;
                _statistics.ActiveSubscriptions--;
                Debug.WriteLine($"取消事件订阅: {eventType.Name}, 处理器: {handler.Name}");
                return true;
            }

            return false;
        }

        /// <summary>
        /// 取消所有订阅
        /// </summary>
        /// <typeparam name="TEvent">事件类型</typeparam>
        public void UnsubscribeAll<TEvent>() where TEvent : class
        {
            UnsubscribeAll(typeof(TEvent));
        }

        /// <summary>
        /// 取消所有订阅
        /// </summary>
        /// <param name="eventType">事件类型</param>
        public void UnsubscribeAll(Type eventType)
        {
            if (eventType == null)
                return;

            if (_subscriptions.TryGetValue(eventType, out var subscriptions))
            {
                var subscriptionList = subscriptions.ToList();
                foreach (var subscription in subscriptionList.Where(s => s.IsEnabled))
                {
                    subscription.IsEnabled = false;
                    _statistics.ActiveSubscriptions--;
                }

                Debug.WriteLine($"取消所有事件订阅: {eventType.Name}, 数量: {subscriptionList.Count(s => !s.IsEnabled)}");
            }
        }

        /// <summary>
        /// 清空所有订阅
        /// </summary>
        public void Clear()
        {
            foreach (var kvp in _subscriptions)
            {
                var subscriptions = kvp.Value.ToList();
                foreach (var subscription in subscriptions.Where(s => s.IsEnabled))
                {
                    subscription.IsEnabled = false;
                    _statistics.ActiveSubscriptions--;
                }
            }

            Debug.WriteLine("清空所有事件订阅");
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 检查事件是否应该被处理
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="eventData">事件数据</param>
        /// <returns>是否应该处理</returns>
        private bool ShouldHandleEvent(Type eventType, object eventData)
        {
            foreach (var filter in _filters)
            {
                if (!filter.ShouldHandle(eventType, eventData))
                {
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 获取事件的订阅信息
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <returns>订阅信息集合</returns>
        private IEnumerable<EventSubscriptionInfo> GetSubscriptionsForEvent(Type eventType)
        {
            if (!_subscriptions.TryGetValue(eventType, out var subscriptions))
                return Enumerable.Empty<EventSubscriptionInfo>();

            return subscriptions.Where(s => s.IsEnabled && s.Handler.IsEnabled)
                              .OrderByDescending(s => s.Handler.Priority);
        }

        /// <summary>
        /// 持久化事件
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="eventData">事件数据</param>
        /// <returns>异步任务</returns>
        private async Task PersistEventAsync(Type eventType, object eventData)
        {
            try
            {
                var storedEvent = new StoredEvent
                {
                    Id = Guid.NewGuid().ToString("N"),
                    EventTypeName = eventType.FullName,
                    EventData = JsonConvert.SerializeObject(eventData),
                    Timestamp = DateTime.UtcNow,
                    Source = "EventBus",
                    Version = 1
                };

                await _eventStore.SaveEventAsync(storedEvent);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"持久化事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 异步处理事件
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="eventData">事件数据</param>
        /// <param name="subscriptions">订阅信息</param>
        /// <returns>异步任务</returns>
        private async Task ProcessEventAsync(Type eventType, object eventData, IEnumerable<EventSubscriptionInfo> subscriptions)
        {
            var tasks = subscriptions.Select(async subscription =>
            {
                await _concurrencySemaphore.WaitAsync();
                try
                {
                    await ProcessSingleHandlerAsync(eventType, eventData, subscription);
                }
                finally
                {
                    _concurrencySemaphore.Release();
                }
            });

            await Task.WhenAll(tasks);
        }

        /// <summary>
        /// 同步处理事件
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="eventData">事件数据</param>
        /// <param name="subscriptions">订阅信息</param>
        private void ProcessEventSync(Type eventType, object eventData, IEnumerable<EventSubscriptionInfo> subscriptions)
        {
            foreach (var subscription in subscriptions)
            {
                ProcessSingleHandlerAsync(eventType, eventData, subscription).Wait();
            }
        }

        /// <summary>
        /// 处理单个处理器
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="eventData">事件数据</param>
        /// <param name="subscription">订阅信息</param>
        /// <returns>异步任务</returns>
        private async Task ProcessSingleHandlerAsync(Type eventType, object eventData, EventSubscriptionInfo subscription)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                using (var cts = new CancellationTokenSource(_handlingTimeout))
                {
                    // 使用反射调用处理器的HandleAsync方法
                    var handlerType = subscription.Handler.GetType();
                    var handleMethod = handlerType.GetMethod("HandleAsync");

                    if (handleMethod != null)
                    {
                        var task = (Task)handleMethod.Invoke(subscription.Handler, new[] { eventData });
                        await task.WaitAsync(cts.Token);
                    }
                }

                // 更新订阅统计信息
                subscription.HandledCount++;
                subscription.LastHandledAt = DateTime.UtcNow;
                UpdateHandlerStatistics(subscription, stopwatch.ElapsedMilliseconds);

                _statistics.TotalEventsHandled++;
            }
            catch (Exception ex)
            {
                subscription.ExceptionCount++;
                subscription.LastExceptionAt = DateTime.UtcNow;
                _statistics.TotalExceptions++;
                _statistics.LastExceptionAt = DateTime.UtcNow;

                // 触发异常事件
                var exceptionArgs = new EventHandlingExceptionEventArgs(eventType, eventData, subscription.Handler, ex);
                OnEventHandlingException(exceptionArgs);

                // 执行拦截器异常处理
                var interceptorContext = new EventInterceptorContext(eventType, eventData);
                await ExecuteInterceptorsOnExceptionAsync(interceptorContext, ex);

                if (!exceptionArgs.Handled)
                {
                    Debug.WriteLine($"事件处理异常: {eventType.Name}, 处理器: {subscription.Handler.Name}, 错误: {ex.Message}");
                }
            }
            finally
            {
                stopwatch.Stop();
            }
        }

        /// <summary>
        /// 执行拦截器（发布前）
        /// </summary>
        /// <param name="context">拦截上下文</param>
        /// <returns>异步任务</returns>
        private async Task ExecuteInterceptorsBeforePublishAsync(EventInterceptorContext context)
        {
            var interceptors = _interceptors.OrderByDescending(i => i.Priority);
            foreach (var interceptor in interceptors)
            {
                try
                {
                    await interceptor.BeforePublishAsync(context);
                    if (context.Cancel)
                        break;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"拦截器执行失败: {interceptor.Name}, 错误: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 执行拦截器（发布后）
        /// </summary>
        /// <param name="context">拦截上下文</param>
        /// <returns>异步任务</returns>
        private async Task ExecuteInterceptorsAfterPublishAsync(EventInterceptorContext context)
        {
            var interceptors = _interceptors.OrderByDescending(i => i.Priority);
            foreach (var interceptor in interceptors)
            {
                try
                {
                    await interceptor.AfterPublishAsync(context);
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"拦截器执行失败: {interceptor.Name}, 错误: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 执行拦截器（异常处理）
        /// </summary>
        /// <param name="context">拦截上下文</param>
        /// <param name="exception">异常对象</param>
        /// <returns>异步任务</returns>
        private async Task ExecuteInterceptorsOnExceptionAsync(EventInterceptorContext context, Exception exception)
        {
            var interceptors = _interceptors.OrderByDescending(i => i.Priority);
            foreach (var interceptor in interceptors)
            {
                try
                {
                    await interceptor.OnExceptionAsync(context, exception);
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"拦截器异常处理失败: {interceptor.Name}, 错误: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 更新发布统计信息
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="elapsedMilliseconds">耗时毫秒数</param>
        /// <param name="handlerCount">处理器数量</param>
        private void UpdatePublishStatistics(Type eventType, long elapsedMilliseconds, int handlerCount)
        {
            _statistics.TotalEventsPublished++;
            _statistics.LastEventPublishedAt = DateTime.UtcNow;

            // 更新平均处理时间
            if (_statistics.TotalEventsPublished == 1)
            {
                _statistics.AverageEventHandlingTime = elapsedMilliseconds;
            }
            else
            {
                _statistics.AverageEventHandlingTime = (_statistics.AverageEventHandlingTime * (_statistics.TotalEventsPublished - 1) + elapsedMilliseconds) / _statistics.TotalEventsPublished;
            }

            // 更新事件类型统计
            var eventTypeName = eventType.FullName;
            if (!_statistics.EventTypeStatistics.TryGetValue(eventTypeName, out var typeStats))
            {
                typeStats = new EventTypeStatistics
                {
                    EventTypeName = eventTypeName
                };
                _statistics.EventTypeStatistics[eventTypeName] = typeStats;
            }

            typeStats.PublishedCount++;
            typeStats.LastPublishedAt = DateTime.UtcNow;
            typeStats.SubscriberCount = handlerCount;

            if (typeStats.PublishedCount == 1)
            {
                typeStats.AverageHandlingTime = elapsedMilliseconds;
            }
            else
            {
                typeStats.AverageHandlingTime = (typeStats.AverageHandlingTime * (typeStats.PublishedCount - 1) + elapsedMilliseconds) / typeStats.PublishedCount;
            }
        }

        /// <summary>
        /// 更新处理器统计信息
        /// </summary>
        /// <param name="subscription">订阅信息</param>
        /// <param name="elapsedMilliseconds">耗时毫秒数</param>
        private void UpdateHandlerStatistics(EventSubscriptionInfo subscription, long elapsedMilliseconds)
        {
            if (subscription.HandledCount == 1)
            {
                subscription.AverageHandlingTime = elapsedMilliseconds;
            }
            else
            {
                subscription.AverageHandlingTime = (subscription.AverageHandlingTime * (subscription.HandledCount - 1) + elapsedMilliseconds) / subscription.HandledCount;
            }
        }

        /// <summary>
        /// 触发事件发布前事件
        /// </summary>
        /// <param name="args">事件参数</param>
        private void OnEventPublishing(EventPublishingEventArgs args)
        {
            try
            {
                EventPublishing?.Invoke(this, args);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"触发事件发布前事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 触发事件发布后事件
        /// </summary>
        /// <param name="args">事件参数</param>
        private void OnEventPublished(EventPublishedEventArgs args)
        {
            try
            {
                EventPublished?.Invoke(this, args);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"触发事件发布后事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 触发事件处理异常事件
        /// </summary>
        /// <param name="args">事件参数</param>
        private void OnEventHandlingException(EventHandlingExceptionEventArgs args)
        {
            try
            {
                EventHandlingException?.Invoke(this, args);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"触发事件处理异常事件失败: {ex.Message}");
            }
        }

        #endregion

        #region 事件过滤

        /// <summary>
        /// 添加事件过滤器
        /// </summary>
        /// <param name="filter">事件过滤器</param>
        public void AddFilter(IEventFilter filter)
        {
            if (filter != null && !_filters.Contains(filter))
            {
                _filters.Add(filter);
                Debug.WriteLine($"添加事件过滤器: {filter.Name}");
            }
        }

        /// <summary>
        /// 移除事件过滤器
        /// </summary>
        /// <param name="filter">事件过滤器</param>
        public void RemoveFilter(IEventFilter filter)
        {
            if (filter != null && _filters.Remove(filter))
            {
                Debug.WriteLine($"移除事件过滤器: {filter.Name}");
            }
        }

        /// <summary>
        /// 清空事件过滤器
        /// </summary>
        public void ClearFilters()
        {
            var count = _filters.Count;
            _filters.Clear();
            Debug.WriteLine($"清空事件过滤器，数量: {count}");
        }

        #endregion

        #region 事件拦截

        /// <summary>
        /// 添加事件拦截器
        /// </summary>
        /// <param name="interceptor">事件拦截器</param>
        public void AddInterceptor(IEventInterceptor interceptor)
        {
            if (interceptor != null && !_interceptors.Contains(interceptor))
            {
                _interceptors.Add(interceptor);
                Debug.WriteLine($"添加事件拦截器: {interceptor.Name}");
            }
        }

        /// <summary>
        /// 移除事件拦截器
        /// </summary>
        /// <param name="interceptor">事件拦截器</param>
        public void RemoveInterceptor(IEventInterceptor interceptor)
        {
            if (interceptor != null && _interceptors.Remove(interceptor))
            {
                Debug.WriteLine($"移除事件拦截器: {interceptor.Name}");
            }
        }

        /// <summary>
        /// 清空事件拦截器
        /// </summary>
        public void ClearInterceptors()
        {
            var count = _interceptors.Count;
            _interceptors.Clear();
            Debug.WriteLine($"清空事件拦截器，数量: {count}");
        }

        #endregion

        #region 事件持久化

        /// <summary>
        /// 启用事件持久化
        /// </summary>
        /// <param name="store">事件存储</param>
        public void EnablePersistence(IEventStore store)
        {
            _eventStore = store ?? throw new ArgumentNullException(nameof(store));
            Debug.WriteLine("启用事件持久化");
        }

        /// <summary>
        /// 禁用事件持久化
        /// </summary>
        public void DisablePersistence()
        {
            _eventStore = null;
            Debug.WriteLine("禁用事件持久化");
        }

        /// <summary>
        /// 重放事件
        /// </summary>
        /// <param name="fromTimestamp">开始时间</param>
        /// <param name="toTimestamp">结束时间</param>
        /// <returns>异步任务</returns>
        public async Task ReplayEventsAsync(DateTime fromTimestamp, DateTime? toTimestamp = null)
        {
            if (_eventStore == null)
                throw new InvalidOperationException("事件持久化未启用");

            var events = await _eventStore.GetEventsAsync(fromTimestamp, toTimestamp);
            var replayCount = 0;

            foreach (var storedEvent in events)
            {
                try
                {
                    var eventType = Type.GetType(storedEvent.EventTypeName);
                    if (eventType != null)
                    {
                        var eventData = JsonConvert.DeserializeObject(storedEvent.EventData, eventType);
                        await PublishAsync(eventType, eventData);
                        replayCount++;
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"重放事件失败: {storedEvent.Id}, 错误: {ex.Message}");
                }
            }

            Debug.WriteLine($"事件重放完成，重放数量: {replayCount}");
        }

        #endregion

        #region 配置和管理

        /// <summary>
        /// 获取所有订阅信息
        /// </summary>
        /// <returns>订阅信息集合</returns>
        public IEnumerable<EventSubscriptionInfo> GetSubscriptions()
        {
            return _subscriptions.Values.SelectMany(bag => bag);
        }

        /// <summary>
        /// 获取指定事件类型的订阅信息
        /// </summary>
        /// <typeparam name="TEvent">事件类型</typeparam>
        /// <returns>订阅信息集合</returns>
        public IEnumerable<EventSubscriptionInfo> GetSubscriptions<TEvent>() where TEvent : class
        {
            return GetSubscriptions(typeof(TEvent));
        }

        /// <summary>
        /// 获取指定事件类型的订阅信息
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <returns>订阅信息集合</returns>
        public IEnumerable<EventSubscriptionInfo> GetSubscriptions(Type eventType)
        {
            if (_subscriptions.TryGetValue(eventType, out var subscriptions))
            {
                return subscriptions.ToList();
            }
            return Enumerable.Empty<EventSubscriptionInfo>();
        }

        /// <summary>
        /// 设置事件处理超时时间
        /// </summary>
        /// <param name="timeout">超时时间</param>
        public void SetHandlingTimeout(TimeSpan timeout)
        {
            _handlingTimeout = timeout;
            Debug.WriteLine($"设置事件处理超时时间: {timeout.TotalSeconds}秒");
        }

        /// <summary>
        /// 设置最大并发处理数
        /// </summary>
        /// <param name="maxConcurrency">最大并发数</param>
        public void SetMaxConcurrency(int maxConcurrency)
        {
            // 注意：这里需要重新创建信号量，但为了简化实现，我们只记录设置
            Debug.WriteLine($"设置最大并发处理数: {maxConcurrency}");
        }

        /// <summary>
        /// 启用/禁用异步处理
        /// </summary>
        /// <param name="enabled">是否启用</param>
        public void SetAsyncProcessing(bool enabled)
        {
            _asyncProcessing = enabled;
            Debug.WriteLine($"设置异步处理: {enabled}");
        }

        /// <summary>
        /// 获取事件总线统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public EventBusStatistics GetStatistics()
        {
            _statistics.ActiveSubscriptions = _subscriptions.Values
                .SelectMany(bag => bag)
                .Count(s => s.IsEnabled);

            return _statistics;
        }

        /// <summary>
        /// 重置统计信息
        /// </summary>
        public void ResetStatistics()
        {
            _statistics.TotalEventsPublished = 0;
            _statistics.TotalEventsHandled = 0;
            _statistics.TotalExceptions = 0;
            _statistics.AverageEventHandlingTime = 0;
            _statistics.LastEventPublishedAt = null;
            _statistics.LastExceptionAt = null;
            _statistics.EventTypeStatistics.Clear();

            Debug.WriteLine("重置事件总线统计信息");
        }

        #endregion

        #region IDisposable实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否正在释放</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                Clear();
                _concurrencySemaphore?.Dispose();
                _disposed = true;
                Debug.WriteLine("事件总线已释放");
            }
        }

        #endregion
    }

    /// <summary>
    /// Task扩展方法
    /// </summary>
    internal static class TaskExtensions
    {
        /// <summary>
        /// 等待任务完成或超时
        /// </summary>
        /// <param name="task">任务</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>异步任务</returns>
        public static async Task WaitAsync(this Task task, CancellationToken cancellationToken)
        {
            var tcs = new TaskCompletionSource<bool>();
            using (cancellationToken.Register(() => tcs.TrySetCanceled()))
            {
                var completedTask = await Task.WhenAny(task, tcs.Task);
                if (completedTask == tcs.Task)
                {
                    throw new OperationCanceledException(cancellationToken);
                }
                await task; // 重新等待原任务以获取可能的异常
            }
        }
    }
}
