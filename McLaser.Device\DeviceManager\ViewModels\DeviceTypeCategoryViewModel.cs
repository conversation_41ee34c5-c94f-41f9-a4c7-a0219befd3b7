#nullable enable
using System.Collections.ObjectModel;
using System.Linq;
using McLaser.Core.Common;
using McLaser.Device;

namespace McLaser.Device.ViewModels
{
     


    public partial class DeviceTypeCategoryViewModel : ObservableObject
    {
        /// <summary>
        /// 设备类别
        /// </summary>
        private DeviceCategory _category;
        public DeviceCategory Category
        {
            get => _category;
            set { Set(ref _category, value); }  
        }

        /// <summary>
        /// 类别名称
        /// </summary>
        private string _categoryName = string.Empty;
        public string CategoryName
        {
            get => _categoryName;
            set { Set(ref _categoryName, value); }
        }

        /// <summary>
        /// 类别图标
        /// </summary>
        private string _categoryIcon = string.Empty;
        public string CategoryIcon
        {
            get => _categoryIcon;
            set { Set(ref _categoryIcon, value); }
        }


        /// <summary>
        /// 设备类型列表
        /// </summary>
        public ObservableCollection<DeviceTypeItemViewModel> DeviceTypes { get; } = new();

        /// <summary>
        /// 设备数量
        /// </summary>
        public int DeviceCount => DeviceTypes.Count;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="category">设备类别</param>
        public DeviceTypeCategoryViewModel(DeviceCategory category)
        {
            Category = category;
            CategoryName = GetCategoryDisplayName(category);
            CategoryIcon = GetCategoryIcon(category);
        }

        /// <summary>
        /// 添加设备类型
        /// </summary>
        /// <param name="metadata">设备类型元数据</param>
        public void AddDeviceType(DeviceTypeMetadata metadata)
        {
            var item = new DeviceTypeItemViewModel(metadata);
            DeviceTypes.Add(item);
            OnPropertyChanged(nameof(DeviceCount));
        }

        /// <summary>
        /// 清空设备类型
        /// </summary>
        public void ClearDeviceTypes()
        {
            DeviceTypes.Clear();
            OnPropertyChanged(nameof(DeviceCount));
        }

        /// <summary>
        /// 获取类别显示名称
        /// </summary>
        /// <param name="category">设备类别</param>
        /// <returns>显示名称</returns>
        private static string GetCategoryDisplayName(DeviceCategory category)
        {
            return category switch
            {
                DeviceCategory.Camera => "相机设备",
                DeviceCategory.MotionController => "运动控制器",
                DeviceCategory.Laser => "激光器设备",
                DeviceCategory.Sensor => "传感器设备",
                DeviceCategory.BarcodeReader => "条码读取器",
                DeviceCategory.NetworkDevice => "网络设备",
                DeviceCategory.SerialDevice => "串口设备",
                _ => "其他设备"
            };
        }

        /// <summary>
        /// 获取类别图标
        /// </summary>
        /// <param name="category">设备类别</param>
        /// <returns>图标字符</returns>
        private static string GetCategoryIcon(DeviceCategory category)
        {
            return category switch
            {
                DeviceCategory.Camera => "📷",
                DeviceCategory.MotionController => "🎛️",
                DeviceCategory.Laser => "🔴",
                DeviceCategory.Sensor => "📡",
                DeviceCategory.BarcodeReader => "📊",
                DeviceCategory.NetworkDevice => "🌐",
                DeviceCategory.SerialDevice => "🔌",
                _ => "❓"
            };
        }
    }
}
