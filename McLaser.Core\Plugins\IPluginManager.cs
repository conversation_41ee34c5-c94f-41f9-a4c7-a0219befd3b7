using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace McLaser.Core.Plugins
{
    /// <summary>
    /// 插件管理器接口
    /// 负责插件的生命周期管理
    /// </summary>
    public interface IPluginManager : IDisposable
    {
        /// <summary>
        /// 已加载的插件列表
        /// </summary>
        IReadOnlyList<IPlugin> LoadedPlugins { get; }

        /// <summary>
        /// 可用的插件列表
        /// </summary>
        IReadOnlyList<PluginInfo> AvailablePlugins { get; }

        /// <summary>
        /// 插件目录
        /// </summary>
        string PluginDirectory { get; set; }

        /// <summary>
        /// 是否启用插件隔离
        /// </summary>
        bool EnablePluginIsolation { get; set; }

        /// <summary>
        /// 是否启用热更新
        /// </summary>
        bool EnableHotSwap { get; set; }

        /// <summary>
        /// 插件加载事件
        /// </summary>
        event EventHandler<PluginLoadedEventArgs> PluginLoaded;

        /// <summary>
        /// 插件卸载事件
        /// </summary>
        event EventHandler<PluginUnloadedEventArgs> PluginUnloaded;

        /// <summary>
        /// 插件启动事件
        /// </summary>
        event EventHandler<PluginStartedEventArgs> PluginStarted;

        /// <summary>
        /// 插件停止事件
        /// </summary>
        event EventHandler<PluginStoppedEventArgs> PluginStopped;

        /// <summary>
        /// 插件错误事件
        /// </summary>
        event EventHandler<PluginErrorEventArgs> PluginError;

        /// <summary>
        /// 初始化插件管理器
        /// </summary>
        /// <param name="context">应用程序上下文</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>初始化任务</returns>
        Task InitializeAsync(object context, CancellationToken cancellationToken = default);

        /// <summary>
        /// 扫描插件目录
        /// </summary>
        /// <param name="directory">目录路径</param>
        /// <param name="recursive">是否递归扫描</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>扫描任务</returns>
        Task<IList<PluginInfo>> ScanPluginsAsync(string? directory = null, bool recursive = true, CancellationToken cancellationToken = default);

        /// <summary>
        /// 加载插件
        /// </summary>
        /// <param name="pluginPath">插件路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>加载任务</returns>
        Task<IPlugin?> LoadPluginAsync(string pluginPath, CancellationToken cancellationToken = default);

        /// <summary>
        /// 加载插件
        /// </summary>
        /// <param name="pluginInfo">插件信息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>加载任务</returns>
        Task<IPlugin?> LoadPluginAsync(PluginInfo pluginInfo, CancellationToken cancellationToken = default);

        /// <summary>
        /// 卸载插件
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>卸载任务</returns>
        Task<bool> UnloadPluginAsync(string pluginId, CancellationToken cancellationToken = default);

        /// <summary>
        /// 卸载插件
        /// </summary>
        /// <param name="plugin">插件实例</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>卸载任务</returns>
        Task<bool> UnloadPluginAsync(IPlugin plugin, CancellationToken cancellationToken = default);

        /// <summary>
        /// 启动插件
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>启动任务</returns>
        Task<bool> StartPluginAsync(string pluginId, CancellationToken cancellationToken = default);

        /// <summary>
        /// 停止插件
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>停止任务</returns>
        Task<bool> StopPluginAsync(string pluginId, CancellationToken cancellationToken = default);

        /// <summary>
        /// 重启插件
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>重启任务</returns>
        Task<bool> RestartPluginAsync(string pluginId, CancellationToken cancellationToken = default);

        /// <summary>
        /// 启动所有插件
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>启动任务</returns>
        Task StartAllPluginsAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 停止所有插件
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>停止任务</returns>
        Task StopAllPluginsAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取插件
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <returns>插件实例</returns>
        IPlugin? GetPlugin(string pluginId);

        /// <summary>
        /// 获取插件
        /// </summary>
        /// <typeparam name="T">插件类型</typeparam>
        /// <returns>插件实例</returns>
        T? GetPlugin<T>() where T : class, IPlugin;

        /// <summary>
        /// 获取插件
        /// </summary>
        /// <param name="pluginType">插件类型</param>
        /// <returns>插件实例</returns>
        IPlugin? GetPlugin(Type pluginType);

        /// <summary>
        /// 检查插件是否已加载
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <returns>是否已加载</returns>
        bool IsPluginLoaded(string pluginId);

        /// <summary>
        /// 检查插件是否正在运行
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <returns>是否正在运行</returns>
        bool IsPluginRunning(string pluginId);

        /// <summary>
        /// 验证插件
        /// </summary>
        /// <param name="pluginPath">插件路径</param>
        /// <returns>验证结果</returns>
        Task<PluginValidationResult> ValidatePluginAsync(string pluginPath);

        /// <summary>
        /// 安装插件
        /// </summary>
        /// <param name="pluginPackagePath">插件包路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>安装任务</returns>
        Task<bool> InstallPluginAsync(string pluginPackagePath, CancellationToken cancellationToken = default);

        /// <summary>
        /// 卸载插件
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <param name="removeFiles">是否删除文件</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>卸载任务</returns>
        Task<bool> UninstallPluginAsync(string pluginId, bool removeFiles = true, CancellationToken cancellationToken = default);

        /// <summary>
        /// 更新插件
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <param name="newPluginPackagePath">新插件包路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>更新任务</returns>
        Task<bool> UpdatePluginAsync(string pluginId, string newPluginPackagePath, CancellationToken cancellationToken = default);

        /// <summary>
        /// 热更新插件
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <param name="newPluginPath">新插件路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>热更新任务</returns>
        Task<bool> HotSwapPluginAsync(string pluginId, string newPluginPath, CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取插件依赖关系
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <returns>依赖关系</returns>
        IList<PluginDependency> GetPluginDependencies(string pluginId);

        /// <summary>
        /// 解析插件依赖关系
        /// </summary>
        /// <param name="plugins">插件列表</param>
        /// <returns>解析结果</returns>
        PluginDependencyResolutionResult ResolveDependencies(IList<PluginInfo> plugins);

        /// <summary>
        /// 获取插件加载顺序
        /// </summary>
        /// <param name="plugins">插件列表</param>
        /// <returns>加载顺序</returns>
        IList<PluginInfo> GetLoadOrder(IList<PluginInfo> plugins);

        /// <summary>
        /// 获取插件统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        PluginManagerStatistics GetStatistics();

        /// <summary>
        /// 重置统计信息
        /// </summary>
        void ResetStatistics();

        /// <summary>
        /// 导出插件配置
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>导出任务</returns>
        Task ExportConfigurationAsync(string filePath, CancellationToken cancellationToken = default);

        /// <summary>
        /// 导入插件配置
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>导入任务</returns>
        Task ImportConfigurationAsync(string filePath, CancellationToken cancellationToken = default);

        /// <summary>
        /// 备份插件
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <param name="backupPath">备份路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>备份任务</returns>
        Task<bool> BackupPluginAsync(string pluginId, string backupPath, CancellationToken cancellationToken = default);

        /// <summary>
        /// 恢复插件
        /// </summary>
        /// <param name="backupPath">备份路径</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>恢复任务</returns>
        Task<bool> RestorePluginAsync(string backupPath, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// 插件管理器配置接口
    /// </summary>
    public interface IPluginManagerConfiguration
    {
        /// <summary>
        /// 插件目录
        /// </summary>
        string PluginDirectory { get; set; }

        /// <summary>
        /// 数据目录
        /// </summary>
        string DataDirectory { get; set; }

        /// <summary>
        /// 配置目录
        /// </summary>
        string ConfigurationDirectory { get; set; }

        /// <summary>
        /// 备份目录
        /// </summary>
        string BackupDirectory { get; set; }

        /// <summary>
        /// 是否启用插件隔离
        /// </summary>
        bool EnablePluginIsolation { get; set; }

        /// <summary>
        /// 是否启用热更新
        /// </summary>
        bool EnableHotSwap { get; set; }

        /// <summary>
        /// 是否启用依赖检查
        /// </summary>
        bool EnableDependencyCheck { get; set; }

        /// <summary>
        /// 是否启用安全检查
        /// </summary>
        bool EnableSecurityCheck { get; set; }

        /// <summary>
        /// 插件加载超时时间（毫秒）
        /// </summary>
        int LoadTimeout { get; set; }

        /// <summary>
        /// 插件启动超时时间（毫秒）
        /// </summary>
        int StartTimeout { get; set; }

        /// <summary>
        /// 插件停止超时时间（毫秒）
        /// </summary>
        int StopTimeout { get; set; }

        /// <summary>
        /// 最大并发加载数
        /// </summary>
        int MaxConcurrentLoads { get; set; }

        /// <summary>
        /// 允许的插件文件扩展名
        /// </summary>
        string[] AllowedExtensions { get; set; }

        /// <summary>
        /// 黑名单插件ID列表
        /// </summary>
        string[] BlacklistedPlugins { get; set; }

        /// <summary>
        /// 白名单插件ID列表
        /// </summary>
        string[] WhitelistedPlugins { get; set; }
    }
}
