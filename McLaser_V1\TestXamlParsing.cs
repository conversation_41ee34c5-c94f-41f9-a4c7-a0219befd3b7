using System;
using System.Windows;
using System.Windows.Markup;
using System.IO;

/// <summary>
/// 简单的XAML解析测试程序
/// </summary>
class TestXamlParsing
{
    [STAThread]
    static void Main()
    {
        try
        {
            Console.WriteLine("开始测试XAML解析...");
            
            // 测试简单的XAML解析
            string xamlContent = @"
<Window xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
        xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
        Title=""测试窗口"" Height=""300"" Width=""400"">
    <Grid>
        <TextBlock Text=""Hello World"" HorizontalAlignment=""Center"" VerticalAlignment=""Center""/>
    </Grid>
</Window>";

            using (var stringReader = new StringReader(xamlContent))
            {
                var window = (Window)XamlReader.Load(stringReader);
                Console.WriteLine("XAML解析成功！");
                
                // 创建应用程序实例
                var app = new Application();
                app.Run(window);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"XAML解析失败: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
            Console.ReadKey();
        }
    }
}
