using System;
using System.Globalization;
using System.Windows.Data;

namespace McLaser.Devices
{
    /// <summary>
    /// Null值转可见性转换器
    /// </summary>
    [ValueConversion(typeof(object), typeof(System.Windows.Visibility))]
    public class NullToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool invert = parameter != null && parameter.ToString().ToLower() == "invert";
            bool isNull = value == null;
            
            if (invert)
                return isNull ? System.Windows.Visibility.Visible : System.Windows.Visibility.Collapsed;
            else
                return isNull ? System.Windows.Visibility.Collapsed : System.Windows.Visibility.Visible;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 