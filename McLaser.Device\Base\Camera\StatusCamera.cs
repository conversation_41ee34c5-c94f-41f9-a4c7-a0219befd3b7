using System;
using Mc<PERSON><PERSON><PERSON>.Device;

namespace McLaser.Devices
{
    /// <summary>
    /// 相机状态类
    /// 继承自设备状态基类，增加相机特有的状态信息
    /// </summary>
    [Serializable]
    public class StatusCamera : StatusDevice
    {
        /// <summary>
        /// 是否开始采集
        /// </summary>
        public bool IsStart = false;

        /// <summary>
        /// 是否单次采集
        /// </summary>
        public bool IsGrapOnce = false;

        /// <summary>
        /// 是否连续采集
        /// </summary>
        public bool IsGrapContinue = false;

        /// <summary>
        /// 是否采集错误
        /// </summary>
        public bool IsGrapError = false;

        /// <summary>
        /// 当前曝光时间
        /// </summary>
        public double Exposure = 0;

        /// <summary>
        /// 当前增益值
        /// </summary>
        public double Gain = 0;

        /// <summary>
        /// 构造函数
        /// </summary>
        public StatusCamera()
        {
        }

        /// <summary>
        /// 重置采集状态
        /// </summary>
        public void ResetGrabStatus()
        {
            IsGrapOnce = false;
            IsGrapContinue = false;
            IsGrapError = false;
        }

        /// <summary>
        /// 设置采集错误状态
        /// </summary>
        /// <param name="hasError">是否有错误</param>
        public void SetGrabError(bool hasError)
        {
            IsGrapError = hasError;
            if (hasError)
            {
                // 如果有错误，停止采集
                IsGrapOnce = false;
                IsGrapContinue = false;
            }
        }

        /// <summary>
        /// 重写ToString方法
        /// </summary>
        /// <returns>状态信息字符串</returns>
        public override string ToString()
        {
            return $"Camera Status - Open: {IsOpen}, Start: {IsStart}, " +
                   $"GrabOnce: {IsGrapOnce}, GrabContinue: {IsGrapContinue}, " +
                   $"Error: {IsGrapError}, Exposure: {Exposure}, Gain: {Gain}";
        }
    }
}
