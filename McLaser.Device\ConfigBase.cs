using System;
using System.ComponentModel;
using McLaser.Core.Common;
 

namespace McLaser.Device
{
    /// <summary>
    /// 配置参数基类
    /// 为设备配置提供基础属性和功能
    /// </summary>
    [Serializable]
    public class ConfigBase : ObservableObject
    {
        /// <summary>
        /// 配置名称
        /// 这里使用虚属性，在子类有扩展时可以重写
        /// </summary>
        [Category("基本"), DisplayName("名称")]
        public virtual string Name { get; set; } = "配置名称";
    }

    /// <summary>
    /// 扩展配置基类
    /// 在基础配置上增加启用状态等扩展属性
    /// </summary>
    [Serializable]
    public class ConfigBaseEx : ConfigBase
    {
        /// <summary>
        /// 是否启用
        /// 这里使用虚属性，在子类有扩展时可以重写
        /// </summary>
        [Category("基本"), DisplayName("是否启用")]
        public virtual bool IsUse { get; set; } = true;
    }
}
