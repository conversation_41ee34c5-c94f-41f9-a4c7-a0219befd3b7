using System;
using System.Configuration;
using System.Windows;
using McLaser.App.Core;
using McLaser.App.Views;
using McLaser.Core.App;
using McLaser.Core.Configuration;
using McLaser.Core.Framework;

namespace McLaser.App
{
 
    public partial class App : ApplicationBase
    {

        protected override Window CreateMainWindow(StartupEventArgs e)
        {
            return base.CreateMainWindow(e);
        }


        public void Initialize()
        {
            try
            {
                // 禁用UI自动化（如果配置中启用）
                DisableUIAutomationIfConfigured();

             
            }
            catch (Exception ex)
            {
                // 显示启动错误
                MessageBox.Show(
                    $"应用程序初始化失败：\n\n{ex.Message}\n\n详细信息：\n{ex}",
                    "McLaser示例应用程序 - 初始化错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);

                // 关闭应用程序
               // Shutdown(1);
            }
        }

        private void DisableUIAutomationIfConfigured()
        {
            try
            {
                //var disableUIAutomation = ConfigurationManager.AppSettings["DisableUIAutomation"];
                //var disableXamlDiagnostics = ConfigurationManager.AppSettings["DISABLE_XAMLDIAGNOSTICS"];

                //if (bool.TryParse(disableUIAutomation, out bool disable) && disable)
                //{
                //    // 设置环境变量禁用UI自动化
                //    Environment.SetEnvironmentVariable("UIAutomationCore.DisableUIAutomation", "true");
                //}

                //if (!string.IsNullOrEmpty(disableXamlDiagnostics))
                //{
                //    Environment.SetEnvironmentVariable("DISABLE_XAMLDIAGNOSTICS", disableXamlDiagnostics);
                //}
            }
            catch (Exception ex)
            {
                // 忽略配置读取错误，但记录到调试输出
                System.Diagnostics.Debug.WriteLine($"读取UI自动化配置失败: {ex.Message}");
            }
        }

      
        private void Application_Exit(object sender, ExitEventArgs e)
        {
            try
            {
                // 先调用Shutdown进行正常清理
                //_appCore?.Shutdown();

                // 再调用Dispose释放资源
                //_appCore?.Dispose();

                // 强制垃圾回收
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
            }
            catch (Exception ex)
            {
                // 记录清理错误（但不阻止应用程序退出）
                System.Diagnostics.Debug.WriteLine($"应用程序清理时发生错误: {ex.Message}");
            }
            finally
            {
                // 确保应用程序退出
                Environment.Exit(e.ApplicationExitCode);
            }
        }

        private void Application_DispatcherUnhandledException(object sender, 
            System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            try
            {
                // 记录异常
                var message = $"未处理的异常：{e.Exception.Message}";
                System.Diagnostics.Debug.WriteLine(message);

                // 显示错误对话框
                var result = MessageBox.Show(
                    $"{message}\n\n是否继续运行应用程序？\n\n点击\"是\"继续，点击\"否\"退出应用程序。",
                    "McLaser示例应用程序 - 未处理异常",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Error);

                if (result == MessageBoxResult.Yes)
                {
                    // 标记异常已处理，继续运行
                    e.Handled = true;
                }
                else
                {
                    // 退出应用程序
                    Shutdown(1);
                }
            }
            catch
            {
                // 如果异常处理本身出错，直接退出
                Shutdown(1);
            }
        }
    }
}
