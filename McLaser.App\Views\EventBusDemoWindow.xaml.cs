using System.Windows;
using McLaser.App.ViewModels;

namespace McLaser.App.Views
{
    /// <summary>
    /// EventBus事件总线演示窗口
    /// 展示McLaser.Core中EventBus模块的实际使用
    /// </summary>
    public partial class EventBusDemoWindow : Window
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public EventBusDemoWindow()
        {
            InitializeComponent();
            
            // 设置DataContext
            if (DataContext == null)
            {
                DataContext = new EventBusDemoViewModel();
            }
        }

        /// <summary>
        /// 构造函数（带ViewModel）
        /// </summary>
        /// <param name="viewModel">ViewModel实例</param>
        public EventBusDemoWindow(EventBusDemoViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
        }

        /// <summary>
        /// 窗口关闭事件处理
        /// </summary>
        /// <param name="e">事件参数</param>
        protected override void OnClosed(System.EventArgs e)
        {
            // 清理资源
            if (DataContext is EventBusDemoViewModel viewModel)
            {
                viewModel.Dispose();
            }
            
            base.OnClosed(e);
        }
    }
}
