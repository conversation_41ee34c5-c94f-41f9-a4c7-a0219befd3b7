#nullable enable
using System;
using System.Collections.Generic;
using System.Windows.Input;

namespace McLaser.App.Models
{
    /// <summary>
    /// 导航项模型
    /// 表示底部导航栏中的一个导航按钮
    /// </summary>
    public class NavigationItem
    {
        /// <summary>
        /// 导航项唯一标识
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 显示名称
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 图标路径或资源键
        /// </summary>
        public string Icon { get; set; } = string.Empty;

        /// <summary>
        /// 导航分类
        /// </summary>
        public NavigationCategory Category { get; set; } = NavigationCategory.Main;

        /// <summary>
        /// 是否为分类按钮（包含多个子页面）
        /// </summary>
        public bool IsCategory { get; set; }

        /// <summary>
        /// 子页面列表（当IsCategory为true时使用）
        /// </summary>
        public List<PageInfo> SubPages { get; set; } = new List<PageInfo>();

        /// <summary>
        /// 单页面信息（当IsCategory为false时使用）
        /// </summary>
        public PageInfo? PageInfo { get; set; }

        /// <summary>
        /// 点击命令
        /// </summary>
        public ICommand? Command { get; set; }

        /// <summary>
        /// 命令参数
        /// </summary>
        public object? CommandParameter { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 是否选中
        /// </summary>
        public bool IsSelected { get; set; }

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// 工具提示
        /// </summary>
        public string ToolTip { get; set; } = string.Empty;
    }
}
