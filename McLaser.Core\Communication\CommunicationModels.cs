using System;
using System.Collections.Generic;
using System.Net;

namespace McLaser.Core.Communication
{
    /// <summary>
    /// 通信通道类型枚举
    /// </summary>
    public enum CommunicationChannelType
    {
        /// <summary>
        /// TCP通信
        /// </summary>
        Tcp,

        /// <summary>
        /// UDP通信
        /// </summary>
        Udp,

        /// <summary>
        /// 串口通信
        /// </summary>
        SerialPort,

        /// <summary>
        /// USB通信
        /// </summary>
        Usb,

        /// <summary>
        /// 蓝牙通信
        /// </summary>
        Bluetooth,

        /// <summary>
        /// 无线通信
        /// </summary>
        Wireless,

        /// <summary>
        /// 自定义通信
        /// </summary>
        Custom
    }

    /// <summary>
    /// 连接状态枚举
    /// </summary>
    public enum ConnectionState
    {
        /// <summary>
        /// 已断开
        /// </summary>
        Disconnected,

        /// <summary>
        /// 正在连接
        /// </summary>
        Connecting,

        /// <summary>
        /// 已连接
        /// </summary>
        Connected,

        /// <summary>
        /// 正在断开
        /// </summary>
        Disconnecting,

        /// <summary>
        /// 连接错误
        /// </summary>
        Error
    }

    /// <summary>
    /// 压缩算法枚举
    /// </summary>
    public enum CompressionAlgorithm
    {
        /// <summary>
        /// 无压缩
        /// </summary>
        None,

        /// <summary>
        /// GZip压缩
        /// </summary>
        GZip,

        /// <summary>
        /// Deflate压缩
        /// </summary>
        Deflate,

        /// <summary>
        /// LZ4压缩
        /// </summary>
        LZ4,

        /// <summary>
        /// Brotli压缩
        /// </summary>
        Brotli
    }

    /// <summary>
    /// 压缩级别枚举
    /// </summary>
    public enum CompressionLevel
    {
        /// <summary>
        /// 最快速度
        /// </summary>
        Fastest,

        /// <summary>
        /// 最优压缩
        /// </summary>
        Optimal,

        /// <summary>
        /// 无压缩
        /// </summary>
        NoCompression
    }

    /// <summary>
    /// 加密算法枚举
    /// </summary>
    public enum EncryptionAlgorithm
    {
        /// <summary>
        /// 无加密
        /// </summary>
        None,

        /// <summary>
        /// AES加密
        /// </summary>
        AES,

        /// <summary>
        /// DES加密
        /// </summary>
        DES,

        /// <summary>
        /// 3DES加密
        /// </summary>
        TripleDES,

        /// <summary>
        /// RSA加密
        /// </summary>
        RSA
    }

    /// <summary>
    /// 消息类型枚举
    /// </summary>
    public enum MessageType
    {
        /// <summary>
        /// 数据消息
        /// </summary>
        Data,

        /// <summary>
        /// 控制消息
        /// </summary>
        Control,

        /// <summary>
        /// 心跳消息
        /// </summary>
        Heartbeat,

        /// <summary>
        /// 确认消息
        /// </summary>
        Acknowledgment,

        /// <summary>
        /// 错误消息
        /// </summary>
        Error,

        /// <summary>
        /// 状态消息
        /// </summary>
        Status
    }

    /// <summary>
    /// 通信配置类
    /// </summary>
    public class CommunicationConfig
    {
        /// <summary>
        /// 配置名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 通道类型
        /// </summary>
        public CommunicationChannelType ChannelType { get; set; }

        /// <summary>
        /// 连接超时时间（毫秒）
        /// </summary>
        public int ConnectionTimeout { get; set; } = 5000;

        /// <summary>
        /// 接收超时时间（毫秒）
        /// </summary>
        public int ReceiveTimeout { get; set; } = 3000;

        /// <summary>
        /// 发送超时时间（毫秒）
        /// </summary>
        public int SendTimeout { get; set; } = 3000;

        /// <summary>
        /// 接收缓冲区大小
        /// </summary>
        public int ReceiveBufferSize { get; set; } = 4096;

        /// <summary>
        /// 发送缓冲区大小
        /// </summary>
        public int SendBufferSize { get; set; } = 4096;

        /// <summary>
        /// 是否启用Nagle算法
        /// </summary>
        public bool NoDelay { get; set; } = true;

        /// <summary>
        /// 是否保持连接活跃
        /// </summary>
        public bool KeepAlive { get; set; } = true;

        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> Properties { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 获取属性值
        /// </summary>
        /// <typeparam name="T">属性类型</typeparam>
        /// <param name="key">属性键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>属性值</returns>
        public T GetProperty<T>(string key, T defaultValue = default!)
        {
            if (Properties.TryGetValue(key, out var value) && value is T typedValue)
            {
                return typedValue;
            }
            return defaultValue;
        }

        /// <summary>
        /// 设置属性值
        /// </summary>
        /// <param name="key">属性键</param>
        /// <param name="value">属性值</param>
        public void SetProperty(string key, object value)
        {
            Properties[key] = value;
        }
    }

    /// <summary>
    /// TCP配置类
    /// </summary>
    public class TcpConfig : CommunicationConfig
    {
        /// <summary>
        /// 服务器地址
        /// </summary>
        public string Host { get; set; } = "localhost";

        /// <summary>
        /// 端口号
        /// </summary>
        public int Port { get; set; } = 8080;

        /// <summary>
        /// 本地端点
        /// </summary>
        public IPEndPoint? LocalEndPoint { get; set; }

        /// <summary>
        /// 是否作为服务器
        /// </summary>
        public bool IsServer { get; set; } = false;

        /// <summary>
        /// 最大连接数（服务器模式）
        /// </summary>
        public int MaxConnections { get; set; } = 100;

        public TcpConfig()
        {
            ChannelType = CommunicationChannelType.Tcp;
        }
    }

    /// <summary>
    /// UDP配置类
    /// </summary>
    public class UdpConfig : CommunicationConfig
    {
        /// <summary>
        /// 本地端口
        /// </summary>
        public int LocalPort { get; set; } = 0;

        /// <summary>
        /// 远程地址
        /// </summary>
        public string RemoteHost { get; set; } = string.Empty;

        /// <summary>
        /// 远程端口
        /// </summary>
        public int RemotePort { get; set; } = 0;

        /// <summary>
        /// 是否启用广播
        /// </summary>
        public bool EnableBroadcast { get; set; } = false;

        /// <summary>
        /// 是否启用多播
        /// </summary>
        public bool EnableMulticast { get; set; } = false;

        /// <summary>
        /// 多播地址
        /// </summary>
        public string MulticastAddress { get; set; } = string.Empty;

        public UdpConfig()
        {
            ChannelType = CommunicationChannelType.Udp;
        }
    }

    /// <summary>
    /// 串口配置类
    /// </summary>
    public class SerialPortConfig : CommunicationConfig
    {
        /// <summary>
        /// 端口名称
        /// </summary>
        public string PortName { get; set; } = "COM1";

        /// <summary>
        /// 波特率
        /// </summary>
        public int BaudRate { get; set; } = 9600;

        /// <summary>
        /// 数据位
        /// </summary>
        public int DataBits { get; set; } = 8;

        /// <summary>
        /// 停止位
        /// </summary>
        public System.IO.Ports.StopBits StopBits { get; set; } = System.IO.Ports.StopBits.One;

        /// <summary>
        /// 奇偶校验
        /// </summary>
        public System.IO.Ports.Parity Parity { get; set; } = System.IO.Ports.Parity.None;

        /// <summary>
        /// 流控制
        /// </summary>
        public System.IO.Ports.Handshake Handshake { get; set; } = System.IO.Ports.Handshake.None;

        /// <summary>
        /// DTR启用
        /// </summary>
        public bool DtrEnable { get; set; } = false;

        /// <summary>
        /// RTS启用
        /// </summary>
        public bool RtsEnable { get; set; } = false;

        public SerialPortConfig()
        {
            ChannelType = CommunicationChannelType.SerialPort;
        }
    }

    /// <summary>
    /// 连接状态变更事件参数
    /// </summary>
    public class ConnectionStateChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 旧状态
        /// </summary>
        public ConnectionState OldState { get; }

        /// <summary>
        /// 新状态
        /// </summary>
        public ConnectionState NewState { get; }

        /// <summary>
        /// 变更时间
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// 错误信息（如果有）
        /// </summary>
        public string? ErrorMessage { get; }

        public ConnectionStateChangedEventArgs(ConnectionState oldState, ConnectionState newState, string? errorMessage = null)
        {
            OldState = oldState;
            NewState = newState;
            Timestamp = DateTime.Now;
            ErrorMessage = errorMessage;
        }
    }

    /// <summary>
    /// 数据接收事件参数
    /// </summary>
    public class DataReceivedEventArgs : EventArgs
    {
        /// <summary>
        /// 接收到的数据
        /// </summary>
        public byte[] Data { get; }

        /// <summary>
        /// 数据长度
        /// </summary>
        public int Length { get; }

        /// <summary>
        /// 接收时间
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// 远程端点（如果适用）
        /// </summary>
        public EndPoint? RemoteEndPoint { get; }

        public DataReceivedEventArgs(byte[] data, int length, EndPoint? remoteEndPoint = null)
        {
            Data = data;
            Length = length;
            Timestamp = DateTime.Now;
            RemoteEndPoint = remoteEndPoint;
        }
    }

    /// <summary>
    /// 通信错误事件参数
    /// </summary>
    public class CommunicationErrorEventArgs : EventArgs
    {
        /// <summary>
        /// 错误类型
        /// </summary>
        public CommunicationErrorType ErrorType { get; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; }

        /// <summary>
        /// 异常对象
        /// </summary>
        public Exception? Exception { get; }

        /// <summary>
        /// 错误时间
        /// </summary>
        public DateTime Timestamp { get; }

        /// <summary>
        /// 是否可恢复
        /// </summary>
        public bool IsRecoverable { get; }

        public CommunicationErrorEventArgs(CommunicationErrorType errorType, string errorMessage, Exception? exception = null, bool isRecoverable = false)
        {
            ErrorType = errorType;
            ErrorMessage = errorMessage;
            Exception = exception;
            Timestamp = DateTime.Now;
            IsRecoverable = isRecoverable;
        }
    }

    /// <summary>
    /// 通信错误类型枚举
    /// </summary>
    public enum CommunicationErrorType
    {
        /// <summary>
        /// 连接错误
        /// </summary>
        ConnectionError,

        /// <summary>
        /// 发送错误
        /// </summary>
        SendError,

        /// <summary>
        /// 接收错误
        /// </summary>
        ReceiveError,

        /// <summary>
        /// 超时错误
        /// </summary>
        TimeoutError,

        /// <summary>
        /// 协议错误
        /// </summary>
        ProtocolError,

        /// <summary>
        /// 配置错误
        /// </summary>
        ConfigurationError,

        /// <summary>
        /// 未知错误
        /// </summary>
        UnknownError
    }

    /// <summary>
    /// 心跳状态变更事件参数
    /// </summary>
    public class HeartbeatStateChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 是否活跃
        /// </summary>
        public bool IsAlive { get; }

        /// <summary>
        /// 最后心跳时间
        /// </summary>
        public DateTime LastHeartbeat { get; }

        /// <summary>
        /// 心跳间隔
        /// </summary>
        public TimeSpan Interval { get; }

        public HeartbeatStateChangedEventArgs(bool isAlive, DateTime lastHeartbeat, TimeSpan interval)
        {
            IsAlive = isAlive;
            LastHeartbeat = lastHeartbeat;
            Interval = interval;
        }
    }

    /// <summary>
    /// 重连状态变更事件参数
    /// </summary>
    public class ReconnectStateChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 是否正在重连
        /// </summary>
        public bool IsReconnecting { get; }

        /// <summary>
        /// 当前重连次数
        /// </summary>
        public int AttemptCount { get; }

        /// <summary>
        /// 最大重连次数
        /// </summary>
        public int MaxAttempts { get; }

        /// <summary>
        /// 下次重连时间
        /// </summary>
        public DateTime? NextAttemptTime { get; }

        public ReconnectStateChangedEventArgs(bool isReconnecting, int attemptCount, int maxAttempts, DateTime? nextAttemptTime = null)
        {
            IsReconnecting = isReconnecting;
            AttemptCount = attemptCount;
            MaxAttempts = maxAttempts;
            NextAttemptTime = nextAttemptTime;
        }
    }

    /// <summary>
    /// 通信统计信息
    /// </summary>
    public class CommunicationStatistics
    {
        /// <summary>
        /// 连接时间
        /// </summary>
        public DateTime? ConnectedTime { get; set; }

        /// <summary>
        /// 总连接时长
        /// </summary>
        public TimeSpan TotalConnectedTime { get; set; }

        /// <summary>
        /// 发送字节数
        /// </summary>
        public long BytesSent { get; set; }

        /// <summary>
        /// 接收字节数
        /// </summary>
        public long BytesReceived { get; set; }

        /// <summary>
        /// 发送消息数
        /// </summary>
        public long MessagesSent { get; set; }

        /// <summary>
        /// 接收消息数
        /// </summary>
        public long MessagesReceived { get; set; }

        /// <summary>
        /// 错误次数
        /// </summary>
        public long ErrorCount { get; set; }

        /// <summary>
        /// 重连次数
        /// </summary>
        public long ReconnectCount { get; set; }

        /// <summary>
        /// 最后活动时间
        /// </summary>
        public DateTime LastActivity { get; set; }

        /// <summary>
        /// 平均发送速度（字节/秒）
        /// </summary>
        public double AverageSendSpeed => TotalConnectedTime.TotalSeconds > 0 ? BytesSent / TotalConnectedTime.TotalSeconds : 0;

        /// <summary>
        /// 平均接收速度（字节/秒）
        /// </summary>
        public double AverageReceiveSpeed => TotalConnectedTime.TotalSeconds > 0 ? BytesReceived / TotalConnectedTime.TotalSeconds : 0;

        /// <summary>
        /// 重置统计信息
        /// </summary>
        public void Reset()
        {
            ConnectedTime = null;
            TotalConnectedTime = TimeSpan.Zero;
            BytesSent = 0;
            BytesReceived = 0;
            MessagesSent = 0;
            MessagesReceived = 0;
            ErrorCount = 0;
            ReconnectCount = 0;
            LastActivity = DateTime.Now;
        }
    }

    /// <summary>
    /// 消息接口
    /// </summary>
    public interface IMessage
    {
        /// <summary>
        /// 消息ID
        /// </summary>
        string MessageId { get; set; }

        /// <summary>
        /// 消息类型
        /// </summary>
        MessageType MessageType { get; set; }

        /// <summary>
        /// 消息内容
        /// </summary>
        byte[] Content { get; set; }

        /// <summary>
        /// 消息头
        /// </summary>
        MessageHeader Header { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        DateTime CreatedTime { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        DateTime? ExpiryTime { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        MessagePriority Priority { get; set; }

        /// <summary>
        /// 是否需要确认
        /// </summary>
        bool RequiresAcknowledgment { get; set; }

        /// <summary>
        /// 扩展属性
        /// </summary>
        Dictionary<string, object> Properties { get; set; }

        /// <summary>
        /// 获取消息大小
        /// </summary>
        /// <returns>消息大小（字节）</returns>
        int GetSize();

        /// <summary>
        /// 验证消息
        /// </summary>
        /// <returns>验证结果</returns>
        bool IsValid();

        /// <summary>
        /// 克隆消息
        /// </summary>
        /// <returns>消息副本</returns>
        IMessage Clone();
    }

    /// <summary>
    /// 消息优先级枚举
    /// </summary>
    public enum MessagePriority
    {
        /// <summary>
        /// 低优先级
        /// </summary>
        Low = 0,

        /// <summary>
        /// 普通优先级
        /// </summary>
        Normal = 1,

        /// <summary>
        /// 高优先级
        /// </summary>
        High = 2,

        /// <summary>
        /// 紧急优先级
        /// </summary>
        Critical = 3
    }

    /// <summary>
    /// 消息头
    /// </summary>
    public class MessageHeader
    {
        /// <summary>
        /// 协议版本
        /// </summary>
        public byte Version { get; set; } = 1;

        /// <summary>
        /// 消息长度
        /// </summary>
        public int Length { get; set; }

        /// <summary>
        /// 消息类型
        /// </summary>
        public MessageType Type { get; set; }

        /// <summary>
        /// 序列号
        /// </summary>
        public uint SequenceNumber { get; set; }

        /// <summary>
        /// 校验和
        /// </summary>
        public uint Checksum { get; set; }

        /// <summary>
        /// 标志位
        /// </summary>
        public MessageFlags Flags { get; set; }

        /// <summary>
        /// 发送者ID
        /// </summary>
        public string SenderId { get; set; } = string.Empty;

        /// <summary>
        /// 接收者ID
        /// </summary>
        public string ReceiverId { get; set; } = string.Empty;

        /// <summary>
        /// 时间戳
        /// </summary>
        public long Timestamp { get; set; }

        /// <summary>
        /// 扩展头
        /// </summary>
        public Dictionary<string, object> Extensions { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// 消息标志位
    /// </summary>
    [Flags]
    public enum MessageFlags : byte
    {
        /// <summary>
        /// 无标志
        /// </summary>
        None = 0,

        /// <summary>
        /// 压缩标志
        /// </summary>
        Compressed = 1,

        /// <summary>
        /// 加密标志
        /// </summary>
        Encrypted = 2,

        /// <summary>
        /// 分片标志
        /// </summary>
        Fragmented = 4,

        /// <summary>
        /// 需要确认标志
        /// </summary>
        RequiresAck = 8,

        /// <summary>
        /// 紧急标志
        /// </summary>
        Urgent = 16
    }

    /// <summary>
    /// 消息分片
    /// </summary>
    public class MessageFragment
    {
        /// <summary>
        /// 原始消息ID
        /// </summary>
        public string OriginalMessageId { get; set; } = string.Empty;

        /// <summary>
        /// 分片索引
        /// </summary>
        public int FragmentIndex { get; set; }

        /// <summary>
        /// 总分片数
        /// </summary>
        public int TotalFragments { get; set; }

        /// <summary>
        /// 分片数据
        /// </summary>
        public byte[] Data { get; set; } = Array.Empty<byte>();

        /// <summary>
        /// 分片大小
        /// </summary>
        public int Size => Data.Length;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 消息验证结果
    /// </summary>
    public class MessageValidationResult
    {
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 错误消息列表
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// 警告消息列表
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();

        /// <summary>
        /// 添加错误
        /// </summary>
        /// <param name="error">错误消息</param>
        public void AddError(string error)
        {
            Errors.Add(error);
            IsValid = false;
        }

        /// <summary>
        /// 添加警告
        /// </summary>
        /// <param name="warning">警告消息</param>
        public void AddWarning(string warning)
        {
            Warnings.Add(warning);
        }

        /// <summary>
        /// 获取所有消息
        /// </summary>
        /// <returns>所有错误和警告消息</returns>
        public string GetAllMessages()
        {
            var messages = new List<string>();
            if (Errors.Count > 0)
            {
                messages.Add("错误: " + string.Join(", ", Errors));
            }
            if (Warnings.Count > 0)
            {
                messages.Add("警告: " + string.Join(", ", Warnings));
            }
            return string.Join("; ", messages);
        }
    }

    /// <summary>
    /// 配置验证结果
    /// </summary>
    public class ConfigurationValidationResult
    {
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 错误消息列表
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// 警告消息列表
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();

        /// <summary>
        /// 建议消息列表
        /// </summary>
        public List<string> Suggestions { get; set; } = new List<string>();
    }

    /// <summary>
    /// 通信配置选项
    /// </summary>
    public class CommunicationConfigOption
    {
        /// <summary>
        /// 选项名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 选项类型
        /// </summary>
        public Type OptionType { get; set; } = typeof(object);

        /// <summary>
        /// 默认值
        /// </summary>
        public object? DefaultValue { get; set; }

        /// <summary>
        /// 是否必需
        /// </summary>
        public bool IsRequired { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 可选值列表
        /// </summary>
        public object[]? PossibleValues { get; set; }

        /// <summary>
        /// 最小值
        /// </summary>
        public object? MinValue { get; set; }

        /// <summary>
        /// 最大值
        /// </summary>
        public object? MaxValue { get; set; }
    }
}
