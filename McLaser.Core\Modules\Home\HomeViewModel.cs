#nullable enable
using System;
using System.Collections.ObjectModel;
using System.Windows.Input;
using McLaser.Core.Common;
using McLaser.Core.Framework.Logging;
using McLaser.Core.Framework.Services;
using McLaser.Core.Framework.UI;
using McLaser.Core.Navigation;
using AppNavigationService = McLaser.Core.Navigation.INavigationService;
using AppPageChangedEventArgs = McLaser.Core.Navigation.PageChangedEventArgs;

namespace McLaser.Core.Modules.Home
{
    /// <summary>
    /// 主页ViewModel
    /// 管理主页的显示和交互
    /// </summary>
    public class HomeViewModel : ViewModelBase
    {
        private readonly AppNavigationService? _navigationService;
        private readonly IThemeService? _themeService;
        private readonly IDialogService? _dialogService;
        private readonly ILogger? _logger;
        private string _currentTheme = "Light";
        private string _currentPageTitle = "主页";
        private string _statusMessage = "系统就绪";


        public HomeViewModel(
            AppNavigationService? navigationService = null,
            IThemeService? themeService = null,
            IDialogService? dialogService = null,
            ILogger? logger = null)
        {
            _navigationService = navigationService;
            _themeService = themeService;
            _dialogService = dialogService;
            _logger = logger;

            InitializeCommands();
            InitializeData();
            SubscribeToEvents();
        }

        #region 属性

        public string CurrentTheme
        {
            get => _currentTheme;
            set => SetProperty(ref _currentTheme, value);
        }

        public string CurrentPageTitle
        {
            get => _currentPageTitle;
            set => SetProperty(ref _currentPageTitle, value);
        }

    
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        public ObservableCollection<PageInfo> NavigationHistory { get; } = new ObservableCollection<PageInfo>();

        #endregion

        #region 命令


        public ICommand QuickNavigateCommand { get; private set; } = null!;

        public ICommand SwitchThemeCommand { get; private set; } = null!;

        public ICommand RefreshStatusCommand { get; private set; } = null!;

        public ICommand ShowAboutCommand { get; private set; } = null!;

        #endregion

        #region 私有方法

        private void InitializeCommands()
        {
            QuickNavigateCommand = new RelayCommand<string>(ExecuteQuickNavigate, CanExecuteQuickNavigate);
            SwitchThemeCommand = new RelayCommand<string>(ExecuteSwitchTheme, CanExecuteSwitchTheme);
            RefreshStatusCommand = new RelayCommand(ExecuteRefreshStatus);
            ShowAboutCommand = new RelayCommand(ExecuteShowAbout);
        }

        private void InitializeData()
        {
            try
            {
                // 设置当前主题
                CurrentTheme = _themeService?.CurrentTheme ?? "Light";

                // 更新状态
                UpdateStatus("主页已加载");

                _logger?.LogInfo("HomePageViewModel初始化完成");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"HomePageViewModel初始化失败: {ex.Message}");
                UpdateStatus($"初始化失败: {ex.Message}");
            }
        }

        private void SubscribeToEvents()
        {
            try
            {
                // 订阅主题变更事件
                if (_themeService != null)
                    _themeService.ThemeChanged += OnThemeChanged;

                // 订阅导航事件
                if (_navigationService != null)
                    _navigationService.PageChanged += OnPageChanged;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"事件订阅失败: {ex.Message}");
            }
        }

        private void UpdateStatus(string message)
        {
            StatusMessage = $"{DateTime.Now:HH:mm:ss} - {message}";
        }

        private void UpdateNavigationHistory()
        {
            NavigationHistory.Clear();
            if (_navigationService?.NavigationHistory != null)
            {
                foreach (var page in _navigationService.NavigationHistory)
                {
                    NavigationHistory.Add(page);
                }
            }
        }

        #endregion

        #region 命令执行方法

        /// <summary>
        /// 执行快速导航命令
        /// </summary>
        /// <param name="pageId">页面ID</param>
        private void ExecuteQuickNavigate(string? pageId)
        {
            if (string.IsNullOrEmpty(pageId)) return;

            try
            {
                if (_navigationService?.NavigateTo(pageId) == true)
                {
                    UpdateStatus($"已导航到: {pageId}");
                }
                else
                {
                    UpdateStatus($"导航失败: {pageId}");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"快速导航失败: {ex.Message}");
                UpdateStatus($"导航错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 判断是否可以执行快速导航命令
        /// </summary>
        /// <param name="pageId">页面ID</param>
        /// <returns>是否可以执行</returns>
        private bool CanExecuteQuickNavigate(string? pageId)
        {
            return !string.IsNullOrEmpty(pageId) && _navigationService != null;
        }

        /// <summary>
        /// 执行切换主题命令
        /// </summary>
        /// <param name="themeName">主题名称</param>
        private void ExecuteSwitchTheme(string? themeName)
        {
            if (string.IsNullOrEmpty(themeName)) return;

            try
            {
                if (_themeService?.ApplyTheme(themeName) == true)
                {
                    CurrentTheme = themeName;
                    UpdateStatus($"已切换到 {themeName} 主题");
                    _logger?.LogInfo($"主题已切换到: {themeName}");
                }
                else
                {
                    // 即使服务不可用，也更新UI状态
                    CurrentTheme = themeName;
                    UpdateStatus($"主题已设置为: {themeName} (服务不可用)");
                    _logger?.LogWarning($"主题服务不可用，仅更新UI状态: {themeName}");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"切换主题时发生错误: {ex.Message}");
                UpdateStatus($"切换主题错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 判断是否可以执行切换主题命令
        /// </summary>
        /// <param name="themeName">主题名称</param>
        /// <returns>是否可以执行</returns>
        private bool CanExecuteSwitchTheme(string? themeName)
        {
            return !string.IsNullOrEmpty(themeName) && themeName != CurrentTheme;
        }

        /// <summary>
        /// 执行刷新状态命令
        /// </summary>
        private void ExecuteRefreshStatus()
        {
            try
            {
                // 刷新主题信息
                CurrentTheme = _themeService?.CurrentTheme ?? "Light";

                // 刷新导航历史
                UpdateNavigationHistory();

                // 刷新当前页面信息
                CurrentPageTitle = _navigationService?.CurrentPage?.Title ?? "主页";

                UpdateStatus("状态已刷新");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"刷新状态失败: {ex.Message}");
                UpdateStatus($"刷新失败: {ex.Message}");
            }
        }

        private void ExecuteShowAbout()
        {
            var aboutMessage = "McLaser示例应用程序\n\n" +
                              "版本: 1.0.0\n" +
                              "基于: McLaser.Core框架\n\n" +
                              "这是一个展示McLaser.Core框架功能的完整示例应用程序。\n\n" +
                              "功能特性:\n" +
                              "• 统一DI容器架构\n" +
                              "• 主题管理系统\n" +
                              "• 窗口管理器\n" +
                              "• 数据验证框架\n" +
                              "• 配置管理服务\n" +
                              "• MVVM模式支持\n" +
                              "• 事件总线系统\n" +
                              "• 异常处理机制\n" +
                              "• 插件管理系统";

            if (_dialogService != null)
            {
                _dialogService.ShowMessage(aboutMessage, "关于", MessageType.Information);
            }
            else
            {
                System.Windows.MessageBox.Show(aboutMessage, "关于");
            }
            
            UpdateStatus("已显示关于信息");
        }

        #endregion

        #region 事件处理

        private void OnThemeChanged(object? sender, ThemeChangedEventArgs e)
        {
            CurrentTheme = e.NewTheme;
            UpdateStatus($"主题已变更: {e.OldTheme} -> {e.NewTheme}");
        }

        private void OnPageChanged(object? sender, AppPageChangedEventArgs e)
        {
            CurrentPageTitle = e.NewPage?.Title ?? "未知页面";
            UpdateNavigationHistory();
            UpdateStatus($"页面已切换: {e.NewPage?.Title}");
        }

        #endregion

        #region 清理

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // 取消事件订阅
                if (_themeService != null)
                    _themeService.ThemeChanged -= OnThemeChanged;
                
                if (_navigationService != null)
                    _navigationService.PageChanged -= OnPageChanged;
            }

            base.Dispose(disposing);
        }

        #endregion
    }
}
