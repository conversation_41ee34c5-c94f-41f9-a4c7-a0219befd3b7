﻿using Newtonsoft.Json;
using System;
using System.IO;
using System.Linq;

namespace McLaser.Core.Common
{
    public class JsonHelper
    {
        public static bool SerialObject(string filePath, object value)
        {
            try
            {
                JsonSerializerSettings settings = new JsonSerializerSettings()
                {
                    TypeNameHandling = TypeNameHandling.All,
                    PreserveReferencesHandling = PreserveReferencesHandling.All,
                    TypeNameAssemblyFormatHandling = TypeNameAssemblyFormatHandling.Full,
                    Formatting = Formatting.Indented,
                };
                //序列化
                var str = JsonConvert.SerializeObject(value, settings);
                File.WriteAllText(filePath, str);
                return true;
            }
            catch (Exception ex)
            {
                return false;   
            }
        }


        public static T DeserializeObject<T>(string filePath)
        {
            try
            {
                string json = File.ReadAllText(filePath);

                JsonSerializerSettings settings = new JsonSerializerSettings()
                {
                    TypeNameHandling = TypeNameHandling.All,
                    PreserveReferencesHandling = PreserveReferencesHandling.All,
                    TypeNameAssemblyFormatHandling = TypeNameAssemblyFormatHandling.Full,
                    Formatting = Formatting.Indented,
                    MissingMemberHandling = MissingMemberHandling.Ignore,
                    Error = (sender, args) =>
                    {
                        // 捕获反序列化错误
                        Console.WriteLine($"忽略错误：{args.ErrorContext.Error.Message}");
                        args.ErrorContext.Handled = true; // 标记为已处理，跳过错误
                    }
                };
                return JsonConvert.DeserializeObject<T>(json, settings);
            }
            catch (Exception ex)
            {
                return default(T); 
            }
        }
    }
}
