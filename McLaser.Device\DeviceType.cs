namespace McLaser.Device
{
    /// <summary>
    /// 设备类型枚举
    /// 定义系统支持的设备类别
    /// </summary>
    public enum DeviceCategory
    {
        /// <summary>
        /// 未知设备
        /// </summary>
        None = 0,
        
        /// <summary>
        /// 相机设备
        /// </summary>
        Camera,
        
        /// <summary>
        /// 条码读取器
        /// </summary>
        BarcodeReader,
        
        /// <summary>
        /// 网络设备
        /// </summary>
        NetworkDevice,
        
        /// <summary>
        /// 串口设备
        /// </summary>
        SerialDevice,
        
        /// <summary>
        /// 运动控制器
        /// </summary>
        MotionController,

        /// <summary>
        /// 激光器设备
        /// </summary>
        Laser,

        /// <summary>
        /// 传感器设备
        /// </summary>
        Sensor,

        /// <summary>
        /// 文件夹节点（用于树形结构）
        /// </summary>
        Folder
    }

    /// <summary>
    /// 设备类型枚举（用于DeviceBase）
    /// </summary>
    public enum DevicesType
    {
        /// <summary>
        /// 未知设备
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// 相机设备
        /// </summary>
        Camera,

        /// <summary>
        /// 运动控制卡
        /// </summary>
        MotionCard,

        /// <summary>
        /// 激光器设备
        /// </summary>
        Laser,

        /// <summary>
        /// 传感器设备
        /// </summary>
        Sensor,

        /// <summary>
        /// 条码读取器
        /// </summary>
        BarcodeReader,

        /// <summary>
        /// 网络设备
        /// </summary>
        NetworkDevice,

        /// <summary>
        /// 串口设备
        /// </summary>
        SerialDevice
    }

    /// <summary>
    /// 设备状态枚举
    /// </summary>
    public enum DeviceStatus
    {
        /// <summary>
        /// 未知状态
        /// </summary>
        UnKnow,

        /// <summary>
        /// 连接中
        /// </summary>
        Connecting,

        /// <summary>
        /// 已连接
        /// </summary>
        Connected,

        /// <summary>
        /// 断开连接中
        /// </summary>
        DisConnecting,

        /// <summary>
        /// 已断开连接
        /// </summary>
        Disconnected,

        /// <summary>
        /// 已释放
        /// </summary>
        Disposed,
    }
}
