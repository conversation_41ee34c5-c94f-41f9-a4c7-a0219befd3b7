using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace McLaser.Core.Communication
{
    /// <summary>
    /// 消息协议基类
    /// 提供消息协议的基础实现
    /// </summary>
    public abstract class MessageProtocolBase : IMessageProtocol, IAsyncMessageProtocol, ICompressibleMessageProtocol, IEncryptableMessageProtocol, IFragmentableMessageProtocol
    {
        #region 属性

        /// <summary>
        /// 协议名称
        /// </summary>
        public abstract string Name { get; }

        /// <summary>
        /// 协议版本
        /// </summary>
        public virtual Version Version => new Version(1, 0, 0, 0);

        /// <summary>
        /// 协议描述
        /// </summary>
        public virtual string Description => $"{Name} 消息协议";

        /// <summary>
        /// 最大消息长度
        /// </summary>
        public virtual int MaxMessageLength { get; set; } = 1024 * 1024; // 1MB

        /// <summary>
        /// 消息头长度
        /// </summary>
        public virtual int HeaderLength { get; protected set; } = 32;

        /// <summary>
        /// 是否支持分片传输
        /// </summary>
        public virtual bool SupportsFragmentation => true;

        /// <summary>
        /// 是否启用压缩
        /// </summary>
        public bool CompressionEnabled { get; set; } = false;

        /// <summary>
        /// 压缩算法
        /// </summary>
        public CompressionAlgorithm CompressionAlgorithm { get; set; } = CompressionAlgorithm.GZip;

        /// <summary>
        /// 压缩级别
        /// </summary>
        public CompressionLevel CompressionLevel { get; set; } = CompressionLevel.Optimal;

        /// <summary>
        /// 压缩阈值（字节）
        /// </summary>
        public int CompressionThreshold { get; set; } = 1024;

        /// <summary>
        /// 是否启用加密
        /// </summary>
        public bool EncryptionEnabled { get; set; } = false;

        /// <summary>
        /// 加密算法
        /// </summary>
        public EncryptionAlgorithm EncryptionAlgorithm { get; set; } = EncryptionAlgorithm.AES;

        /// <summary>
        /// 加密密钥
        /// </summary>
        public byte[] EncryptionKey { get; set; } = Array.Empty<byte>();

        /// <summary>
        /// 初始化向量
        /// </summary>
        public byte[] InitializationVector { get; set; } = Array.Empty<byte>();

        /// <summary>
        /// 最大分片大小
        /// </summary>
        public int MaxFragmentSize { get; set; } = 4096;

        #endregion

        #region 核心方法

        /// <summary>
        /// 编码消息
        /// </summary>
        /// <param name="message">要编码的消息</param>
        /// <returns>编码后的字节数组</returns>
        public virtual byte[] EncodeMessage(IMessage message)
        {
            if (message == null)
                throw new ArgumentNullException(nameof(message));

            try
            {
                // 1. 序列化消息内容
                var content = SerializeMessageContent(message);

                // 2. 压缩（如果启用）
                if (CompressionEnabled && content.Length > CompressionThreshold)
                {
                    content = Compress(content);
                    message.Header.Flags |= MessageFlags.Compressed;
                }

                // 3. 加密（如果启用）
                if (EncryptionEnabled)
                {
                    content = Encrypt(content);
                    message.Header.Flags |= MessageFlags.Encrypted;
                }

                // 4. 更新消息头
                message.Header.Length = content.Length;
                message.Header.Checksum = CalculateChecksum(content);
                message.Header.Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

                // 5. 编码消息头
                var header = EncodeHeader(message.Header);

                // 6. 组合头部和内容
                var result = new byte[header.Length + content.Length];
                Array.Copy(header, 0, result, 0, header.Length);
                Array.Copy(content, 0, result, header.Length, content.Length);

                return result;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"编码消息失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 解码消息
        /// </summary>
        /// <param name="data">要解码的数据</param>
        /// <returns>解码后的消息</returns>
        public virtual IMessage DecodeMessage(byte[] data)
        {
            if (data == null)
                throw new ArgumentNullException(nameof(data));

            if (data.Length < HeaderLength)
                throw new ArgumentException($"数据长度不足，至少需要 {HeaderLength} 字节");

            try
            {
                // 1. 解码消息头
                var headerData = new byte[HeaderLength];
                Array.Copy(data, 0, headerData, 0, HeaderLength);
                var header = DecodeHeader(headerData);

                // 2. 提取消息内容
                var contentLength = data.Length - HeaderLength;
                var content = new byte[contentLength];
                Array.Copy(data, HeaderLength, content, 0, contentLength);

                // 3. 验证校验和
                var calculatedChecksum = CalculateChecksum(content);
                if (header.Checksum != calculatedChecksum)
                {
                    throw new InvalidDataException($"校验和不匹配，期望: {header.Checksum}, 实际: {calculatedChecksum}");
                }

                // 4. 解密（如果需要）
                if (header.Flags.HasFlag(MessageFlags.Encrypted))
                {
                    content = Decrypt(content);
                }

                // 5. 解压（如果需要）
                if (header.Flags.HasFlag(MessageFlags.Compressed))
                {
                    content = Decompress(content);
                }

                // 6. 反序列化消息内容
                var message = DeserializeMessageContent(content, header);
                message.Header = header;

                return message;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"解码消息失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 尝试解码消息
        /// </summary>
        /// <param name="data">要解码的数据</param>
        /// <param name="message">解码后的消息</param>
        /// <returns>是否成功解码</returns>
        public virtual bool TryDecodeMessage(byte[] data, out IMessage? message)
        {
            message = null;
            try
            {
                message = DecodeMessage(data);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证消息格式
        /// </summary>
        /// <param name="data">要验证的数据</param>
        /// <returns>验证结果</returns>
        public virtual MessageValidationResult ValidateMessage(byte[] data)
        {
            var result = new MessageValidationResult { IsValid = true };

            try
            {
                // 基本长度检查
                if (data == null)
                {
                    result.AddError("数据为空");
                    return result;
                }

                if (data.Length < HeaderLength)
                {
                    result.AddError($"数据长度不足，至少需要 {HeaderLength} 字节");
                    return result;
                }

                // 尝试解码头部
                var headerData = new byte[HeaderLength];
                Array.Copy(data, 0, headerData, 0, HeaderLength);
                var header = DecodeHeader(headerData);

                // 验证版本
                if (header.Version != Version.Major)
                {
                    result.AddWarning($"协议版本不匹配，期望: {Version.Major}, 实际: {header.Version}");
                }

                // 验证长度
                var expectedLength = HeaderLength + header.Length;
                if (data.Length != expectedLength)
                {
                    result.AddError($"消息长度不匹配，期望: {expectedLength}, 实际: {data.Length}");
                }

                // 验证校验和
                if (data.Length > HeaderLength)
                {
                    var content = new byte[data.Length - HeaderLength];
                    Array.Copy(data, HeaderLength, content, 0, content.Length);
                    var calculatedChecksum = CalculateChecksum(content);
                    if (header.Checksum != calculatedChecksum)
                    {
                        result.AddError($"校验和不匹配，期望: {header.Checksum}, 实际: {calculatedChecksum}");
                    }
                }
            }
            catch (Exception ex)
            {
                result.AddError($"验证过程中发生异常: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 检查数据是否包含完整消息
        /// </summary>
        /// <param name="buffer">数据缓冲区</param>
        /// <param name="messageLength">消息长度</param>
        /// <returns>是否包含完整消息</returns>
        public virtual bool HasCompleteMessage(byte[] buffer, out int messageLength)
        {
            messageLength = 0;

            if (buffer == null || buffer.Length < HeaderLength)
                return false;

            try
            {
                var headerData = new byte[HeaderLength];
                Array.Copy(buffer, 0, headerData, 0, HeaderLength);
                var header = DecodeHeader(headerData);

                messageLength = HeaderLength + header.Length;
                return buffer.Length >= messageLength;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region 抽象方法

        /// <summary>
        /// 编码消息头
        /// </summary>
        /// <param name="header">消息头</param>
        /// <returns>编码后的头部数据</returns>
        protected abstract byte[] EncodeHeader(MessageHeader header);

        /// <summary>
        /// 解码消息头
        /// </summary>
        /// <param name="data">头部数据</param>
        /// <returns>解码后的消息头</returns>
        protected abstract MessageHeader DecodeHeader(byte[] data);

        /// <summary>
        /// 序列化消息内容
        /// </summary>
        /// <param name="message">消息</param>
        /// <returns>序列化后的内容</returns>
        protected abstract byte[] SerializeMessageContent(IMessage message);

        /// <summary>
        /// 反序列化消息内容
        /// </summary>
        /// <param name="content">消息内容</param>
        /// <param name="header">消息头</param>
        /// <returns>反序列化后的消息</returns>
        protected abstract IMessage DeserializeMessageContent(byte[] content, MessageHeader header);

        #endregion

        #region 辅助方法

        /// <summary>
        /// 创建心跳消息
        /// </summary>
        /// <returns>心跳消息</returns>
        public virtual IMessage CreateHeartbeatMessage()
        {
            return new BasicMessage
            {
                MessageId = Guid.NewGuid().ToString(),
                MessageType = MessageType.Heartbeat,
                Content = Encoding.UTF8.GetBytes("HEARTBEAT"),
                Header = new MessageHeader
                {
                    Type = MessageType.Heartbeat,
                    Version = (byte)Version.Major
                },
                CreatedTime = DateTime.UtcNow,
                Priority = MessagePriority.Low
            };
        }

        /// <summary>
        /// 创建确认消息
        /// </summary>
        /// <param name="originalMessage">原始消息</param>
        /// <returns>确认消息</returns>
        public virtual IMessage CreateAcknowledgmentMessage(IMessage originalMessage)
        {
            if (originalMessage == null)
                throw new ArgumentNullException(nameof(originalMessage));

            return new BasicMessage
            {
                MessageId = Guid.NewGuid().ToString(),
                MessageType = MessageType.Acknowledgment,
                Content = Encoding.UTF8.GetBytes($"ACK:{originalMessage.MessageId}"),
                Header = new MessageHeader
                {
                    Type = MessageType.Acknowledgment,
                    Version = (byte)Version.Major,
                    ReceiverId = originalMessage.Header.SenderId,
                    SenderId = originalMessage.Header.ReceiverId
                },
                CreatedTime = DateTime.UtcNow,
                Priority = MessagePriority.Normal
            };
        }

        /// <summary>
        /// 创建错误消息
        /// </summary>
        /// <param name="errorCode">错误代码</param>
        /// <param name="errorMessage">错误消息</param>
        /// <returns>错误消息</returns>
        public virtual IMessage CreateErrorMessage(int errorCode, string errorMessage)
        {
            var content = $"ERROR:{errorCode}:{errorMessage}";
            return new BasicMessage
            {
                MessageId = Guid.NewGuid().ToString(),
                MessageType = MessageType.Error,
                Content = Encoding.UTF8.GetBytes(content),
                Header = new MessageHeader
                {
                    Type = MessageType.Error,
                    Version = (byte)Version.Major
                },
                CreatedTime = DateTime.UtcNow,
                Priority = MessagePriority.High
            };
        }

        /// <summary>
        /// 计算校验和
        /// </summary>
        /// <param name="data">数据</param>
        /// <returns>校验和</returns>
        protected virtual uint CalculateChecksum(byte[] data)
        {
            if (data == null || data.Length == 0)
                return 0;

            uint checksum = 0;
            foreach (byte b in data)
            {
                checksum = (checksum << 1) ^ b;
            }
            return checksum;
        }

        #endregion

        #region 异步方法

        /// <summary>
        /// 异步编码消息
        /// </summary>
        /// <param name="message">要编码的消息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>编码任务</returns>
        public virtual Task<byte[]> EncodeMessageAsync(IMessage message, CancellationToken cancellationToken = default)
        {
            return Task.Run(() => EncodeMessage(message), cancellationToken);
        }

        /// <summary>
        /// 异步解码消息
        /// </summary>
        /// <param name="data">要解码的数据</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>解码任务</returns>
        public virtual Task<IMessage> DecodeMessageAsync(byte[] data, CancellationToken cancellationToken = default)
        {
            return Task.Run(() => DecodeMessage(data), cancellationToken);
        }

        /// <summary>
        /// 异步验证消息
        /// </summary>
        /// <param name="data">要验证的数据</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>验证任务</returns>
        public virtual Task<MessageValidationResult> ValidateMessageAsync(byte[] data, CancellationToken cancellationToken = default)
        {
            return Task.Run(() => ValidateMessage(data), cancellationToken);
        }

        #endregion

        #region 压缩方法

        /// <summary>
        /// 压缩数据
        /// </summary>
        /// <param name="data">原始数据</param>
        /// <returns>压缩后的数据</returns>
        public virtual byte[] Compress(byte[] data)
        {
            if (data == null || data.Length == 0)
                return data;

            using (var output = new MemoryStream())
            {
                // 将自定义压缩级别转换为系统压缩级别
                var systemCompressionLevel = ConvertToSystemCompressionLevel(CompressionLevel);

                Stream compressionStream = CompressionAlgorithm switch
                {
                    CompressionAlgorithm.GZip => new GZipStream(output, systemCompressionLevel),
                    CompressionAlgorithm.Deflate => new DeflateStream(output, systemCompressionLevel),
                    _ => throw new NotSupportedException($"不支持的压缩算法: {CompressionAlgorithm}")
                };

                using (compressionStream)
                {
                    compressionStream.Write(data, 0, data.Length);
                }

                return output.ToArray();
            }
        }

        /// <summary>
        /// 解压数据
        /// </summary>
        /// <param name="compressedData">压缩数据</param>
        /// <returns>解压后的数据</returns>
        public virtual byte[] Decompress(byte[] compressedData)
        {
            if (compressedData == null || compressedData.Length == 0)
                return compressedData;

            using (var input = new MemoryStream(compressedData))
            using (var output = new MemoryStream())
            {
                Stream decompressionStream = CompressionAlgorithm switch
                {
                    CompressionAlgorithm.GZip => new GZipStream(input, CompressionMode.Decompress),
                    CompressionAlgorithm.Deflate => new DeflateStream(input, CompressionMode.Decompress),
                    _ => throw new NotSupportedException($"不支持的压缩算法: {CompressionAlgorithm}")
                };

                using (decompressionStream)
                {
                    decompressionStream.CopyTo(output);
                }

                return output.ToArray();
            }
        }

        /// <summary>
        /// 将自定义压缩级别转换为系统压缩级别
        /// </summary>
        /// <param name="level">自定义压缩级别</param>
        /// <returns>系统压缩级别</returns>
        private System.IO.Compression.CompressionLevel ConvertToSystemCompressionLevel(CompressionLevel level)
        {
            return level switch
            {
                CompressionLevel.Fastest => System.IO.Compression.CompressionLevel.Fastest,
                CompressionLevel.Optimal => System.IO.Compression.CompressionLevel.Optimal,
                CompressionLevel.NoCompression => System.IO.Compression.CompressionLevel.NoCompression,
                _ => System.IO.Compression.CompressionLevel.Optimal
            };
        }

        #endregion

        #region 加密方法

        /// <summary>
        /// 加密数据
        /// </summary>
        /// <param name="data">原始数据</param>
        /// <returns>加密后的数据</returns>
        public virtual byte[] Encrypt(byte[] data)
        {
            if (data == null || data.Length == 0 || !EncryptionEnabled)
                return data;

            return EncryptionAlgorithm switch
            {
                EncryptionAlgorithm.AES => EncryptAES(data),
                EncryptionAlgorithm.DES => EncryptDES(data),
                EncryptionAlgorithm.TripleDES => EncryptTripleDES(data),
                _ => throw new NotSupportedException($"不支持的加密算法: {EncryptionAlgorithm}")
            };
        }

        /// <summary>
        /// 解密数据
        /// </summary>
        /// <param name="encryptedData">加密数据</param>
        /// <returns>解密后的数据</returns>
        public virtual byte[] Decrypt(byte[] encryptedData)
        {
            if (encryptedData == null || encryptedData.Length == 0 || !EncryptionEnabled)
                return encryptedData;

            return EncryptionAlgorithm switch
            {
                EncryptionAlgorithm.AES => DecryptAES(encryptedData),
                EncryptionAlgorithm.DES => DecryptDES(encryptedData),
                EncryptionAlgorithm.TripleDES => DecryptTripleDES(encryptedData),
                _ => throw new NotSupportedException($"不支持的加密算法: {EncryptionAlgorithm}")
            };
        }

        /// <summary>
        /// 生成密钥
        /// </summary>
        /// <returns>生成的密钥</returns>
        public virtual byte[] GenerateKey()
        {
            return EncryptionAlgorithm switch
            {
                EncryptionAlgorithm.AES => GenerateAESKey(),
                EncryptionAlgorithm.DES => GenerateDESKey(),
                EncryptionAlgorithm.TripleDES => GenerateTripleDESKey(),
                _ => throw new NotSupportedException($"不支持的加密算法: {EncryptionAlgorithm}")
            };
        }

        /// <summary>
        /// 生成初始化向量
        /// </summary>
        /// <returns>生成的初始化向量</returns>
        public virtual byte[] GenerateIV()
        {
            return EncryptionAlgorithm switch
            {
                EncryptionAlgorithm.AES => GenerateAESIV(),
                EncryptionAlgorithm.DES => GenerateDESIV(),
                EncryptionAlgorithm.TripleDES => GenerateTripleDESIV(),
                _ => throw new NotSupportedException($"不支持的加密算法: {EncryptionAlgorithm}")
            };
        }

        private byte[] EncryptAES(byte[] data)
        {
            using (var aes = Aes.Create())
            {
                aes.Key = EncryptionKey;
                aes.IV = InitializationVector;
                using (var encryptor = aes.CreateEncryptor())
                using (var ms = new MemoryStream())
                using (var cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
                {
                    cs.Write(data, 0, data.Length);
                    cs.FlushFinalBlock();
                    return ms.ToArray();
                }
            }
        }

        private byte[] DecryptAES(byte[] encryptedData)
        {
            using (var aes = Aes.Create())
            {
                aes.Key = EncryptionKey;
                aes.IV = InitializationVector;
                using (var decryptor = aes.CreateDecryptor())
                using (var ms = new MemoryStream(encryptedData))
                using (var cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read))
                using (var result = new MemoryStream())
                {
                    cs.CopyTo(result);
                    return result.ToArray();
                }
            }
        }

        private byte[] GenerateAESKey()
        {
            using (var aes = Aes.Create())
            {
                aes.GenerateKey();
                return aes.Key;
            }
        }

        private byte[] GenerateAESIV()
        {
            using (var aes = Aes.Create())
            {
                aes.GenerateIV();
                return aes.IV;
            }
        }

        private byte[] EncryptDES(byte[] data)
        {
            using (var des = DES.Create())
            {
                des.Key = EncryptionKey;
                des.IV = InitializationVector;
                using (var encryptor = des.CreateEncryptor())
                using (var ms = new MemoryStream())
                using (var cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
                {
                    cs.Write(data, 0, data.Length);
                    cs.FlushFinalBlock();
                    return ms.ToArray();
                }
            }
        }

        private byte[] DecryptDES(byte[] encryptedData)
        {
            using (var des = DES.Create())
            {
                des.Key = EncryptionKey;
                des.IV = InitializationVector;
                using (var decryptor = des.CreateDecryptor())
                using (var ms = new MemoryStream(encryptedData))
                using (var cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read))
                using (var result = new MemoryStream())
                {
                    cs.CopyTo(result);
                    return result.ToArray();
                }
            }
        }

        private byte[] GenerateDESKey()
        {
            using (var des = DES.Create())
            {
                des.GenerateKey();
                return des.Key;
            }
        }

        private byte[] GenerateDESIV()
        {
            using (var des = DES.Create())
            {
                des.GenerateIV();
                return des.IV;
            }
        }

        private byte[] EncryptTripleDES(byte[] data)
        {
            using (var des3 = TripleDES.Create())
            {
                des3.Key = EncryptionKey;
                des3.IV = InitializationVector;
                using (var encryptor = des3.CreateEncryptor())
                using (var ms = new MemoryStream())
                using (var cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
                {
                    cs.Write(data, 0, data.Length);
                    cs.FlushFinalBlock();
                    return ms.ToArray();
                }
            }
        }

        private byte[] DecryptTripleDES(byte[] encryptedData)
        {
            using (var des3 = TripleDES.Create())
            {
                des3.Key = EncryptionKey;
                des3.IV = InitializationVector;
                using (var decryptor = des3.CreateDecryptor())
                using (var ms = new MemoryStream(encryptedData))
                using (var cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read))
                using (var result = new MemoryStream())
                {
                    cs.CopyTo(result);
                    return result.ToArray();
                }
            }
        }

        private byte[] GenerateTripleDESKey()
        {
            using (var des3 = TripleDES.Create())
            {
                des3.GenerateKey();
                return des3.Key;
            }
        }

        private byte[] GenerateTripleDESIV()
        {
            using (var des3 = TripleDES.Create())
            {
                des3.GenerateIV();
                return des3.IV;
            }
        }

        #endregion

        #region 分片方法

        /// <summary>
        /// 分片消息
        /// </summary>
        /// <param name="message">要分片的消息</param>
        /// <returns>分片列表</returns>
        public virtual IList<MessageFragment> FragmentMessage(IMessage message)
        {
            if (message == null)
                throw new ArgumentNullException(nameof(message));

            var fragments = new List<MessageFragment>();
            var content = message.Content;

            if (content.Length <= MaxFragmentSize)
            {
                // 不需要分片
                fragments.Add(new MessageFragment
                {
                    OriginalMessageId = message.MessageId,
                    FragmentIndex = 0,
                    TotalFragments = 1,
                    Data = content
                });
            }
            else
            {
                // 需要分片
                var totalFragments = (int)Math.Ceiling((double)content.Length / MaxFragmentSize);

                for (int i = 0; i < totalFragments; i++)
                {
                    var offset = i * MaxFragmentSize;
                    var length = Math.Min(MaxFragmentSize, content.Length - offset);
                    var fragmentData = new byte[length];
                    Array.Copy(content, offset, fragmentData, 0, length);

                    fragments.Add(new MessageFragment
                    {
                        OriginalMessageId = message.MessageId,
                        FragmentIndex = i,
                        TotalFragments = totalFragments,
                        Data = fragmentData
                    });
                }
            }

            return fragments;
        }

        /// <summary>
        /// 重组消息
        /// </summary>
        /// <param name="fragments">分片列表</param>
        /// <returns>重组后的消息</returns>
        public virtual IMessage ReassembleMessage(IList<MessageFragment> fragments)
        {
            if (fragments == null || fragments.Count == 0)
                throw new ArgumentException("分片列表不能为空");

            // 按索引排序
            var sortedFragments = fragments.OrderBy(f => f.FragmentIndex).ToList();

            // 验证分片完整性
            if (!IsFragmentationComplete(sortedFragments))
                throw new InvalidOperationException("分片不完整，无法重组消息");

            // 计算总长度
            var totalLength = sortedFragments.Sum(f => f.Data.Length);
            var content = new byte[totalLength];

            // 重组内容
            var offset = 0;
            foreach (var fragment in sortedFragments)
            {
                Array.Copy(fragment.Data, 0, content, offset, fragment.Data.Length);
                offset += fragment.Data.Length;
            }

            // 创建重组后的消息
            return new BasicMessage
            {
                MessageId = sortedFragments[0].OriginalMessageId,
                Content = content,
                CreatedTime = DateTime.UtcNow
            };
        }

        /// <summary>
        /// 检查分片是否完整
        /// </summary>
        /// <param name="fragments">分片列表</param>
        /// <returns>是否完整</returns>
        public virtual bool IsFragmentationComplete(IList<MessageFragment> fragments)
        {
            if (fragments == null || fragments.Count == 0)
                return false;

            var totalFragments = fragments[0].TotalFragments;
            if (fragments.Count != totalFragments)
                return false;

            var indices = fragments.Select(f => f.FragmentIndex).OrderBy(i => i).ToList();
            for (int i = 0; i < totalFragments; i++)
            {
                if (indices[i] != i)
                    return false;
            }

            return true;
        }

        /// <summary>
        /// 获取缺失的分片索引
        /// </summary>
        /// <param name="fragments">分片列表</param>
        /// <param name="totalFragments">总分片数</param>
        /// <returns>缺失的分片索引</returns>
        public virtual IList<int> GetMissingFragmentIndices(IList<MessageFragment> fragments, int totalFragments)
        {
            var existingIndices = new HashSet<int>(fragments.Select(f => f.FragmentIndex));
            var missingIndices = new List<int>();

            for (int i = 0; i < totalFragments; i++)
            {
                if (!existingIndices.Contains(i))
                {
                    missingIndices.Add(i);
                }
            }

            return missingIndices;
        }

        #endregion
    }

    /// <summary>
    /// 基础消息实现
    /// </summary>
    public class BasicMessage : IMessage
    {
        public string MessageId { get; set; } = Guid.NewGuid().ToString();
        public MessageType MessageType { get; set; }
        public byte[] Content { get; set; } = Array.Empty<byte>();
        public MessageHeader Header { get; set; } = new MessageHeader();
        public DateTime CreatedTime { get; set; } = DateTime.UtcNow;
        public DateTime? ExpiryTime { get; set; }
        public MessagePriority Priority { get; set; } = MessagePriority.Normal;
        public bool RequiresAcknowledgment { get; set; } = false;
        public Dictionary<string, object> Properties { get; set; } = new Dictionary<string, object>();

        public int GetSize()
        {
            return Content?.Length ?? 0;
        }

        public bool IsValid()
        {
            return !string.IsNullOrEmpty(MessageId) && Content != null;
        }

        public IMessage Clone()
        {
            return new BasicMessage
            {
                MessageId = MessageId,
                MessageType = MessageType,
                Content = (byte[])Content.Clone(),
                Header = Header,
                CreatedTime = CreatedTime,
                ExpiryTime = ExpiryTime,
                Priority = Priority,
                RequiresAcknowledgment = RequiresAcknowledgment,
                Properties = new Dictionary<string, object>(Properties)
            };
        }
    }
}
