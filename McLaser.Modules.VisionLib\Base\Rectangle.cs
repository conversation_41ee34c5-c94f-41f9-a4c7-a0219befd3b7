﻿using McLaser.Core.Common;
using System;

namespace McLaser.Modules.Vision
{
    [Serializable]
    public class Rectangle : ObservableObject
    {
        public bool Status;
        public double Row1;
        public double Col1;

        public double Row2;
        public double Col2;
        public Rectangle()
        {
        }
        public Rectangle(double row1, double col1, double row2, double col2)
        {
            this.Row1 = row1;
            this.Col1 = col1;
            this.Row2 = row2;
            this.Col2 = col2;
            Status = true;
        }
    }
}
