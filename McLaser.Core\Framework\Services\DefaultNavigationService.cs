//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Windows;
//using System.Windows.Controls;

//namespace McLaser.Core.Framework.Services
//{
//    /// <summary>
//    /// 默认导航服务实现
//    /// 基于WPF Frame控件的导航
//    /// </summary>
//    public class DefaultNavigationService : INavigationService
//    {
//        private readonly Frame? _frame;
//        private readonly Dictionary<string, ViewRegistration> _viewRegistrations = new();
//        private readonly Dictionary<Type, ViewRegistration> _typeRegistrations = new();
//        private readonly Stack<NavigationEntry> _backStack = new();
//        private readonly Stack<NavigationEntry> _forwardStack = new();
//        private NavigationEntry? _currentEntry;

//        /// <summary>
//        /// 当前页面/视图
//        /// </summary>
//        public object? CurrentView => _frame?.Content ?? _currentEntry?.View;

//        /// <summary>
//        /// 是否可以后退
//        /// </summary>
//        public bool CanGoBack => _backStack.Count > 0;

//        /// <summary>
//        /// 是否可以前进
//        /// </summary>
//        public bool CanGoForward => _forwardStack.Count > 0;

//        /// <summary>
//        /// 导航事件
//        /// </summary>
//        public event EventHandler<NavigationEventArgs>? Navigated;

//        /// <summary>
//        /// 导航前事件
//        /// </summary>
//        public event EventHandler<NavigatingEventArgs>? Navigating;

//        /// <summary>
//        /// 初始化导航服务
//        /// </summary>
//        /// <param name="frame">WPF Frame控件，为null时使用内部导航管理</param>
//        public DefaultNavigationService(Frame? frame = null)
//        {
//            _frame = frame;
//        }

//        /// <summary>
//        /// 导航到指定视图
//        /// </summary>
//        /// <typeparam name="T">视图类型</typeparam>
//        /// <param name="parameter">导航参数</param>
//        public void NavigateTo<T>(object? parameter = null) where T : class
//        {
//            NavigateTo(typeof(T), parameter);
//        }

//        /// <summary>
//        /// 导航到指定视图
//        /// </summary>
//        /// <param name="viewType">视图类型</param>
//        /// <param name="parameter">导航参数</param>
//        public void NavigateTo(Type viewType, object? parameter = null)
//        {
//            if (viewType == null)
//                throw new ArgumentNullException(nameof(viewType));

//            // 查找视图注册信息
//            if (!_typeRegistrations.TryGetValue(viewType, out var registration))
//            {
//                throw new InvalidOperationException($"视图类型 {viewType.Name} 未注册");
//            }

//            NavigateToInternal(registration, parameter);
//        }

//        /// <summary>
//        /// 导航到指定视图（通过名称）
//        /// </summary>
//        /// <param name="viewName">视图名称</param>
//        /// <param name="parameter">导航参数</param>
//        public void NavigateTo(string viewName, object? parameter = null)
//        {
//            if (string.IsNullOrEmpty(viewName))
//                throw new ArgumentException("视图名称不能为空", nameof(viewName));

//            // 查找视图注册信息
//            if (!_viewRegistrations.TryGetValue(viewName, out var registration))
//            {
//                throw new InvalidOperationException($"视图 {viewName} 未注册");
//            }

//            NavigateToInternal(registration, parameter);
//        }

//        /// <summary>
//        /// 后退
//        /// </summary>
//        public void GoBack()
//        {
//            if (!CanGoBack)
//                return;

//            var previousEntry = _backStack.Pop();
            
//            if (_currentEntry != null)
//            {
//                _forwardStack.Push(_currentEntry);
//            }

//            NavigateToEntry(previousEntry);
//        }

//        /// <summary>
//        /// 前进
//        /// </summary>
//        public void GoForward()
//        {
//            if (!CanGoForward)
//                return;

//            var nextEntry = _forwardStack.Pop();
            
//            if (_currentEntry != null)
//            {
//                _backStack.Push(_currentEntry);
//            }

//            NavigateToEntry(nextEntry);
//        }

//        /// <summary>
//        /// 清除导航历史
//        /// </summary>
//        public void ClearHistory()
//        {
//            _backStack.Clear();
//            _forwardStack.Clear();
//        }

//        /// <summary>
//        /// 注册视图
//        /// </summary>
//        /// <typeparam name="TView">视图类型</typeparam>
//        /// <typeparam name="TViewModel">视图模型类型</typeparam>
//        /// <param name="viewName">视图名称</param>
//        public void RegisterView<TView, TViewModel>(string? viewName = null)
//            where TView : class
//            where TViewModel : class
//        {
//            RegisterView(typeof(TView), typeof(TViewModel), viewName);
//        }

//        /// <summary>
//        /// 注册视图
//        /// </summary>
//        /// <param name="viewType">视图类型</param>
//        /// <param name="viewModelType">视图模型类型</param>
//        /// <param name="viewName">视图名称</param>
//        public void RegisterView(Type viewType, Type? viewModelType = null, string? viewName = null)
//        {
//            if (viewType == null)
//                throw new ArgumentNullException(nameof(viewType));

//            var registration = new ViewRegistration(viewType, viewModelType, viewName);
            
//            _typeRegistrations[viewType] = registration;
//            _viewRegistrations[registration.ViewName] = registration;
//        }

//        /// <summary>
//        /// 内部导航实现
//        /// </summary>
//        /// <param name="registration">视图注册信息</param>
//        /// <param name="parameter">导航参数</param>
//        private void NavigateToInternal(ViewRegistration registration, object? parameter)
//        {
//            var sourceViewType = _currentEntry?.Registration.ViewType;

//            // 触发导航前事件
//            var navigatingArgs = new NavigatingEventArgs(registration.ViewType, parameter, sourceViewType);
//            OnNavigating(navigatingArgs);

//            if (navigatingArgs.Cancel)
//                return;

//            try
//            {
//                // 创建视图实例
//                var view = CreateView(registration);
                
//                // 创建导航条目
//                var entry = new NavigationEntry(registration, view, parameter);

//                // 保存当前条目到后退栈
//                if (_currentEntry != null)
//                {
//                    _backStack.Push(_currentEntry);
//                }

//                // 清空前进栈
//                _forwardStack.Clear();

//                // 执行导航
//                NavigateToEntry(entry);

//                // 触发导航完成事件
//                var navigatedArgs = new NavigationEventArgs(registration.ViewType, parameter, sourceViewType);
//                OnNavigated(navigatedArgs);
//            }
//            catch (Exception ex)
//            {
//                throw new InvalidOperationException($"导航到视图 {registration.ViewName} 失败", ex);
//            }
//        }

//        /// <summary>
//        /// 导航到指定条目
//        /// </summary>
//        /// <param name="entry">导航条目</param>
//        private void NavigateToEntry(NavigationEntry entry)
//        {
//            _currentEntry = entry;

//            if (_frame != null)
//            {
//                _frame.Content = entry.View;
//            }
//        }

//        /// <summary>
//        /// 创建视图实例
//        /// </summary>
//        /// <param name="registration">视图注册信息</param>
//        /// <returns>视图实例</returns>
//        private object CreateView(ViewRegistration registration)
//        {
//            // 创建视图实例
//            var view = Activator.CreateInstance(registration.ViewType);
//            if (view == null)
//            {
//                throw new InvalidOperationException($"无法创建视图实例: {registration.ViewType.Name}");
//            }

//            // 如果有ViewModel类型，创建并设置DataContext
//            if (registration.ViewModelType != null && view is FrameworkElement element)
//            {
//                var viewModel = Activator.CreateInstance(registration.ViewModelType);
//                element.DataContext = viewModel;
//            }

//            return view;
//        }

//        /// <summary>
//        /// 触发导航前事件
//        /// </summary>
//        /// <param name="args">事件参数</param>
//        protected virtual void OnNavigating(NavigatingEventArgs args)
//        {
//            Navigating?.Invoke(this, args);
//        }

//        /// <summary>
//        /// 触发导航完成事件
//        /// </summary>
//        /// <param name="args">事件参数</param>
//        protected virtual void OnNavigated(NavigationEventArgs args)
//        {
//            Navigated?.Invoke(this, args);
//        }

//        /// <summary>
//        /// 导航条目
//        /// </summary>
//        private class NavigationEntry
//        {
//            public ViewRegistration Registration { get; }
//            public object View { get; }
//            public object? Parameter { get; }

//            public NavigationEntry(ViewRegistration registration, object view, object? parameter)
//            {
//                Registration = registration;
//                View = view;
//                Parameter = parameter;
//            }
//        }
//    }
//}
