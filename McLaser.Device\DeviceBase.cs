using System;
using System.Collections.Generic;
using System.ComponentModel;
using McLaser.Core.Common;
using Newtonsoft.Json;

namespace McLaser.Device
{
    /// <summary>
    /// 设备基类
    /// 为所有设备提供基础功能和状态管理
    /// </summary>
    [Serializable]
    public class DeviceBase : ObservableObject, IDevice
    {
        #region 私有字段

        private string _id = Guid.NewGuid().ToString();
        private string _name = "Device";
        private DevicesType _deviceType = DevicesType.Unknown;
        private bool _isEnabled = true;
        private bool _isConnected = false;
        private Dictionary<string, object> _configuration = new Dictionary<string, object>();

        #endregion

        #region 事件

        /// <summary>
        /// 设备更新事件委托
        /// </summary>
        /// <param name="obj">事件参数</param>
        public delegate void EventHandlerUpdate(object obj);

        /// <summary>
        /// 设备更新事件
        /// </summary>
        public event EventHandlerUpdate EventUpdate;

        /// <summary>
        /// 设备状态变更事件
        /// </summary>
        public event EventHandler DeviceStatusChanged;

        #endregion

        #region 属性

        /// <summary>
        /// 设备唯一标识符
        /// </summary>
        public virtual string Id
        {
            get { return _id; }
            protected set { Set(ref _id, value); }
        }

        /// <summary>
        /// 设备名称
        /// </summary>
        public virtual string Name
        {
            get { return _name; }
            set { Set(ref _name, value); }
        }

        /// <summary>
        /// 设备类型
        /// </summary>
        public virtual DevicesType DeviceType
        {
            get { return _deviceType; }
            protected set { Set(ref _deviceType, value); }
        }

        /// <summary>
        /// 设备类别（用于分组）
        /// </summary>
        public virtual DeviceCategory Category
        {
            get
            {
                switch (DeviceType)
                {
                    case DevicesType.Camera:
                        return DeviceCategory.Camera;
                    case DevicesType.MotionCard:
                        return DeviceCategory.MotionController;
                    case DevicesType.Laser:
                        return DeviceCategory.Laser;
                    case DevicesType.Sensor:
                        return DeviceCategory.Sensor;
                    case DevicesType.BarcodeReader:
                        return DeviceCategory.BarcodeReader;
                    case DevicesType.NetworkDevice:
                        return DeviceCategory.NetworkDevice;
                    case DevicesType.SerialDevice:
                        return DeviceCategory.SerialDevice;
                    default:
                        return DeviceCategory.None;
                }
            }
        }

        /// <summary>
        /// 是否启用
        /// </summary>
        public virtual bool IsEnabled
        {
            get { return _isEnabled; }
            set { Set(ref _isEnabled, value); }
        }

        /// <summary>
        /// 是否已连接
        /// </summary>
        public virtual bool IsConnected
        {
            get { return _isConnected; }
            set
            {
                Set(ref _isConnected, value);
                Status.IsConnected = value;
                Status.IsOpen = value;
            }
        }

        /// <summary>
        /// 设备配置参数
        /// </summary>
        public virtual Dictionary<string, object> Configuration
        {
            get { return _configuration; }
            set { Set(ref _configuration, value); }
        }

        /// <summary>
        /// 设备状态
        /// </summary>
        [JsonIgnore]
        public virtual StatusDevice Status { get; set; } = new StatusDevice();

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public DeviceBase()
        {
            Id = Guid.NewGuid().ToString();
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="name">设备名称</param>
        /// <param name="deviceType">设备类型</param>
        public DeviceBase(string name, DevicesType deviceType) : this()
        {
            Name = name;
            DeviceType = deviceType;
        }

        #endregion

        #region 设备操作方法

        /// <summary>
        /// 打开设备
        /// </summary>
        /// <returns>操作是否成功</returns>
        public virtual bool Open()
        {
            try
            {
                // 子类应该重写此方法实现具体的打开逻辑
                IsConnected = true;
                Status.StatusMessage = "设备已连接";
                return true;
            }
            catch (Exception ex)
            {
                Status.StatusMessage = $"连接失败：{ex.Message}";
                return false;
            }
        }

        /// <summary>
        /// 关闭设备
        /// </summary>
        /// <returns>操作是否成功</returns>
        public virtual bool Close()
        {
            try
            {
                // 子类应该重写此方法实现具体的关闭逻辑
                IsConnected = false;
                Status.StatusMessage = "设备已断开";
                return true;
            }
            catch (Exception ex)
            {
                Status.StatusMessage = $"断开失败：{ex.Message}";
                return false;
            }
        }

        /// <summary>
        /// 检查设备是否已打开
        /// </summary>
        /// <returns>设备是否已打开</returns>
        public virtual bool IsOpen()
        {
            return IsConnected;
        }

        /// <summary>
        /// 初始化设备
        /// </summary>
        public virtual void Initialize()
        {
            // 子类应该重写此方法实现具体的初始化逻辑
            Status.StatusMessage = "设备已初始化";
        }

        /// <summary>
        /// 关闭设备并释放资源
        /// </summary>
        public virtual void Shutdown()
        {
            // 子类应该重写此方法实现具体的关闭逻辑
            Close();
            Status.StatusMessage = "设备已关闭";
        }

        /// <summary>
        /// 应用配置参数
        /// </summary>
        /// <param name="config">配置参数</param>
        public virtual void ApplyConfiguration(Dictionary<string, object> config)
        {
            if (config != null)
            {
                Configuration = new Dictionary<string, object>(config);
                Status.StatusMessage = "配置已应用";
            }
        }

        /// <summary>
        /// 获取设备配置界面
        /// </summary>
        /// <returns>配置界面控件</returns>
        public virtual System.Windows.Controls.UserControl GetConfigurationView()
        {
            // 子类应该重写此方法返回具体的配置界面
            var textBlock = new System.Windows.Controls.TextBlock
            {
                Text = $"设备 {Name} 的配置界面",
                HorizontalAlignment = System.Windows.HorizontalAlignment.Center,
                VerticalAlignment = System.Windows.VerticalAlignment.Center
            };

            var userControl = new System.Windows.Controls.UserControl();
            userControl.Content = textBlock;
            return userControl;
        }

        #endregion

        #region 状态检查方法

        /// <summary>
        /// 检查设备状态是否发生变化
        /// </summary>
        /// <returns>状态是否发生变化</returns>
        public virtual bool IsChange()
        {
            if (Status.IsOpenLast == Status.IsOpen)
                return false;

            Status.IsOpenLast = Status.IsOpen;
            return true;
        }

        /// <summary>
        /// 触发设备更新事件
        /// </summary>
        /// <returns>事件是否成功触发</returns>
        public virtual bool SetEventUpdate()
        {
            if (this.EventUpdate != null)
                EventUpdate(this);
            return true;
        }

        /// <summary>
        /// 触发设备状态变更事件
        /// </summary>
        protected virtual void OnDeviceStatusChanged()
        {
            DeviceStatusChanged?.Invoke(this, EventArgs.Empty);
        }

        #endregion

        #region 重写方法

        /// <summary>
        /// 重写ToString方法
        /// </summary>
        /// <returns>设备信息字符串</returns>
        public override string ToString()
        {
            return $"{Name} ({DeviceType})";
        }

        #endregion
    }
}
