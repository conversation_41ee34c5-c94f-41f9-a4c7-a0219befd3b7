using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using McLaser.Core.Common;
using McLaser.Core.ExceptionHandling;
using McLaser.Core.Framework.Logging;
using McLaser.App.Core;
using McLaser.App.Events;
using McLaser.App.Models;

namespace McLaser.App.ViewModels
{
    /// <summary>
    /// 异常处理演示ViewModel
    /// 展示异常处理系统的各种功能特性
    /// </summary>
    public class ExceptionDemoViewModel : ViewModelBase
    {
        #region 私有字段

        private readonly IExceptionHandler _exceptionHandler;
        private readonly ILogger _logger;
        
        private string _exceptionHandlerStatus = "未初始化";
        private string _statusMessage = "就绪";
        private int _handlerCount = 0;
        private int _handledException = 0;
        private int _unhandledException = 0;
        private int _recoveryCount = 0;
        
        // 异常测试相关
        private string _customExceptionMessage = "这是一个自定义异常消息";
        
        // 异常恢复相关
        private bool _enableAutoRecovery = true;
        private bool _enableUserConfirmRecovery = false;
        private bool _enableExceptionRetry = true;
        private int _maxRetryCount = 3;
        private int _retryIntervalSeconds = 2;
        
        // 全局异常处理
        private bool _enableGlobalExceptionHandling = true;
        private bool _enableUnhandledExceptionLogging = true;
        private bool _enableExceptionNotification = true;
        
        // 统计信息
        private int _totalExceptions = 0;
        private int _infoExceptions = 0;
        private int _warningExceptions = 0;
        private int _errorExceptions = 0;
        private int _criticalExceptions = 0;
        private DateTime _lastExceptionTime = DateTime.MinValue;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public ExceptionDemoViewModel()
        {
            _logger = new McLaser.App.Core.ConsoleLogger("ExceptionDemo");
            _exceptionHandler = new SimpleExceptionHandler();
            
            InitializeCommands();
            InitializeData();
            InitializeExceptionHandler();
        }

        /// <summary>
        /// 构造函数（依赖注入）
        /// </summary>
        /// <param name="exceptionHandler">异常处理器</param>
        /// <param name="logger">日志服务</param>
        public ExceptionDemoViewModel(IExceptionHandler exceptionHandler, ILogger logger)
        {
            _exceptionHandler = exceptionHandler ?? throw new ArgumentNullException(nameof(exceptionHandler));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            InitializeCommands();
            InitializeData();
            InitializeExceptionHandler();
        }

        #endregion

        #region 属性

        /// <summary>
        /// 异常处理器状态
        /// </summary>
        public string ExceptionHandlerStatus
        {
            get => _exceptionHandlerStatus;
            set => SetProperty(ref _exceptionHandlerStatus, value);
        }

        /// <summary>
        /// 状态消息
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        /// <summary>
        /// 处理器数量
        /// </summary>
        public int HandlerCount
        {
            get => _handlerCount;
            set => SetProperty(ref _handlerCount, value);
        }

        /// <summary>
        /// 已处理异常数量
        /// </summary>
        public int HandledException
        {
            get => _handledException;
            set => SetProperty(ref _handledException, value);
        }

        /// <summary>
        /// 未处理异常数量
        /// </summary>
        public int UnhandledException
        {
            get => _unhandledException;
            set => SetProperty(ref _unhandledException, value);
        }

        /// <summary>
        /// 恢复次数
        /// </summary>
        public int RecoveryCount
        {
            get => _recoveryCount;
            set => SetProperty(ref _recoveryCount, value);
        }

        /// <summary>
        /// 自定义异常消息
        /// </summary>
        public string CustomExceptionMessage
        {
            get => _customExceptionMessage;
            set => SetProperty(ref _customExceptionMessage, value);
        }

        /// <summary>
        /// 启用自动恢复
        /// </summary>
        public bool EnableAutoRecovery
        {
            get => _enableAutoRecovery;
            set => SetProperty(ref _enableAutoRecovery, value);
        }

        /// <summary>
        /// 启用用户确认恢复
        /// </summary>
        public bool EnableUserConfirmRecovery
        {
            get => _enableUserConfirmRecovery;
            set => SetProperty(ref _enableUserConfirmRecovery, value);
        }

        /// <summary>
        /// 启用异常重试
        /// </summary>
        public bool EnableExceptionRetry
        {
            get => _enableExceptionRetry;
            set => SetProperty(ref _enableExceptionRetry, value);
        }

        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetryCount
        {
            get => _maxRetryCount;
            set => SetProperty(ref _maxRetryCount, value);
        }

        /// <summary>
        /// 重试间隔秒数
        /// </summary>
        public int RetryIntervalSeconds
        {
            get => _retryIntervalSeconds;
            set => SetProperty(ref _retryIntervalSeconds, value);
        }

        /// <summary>
        /// 启用全局异常处理
        /// </summary>
        public bool EnableGlobalExceptionHandling
        {
            get => _enableGlobalExceptionHandling;
            set => SetProperty(ref _enableGlobalExceptionHandling, value);
        }

        /// <summary>
        /// 启用未处理异常日志
        /// </summary>
        public bool EnableUnhandledExceptionLogging
        {
            get => _enableUnhandledExceptionLogging;
            set => SetProperty(ref _enableUnhandledExceptionLogging, value);
        }

        /// <summary>
        /// 启用异常通知
        /// </summary>
        public bool EnableExceptionNotification
        {
            get => _enableExceptionNotification;
            set => SetProperty(ref _enableExceptionNotification, value);
        }

        /// <summary>
        /// 总异常数
        /// </summary>
        public int TotalExceptions
        {
            get => _totalExceptions;
            set => SetProperty(ref _totalExceptions, value);
        }

        /// <summary>
        /// Info级别异常数
        /// </summary>
        public int InfoExceptions
        {
            get => _infoExceptions;
            set => SetProperty(ref _infoExceptions, value);
        }

        /// <summary>
        /// Warning级别异常数
        /// </summary>
        public int WarningExceptions
        {
            get => _warningExceptions;
            set => SetProperty(ref _warningExceptions, value);
        }

        /// <summary>
        /// Error级别异常数
        /// </summary>
        public int ErrorExceptions
        {
            get => _errorExceptions;
            set => SetProperty(ref _errorExceptions, value);
        }

        /// <summary>
        /// Critical级别异常数
        /// </summary>
        public int CriticalExceptions
        {
            get => _criticalExceptions;
            set => SetProperty(ref _criticalExceptions, value);
        }

        /// <summary>
        /// 最后异常时间
        /// </summary>
        public DateTime LastExceptionTime
        {
            get => _lastExceptionTime;
            set => SetProperty(ref _lastExceptionTime, value);
        }

        /// <summary>
        /// 异常日志
        /// </summary>
        public ObservableCollection<ExceptionLogEntry> ExceptionLogs { get; } = new ObservableCollection<ExceptionLogEntry>();

        /// <summary>
        /// 活跃处理器列表
        /// </summary>
        public ObservableCollection<HandlerInfo> ActiveHandlers { get; } = new ObservableCollection<HandlerInfo>();

        #endregion

        #region 命令

        /// <summary>
        /// 初始化异常处理器命令
        /// </summary>
        public ICommand InitializeExceptionHandlerCommand { get; private set; } = null!;

        /// <summary>
        /// 触发Info异常命令
        /// </summary>
        public ICommand TriggerInfoExceptionCommand { get; private set; } = null!;

        /// <summary>
        /// 触发Warning异常命令
        /// </summary>
        public ICommand TriggerWarningExceptionCommand { get; private set; } = null!;

        /// <summary>
        /// 触发Error异常命令
        /// </summary>
        public ICommand TriggerErrorExceptionCommand { get; private set; } = null!;

        /// <summary>
        /// 触发Critical异常命令
        /// </summary>
        public ICommand TriggerCriticalExceptionCommand { get; private set; } = null!;

        /// <summary>
        /// 触发自定义异常命令
        /// </summary>
        public ICommand TriggerCustomExceptionCommand { get; private set; } = null!;

        /// <summary>
        /// 测试异常恢复命令
        /// </summary>
        public ICommand TestExceptionRecoveryCommand { get; private set; } = null!;

        /// <summary>
        /// 触发未处理异常命令
        /// </summary>
        public ICommand TriggerUnhandledExceptionCommand { get; private set; } = null!;

        /// <summary>
        /// 清空异常日志命令
        /// </summary>
        public ICommand ClearExceptionLogCommand { get; private set; } = null!;

        /// <summary>
        /// 导出异常报告命令
        /// </summary>
        public ICommand ExportExceptionReportCommand { get; private set; } = null!;

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化命令
        /// </summary>
        private void InitializeCommands()
        {
            InitializeExceptionHandlerCommand = new RelayCommand(async () => await InitializeExceptionHandlerAsync());
            TriggerInfoExceptionCommand = new RelayCommand(async () => await TriggerInfoExceptionAsync());
            TriggerWarningExceptionCommand = new RelayCommand(async () => await TriggerWarningExceptionAsync());
            TriggerErrorExceptionCommand = new RelayCommand(async () => await TriggerErrorExceptionAsync());
            TriggerCriticalExceptionCommand = new RelayCommand(async () => await TriggerCriticalExceptionAsync());
            TriggerCustomExceptionCommand = new RelayCommand(async () => await TriggerCustomExceptionAsync());
            TestExceptionRecoveryCommand = new RelayCommand(async () => await TestExceptionRecoveryAsync());
            TriggerUnhandledExceptionCommand = new RelayCommand(async () => await TriggerUnhandledExceptionAsync());
            ClearExceptionLogCommand = new RelayCommand(ClearExceptionLog);
            ExportExceptionReportCommand = new RelayCommand(async () => await ExportExceptionReportAsync());
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            // 添加示例日志
            AddExceptionLog(ExceptionLevel.Info, "异常处理演示系统启动");

            // 初始化统计信息
            UpdateStatistics();
        }

        /// <summary>
        /// 初始化异常处理器
        /// </summary>
        private void InitializeExceptionHandler()
        {
            try
            {
                // 注册异常处理器
                RegisterExceptionHandlers();

                // 设置全局异常处理
                if (EnableGlobalExceptionHandling)
                {
                    SetupGlobalExceptionHandling();
                }

                ExceptionHandlerStatus = "已初始化";
                StatusMessage = "异常处理器初始化完成";
                AddExceptionLog(ExceptionLevel.Info, "异常处理器初始化成功");

                UpdateHandlerCount();
            }
            catch (Exception ex)
            {
                _logger.Error($"异常处理器初始化失败: {ex.Message}", ex);
                ExceptionHandlerStatus = "初始化失败";
                StatusMessage = $"初始化失败: {ex.Message}";
                UnhandledException++;
            }
        }

        /// <summary>
        /// 注册异常处理器
        /// </summary>
        private void RegisterExceptionHandlers()
        {
            // 检查是否为SimpleExceptionHandler类型
            if (_exceptionHandler is SimpleExceptionHandler simpleHandler)
            {
                // 注册Info级别处理器
                simpleHandler.RegisterHandler(ExceptionLevel.Info, OnInfoException);
                AddHandler("Info异常处理器", ExceptionLevel.Info, 0);

                // 注册Warning级别处理器
                simpleHandler.RegisterHandler(ExceptionLevel.Warning, OnWarningException);
                AddHandler("Warning异常处理器", ExceptionLevel.Warning, 0);

                // 注册Error级别处理器
                simpleHandler.RegisterHandler(ExceptionLevel.Error, OnErrorException);
                AddHandler("Error异常处理器", ExceptionLevel.Error, 0);

                // 注册Critical级别处理器
                simpleHandler.RegisterHandler(ExceptionLevel.Critical, OnCriticalException);
                AddHandler("Critical异常处理器", ExceptionLevel.Critical, 0);
            }
            else
            {
                // 对于其他类型的异常处理器，使用默认处理
                AddHandler("默认异常处理器", ExceptionLevel.Error, 0);
            }
        }

        /// <summary>
        /// 设置全局异常处理
        /// </summary>
        private void SetupGlobalExceptionHandling()
        {
            // 设置应用程序域未处理异常处理
            AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;

            // 设置任务未处理异常处理
            TaskScheduler.UnobservedTaskException += OnUnobservedTaskException;
        }

        /// <summary>
        /// 异步初始化异常处理器
        /// </summary>
        private async Task InitializeExceptionHandlerAsync()
        {
            try
            {
                StatusMessage = "正在重新初始化异常处理器...";

                await Task.Run(() =>
                {
                    // 清理现有处理器
                    ClearHandlers();

                    // 重新初始化
                    InitializeExceptionHandler();
                });

                StatusMessage = "异常处理器重新初始化完成";
                AddExceptionLog(ExceptionLevel.Info, "异常处理器重新初始化成功");
            }
            catch (Exception ex)
            {
                _logger.Error($"异常处理器重新初始化失败: {ex.Message}", ex);
                StatusMessage = $"重新初始化失败: {ex.Message}";
                UnhandledException++;
            }
        }

        /// <summary>
        /// 触发Info级别异常
        /// </summary>
        private async Task TriggerInfoExceptionAsync()
        {
            try
            {
                await Task.Run(() =>
                {
                    var exception = new InformationException("这是一个Info级别的异常示例");

                    // 使用SimpleExceptionHandler的HandleException方法
                    if (_exceptionHandler is SimpleExceptionHandler simpleHandler)
                    {
                        simpleHandler.HandleException(exception, ExceptionLevel.Info);
                    }
                    else
                    {
                        // 使用标准IExceptionHandler接口
                        var context = new ExceptionContext { Source = "ExceptionDemo" };
                        _exceptionHandler.Handle(exception, context);
                    }
                });

                StatusMessage = "Info异常已触发";
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"触发Info异常失败: {ex.Message}");
                StatusMessage = $"触发Info异常失败: {ex.Message}";
                UnhandledException++;
            }
        }

        /// <summary>
        /// 触发Warning级别异常
        /// </summary>
        private async Task TriggerWarningExceptionAsync()
        {
            try
            {
                await Task.Run(() =>
                {
                    var exception = new WarningException("这是一个Warning级别的异常示例");

                    if (_exceptionHandler is SimpleExceptionHandler simpleHandler)
                    {
                        simpleHandler.HandleException(exception, ExceptionLevel.Warning);
                    }
                    else
                    {
                        var context = new ExceptionContext { Source = "ExceptionDemo" };
                        _exceptionHandler.Handle(exception, context);
                    }
                });

                StatusMessage = "Warning异常已触发";
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"触发Warning异常失败: {ex.Message}");
                StatusMessage = $"触发Warning异常失败: {ex.Message}";
                UnhandledException++;
            }
        }

        /// <summary>
        /// 触发Error级别异常
        /// </summary>
        private async Task TriggerErrorExceptionAsync()
        {
            try
            {
                await Task.Run(() =>
                {
                    var exception = new ErrorException("这是一个Error级别的异常示例");

                    if (_exceptionHandler is SimpleExceptionHandler simpleHandler)
                    {
                        simpleHandler.HandleException(exception, ExceptionLevel.Error);
                    }
                    else
                    {
                        var context = new ExceptionContext { Source = "ExceptionDemo" };
                        _exceptionHandler.Handle(exception, context);
                    }
                });

                StatusMessage = "Error异常已触发";
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"触发Error异常失败: {ex.Message}");
                StatusMessage = $"触发Error异常失败: {ex.Message}";
                UnhandledException++;
            }
        }

        /// <summary>
        /// 触发Critical级别异常
        /// </summary>
        private async Task TriggerCriticalExceptionAsync()
        {
            try
            {
                await Task.Run(() =>
                {
                    var exception = new CriticalException("这是一个Critical级别的异常示例");

                    if (_exceptionHandler is SimpleExceptionHandler simpleHandler)
                    {
                        simpleHandler.HandleException(exception, ExceptionLevel.Critical);
                    }
                    else
                    {
                        var context = new ExceptionContext { Source = "ExceptionDemo" };
                        _exceptionHandler.Handle(exception, context);
                    }
                });

                StatusMessage = "Critical异常已触发";
            }
            catch (Exception ex)
            {
                _logger.Error($"触发Critical异常失败: {ex.Message}", ex);
                StatusMessage = $"触发Critical异常失败: {ex.Message}";
                UnhandledException++;
            }
        }

        /// <summary>
        /// 触发自定义异常
        /// </summary>
        private async Task TriggerCustomExceptionAsync()
        {
            try
            {
                await Task.Run(() =>
                {
                    var exception = new Exception(CustomExceptionMessage);

                    if (_exceptionHandler is SimpleExceptionHandler simpleHandler)
                    {
                        simpleHandler.HandleException(exception, ExceptionLevel.Error);
                    }
                    else
                    {
                        var context = new ExceptionContext { Source = "ExceptionDemo" };
                        _exceptionHandler.Handle(exception, context);
                    }
                });

                StatusMessage = "自定义异常已触发";
            }
            catch (Exception ex)
            {
                _logger.Error($"触发自定义异常失败: {ex.Message}", ex);
                StatusMessage = $"触发自定义异常失败: {ex.Message}";
                UnhandledException++;
            }
        }

        /// <summary>
        /// 测试异常恢复
        /// </summary>
        private async Task TestExceptionRecoveryAsync()
        {
            try
            {
                StatusMessage = "正在测试异常恢复...";

                await Task.Run(() =>
                {
                    // 模拟异常恢复过程
                    System.Threading.Thread.Sleep(1000);
                });

                RecoveryCount++;
                StatusMessage = "异常恢复测试完成";
                AddExceptionLog(ExceptionLevel.Info, "异常恢复测试成功");
            }
            catch (Exception ex)
            {
                _logger.Error($"异常恢复测试失败: {ex.Message}", ex);
                StatusMessage = $"异常恢复测试失败: {ex.Message}";
                UnhandledException++;
            }
        }

        /// <summary>
        /// 触发未处理异常
        /// </summary>
        private async Task TriggerUnhandledExceptionAsync()
        {
            try
            {
                await Task.Run(() =>
                {
                    throw new Exception("这是一个未处理的异常示例");
                });
            }
            catch (Exception ex)
            {
                UnhandledException++;
                AddExceptionLog(ExceptionLevel.Critical, $"未处理异常: {ex.Message}");
                StatusMessage = "未处理异常已触发";
            }
        }

        /// <summary>
        /// 清空异常日志
        /// </summary>
        private void ClearExceptionLog()
        {
            ExceptionLogs.Clear();
            AddExceptionLog(ExceptionLevel.Info, "异常日志已清空");
            StatusMessage = "异常日志已清空";
        }

        /// <summary>
        /// 导出异常报告
        /// </summary>
        private async Task ExportExceptionReportAsync()
        {
            try
            {
                StatusMessage = "正在导出异常报告...";

                await Task.Run(() =>
                {
                    var fileName = $"ExceptionReport_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                    var filePath = System.IO.Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), fileName);

                    var reportContent = string.Join(Environment.NewLine,
                        ExceptionLogs.Select(log => log.ToString()));

                    System.IO.File.WriteAllText(filePath, reportContent);

                    StatusMessage = $"异常报告已导出到: {filePath}";
                });

                AddExceptionLog(ExceptionLevel.Info, "异常报告导出完成");
            }
            catch (Exception ex)
            {
                _logger.Error($"导出异常报告失败: {ex.Message}", ex);
                StatusMessage = $"导出异常报告失败: {ex.Message}";
                UnhandledException++;
            }
        }

        /// <summary>
        /// 添加异常日志
        /// </summary>
        /// <param name="level">异常级别</param>
        /// <param name="message">消息</param>
        private void AddExceptionLog(ExceptionLevel level, string message)
        {
            var logEntry = new ExceptionLogEntry(level, message, "ExceptionDemo");

            // 在UI线程中添加日志
            System.Windows.Application.Current?.Dispatcher.Invoke(() =>
            {
                ExceptionLogs.Insert(0, logEntry);

                // 限制日志数量，保留最新的1000条
                while (ExceptionLogs.Count > 1000)
                {
                    ExceptionLogs.RemoveAt(ExceptionLogs.Count - 1);
                }
            });

            // 更新统计信息
            TotalExceptions++;
            LastExceptionTime = DateTime.Now;

            switch (level)
            {
                case ExceptionLevel.Info:
                    InfoExceptions++;
                    break;
                case ExceptionLevel.Warning:
                    WarningExceptions++;
                    break;
                case ExceptionLevel.Error:
                    ErrorExceptions++;
                    break;
                case ExceptionLevel.Critical:
                    CriticalExceptions++;
                    break;
            }
        }

        /// <summary>
        /// 添加处理器
        /// </summary>
        /// <param name="name">处理器名称</param>
        /// <param name="level">异常级别</param>
        /// <param name="handledCount">处理次数</param>
        private void AddHandler(string name, ExceptionLevel level, int handledCount)
        {
            var handler = new HandlerInfo(name, level)
            {
                HandledCount = handledCount
            };

            System.Windows.Application.Current?.Dispatcher.Invoke(() =>
            {
                ActiveHandlers.Add(handler);
            });
        }

        /// <summary>
        /// 清理处理器
        /// </summary>
        private void ClearHandlers()
        {
            System.Windows.Application.Current?.Dispatcher.Invoke(() =>
            {
                ActiveHandlers.Clear();
            });
        }

        /// <summary>
        /// 更新处理器数量
        /// </summary>
        private void UpdateHandlerCount()
        {
            HandlerCount = ActiveHandlers.Count;
        }

        /// <summary>
        /// 更新统计信息
        /// </summary>
        private void UpdateStatistics()
        {
            // 更新处理器数量
            UpdateHandlerCount();
        }

        #region 事件处理方法

        /// <summary>
        /// 处理Info级别异常
        /// </summary>
        /// <param name="exception">异常对象</param>
        private void OnInfoException(Exception exception)
        {
            AddExceptionLog(ExceptionLevel.Info, $"Info异常处理: {exception.Message}");
            HandledException++;

            // 更新处理器统计
            var handler = ActiveHandlers.FirstOrDefault(h => h.Name == "Info异常处理器");
            handler?.RecordSuccess();
        }

        /// <summary>
        /// 处理Warning级别异常
        /// </summary>
        /// <param name="exception">异常对象</param>
        private void OnWarningException(Exception exception)
        {
            AddExceptionLog(ExceptionLevel.Warning, $"Warning异常处理: {exception.Message}");
            HandledException++;

            // 更新处理器统计
            var handler = ActiveHandlers.FirstOrDefault(h => h.Name == "Warning异常处理器");
            handler?.RecordSuccess();
        }

        /// <summary>
        /// 处理Error级别异常
        /// </summary>
        /// <param name="exception">异常对象</param>
        private void OnErrorException(Exception exception)
        {
            AddExceptionLog(ExceptionLevel.Error, $"Error异常处理: {exception.Message}");
            HandledException++;

            // 更新处理器统计
            var handler = ActiveHandlers.FirstOrDefault(h => h.Name == "Error异常处理器");
            handler?.RecordSuccess();
        }

        /// <summary>
        /// 处理Critical级别异常
        /// </summary>
        /// <param name="exception">异常对象</param>
        private void OnCriticalException(Exception exception)
        {
            AddExceptionLog(ExceptionLevel.Critical, $"Critical异常处理: {exception.Message}");
            HandledException++;

            // 更新处理器统计
            var handler = ActiveHandlers.FirstOrDefault(h => h.Name == "Critical异常处理器");
            handler?.RecordSuccess();
        }

        /// <summary>
        /// 处理未处理异常
        /// </summary>
        /// <param name="sender">发送者</param>
        /// <param name="e">事件参数</param>
        private void OnUnhandledException(object sender, System.UnhandledExceptionEventArgs e)
        {
            if (e.ExceptionObject is Exception exception)
            {
                AddExceptionLog(ExceptionLevel.Critical, $"未处理异常: {exception.Message}");
                UnhandledException++;
            }
        }

        /// <summary>
        /// 处理未观察到的任务异常
        /// </summary>
        /// <param name="sender">发送者</param>
        /// <param name="e">事件参数</param>
        private void OnUnobservedTaskException(object sender, UnobservedTaskExceptionEventArgs e)
        {
            AddExceptionLog(ExceptionLevel.Critical, $"未观察到的任务异常: {e.Exception.Message}");
            UnhandledException++;
            e.SetObserved(); // 标记异常已被观察
        }

        #endregion

        #endregion
    }
}
