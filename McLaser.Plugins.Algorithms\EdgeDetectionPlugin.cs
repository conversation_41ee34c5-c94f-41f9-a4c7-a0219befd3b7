using System;
using System.Collections.Generic;
using System.Drawing;
using System.Threading.Tasks;
using McLaser.Core.Plugins;
using McLaser.Core.Algorithms;

namespace McLaser.Plugins.Algorithms
{
    /// <summary>
    /// 边缘检测算法插件
    /// 实现多种边缘检测算法，用于激光加工中的图像处理
    /// </summary>
    [PluginMetadata(
        Id = "McLaser.Plugins.EdgeDetection",
        Name = "边缘检测算法",
        Version = "1.2.0",
        Description = "提供Canny、Sobel、Laplacian等多种边缘检测算法，用于工件边缘识别和路径规划",
        Author = "McLaser Algorithm Team",
        Category = "图像算法",
        SupportedPlatforms = new[] { "Windows" },
        MinFrameworkVersion = "4.7.2"
    )]
    public class EdgeDetectionPlugin : IPlugin, IImageProcessingAlgorithm
    {
        #region 私有字段

        private IPluginContext _context;
        private EdgeDetectionParameters _parameters;
        private readonly Dictionary<string, IEdgeDetector> _detectors;

        #endregion

        #region 构造函数

        public EdgeDetectionPlugin()
        {
            _detectors = new Dictionary<string, IEdgeDetector>
            {
                ["Canny"] = new CannyEdgeDetector(),
                ["Sobel"] = new SobelEdgeDetector(),
                ["Laplacian"] = new LaplacianEdgeDetector(),
                ["Roberts"] = new RobertsEdgeDetector()
            };

            _parameters = new EdgeDetectionParameters();
        }

        #endregion

        #region IPlugin 实现

        public string Id => "McLaser.Plugins.EdgeDetection";
        public string Name => "边缘检测算法";
        public Version Version => new Version(1, 2, 0);
        public string Description => "多种边缘检测算法集合";
        public PluginStatus Status { get; private set; } = PluginStatus.Stopped;

        public event EventHandler<PluginStatusChangedEventArgs> StatusChanged;
        public event EventHandler<PluginErrorEventArgs> ErrorOccurred;

        public async Task InitializeAsync(IPluginContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            
            try
            {
                // 初始化算法参数
                await LoadParametersAsync();
                
                // 初始化各个检测器
                foreach (var detector in _detectors.Values)
                {
                    await detector.InitializeAsync();
                }
                
                Status = PluginStatus.Initialized;
                OnStatusChanged(PluginStatus.Stopped, PluginStatus.Initialized);
            }
            catch (Exception ex)
            {
                Status = PluginStatus.Error;
                OnErrorOccurred($"边缘检测算法初始化失败: {ex.Message}", ex);
                throw;
            }
        }

        public async Task StartAsync()
        {
            try
            {
                if (Status != PluginStatus.Initialized && Status != PluginStatus.Stopped)
                    throw new InvalidOperationException($"插件状态错误: {Status}");

                Status = PluginStatus.Running;
                OnStatusChanged(PluginStatus.Initialized, PluginStatus.Running);
                
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                Status = PluginStatus.Error;
                OnErrorOccurred($"边缘检测算法启动失败: {ex.Message}", ex);
                throw;
            }
        }

        public async Task StopAsync()
        {
            try
            {
                Status = PluginStatus.Stopped;
                OnStatusChanged(PluginStatus.Running, PluginStatus.Stopped);
                
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"边缘检测算法停止失败: {ex.Message}", ex);
            }
        }

        public async Task ShutdownAsync()
        {
            try
            {
                if (Status == PluginStatus.Running)
                {
                    await StopAsync();
                }

                // 清理检测器资源
                foreach (var detector in _detectors.Values)
                {
                    detector.Dispose();
                }
                
                Status = PluginStatus.Shutdown;
                OnStatusChanged(Status, PluginStatus.Shutdown);
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"边缘检测算法关闭失败: {ex.Message}", ex);
            }
        }

        #endregion

        #region IImageProcessingAlgorithm 实现

        public string AlgorithmName => "EdgeDetection";
        public string AlgorithmVersion => "1.2.0";
        public AlgorithmCategory Category => AlgorithmCategory.ImageProcessing;
        public bool SupportsRealTime => true;

        public async Task<ImageProcessingResult> ProcessImageAsync(Bitmap inputImage, Dictionary<string, object> parameters = null)
        {
            try
            {
                if (Status != PluginStatus.Running)
                    throw new InvalidOperationException("算法插件未运行");

                if (inputImage == null)
                    throw new ArgumentNullException(nameof(inputImage));

                // 解析参数
                var processingParams = ParseParameters(parameters);
                
                // 选择检测器
                var detectorName = processingParams.DetectorType;
                if (!_detectors.TryGetValue(detectorName, out var detector))
                {
                    throw new ArgumentException($"不支持的边缘检测算法: {detectorName}");
                }

                // 执行边缘检测
                var startTime = DateTime.Now;
                var result = await detector.DetectEdgesAsync(inputImage, processingParams);
                var processingTime = DateTime.Now - startTime;

                // 构建结果
                return new ImageProcessingResult
                {
                    Success = true,
                    ProcessedImage = result.EdgeImage,
                    ProcessingTime = processingTime,
                    AlgorithmName = AlgorithmName,
                    Parameters = parameters,
                    Metadata = new Dictionary<string, object>
                    {
                        ["DetectorType"] = detectorName,
                        ["EdgeCount"] = result.EdgeCount,
                        ["EdgePoints"] = result.EdgePoints,
                        ["Threshold1"] = processingParams.Threshold1,
                        ["Threshold2"] = processingParams.Threshold2,
                        ["KernelSize"] = processingParams.KernelSize
                    }
                };
            }
            catch (Exception ex)
            {
                OnErrorOccurred($"图像处理失败: {ex.Message}", ex);
                return new ImageProcessingResult
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    ProcessingTime = TimeSpan.Zero
                };
            }
        }

        public async Task<bool> ValidateParametersAsync(Dictionary<string, object> parameters)
        {
            try
            {
                var processingParams = ParseParameters(parameters);
                
                // 验证参数有效性
                if (processingParams.Threshold1 < 0 || processingParams.Threshold1 > 255)
                    return false;
                
                if (processingParams.Threshold2 < 0 || processingParams.Threshold2 > 255)
                    return false;
                
                if (processingParams.KernelSize < 3 || processingParams.KernelSize % 2 == 0)
                    return false;
                
                if (!_detectors.ContainsKey(processingParams.DetectorType))
                    return false;

                return true;
            }
            catch
            {
                return false;
            }
        }

        public Dictionary<string, object> GetDefaultParameters()
        {
            return new Dictionary<string, object>
            {
                ["DetectorType"] = "Canny",
                ["Threshold1"] = 50.0,
                ["Threshold2"] = 150.0,
                ["KernelSize"] = 3,
                ["GaussianBlur"] = true,
                ["BlurKernelSize"] = 5,
                ["BlurSigma"] = 1.0
            };
        }

        public List<AlgorithmParameter> GetParameterDefinitions()
        {
            return new List<AlgorithmParameter>
            {
                new AlgorithmParameter
                {
                    Name = "DetectorType",
                    DisplayName = "检测算法",
                    Description = "选择边缘检测算法类型",
                    Type = typeof(string),
                    DefaultValue = "Canny",
                    AllowedValues = new object[] { "Canny", "Sobel", "Laplacian", "Roberts" }
                },
                new AlgorithmParameter
                {
                    Name = "Threshold1",
                    DisplayName = "低阈值",
                    Description = "边缘检测的低阈值",
                    Type = typeof(double),
                    DefaultValue = 50.0,
                    MinValue = 0.0,
                    MaxValue = 255.0
                },
                new AlgorithmParameter
                {
                    Name = "Threshold2",
                    DisplayName = "高阈值",
                    Description = "边缘检测的高阈值",
                    Type = typeof(double),
                    DefaultValue = 150.0,
                    MinValue = 0.0,
                    MaxValue = 255.0
                },
                new AlgorithmParameter
                {
                    Name = "KernelSize",
                    DisplayName = "核大小",
                    Description = "卷积核的大小",
                    Type = typeof(int),
                    DefaultValue = 3,
                    AllowedValues = new object[] { 3, 5, 7, 9 }
                },
                new AlgorithmParameter
                {
                    Name = "GaussianBlur",
                    DisplayName = "高斯模糊",
                    Description = "是否在边缘检测前应用高斯模糊",
                    Type = typeof(bool),
                    DefaultValue = true
                }
            };
        }

        #endregion

        #region 私有方法

        private async Task LoadParametersAsync()
        {
            // 从配置文件加载参数
            var configPath = System.IO.Path.Combine(_context.ConfigurationDirectory, "EdgeDetection.json");
            if (System.IO.File.Exists(configPath))
            {
                var json = System.IO.File.ReadAllText(configPath);
                _parameters = Newtonsoft.Json.JsonConvert.DeserializeObject<EdgeDetectionParameters>(json);
            }
            
            await Task.CompletedTask;
        }

        private EdgeDetectionParameters ParseParameters(Dictionary<string, object> parameters)
        {
            var result = new EdgeDetectionParameters();
            
            if (parameters != null)
            {
                if (parameters.TryGetValue("DetectorType", out var detectorType))
                    result.DetectorType = detectorType.ToString();
                
                if (parameters.TryGetValue("Threshold1", out var threshold1))
                    result.Threshold1 = Convert.ToDouble(threshold1);
                
                if (parameters.TryGetValue("Threshold2", out var threshold2))
                    result.Threshold2 = Convert.ToDouble(threshold2);
                
                if (parameters.TryGetValue("KernelSize", out var kernelSize))
                    result.KernelSize = Convert.ToInt32(kernelSize);
                
                if (parameters.TryGetValue("GaussianBlur", out var gaussianBlur))
                    result.GaussianBlur = Convert.ToBoolean(gaussianBlur);
            }
            
            return result;
        }

        private void OnStatusChanged(PluginStatus oldStatus, PluginStatus newStatus)
        {
            StatusChanged?.Invoke(this, new PluginStatusChangedEventArgs(oldStatus, newStatus));
        }

        private void OnErrorOccurred(string message, Exception exception = null)
        {
            ErrorOccurred?.Invoke(this, new PluginErrorEventArgs(this, message, exception));
        }

        #endregion
    }

    #region 辅助类

    public class EdgeDetectionParameters
    {
        public string DetectorType { get; set; } = "Canny";
        public double Threshold1 { get; set; } = 50.0;
        public double Threshold2 { get; set; } = 150.0;
        public int KernelSize { get; set; } = 3;
        public bool GaussianBlur { get; set; } = true;
        public int BlurKernelSize { get; set; } = 5;
        public double BlurSigma { get; set; } = 1.0;
    }

    public class EdgeDetectionResult
    {
        public Bitmap EdgeImage { get; set; }
        public int EdgeCount { get; set; }
        public List<Point> EdgePoints { get; set; } = new List<Point>();
        public double ProcessingTime { get; set; }
    }

    #endregion
}
