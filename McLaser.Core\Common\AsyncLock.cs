using System;
using System.Threading;
using System.Threading.Tasks;

namespace McLaser.Core.Common
{
    /// <summary>
    /// 异步锁实现
    /// 提供异步操作的互斥锁功能
    /// </summary>
    public sealed class AsyncLock : IDisposable
    {
        private readonly SemaphoreSlim _semaphore;
        private readonly Task<IDisposable> _releaser;
        private bool _disposed;

        /// <summary>
        /// 初始化异步锁
        /// </summary>
        public AsyncLock()
        {
            _semaphore = new SemaphoreSlim(1, 1);
            _releaser = Task.FromResult((IDisposable)new Releaser(_semaphore));
        }

        /// <summary>
        /// 异步获取锁
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>锁释放器</returns>
        public Task<IDisposable> LockAsync(CancellationToken cancellationToken = default)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(AsyncLock));

            var wait = _semaphore.WaitAsync(cancellationToken);
            return wait.IsCompleted
                ? _releaser
                : wait.ContinueWith(
                    (_, state) => (IDisposable)state!,
                    new Releaser(_semaphore),
                    cancellationToken,
                    TaskContinuationOptions.ExecuteSynchronously,
                    TaskScheduler.Default);
        }

        /// <summary>
        /// 同步获取锁
        /// </summary>
        /// <param name="timeout">超时时间</param>
        /// <returns>锁释放器</returns>
        public IDisposable Lock(TimeSpan timeout = default)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(AsyncLock));

            if (timeout == default)
                timeout = TimeSpan.FromMilliseconds(-1); // 无限等待

            if (_semaphore.Wait(timeout))
            {
                return new Releaser(_semaphore);
            }

            throw new TimeoutException("获取锁超时");
        }

        /// <summary>
        /// 尝试获取锁
        /// </summary>
        /// <param name="releaser">锁释放器</param>
        /// <returns>是否成功获取锁</returns>
        public bool TryLock(out IDisposable? releaser)
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(AsyncLock));

            if (_semaphore.Wait(0))
            {
                releaser = new Releaser(_semaphore);
                return true;
            }

            releaser = null;
            return false;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _semaphore?.Dispose();
                _disposed = true;
            }
        }

        /// <summary>
        /// 锁释放器
        /// </summary>
        private sealed class Releaser : IDisposable
        {
            private readonly SemaphoreSlim _semaphore;
            private bool _disposed;

            internal Releaser(SemaphoreSlim semaphore)
            {
                _semaphore = semaphore;
            }

            public void Dispose()
            {
                if (!_disposed)
                {
                    _semaphore.Release();
                    _disposed = true;
                }
            }
        }
    }

    /// <summary>
    /// 异步锁扩展方法
    /// </summary>
    public static class AsyncLockExtensions
    {
        /// <summary>
        /// 使用异步锁执行操作
        /// </summary>
        /// <param name="asyncLock">异步锁</param>
        /// <param name="action">要执行的操作</param>
        /// <param name="cancellationToken">取消令牌</param>
        public static async Task WithLockAsync(this AsyncLock asyncLock, Func<Task> action, CancellationToken cancellationToken = default)
        {
            using (await asyncLock.LockAsync(cancellationToken))
            {
                await action();
            }
        }

        /// <summary>
        /// 使用异步锁执行操作并返回结果
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="asyncLock">异步锁</param>
        /// <param name="func">要执行的函数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>操作结果</returns>
        public static async Task<T> WithLockAsync<T>(this AsyncLock asyncLock, Func<Task<T>> func, CancellationToken cancellationToken = default)
        {
            using (await asyncLock.LockAsync(cancellationToken))
            {
                return await func();
            }
        }

        /// <summary>
        /// 使用同步锁执行操作
        /// </summary>
        /// <param name="asyncLock">异步锁</param>
        /// <param name="action">要执行的操作</param>
        /// <param name="timeout">超时时间</param>
        public static void WithLock(this AsyncLock asyncLock, Action action, TimeSpan timeout = default)
        {
            using (asyncLock.Lock(timeout))
            {
                action();
            }
        }

        /// <summary>
        /// 使用同步锁执行操作并返回结果
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="asyncLock">异步锁</param>
        /// <param name="func">要执行的函数</param>
        /// <param name="timeout">超时时间</param>
        /// <returns>操作结果</returns>
        public static T WithLock<T>(this AsyncLock asyncLock, Func<T> func, TimeSpan timeout = default)
        {
            using (asyncLock.Lock(timeout))
            {
                return func();
            }
        }
    }
}
