using System;

namespace McLaser.Core.Framework.Logging
{
    /// <summary>
    /// 日志工厂接口
    /// </summary>
    public interface ILoggerFactory
    {
        /// <summary>
        /// 创建日志记录器
        /// </summary>
        /// <param name="name">日志记录器名称</param>
        /// <returns>日志记录器实例</returns>
        ILogger CreateLogger(string name);

        /// <summary>
        /// 创建日志记录器
        /// </summary>
        /// <typeparam name="T">类型</typeparam>
        /// <returns>日志记录器实例</returns>
        ILogger CreateLogger<T>();

        /// <summary>
        /// 创建日志记录器
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>日志记录器实例</returns>
        ILogger CreateLogger(Type type);
    }
}
