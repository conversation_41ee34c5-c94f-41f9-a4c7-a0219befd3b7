using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace McLaser.Device
{
    /// <summary>
    /// 传感器设备接口
    /// 定义传感器设备的基本操作和属性
    /// </summary>
    public interface ISensor : IDevice
    {
        #region 属性

        /// <summary>
        /// 传感器类型
        /// </summary>
        SensorType SensorType { get; }

        /// <summary>
        /// 测量单位
        /// </summary>
        string Unit { get; }

        /// <summary>
        /// 测量范围最小值
        /// </summary>
        double MinValue { get; }

        /// <summary>
        /// 测量范围最大值
        /// </summary>
        double MaxValue { get; }

        /// <summary>
        /// 测量精度
        /// </summary>
        double Accuracy { get; }

        /// <summary>
        /// 采样频率(Hz)
        /// </summary>
        double SamplingRate { get; set; }

        /// <summary>
        /// 是否启用自动采样
        /// </summary>
        bool AutoSampling { get; set; }

        /// <summary>
        /// 当前测量值
        /// </summary>
        double CurrentValue { get; }

        /// <summary>
        /// 传感器状态
        /// </summary>
        SensorStatus SensorStatus { get; }

        /// <summary>
        /// 校准状态
        /// </summary>
        bool IsCalibrated { get; }

        #endregion

        #region 方法

        /// <summary>
        /// 读取传感器值
        /// </summary>
        /// <param name="value">测量值</param>
        /// <returns>是否成功</returns>
        bool ReadValue(ref double value);

        /// <summary>
        /// 读取多个传感器值
        /// </summary>
        /// <param name="values">测量值数组</param>
        /// <param name="count">读取数量</param>
        /// <returns>是否成功</returns>
        bool ReadValues(ref double[] values, int count);

        /// <summary>
        /// 开始连续采样
        /// </summary>
        /// <returns>是否成功</returns>
        bool StartSampling();

        /// <summary>
        /// 停止连续采样
        /// </summary>
        /// <returns>是否成功</returns>
        bool StopSampling();

        /// <summary>
        /// 检查是否正在采样
        /// </summary>
        /// <returns>是否正在采样</returns>
        bool IsSampling();

        /// <summary>
        /// 设置采样参数
        /// </summary>
        /// <param name="rate">采样频率</param>
        /// <param name="bufferSize">缓冲区大小</param>
        /// <returns>是否成功</returns>
        bool SetSamplingParameters(double rate, int bufferSize);

        /// <summary>
        /// 校准传感器
        /// </summary>
        /// <param name="referenceValue">参考值</param>
        /// <returns>是否成功</returns>
        bool Calibrate(double referenceValue);

        /// <summary>
        /// 零点校准
        /// </summary>
        /// <returns>是否成功</returns>
        bool ZeroCalibration();

        /// <summary>
        /// 满量程校准
        /// </summary>
        /// <param name="fullScaleValue">满量程值</param>
        /// <returns>是否成功</returns>
        bool FullScaleCalibration(double fullScaleValue);

        /// <summary>
        /// 重置校准
        /// </summary>
        /// <returns>是否成功</returns>
        bool ResetCalibration();

        /// <summary>
        /// 设置报警阈值
        /// </summary>
        /// <param name="lowThreshold">低阈值</param>
        /// <param name="highThreshold">高阈值</param>
        /// <returns>是否成功</returns>
        bool SetAlarmThresholds(double lowThreshold, double highThreshold);

        /// <summary>
        /// 获取报警阈值
        /// </summary>
        /// <param name="lowThreshold">低阈值</param>
        /// <param name="highThreshold">高阈值</param>
        /// <returns>是否成功</returns>
        bool GetAlarmThresholds(ref double lowThreshold, ref double highThreshold);

        /// <summary>
        /// 启用/禁用报警
        /// </summary>
        /// <param name="enable">是否启用</param>
        /// <returns>是否成功</returns>
        bool EnableAlarm(bool enable);

        /// <summary>
        /// 获取传感器信息
        /// </summary>
        /// <returns>传感器信息</returns>
        SensorInfo GetSensorInfo();

        /// <summary>
        /// 获取历史数据
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>历史数据列表</returns>
        List<SensorDataPoint> GetHistoryData(DateTime startTime, DateTime endTime);

        /// <summary>
        /// 清除历史数据
        /// </summary>
        /// <returns>是否成功</returns>
        bool ClearHistoryData();

        /// <summary>
        /// 传感器自检
        /// </summary>
        /// <returns>自检结果</returns>
        bool SelfTest();

        #endregion

        #region 事件

        /// <summary>
        /// 数据更新事件
        /// </summary>
        event EventHandler<SensorDataEventArgs> DataUpdated;

        /// <summary>
        /// 报警事件
        /// </summary>
        event EventHandler<SensorAlarmEventArgs> AlarmTriggered;

        /// <summary>
        /// 传感器状态变化事件
        /// </summary>
        event EventHandler<SensorStatusChangedEventArgs> StatusChanged;

        /// <summary>
        /// 校准完成事件
        /// </summary>
        event EventHandler<SensorCalibrationEventArgs> CalibrationCompleted;

        #endregion
    }

    /// <summary>
    /// 传感器类型枚举
    /// </summary>
    public enum SensorType
    {
        /// <summary>
        /// 未知类型
        /// </summary>
        Unknown,

        /// <summary>
        /// 温度传感器
        /// </summary>
        Temperature,

        /// <summary>
        /// 湿度传感器
        /// </summary>
        Humidity,

        /// <summary>
        /// 压力传感器
        /// </summary>
        Pressure,

        /// <summary>
        /// 位移传感器
        /// </summary>
        Displacement,

        /// <summary>
        /// 力传感器
        /// </summary>
        Force,

        /// <summary>
        /// 振动传感器
        /// </summary>
        Vibration,

        /// <summary>
        /// 光强传感器
        /// </summary>
        LightIntensity,

        /// <summary>
        /// 距离传感器
        /// </summary>
        Distance,

        /// <summary>
        /// 角度传感器
        /// </summary>
        Angle,

        /// <summary>
        /// 速度传感器
        /// </summary>
        Velocity,

        /// <summary>
        /// 加速度传感器
        /// </summary>
        Acceleration
    }

    /// <summary>
    /// 传感器状态枚举
    /// </summary>
    public enum SensorStatus
    {
        /// <summary>
        /// 未知状态
        /// </summary>
        Unknown,

        /// <summary>
        /// 离线
        /// </summary>
        Offline,

        /// <summary>
        /// 初始化中
        /// </summary>
        Initializing,

        /// <summary>
        /// 准备就绪
        /// </summary>
        Ready,

        /// <summary>
        /// 正在采样
        /// </summary>
        Sampling,

        /// <summary>
        /// 校准中
        /// </summary>
        Calibrating,

        /// <summary>
        /// 错误状态
        /// </summary>
        Error,

        /// <summary>
        /// 警告状态
        /// </summary>
        Warning,

        /// <summary>
        /// 维护模式
        /// </summary>
        Maintenance
    }

    /// <summary>
    /// 传感器信息类
    /// </summary>
    public class SensorInfo
    {
        /// <summary>
        /// 传感器型号
        /// </summary>
        public string Model { get; set; }

        /// <summary>
        /// 制造商
        /// </summary>
        public string Manufacturer { get; set; }

        /// <summary>
        /// 序列号
        /// </summary>
        public string SerialNumber { get; set; }

        /// <summary>
        /// 固件版本
        /// </summary>
        public string FirmwareVersion { get; set; }

        /// <summary>
        /// 校准日期
        /// </summary>
        public DateTime CalibrationDate { get; set; }

        /// <summary>
        /// 下次校准日期
        /// </summary>
        public DateTime NextCalibrationDate { get; set; }
    }

    /// <summary>
    /// 传感器数据点类
    /// </summary>
    public class SensorDataPoint
    {
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 测量值
        /// </summary>
        public double Value { get; set; }

        /// <summary>
        /// 数据质量
        /// </summary>
        public DataQuality Quality { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="value">测量值</param>
        /// <param name="quality">数据质量</param>
        public SensorDataPoint(double value, DataQuality quality = DataQuality.Good)
        {
            Timestamp = DateTime.Now;
            Value = value;
            Quality = quality;
        }
    }

    /// <summary>
    /// 数据质量枚举
    /// </summary>
    public enum DataQuality
    {
        /// <summary>
        /// 良好
        /// </summary>
        Good,

        /// <summary>
        /// 可疑
        /// </summary>
        Uncertain,

        /// <summary>
        /// 错误
        /// </summary>
        Bad
    }

    /// <summary>
    /// 传感器数据事件参数
    /// </summary>
    public class SensorDataEventArgs : EventArgs
    {
        /// <summary>
        /// 数据点
        /// </summary>
        public SensorDataPoint DataPoint { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dataPoint">数据点</param>
        public SensorDataEventArgs(SensorDataPoint dataPoint)
        {
            DataPoint = dataPoint;
        }
    }

    /// <summary>
    /// 传感器报警事件参数
    /// </summary>
    public class SensorAlarmEventArgs : EventArgs
    {
        /// <summary>
        /// 报警类型
        /// </summary>
        public AlarmType AlarmType { get; set; }

        /// <summary>
        /// 当前值
        /// </summary>
        public double CurrentValue { get; set; }

        /// <summary>
        /// 阈值
        /// </summary>
        public double Threshold { get; set; }

        /// <summary>
        /// 报警信息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 报警时间
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="alarmType">报警类型</param>
        /// <param name="currentValue">当前值</param>
        /// <param name="threshold">阈值</param>
        /// <param name="message">报警信息</param>
        public SensorAlarmEventArgs(AlarmType alarmType, double currentValue, double threshold, string message)
        {
            AlarmType = alarmType;
            CurrentValue = currentValue;
            Threshold = threshold;
            Message = message;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 报警类型枚举
    /// </summary>
    public enum AlarmType
    {
        /// <summary>
        /// 低报警
        /// </summary>
        Low,

        /// <summary>
        /// 高报警
        /// </summary>
        High,

        /// <summary>
        /// 超出范围
        /// </summary>
        OutOfRange,

        /// <summary>
        /// 传感器故障
        /// </summary>
        SensorFault
    }

    /// <summary>
    /// 传感器状态变化事件参数
    /// </summary>
    public class SensorStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 旧状态
        /// </summary>
        public SensorStatus OldStatus { get; set; }

        /// <summary>
        /// 新状态
        /// </summary>
        public SensorStatus NewStatus { get; set; }

        /// <summary>
        /// 变化时间
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="oldStatus">旧状态</param>
        /// <param name="newStatus">新状态</param>
        public SensorStatusChangedEventArgs(SensorStatus oldStatus, SensorStatus newStatus)
        {
            OldStatus = oldStatus;
            NewStatus = newStatus;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 传感器校准事件参数
    /// </summary>
    public class SensorCalibrationEventArgs : EventArgs
    {
        /// <summary>
        /// 校准类型
        /// </summary>
        public CalibrationType CalibrationType { get; set; }

        /// <summary>
        /// 校准结果
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 校准信息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 校准时间
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="calibrationType">校准类型</param>
        /// <param name="success">校准结果</param>
        /// <param name="message">校准信息</param>
        public SensorCalibrationEventArgs(CalibrationType calibrationType, bool success, string message)
        {
            CalibrationType = calibrationType;
            Success = success;
            Message = message;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 校准类型枚举
    /// </summary>
    public enum CalibrationType
    {
        /// <summary>
        /// 零点校准
        /// </summary>
        Zero,

        /// <summary>
        /// 满量程校准
        /// </summary>
        FullScale,

        /// <summary>
        /// 两点校准
        /// </summary>
        TwoPoint,

        /// <summary>
        /// 多点校准
        /// </summary>
        MultiPoint
    }
}
