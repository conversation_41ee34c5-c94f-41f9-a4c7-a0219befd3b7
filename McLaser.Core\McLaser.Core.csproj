<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{B67B75F4-B65B-4FB4-9E9B-F86D2AA29B38}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>McLaser.Core</RootNamespace>
    <AssemblyName>McLaser.Core</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <LangVersion>11.0</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\bin\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.Composition.Registration" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Reflection.Context" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xaml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ApplicationBase\ViewModels\MainViewModel.cs" />
    <Compile Include="ApplicationBase\ViewModels\NavigationItemViewModel.cs" />
    <Compile Include="ApplicationBase\ViewModels\NavigationViewModel.cs" />
    <Compile Include="ApplicationBase\Views\BottomNavigationBar.xaml.cs">
      <DependentUpon>BottomNavigationBar.xaml</DependentUpon>
    </Compile>
    <Compile Include="ApplicationBase\Views\CategoryPopup.xaml.cs">
      <DependentUpon>CategoryPopup.xaml</DependentUpon>
    </Compile>
    <Compile Include="ApplicationBase\Views\MainWindow.xaml.cs">
      <DependentUpon>MainWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="ApplicationBase\Views\NavigationButton.xaml.cs">
      <DependentUpon>NavigationButton.xaml</DependentUpon>
    </Compile>
    <Compile Include="Common\AsyncLock.cs" />
    <Compile Include="Common\AsyncRelayCommand.cs" />
    <Compile Include="Common\DisposableBase.cs" />
    <Compile Include="Common\JsonHelper.cs" />
    <Compile Include="Common\NotifyPropertyBase.cs" />
    <Compile Include="Common\PathDefine.cs" />
    <Compile Include="Common\RelayCommand.cs" />
    <Compile Include="Common\RetryHelper.cs" />
    <Compile Include="Common\TypeHelper.cs" />
    <Compile Include="Common\ViewModelBase.cs" />
    <Compile Include="Communication\CommunicationModels.cs" />
    <Compile Include="Communication\ICommunicationChannel.cs" />
    <Compile Include="Communication\IMessageProtocol.cs" />
    <Compile Include="Communication\MessageProtocolBase.cs" />
    <Compile Include="Communication\SerialPortChannel.cs" />
    <Compile Include="Communication\TcpCommunicationChannel.cs" />
    <Compile Include="Communication\UdpCommunicationChannel.cs" />
    <Compile Include="Configuration\ConfigurationManager.cs" />
    <Compile Include="Configuration\ConfigurationModels.cs" />
    <Compile Include="Configuration\IConfigurationManager.cs" />
    <Compile Include="Configuration\IConfigurationProvider.cs" />
    <Compile Include="Configuration\JsonConfigurationProvider.cs" />
    <Compile Include="EventBus\EventBus.cs" />
    <Compile Include="EventBus\EventBusModels.cs" />
    <Compile Include="EventBus\IEventBus.cs" />
    <Compile Include="ExceptionHandling\ExceptionModels.cs" />
    <Compile Include="ExceptionHandling\ExceptionPolicy.cs" />
    <Compile Include="ExceptionHandling\GlobalExceptionHandler.cs" />
    <Compile Include="ExceptionHandling\IExceptionHandler.cs" />
    <Compile Include="ApplicationBase\ApplicationBase.cs" />
    <Compile Include="Framework\Bootstrapper\ApplicationInfo.cs" />
    <Compile Include="Framework\Bootstrapper\DefaultServiceRegistry.cs" />
    <Compile Include="Framework\Bootstrapper\IApplicationBuilder.cs" />
    <Compile Include="Framework\Bootstrapper\IApplicationCore.cs" />
    <Compile Include="Framework\Bootstrapper\IConfigureableApplication.cs" />
    <Compile Include="Framework\Bootstrapper\IServiceProvider.cs" />
    <Compile Include="Framework\Bootstrapper\IServiceRegistry.cs" />
    <Compile Include="Framework\Caching\ICacheManager.cs" />
    <Compile Include="Framework\Configuration\DefaultConfigurationService.cs" />
    <Compile Include="Framework\Configuration\IConfigurationService.cs" />
    <Compile Include="Framework\Container\ContainerManager.cs" />
    <Compile Include="Framework\Container\ContainerServiceProviderAdapter.cs" />
    <Compile Include="Framework\Container\ContainerTests.cs" />
    <Compile Include="Framework\Container\DefaultContainerAdapter.cs" />
    <Compile Include="Framework\Container\IContainer.cs" />
    <Compile Include="Framework\Container\IoC.cs" />
    <Compile Include="Framework\Container\MefContainerAdapter.cs" />
    <Compile Include="Framework\Container\ServiceLocator.cs" />
    <Compile Include="Framework\Container\ServiceRegistration.cs" />
    <Compile Include="Framework\Data\IRepository.cs" />
    <Compile Include="Framework\Data\IUnitOfWork.cs" />
    <Compile Include="Framework\IO\AppPaths.cs" />
    <Compile Include="Framework\IO\AppPathService.cs" />
    <Compile Include="Framework\IO\IAppPathServce.cs" />
    <Compile Include="Framework\IO\IFileSystemService.cs" />
    <Compile Include="Framework\Logging\DefaultLogger.cs" />
    <Compile Include="Framework\Logging\ILogger.cs" />
    <Compile Include="Framework\Logging\ILoggerFactory.cs" />
    <Compile Include="Framework\Logging\LoggerExtensions.cs" />
    <Compile Include="Framework\Logging\LogLevel.cs" />
    <Compile Include="Framework\Network\IHttpClientService.cs" />
    <Compile Include="Framework\Performance\IPerformanceCounter.cs" />
    <Compile Include="Framework\Security\DefaultPermissionService.cs" />
    <Compile Include="Framework\Security\DefaultRoleService.cs" />
    <Compile Include="Framework\Security\DefaultUserService.cs" />
    <Compile Include="Framework\Security\IPermissionService.cs" />
    <Compile Include="Framework\Security\IRoleService.cs" />
    <Compile Include="Framework\Security\IUserService.cs" />
    <Compile Include="Framework\Security\SecurityEventArgs.cs" />
    <Compile Include="Framework\Security\SecurityModels.cs" />
    <Compile Include="Framework\Serialization\DefaultJsonService.cs" />
    <Compile Include="Framework\Serialization\IJsonService.cs" />
    <Compile Include="Framework\Services\DefaultDialogService.cs" />
    <Compile Include="Framework\Services\DefaultExceptionHandlingService.cs" />
    <Compile Include="Framework\Services\DefaultNavigationService.cs" />
    <Compile Include="Framework\Services\IDialogService.cs" />
    <Compile Include="Framework\Services\IExceptionHandlingService.cs" />
    <Compile Include="Framework\Services\INavigationService.cs" />
    <Compile Include="Framework\Styles\DefaultStyleService.cs" />
    <Compile Include="Framework\Styles\IStyleService.cs" />
    <Compile Include="Framework\UI\BindingProxy.cs" />
    <Compile Include="Framework\UI\IThemeService.cs" />
    <Compile Include="Framework\UI\IWindowManager.cs" />
    <Compile Include="Framework\UI\ThemeManager.cs" />
    <Compile Include="Framework\UI\WindowManager.cs" />
    <Compile Include="Framework\Validation\IValidationRule.cs" />
    <Compile Include="Framework\Validation\ValidationEngine.cs" />
    <Compile Include="Modules\Home\HomeViewModel.cs" />
    <Compile Include="Modules\Home\HomeView.xaml.cs">
      <DependentUpon>HomeView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Modules\RecipeManager\ILoadable.cs" />
    <Compile Include="Modules\RecipeManager\IRecipe.cs" />
    <Compile Include="Modules\RecipeManager\IRecipeItem.cs" />
    <Compile Include="Modules\RecipeManager\IRecipeManager.cs" />
    <Compile Include="Modules\RecipeManager\IRecipeManagerOption.cs" />
    <Compile Include="Modules\RecipeManager\ISaveable.cs" />
    <Compile Include="Modules\RecipeManager\Recipe.cs" />
    <Compile Include="Modules\RecipeManager\RecipeManager.cs" />
    <Compile Include="Modules\RecipeManager\RecipeManagerOption.cs" />
    <Compile Include="Modules\RecipeManager\Test\RecipeData1.cs" />
    <Compile Include="Modules\RecipeManager\ViewModels\RecipeManagerViewModel.cs" />
    <Compile Include="Modules\RecipeManager\Views\RecipeManagerView.xaml.cs">
      <DependentUpon>RecipeManagerView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Navigation\INavigationPage.cs" />
    <Compile Include="Navigation\INavigationService.cs" />
    <Compile Include="Navigation\NavigationCategory.cs" />
    <Compile Include="Navigation\NavigationItem.cs" />
    <Compile Include="Navigation\NavigationService.cs" />
    <Compile Include="Navigation\PageInfo.cs" />
    <Compile Include="Plugins\DataAnalyzers.cs" />
    <Compile Include="Plugins\IPlugin.cs" />
    <Compile Include="Plugins\IPluginManager.cs" />
    <Compile Include="Plugins\PluginInterfaces.cs" />
    <Compile Include="Plugins\PluginLoader.cs" />
    <Compile Include="Plugins\PluginManager.cs" />
    <Compile Include="Plugins\PluginModels.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Plugins\Documentation\兼容现有架构的插件使用指南.md" />
    <None Include="Plugins\Documentation\插件使用示例.md" />
    <None Include="Plugins\Documentation\插件应用场景说明.md" />
    <None Include="Plugins\Documentation\插件系统总结.md" />
  </ItemGroup>
  <ItemGroup>
    <Page Include="ApplicationBase\Views\BottomNavigationBar.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="ApplicationBase\Views\CategoryPopup.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="ApplicationBase\Views\MainWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="ApplicationBase\Views\NavigationButton.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Modules\Home\HomeView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Modules\RecipeManager\Views\RecipeManagerView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Modules\DataInput\" />
    <Folder Include="Modules\DeviceManager\" />
    <Folder Include="Modules\DeviceStatus\" />
    <Folder Include="Modules\EventBusDemo\" />
    <Folder Include="Modules\ExceptionDemo\" />
    <Folder Include="Modules\PluginDemo\" />
    <Folder Include="Modules\Settings\" />
    <Folder Include="Services\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>