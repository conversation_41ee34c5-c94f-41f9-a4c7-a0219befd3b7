using System;
using System.Collections.Generic;

namespace McLaser.Core.Framework.Container
{
    /// <summary>
    /// 容器管理器
    /// 提供统一的容器访问和管理功能
    /// </summary>
    public static class ContainerManager
    {
        private static IContainer? _currentContainer;
        private static readonly object _lockObject = new object();
        private static ContainerType _containerType = ContainerType.Default;

        /// <summary>
        /// 当前容器实例
        /// </summary>
        public static IContainer Current
        {
            get
            {
                if (_currentContainer == null)
                {
                    lock (_lockObject)
                    {
                        if (_currentContainer == null)
                        {
                            _currentContainer = CreateDefaultContainer();
                        }
                    }
                }
                return _currentContainer;
            }
        }

        /// <summary>
        /// 当前容器类型
        /// </summary>
        public static ContainerType CurrentType => _containerType;

        /// <summary>
        /// 容器变更事件
        /// </summary>
        public static event EventHandler<ContainerChangedEventArgs>? ContainerChanged;

        /// <summary>
        /// 设置容器实例
        /// </summary>
        /// <param name="container">容器实例</param>
        /// <param name="containerType">容器类型</param>
        public static void SetContainer(IContainer container, ContainerType containerType = ContainerType.Custom)
        {
            if (container == null)
                throw new ArgumentNullException(nameof(container));

            lock (_lockObject)
            {
                var oldContainer = _currentContainer;
                var oldType = _containerType;

                _currentContainer = container;
                _containerType = containerType;

                // 触发容器变更事件
                OnContainerChanged(new ContainerChangedEventArgs(oldContainer, oldType, container, containerType));

                // 释放旧容器
                oldContainer?.Dispose();
            }
        }

        /// <summary>
        /// 使用默认容器
        /// </summary>
        public static void UseDefaultContainer()
        {
            var container = new DefaultContainerAdapter();
            SetContainer(container, ContainerType.Default);
        }

        /// <summary>
        /// 使用MEF容器
        /// </summary>
        public static void UseMefContainer()
        {
            var container = MefContainerAdapter.CreateDefault();
            SetContainer(container, ContainerType.Mef);
        }

        /// <summary>
        /// 使用自定义容器
        /// </summary>
        /// <param name="container">自定义容器实例</param>
        public static void UseCustomContainer(IContainer container)
        {
            SetContainer(container, ContainerType.Custom);
        }

        /// <summary>
        /// 重置为默认容器
        /// </summary>
        public static void Reset()
        {
            lock (_lockObject)
            {
                _currentContainer?.Dispose();
                _currentContainer = null;
                _containerType = ContainerType.Default;
            }
        }

        /// <summary>
        /// 注册核心服务
        /// </summary>
        /// <param name="container">容器实例</param>
        public static void RegisterCoreServices(IContainer container)
        {
            if (container == null)
                throw new ArgumentNullException(nameof(container));

            // 注册容器本身
            container.RegisterSingleton<IContainer>(container);

            // 注册核心框架服务
            RegisterFrameworkServices(container);
        }

        /// <summary>
        /// 注册框架服务
        /// </summary>
        /// <param name="container">容器实例</param>
        private static void RegisterFrameworkServices(IContainer container)
        {
            // 这里可以注册框架的核心服务
            // 例如：日志、配置、异常处理等服务的默认实现
            
            // 注册日志工厂
            container.RegisterFactory<Framework.Logging.ILoggerFactory>(c => 
                new Framework.Logging.DefaultLoggerFactory());

            // 注册配置服务
            //container.RegisterSingleton<Framework.Configuration.IConfigurationService, 
            //    Framework.Configuration.DefaultConfigurationService>();

            // 注册对话框服务
            container.RegisterSingleton<Framework.Services.IDialogService, 
                Framework.Services.DefaultDialogService>();

            // 注册导航服务
            //container.RegisterSingleton<Framework.Services.INavigationService, 
            //    Framework.Services.DefaultNavigationService>();

            // 注册异常处理服务
            container.RegisterFactory<Framework.Services.IExceptionHandlingService>(c =>
            {
                var logger = c.TryResolve<Framework.Logging.ILoggerFactory>()?.CreateLogger("ExceptionHandler");
                var dialogService = c.TryResolve<Framework.Services.IDialogService>();
                return new Framework.Services.DefaultExceptionHandlingService(logger, dialogService);
            });
        }

        /// <summary>
        /// 创建默认容器
        /// </summary>
        /// <returns>默认容器实例</returns>
        private static IContainer CreateDefaultContainer()
        {
            var container = new DefaultContainerAdapter();
            RegisterCoreServices(container);
            return container;
        }

        /// <summary>
        /// 触发容器变更事件
        /// </summary>
        /// <param name="args">事件参数</param>
        private static void OnContainerChanged(ContainerChangedEventArgs args)
        {
            ContainerChanged?.Invoke(null, args);
        }

        /// <summary>
        /// 获取容器统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public static IContainerStatistics GetStatistics()
        {
            return Current.Statistics;
        }

        /// <summary>
        /// 验证容器配置
        /// </summary>
        /// <returns>验证结果</returns>
        public static ContainerValidationResult ValidateConfiguration()
        {
            var result = new ContainerValidationResult();
            var container = Current;

            try
            {
                // 验证核心服务是否可以解析
                var coreServices = new[]
                {
                    typeof(Framework.Logging.ILoggerFactory),
                    typeof(Framework.Configuration.IConfigurationService),
                    typeof(Framework.Services.IDialogService),
                    //typeof(Framework.Services.INavigationService),
                    typeof(Framework.Services.IExceptionHandlingService)
                };

                foreach (var serviceType in coreServices)
                {
                    try
                    {
                        var service = container.TryResolve(serviceType);
                        if (service == null)
                        {
                            result.AddError($"核心服务 {serviceType.Name} 无法解析");
                        }
                        else
                        {
                            result.AddSuccess($"核心服务 {serviceType.Name} 解析成功");
                        }
                    }
                    catch (Exception ex)
                    {
                        result.AddError($"核心服务 {serviceType.Name} 解析失败: {ex.Message}");
                    }
                }

                result.IsValid = result.Errors.Count == 0;
            }
            catch (Exception ex)
            {
                result.AddError($"容器验证过程中发生异常: {ex.Message}");
                result.IsValid = false;
            }

            return result;
        }
    }

    /// <summary>
    /// 容器类型枚举
    /// </summary>
    public enum ContainerType
    {
        /// <summary>
        /// 默认容器
        /// </summary>
        Default,

        /// <summary>
        /// MEF容器
        /// </summary>
        Mef,

        /// <summary>
        /// 自定义容器
        /// </summary>
        Custom
    }

    /// <summary>
    /// 容器变更事件参数
    /// </summary>
    public class ContainerChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 旧容器
        /// </summary>
        public IContainer? OldContainer { get; }

        /// <summary>
        /// 旧容器类型
        /// </summary>
        public ContainerType OldType { get; }

        /// <summary>
        /// 新容器
        /// </summary>
        public IContainer NewContainer { get; }

        /// <summary>
        /// 新容器类型
        /// </summary>
        public ContainerType NewType { get; }

        public ContainerChangedEventArgs(IContainer? oldContainer, ContainerType oldType, 
            IContainer newContainer, ContainerType newType)
        {
            OldContainer = oldContainer;
            OldType = oldType;
            NewContainer = newContainer;
            NewType = newType;
        }
    }

    /// <summary>
    /// 容器验证结果
    /// </summary>
    public class ContainerValidationResult
    {
        /// <summary>
        /// 是否验证通过
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 错误信息列表
        /// </summary>
        public List<string> Errors { get; } = new List<string>();

        /// <summary>
        /// 成功信息列表
        /// </summary>
        public List<string> Successes { get; } = new List<string>();

        /// <summary>
        /// 添加错误信息
        /// </summary>
        /// <param name="error">错误信息</param>
        public void AddError(string error)
        {
            Errors.Add(error);
        }

        /// <summary>
        /// 添加成功信息
        /// </summary>
        /// <param name="success">成功信息</param>
        public void AddSuccess(string success)
        {
            Successes.Add(success);
        }

        /// <summary>
        /// 获取验证摘要
        /// </summary>
        /// <returns>验证摘要</returns>
        public string GetSummary()
        {
            return $"验证结果: {(IsValid ? "通过" : "失败")}, 成功: {Successes.Count}, 错误: {Errors.Count}";
        }
    }
}
