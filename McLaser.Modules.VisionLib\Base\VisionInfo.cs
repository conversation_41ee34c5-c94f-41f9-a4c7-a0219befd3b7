﻿using McLaser.Core.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace McLaser.Modules.Vision
{
    /// <summary>
    /// 九点信息
    /// 索引 像素X,Y;机械X,Y
    /// </summary>
    [Serializable]
    public class NPoint : ObservableObject
    {
        // 索引
        private int _Index;
        public int Index
        {
            get { return _Index; }
            set { Set(ref _Index, value); }
        }

        // 像素X
        private double _ImageX;
        public double ImageX
        {
            get { return _ImageX; }
            set { Set(ref _ImageX, value); }
        }
        // 像素Y
        private double _ImageY;
        public double ImageY
        {
            get { return _ImageY; }
            set { Set(ref _ImageY, value); }
        }
        // 机械X
        private double _RobotX;
        public double RobotX
        {
            get { return _RobotX; }
            set { Set(ref _RobotX, value); }
        }
        // 机械Y
        private double _RobotY;
        public double RobotY
        {
            get { return _RobotY; }
            set { Set(ref _RobotY, value); }
        }

        public NPoint() { }

        public NPoint(int _Index, double _ImageX, double _ImageY, double _RobotX, double _RobotY)
        {
            Index = _Index;
            ImageX = _ImageX;
            ImageY = _ImageY;
            RobotX = _RobotX;
            RobotY = _RobotY;
        }
    }

    [Serializable]
    public class RPoint
    {
        public bool Status = false;
        public double X;
        public double Y;
        public double R;
        public double[] X1;
        public double[] Y1;
        public RPoint() { }
        public RPoint(double x, double y, double r)
        {
            this.X = x;
            this.Y = y;
            this.R = r;
        }
        public RPoint(double[] x, double[] y)
        {
            this.X1 = x;
            this.Y1 = y;
        }
        // 重写点
        public static RPoint operator -(RPoint p1, RPoint p2)
        {
            return new RPoint(p1.X - p2.X, p1.Y - p2.Y, p1.R - p2.R);
        }
        // 获得点矢量长度
        public double GetDistance
        {
            get
            {
                return System.Math.Sqrt(X * X + Y * Y);
            }
        }
        public string ToShowTip()
        {
            return X.ToString() + " | " + Y.ToString();
        }
    }
}
