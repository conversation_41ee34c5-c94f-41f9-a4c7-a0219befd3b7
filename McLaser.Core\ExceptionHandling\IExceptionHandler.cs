using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace McLaser.Core.ExceptionHandling
{
    /// <summary>
    /// 异常处理器接口
    /// 定义了异常处理的基本规范
    /// </summary>
    public interface IExceptionHandler
    {
        /// <summary>
        /// 处理器名称
        /// </summary>
        string Name { get; }

        /// <summary>
        /// 处理器优先级
        /// </summary>
        int Priority { get; }

        /// <summary>
        /// 支持的异常类型
        /// </summary>
        Type[] SupportedExceptionTypes { get; }

        /// <summary>
        /// 是否可以处理指定异常
        /// </summary>
        /// <param name="exception">异常</param>
        /// <returns>是否可以处理</returns>
        bool CanHandle(Exception exception);

        /// <summary>
        /// 处理异常
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="context">异常上下文</param>
        /// <returns>处理结果</returns>
        ExceptionHandlingResult Handle(Exception exception, ExceptionContext context);

        /// <summary>
        /// 异步处理异常
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="context">异常上下文</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>处理任务</returns>
        Task<ExceptionHandlingResult> HandleAsync(Exception exception, ExceptionContext context, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// 泛型异常处理器接口
    /// </summary>
    /// <typeparam name="T">异常类型</typeparam>
    public interface IExceptionHandler<T> : IExceptionHandler where T : Exception
    {
        /// <summary>
        /// 处理特定类型的异常
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="context">异常上下文</param>
        /// <returns>处理结果</returns>
        ExceptionHandlingResult Handle(T exception, ExceptionContext context);

        /// <summary>
        /// 异步处理特定类型的异常
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="context">异常上下文</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>处理任务</returns>
        Task<ExceptionHandlingResult> HandleAsync(T exception, ExceptionContext context, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// 全局异常处理器接口
    /// </summary>
    public interface IGlobalExceptionHandler : IDisposable
    {
        /// <summary>
        /// 是否已启用
        /// </summary>
        bool IsEnabled { get; }

        /// <summary>
        /// 异常处理策略
        /// </summary>
        ExceptionHandlingPolicy Policy { get; set; }

        /// <summary>
        /// 已注册的处理器列表
        /// </summary>
        IReadOnlyList<IExceptionHandler> RegisteredHandlers { get; }

        /// <summary>
        /// 异常处理事件
        /// </summary>
        event EventHandler<ExceptionHandledEventArgs> ExceptionHandled;

        /// <summary>
        /// 未处理异常事件
        /// </summary>
        event EventHandler<UnhandledExceptionEventArgs> UnhandledException;

        /// <summary>
        /// 启动全局异常处理
        /// </summary>
        void Start();

        /// <summary>
        /// 停止全局异常处理
        /// </summary>
        void Stop();

        /// <summary>
        /// 注册异常处理器
        /// </summary>
        /// <param name="handler">异常处理器</param>
        void RegisterHandler(IExceptionHandler handler);

        /// <summary>
        /// 注册异常处理器
        /// </summary>
        /// <typeparam name="T">异常类型</typeparam>
        /// <param name="handler">异常处理器</param>
        void RegisterHandler<T>(IExceptionHandler<T> handler) where T : Exception;

        /// <summary>
        /// 注册异常处理器
        /// </summary>
        /// <typeparam name="T">异常类型</typeparam>
        /// <param name="handleFunc">处理函数</param>
        /// <param name="name">处理器名称</param>
        /// <param name="priority">优先级</param>
        void RegisterHandler<T>(Func<T, ExceptionContext, ExceptionHandlingResult> handleFunc, string name = "", int priority = 0) where T : Exception;

        /// <summary>
        /// 注册异步异常处理器
        /// </summary>
        /// <typeparam name="T">异常类型</typeparam>
        /// <param name="handleFunc">异步处理函数</param>
        /// <param name="name">处理器名称</param>
        /// <param name="priority">优先级</param>
        void RegisterAsyncHandler<T>(Func<T, ExceptionContext, CancellationToken, Task<ExceptionHandlingResult>> handleFunc, string name = "", int priority = 0) where T : Exception;

        /// <summary>
        /// 移除异常处理器
        /// </summary>
        /// <param name="handler">异常处理器</param>
        /// <returns>是否成功移除</returns>
        bool RemoveHandler(IExceptionHandler handler);

        /// <summary>
        /// 移除异常处理器
        /// </summary>
        /// <param name="name">处理器名称</param>
        /// <returns>是否成功移除</returns>
        bool RemoveHandler(string name);

        /// <summary>
        /// 移除指定类型的所有处理器
        /// </summary>
        /// <typeparam name="T">异常类型</typeparam>
        /// <returns>移除的处理器数量</returns>
        int RemoveHandlers<T>() where T : Exception;

        /// <summary>
        /// 清空所有处理器
        /// </summary>
        void ClearHandlers();

        /// <summary>
        /// 处理异常
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="context">异常上下文</param>
        /// <returns>处理结果</returns>
        ExceptionHandlingResult HandleException(Exception exception, ExceptionContext? context = null);

        /// <summary>
        /// 异步处理异常
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="context">异常上下文</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>处理任务</returns>
        Task<ExceptionHandlingResult> HandleExceptionAsync(Exception exception, ExceptionContext? context = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取异常处理器
        /// </summary>
        /// <param name="exceptionType">异常类型</param>
        /// <returns>处理器列表</returns>
        IList<IExceptionHandler> GetHandlers(Type exceptionType);

        /// <summary>
        /// 获取异常处理器
        /// </summary>
        /// <typeparam name="T">异常类型</typeparam>
        /// <returns>处理器列表</returns>
        IList<IExceptionHandler> GetHandlers<T>() where T : Exception;

        /// <summary>
        /// 获取异常处理统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        ExceptionHandlingStatistics GetStatistics();

        /// <summary>
        /// 重置统计信息
        /// </summary>
        void ResetStatistics();

        /// <summary>
        /// 导出异常日志
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>导出任务</returns>
        Task ExportExceptionLogAsync(string filePath, DateTime? startTime = null, DateTime? endTime = null, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// 异常恢复处理器接口
    /// </summary>
    public interface IExceptionRecoveryHandler : IExceptionHandler
    {
        /// <summary>
        /// 是否支持恢复
        /// </summary>
        bool SupportsRecovery { get; }

        /// <summary>
        /// 尝试恢复
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="context">异常上下文</param>
        /// <returns>恢复结果</returns>
        RecoveryResult TryRecover(Exception exception, ExceptionContext context);

        /// <summary>
        /// 异步尝试恢复
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="context">异常上下文</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>恢复任务</returns>
        Task<RecoveryResult> TryRecoverAsync(Exception exception, ExceptionContext context, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// 异常通知处理器接口
    /// </summary>
    public interface IExceptionNotificationHandler : IExceptionHandler
    {
        /// <summary>
        /// 通知配置
        /// </summary>
        NotificationConfiguration Configuration { get; set; }

        /// <summary>
        /// 发送通知
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="context">异常上下文</param>
        /// <param name="result">处理结果</param>
        /// <returns>通知任务</returns>
        Task SendNotificationAsync(Exception exception, ExceptionContext context, ExceptionHandlingResult result);
    }

    /// <summary>
    /// 异常处理器工厂接口
    /// </summary>
    public interface IExceptionHandlerFactory
    {
        /// <summary>
        /// 创建异常处理器
        /// </summary>
        /// <param name="handlerType">处理器类型</param>
        /// <returns>处理器实例</returns>
        IExceptionHandler CreateHandler(Type handlerType);

        /// <summary>
        /// 创建异常处理器
        /// </summary>
        /// <typeparam name="T">处理器类型</typeparam>
        /// <returns>处理器实例</returns>
        T CreateHandler<T>() where T : class, IExceptionHandler;

        /// <summary>
        /// 注册处理器类型
        /// </summary>
        /// <param name="handlerType">处理器类型</param>
        /// <param name="exceptionType">异常类型</param>
        void RegisterHandlerType(Type handlerType, Type exceptionType);

        /// <summary>
        /// 注册处理器类型
        /// </summary>
        /// <typeparam name="THandler">处理器类型</typeparam>
        /// <typeparam name="TException">异常类型</typeparam>
        void RegisterHandlerType<THandler, TException>() 
            where THandler : class, IExceptionHandler<TException> 
            where TException : Exception;

        /// <summary>
        /// 获取已注册的处理器类型
        /// </summary>
        /// <param name="exceptionType">异常类型</param>
        /// <returns>处理器类型列表</returns>
        IList<Type> GetRegisteredHandlerTypes(Type exceptionType);

        /// <summary>
        /// 创建默认处理器
        /// </summary>
        /// <param name="exceptionType">异常类型</param>
        /// <returns>默认处理器</returns>
        IExceptionHandler CreateDefaultHandler(Type exceptionType);
    }
}
