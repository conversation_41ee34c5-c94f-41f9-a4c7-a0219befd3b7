using System;

namespace McLaser.App.Events
{
    /// <summary>
    /// 用户操作事件
    /// 记录用户在系统中执行的各种操作
    /// </summary>
    public class UserActionEvent : IEvent
    {
        /// <summary>
        /// 事件ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 事件时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 操作类型
        /// </summary>
        public string ActionType { get; set; } = string.Empty;

        /// <summary>
        /// 操作描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 事件来源
        /// </summary>
        public string Source { get; set; } = "UserInterface";

        /// <summary>
        /// 事件优先级
        /// </summary>
        public EventPriority Priority { get; set; } = EventPriority.Low;

        /// <summary>
        /// 是否需要持久化
        /// </summary>
        public bool RequiresPersistence { get; set; } = true;

        /// <summary>
        /// 扩展数据
        /// </summary>
        public object? Data { get; set; }

        /// <summary>
        /// 用户IP地址
        /// </summary>
        public string IPAddress { get; set; } = string.Empty;

        /// <summary>
        /// 会话ID
        /// </summary>
        public string SessionId { get; set; } = string.Empty;

        /// <summary>
        /// 操作模块
        /// </summary>
        public string Module { get; set; } = string.Empty;

        /// <summary>
        /// 操作结果
        /// </summary>
        public ActionResult Result { get; set; } = ActionResult.Success;

        /// <summary>
        /// 错误消息（如果操作失败）
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// 操作耗时（毫秒）
        /// </summary>
        public long DurationMs { get; set; } = 0;

        /// <summary>
        /// 构造函数
        /// </summary>
        public UserActionEvent()
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="userName">用户名</param>
        /// <param name="actionType">操作类型</param>
        /// <param name="description">操作描述</param>
        public UserActionEvent(string userName, string actionType, string description)
        {
            UserName = userName;
            ActionType = actionType;
            Description = description;
            
            // 根据操作类型设置优先级
            switch (actionType.ToLower())
            {
                case "登录":
                case "登出":
                    Priority = EventPriority.Normal;
                    RequiresPersistence = true;
                    break;
                case "删除":
                    Priority = EventPriority.High;
                    RequiresPersistence = true;
                    break;
                case "创建":
                case "编辑":
                    Priority = EventPriority.Normal;
                    RequiresPersistence = true;
                    break;
                case "查看":
                    Priority = EventPriority.Low;
                    RequiresPersistence = false;
                    break;
                default:
                    Priority = EventPriority.Low;
                    RequiresPersistence = true;
                    break;
            }
        }

        /// <summary>
        /// 获取事件描述
        /// </summary>
        /// <returns>事件描述</returns>
        public override string ToString()
        {
            return $"UserAction: {UserName} - {ActionType} ({Description})";
        }
    }

    /// <summary>
    /// 操作结果枚举
    /// </summary>
    public enum ActionResult
    {
        /// <summary>
        /// 成功
        /// </summary>
        Success,

        /// <summary>
        /// 失败
        /// </summary>
        Failed,

        /// <summary>
        /// 取消
        /// </summary>
        Cancelled,

        /// <summary>
        /// 超时
        /// </summary>
        Timeout
    }
}
